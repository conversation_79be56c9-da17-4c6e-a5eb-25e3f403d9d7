(()=>{var e={};e.id=636,e.ids=[636],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4518:(e,s,r)=>{Promise.resolve().then(r.bind(r,75758))},8102:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>p,tree:()=>o});var a=r(65239),t=r(48088),l=r(88170),i=r.n(l),n=r(30893),d={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);r.d(s,d);let o={children:["",{children:["profile",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,75758)),"C:\\Users\\<USER>\\Desktop\\Programming\\BudgetWise\\src\\app\\profile\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\Desktop\\Programming\\BudgetWise\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\Programming\\BudgetWise\\src\\app\\profile\\page.tsx"],m={require:r,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:t.RouteKind.APP_PAGE,page:"/profile/page",pathname:"/profile",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29790:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>A});var a=r(60687),t=r(43210),l=r(16189),i=r(78850),n=r(85706),d=r(27605),o=r(63442),c=r(45880),m=r(29523),p=r(89667),u=r(80013),x=r(71669),f=r(29867),h=r(44493),j=r(82614);let g=(0,j.A)("CircleUser",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}],["path",{d:"M7 20.662V19a2 2 0 0 1 2-2h6a2 2 0 0 1 2 2v1.662",key:"154egf"}]]),w=c.Ik({displayName:c.Yj().min(1,{message:"Display name cannot be empty."}).max(50,{message:"Display name must be 50 characters or less."}),email:c.Yj().email().optional()});function y(){let{currentUser:e,updateUserProfile:s,loading:r,error:t,clearError:l}=(0,i.A)(),{toast:n}=(0,f.dj)(),c=(0,d.mN)({resolver:(0,o.u)(w),defaultValues:{displayName:"",email:""}}),j=async r=>{if(e){l();try{await s(r.displayName,void 0),n({title:"Profile Updated",description:"Your display name has been updated."}),c.reset({},{keepValues:!0,keepDirty:!1,keepDefaultValues:!1})}catch(e){n({variant:"destructive",title:"Update Failed",description:t||e.message||"Could not update profile."})}}};return(0,a.jsxs)(h.Zp,{children:[(0,a.jsxs)(h.aR,{children:[(0,a.jsxs)(h.ZB,{className:"text-lg flex items-center gap-2 font-headline",children:[(0,a.jsx)(g,{className:"h-5 w-5 text-primary"}),"Profile Information"]}),(0,a.jsx)(h.BT,{children:"Update your display name and view your email address."})]}),(0,a.jsx)(h.Wu,{children:(0,a.jsx)(x.lV,{...c,children:(0,a.jsxs)("form",{onSubmit:c.handleSubmit(j),className:"space-y-6",children:[(0,a.jsx)(x.zB,{control:c.control,name:"displayName",render:({field:e})=>(0,a.jsxs)(x.eI,{children:[(0,a.jsx)(u.J,{htmlFor:"displayName",children:"Display Name"}),(0,a.jsx)(x.MJ,{children:(0,a.jsx)(p.p,{id:"displayName",placeholder:"Your display name",...e})}),(0,a.jsx)(x.C5,{})]})}),(0,a.jsx)(x.zB,{control:c.control,name:"email",render:({field:e})=>(0,a.jsxs)(x.eI,{children:[(0,a.jsx)(u.J,{htmlFor:"email",children:"Email"}),(0,a.jsx)(x.MJ,{children:(0,a.jsx)(p.p,{id:"email",type:"email",...e,readOnly:!0,disabled:!0,className:"bg-muted/50 cursor-not-allowed"})}),(0,a.jsx)(x.Rr,{children:"Your email address cannot be changed here."}),(0,a.jsx)(x.C5,{})]})}),(0,a.jsx)(m.$,{type:"submit",disabled:r||!c.formState.isDirty,children:r?"Saving...":"Save Name Changes"})]})})})]})}var v=r(32584);let N=(0,j.A)("ImageUp",[["path",{d:"M10.3 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2v10l-3.1-3.1a2 2 0 0 0-2.814.014L6 21",key:"9csbqa"}],["path",{d:"m14 19.5 3-3 3 3",key:"9vmjn0"}],["path",{d:"M17 22v-5.5",key:"1aa6fl"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}]]);var b=r(57207);function P(){let{currentUser:e,updateUserProfile:s,loading:r,error:l,clearError:n}=(0,i.A)(),[d,o]=(0,t.useState)(null),[c,x]=(0,t.useState)(null),j=(0,t.useRef)(null),{toast:g}=(0,f.dj)(),w=async()=>{if(!d){g({variant:"destructive",title:"No file selected",description:"Please select an image to upload."});return}if(e){n();try{await s(e.displayName,d),g({title:"Profile Picture Updated",description:"Your new profile picture has been saved."}),o(null),x(null),j.current&&(j.current.value="")}catch(e){g({variant:"destructive",title:"Upload Failed",description:l||e.message||"Could not upload profile picture."})}}},y=async()=>{if(e){if(!e.photoURL){g({title:"No Picture to Remove",description:"You do not have a profile picture set."});return}n();try{await s(e.displayName,null),g({title:"Profile Picture Removed",description:"Your profile picture has been removed."}),o(null),x(null),j.current&&(j.current.value="")}catch(e){g({variant:"destructive",title:"Removal Failed",description:l||e.message||"Could not remove profile picture."})}}};return(0,a.jsxs)(h.Zp,{children:[(0,a.jsxs)(h.aR,{children:[(0,a.jsxs)(h.ZB,{className:"text-lg flex items-center gap-2 font-headline",children:[(0,a.jsx)(N,{className:"h-5 w-5 text-primary"}),"Profile Picture"]}),(0,a.jsx)(h.BT,{children:"Upload, change, or remove your profile picture. Max 5MB."})]}),(0,a.jsxs)(h.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex flex-col items-center space-y-4",children:[(0,a.jsxs)(v.eu,{className:"h-32 w-32 text-4xl",children:[(0,a.jsx)(v.BK,{src:c||e?.photoURL||void 0,alt:e?.displayName||"User"}),(0,a.jsx)(v.q5,{children:((e,s)=>{if(s){let e=s.split(" ").filter(Boolean);return e.length>1?(e[0][0]+e[1][0]).toUpperCase():s.substring(0,2).toUpperCase()}return e?e.substring(0,2).toUpperCase():"U"})(e?.email,e?.displayName)})]}),(0,a.jsxs)("div",{className:"w-full max-w-xs space-y-2",children:[(0,a.jsx)(u.J,{htmlFor:"profilePictureFile",className:"sr-only",children:"Choose profile picture"}),(0,a.jsx)(p.p,{id:"profilePictureFile",type:"file",accept:"image/png, image/jpeg, image/gif",onChange:e=>{let s=e.target.files?.[0];if(s){if(s.size>5242880){g({variant:"destructive",title:"File too large",description:"Please select an image smaller than 5MB."}),j.current&&(j.current.value=""),o(null),x(null);return}o(s),c&&c.startsWith("blob:")&&URL.revokeObjectURL(c),x(URL.createObjectURL(s))}else o(null),x(null)},ref:j,disabled:r})]})]}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-2 justify-center",children:[d&&(0,a.jsxs)(m.$,{onClick:w,disabled:r||!d,className:"flex-grow",children:[(0,a.jsx)(N,{className:"mr-2 h-4 w-4"}),r?"Uploading...":"Upload Picture"]}),e?.photoURL&&(0,a.jsxs)(m.$,{onClick:y,variant:"outline",className:"flex-grow",disabled:r,children:[(0,a.jsx)(b.A,{className:"mr-2 h-4 w-4"})," Remove Picture"]})]})]})]})}let C=(0,j.A)("KeyRound",[["path",{d:"M2.586 17.414A2 2 0 0 0 2 18.828V21a1 1 0 0 0 1 1h3a1 1 0 0 0 1-1v-1a1 1 0 0 1 1-1h1a1 1 0 0 0 1-1v-1a1 1 0 0 1 1-1h.172a2 2 0 0 0 1.414-.586l.814-.814a6.5 6.5 0 1 0-4-4z",key:"1s6t7t"}],["circle",{cx:"16.5",cy:"7.5",r:".5",fill:"currentColor",key:"w0ekpg"}]]),k=c.Ik({currentPassword:c.Yj().min(1,{message:"Current password is required."}),newPassword:c.Yj().min(6,{message:"New password must be at least 6 characters."}),confirmNewPassword:c.Yj()}).refine(e=>e.newPassword===e.confirmNewPassword,{message:"New passwords don't match.",path:["confirmNewPassword"]});function R(){let{changeUserPassword:e,loading:s,error:r,clearError:t,currentUser:l}=(0,i.A)(),{toast:n}=(0,f.dj)(),c=(0,d.mN)({resolver:(0,o.u)(k),defaultValues:{currentPassword:"",newPassword:"",confirmNewPassword:""}}),j=l?.providerData.some(e=>"password"===e.providerId),g=async s=>{t();try{await e(s.currentPassword,s.newPassword),n({title:"Password Changed",description:"Your password has been updated successfully."}),c.reset()}catch(e){n({variant:"destructive",title:"Password Change Failed",description:r||e.message||"Could not change password."})}};return j?(0,a.jsxs)(h.Zp,{children:[(0,a.jsxs)(h.aR,{children:[(0,a.jsxs)(h.ZB,{className:"text-lg flex items-center gap-2 font-headline",children:[(0,a.jsx)(C,{className:"h-5 w-5 text-primary"}),"Change Password"]}),(0,a.jsx)(h.BT,{children:"Update your account password."})]}),(0,a.jsx)(h.Wu,{children:(0,a.jsx)(x.lV,{...c,children:(0,a.jsxs)("form",{onSubmit:c.handleSubmit(g),className:"space-y-6",children:[(0,a.jsx)(x.zB,{control:c.control,name:"currentPassword",render:({field:e})=>(0,a.jsxs)(x.eI,{children:[(0,a.jsx)(u.J,{htmlFor:"currentPassword",children:"Current Password"}),(0,a.jsx)(x.MJ,{children:(0,a.jsx)(p.p,{id:"currentPassword",type:"password",placeholder:"••••••••",...e})}),(0,a.jsx)(x.C5,{})]})}),(0,a.jsx)(x.zB,{control:c.control,name:"newPassword",render:({field:e})=>(0,a.jsxs)(x.eI,{children:[(0,a.jsx)(u.J,{htmlFor:"newPassword",children:"New Password"}),(0,a.jsx)(x.MJ,{children:(0,a.jsx)(p.p,{id:"newPassword",type:"password",placeholder:"••••••••",...e})}),(0,a.jsx)(x.C5,{})]})}),(0,a.jsx)(x.zB,{control:c.control,name:"confirmNewPassword",render:({field:e})=>(0,a.jsxs)(x.eI,{children:[(0,a.jsx)(u.J,{htmlFor:"confirmNewPassword",children:"Confirm New Password"}),(0,a.jsx)(x.MJ,{children:(0,a.jsx)(p.p,{id:"confirmNewPassword",type:"password",placeholder:"••••••••",...e})}),(0,a.jsx)(x.C5,{})]})}),(0,a.jsx)(m.$,{type:"submit",disabled:s,children:s?"Updating Password...":"Update Password"})]})})})]}):(0,a.jsxs)(h.Zp,{children:[(0,a.jsx)(h.aR,{children:(0,a.jsxs)(h.ZB,{className:"text-lg flex items-center gap-2 font-headline",children:[(0,a.jsx)(C,{className:"h-5 w-5 text-primary"}),"Change Password"]})}),(0,a.jsx)(h.Wu,{children:(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"You signed in using a social provider (e.g., Google). Password changes are managed through your social provider account."})})]})}var B=r(39907),U=r(85726);function A(){let{currentUser:e,loading:s}=(0,i.A)();return((0,l.useRouter)(),s||!e)?(0,a.jsxs)("div",{className:"flex flex-col min-h-screen bg-background",children:[(0,a.jsx)(n.default,{title:"BudgetWise"}),(0,a.jsx)("main",{className:"flex-grow container mx-auto py-6 sm:py-8 px-2 sm:px-4",children:(0,a.jsxs)("div",{className:"max-w-2xl mx-auto space-y-6 sm:space-y-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(U.EA,{className:"h-8 w-1/2 mb-2"}),(0,a.jsx)(U.EA,{className:"h-4 w-3/4"})]}),(0,a.jsx)(B.Separator,{}),(0,a.jsx)(U.EA,{className:"h-60 w-full"}),(0,a.jsx)(U.EA,{className:"h-60 w-full"}),(0,a.jsx)(U.EA,{className:"h-60 w-full"})]})})]}):(0,a.jsxs)("div",{className:"flex flex-col min-h-screen bg-background",children:[(0,a.jsx)(n.default,{title:"BudgetWise"}),(0,a.jsx)("main",{className:"flex-grow container mx-auto py-6 sm:py-8 px-2 sm:px-4",children:(0,a.jsxs)("div",{className:"max-w-2xl mx-auto space-y-6 sm:space-y-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl sm:text-3xl font-headline font-bold text-foreground",children:"Profile Settings"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"Manage your account details and preferences."})]}),(0,a.jsx)(B.Separator,{}),(0,a.jsx)(y,{}),(0,a.jsx)(B.Separator,{}),(0,a.jsx)(P,{}),(0,a.jsx)(B.Separator,{}),(0,a.jsx)(R,{})]})})]})}},33873:e=>{"use strict";e.exports=require("path")},39907:(e,s,r)=>{"use strict";r.d(s,{Separator:()=>c});var a=r(60687),t=r(43210),l=r(14163),i="horizontal",n=["horizontal","vertical"],d=t.forwardRef((e,s)=>{var r;let{decorative:t,orientation:d=i,...o}=e,c=(r=d,n.includes(r))?d:i;return(0,a.jsx)(l.sG.div,{"data-orientation":c,...t?{role:"none"}:{"aria-orientation":"vertical"===c?c:void 0,role:"separator"},...o,ref:s})});d.displayName="Separator";var o=r(4780);let c=t.forwardRef(({className:e,orientation:s="horizontal",decorative:r=!0,...t},l)=>(0,a.jsx)(d,{ref:l,decorative:r,orientation:s,className:(0,o.cn)("shrink-0 bg-border","horizontal"===s?"h-[1px] w-full":"h-full w-[1px]",e),...t}));c.displayName=d.displayName},44493:(e,s,r)=>{"use strict";r.d(s,{BT:()=>o,Wu:()=>c,ZB:()=>d,Zp:()=>i,aR:()=>n,wL:()=>m});var a=r(60687),t=r(43210),l=r(4780);let i=t.forwardRef(({className:e,...s},r)=>(0,a.jsx)("div",{ref:r,className:(0,l.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...s}));i.displayName="Card";let n=t.forwardRef(({className:e,...s},r)=>(0,a.jsx)("div",{ref:r,className:(0,l.cn)("flex flex-col space-y-1.5 p-6",e),...s}));n.displayName="CardHeader";let d=t.forwardRef(({className:e,...s},r)=>(0,a.jsx)("div",{ref:r,className:(0,l.cn)("text-2xl font-semibold leading-none tracking-tight",e),...s}));d.displayName="CardTitle";let o=t.forwardRef(({className:e,...s},r)=>(0,a.jsx)("div",{ref:r,className:(0,l.cn)("text-sm text-muted-foreground",e),...s}));o.displayName="CardDescription";let c=t.forwardRef(({className:e,...s},r)=>(0,a.jsx)("div",{ref:r,className:(0,l.cn)("p-6 pt-0",e),...s}));c.displayName="CardContent";let m=t.forwardRef(({className:e,...s},r)=>(0,a.jsx)("div",{ref:r,className:(0,l.cn)("flex items-center p-6 pt-0",e),...s}));m.displayName="CardFooter"},57207:(e,s,r)=>{"use strict";r.d(s,{A:()=>a});let a=(0,r(82614).A)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},71669:(e,s,r)=>{"use strict";r.d(s,{C5:()=>g,MJ:()=>h,Rr:()=>j,eI:()=>x,lR:()=>f,lV:()=>o,zB:()=>m});var a=r(60687),t=r(43210),l=r(8730),i=r(27605),n=r(4780),d=r(80013);let o=i.Op,c=t.createContext({}),m=({...e})=>(0,a.jsx)(c.Provider,{value:{name:e.name},children:(0,a.jsx)(i.xI,{...e})}),p=()=>{let e=t.useContext(c),s=t.useContext(u),{getFieldState:r,formState:a}=(0,i.xW)(),l=r(e.name,a);if(!e)throw Error("useFormField should be used within <FormField>");let{id:n}=s;return{id:n,name:e.name,formItemId:`${n}-form-item`,formDescriptionId:`${n}-form-item-description`,formMessageId:`${n}-form-item-message`,...l}},u=t.createContext({}),x=t.forwardRef(({className:e,...s},r)=>{let l=t.useId();return(0,a.jsx)(u.Provider,{value:{id:l},children:(0,a.jsx)("div",{ref:r,className:(0,n.cn)("space-y-2",e),...s})})});x.displayName="FormItem";let f=t.forwardRef(({className:e,...s},r)=>{let{error:t,formItemId:l}=p();return(0,a.jsx)(d.J,{ref:r,className:(0,n.cn)(t&&"text-destructive",e),htmlFor:l,...s})});f.displayName="FormLabel";let h=t.forwardRef(({...e},s)=>{let{error:r,formItemId:t,formDescriptionId:i,formMessageId:n}=p();return(0,a.jsx)(l.DX,{ref:s,id:t,"aria-describedby":r?`${i} ${n}`:`${i}`,"aria-invalid":!!r,...e})});h.displayName="FormControl";let j=t.forwardRef(({className:e,...s},r)=>{let{formDescriptionId:t}=p();return(0,a.jsx)("p",{ref:r,id:t,className:(0,n.cn)("text-sm text-muted-foreground",e),...s})});j.displayName="FormDescription";let g=t.forwardRef(({className:e,children:s,...r},t)=>{let{error:l,formMessageId:i}=p(),d=l?String(l?.message??""):s;return d?(0,a.jsx)("p",{ref:t,id:i,className:(0,n.cn)("text-sm font-medium text-destructive",e),...r,children:d}):null});g.displayName="FormMessage"},75758:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programming\\\\BudgetWise\\\\src\\\\app\\\\profile\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Programming\\BudgetWise\\src\\app\\profile\\page.tsx","default")},79551:e=>{"use strict";e.exports=require("url")},80013:(e,s,r)=>{"use strict";r.d(s,{J:()=>o});var a=r(60687),t=r(43210),l=r(78148),i=r(24224),n=r(4780);let d=(0,i.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),o=t.forwardRef(({className:e,...s},r)=>(0,a.jsx)(l.b,{ref:r,className:(0,n.cn)(d(),e),...s}));o.displayName=l.b.displayName},85726:(e,s,r)=>{"use strict";r.d(s,{EA:()=>l,eX:()=>d});var a=r(60687),t=r(4780);function l({className:e,...s}){return(0,a.jsx)("div",{className:(0,t.cn)("animate-pulse rounded-md bg-muted",e),...s})}function i(){return(0,a.jsxs)("div",{className:"rounded-lg border bg-card p-6 shadow-sm animate-fade-in",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(l,{className:"h-5 w-5 rounded"}),(0,a.jsx)(l,{className:"h-5 w-32"})]}),(0,a.jsx)(l,{className:"h-8 w-20 rounded-md"})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)(l,{className:"h-4 w-24"}),(0,a.jsx)(l,{className:"h-4 w-16"})]}),(0,a.jsx)(l,{className:"h-2 w-full rounded-full"}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)(l,{className:"h-4 w-20"}),(0,a.jsx)(l,{className:"h-4 w-16"})]})]})]})}function n(){return(0,a.jsxs)("div",{className:"rounded-lg border bg-card p-6 shadow-sm animate-fade-in",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-6",children:[(0,a.jsx)(l,{className:"h-5 w-5 rounded"}),(0,a.jsx)(l,{className:"h-5 w-32"})]}),(0,a.jsx)("div",{className:"flex items-center justify-center",children:(0,a.jsx)(l,{className:"h-48 w-48 rounded-full"})}),(0,a.jsxs)("div",{className:"mt-6 flex flex-wrap gap-2 justify-center",children:[(0,a.jsx)(l,{className:"h-4 w-16 rounded-full"}),(0,a.jsx)(l,{className:"h-4 w-20 rounded-full"}),(0,a.jsx)(l,{className:"h-4 w-14 rounded-full"}),(0,a.jsx)(l,{className:"h-4 w-18 rounded-full"})]})]})}function d(){return(0,a.jsx)("div",{className:"container mx-auto py-4 sm:py-6 px-2 sm:px-4",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 sm:gap-6",children:[(0,a.jsxs)("div",{className:"md:col-span-1 space-y-4 sm:space-y-6",children:[(0,a.jsx)(i,{}),(0,a.jsx)(i,{}),(0,a.jsx)(i,{})]}),(0,a.jsxs)("div",{className:"md:col-span-2 space-y-4 sm:space-y-6",children:[(0,a.jsx)(i,{}),(0,a.jsx)(n,{})]})]})})}},89667:(e,s,r)=>{"use strict";r.d(s,{p:()=>i});var a=r(60687),t=r(43210),l=r(4780);let i=t.forwardRef(({className:e,type:s,...r},t)=>(0,a.jsx)("input",{type:s,className:(0,l.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:t,...r}));i.displayName="Input"},90606:(e,s,r)=>{Promise.resolve().then(r.bind(r,29790))}};var s=require("../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),a=s.X(0,[447,242,99,758,330,253,529],()=>r(8102));module.exports=a})();