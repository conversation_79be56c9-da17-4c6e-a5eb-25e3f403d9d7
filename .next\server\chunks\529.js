"use strict";exports.id=529,exports.ids=[529],exports.modules={29523:(e,a,s)=>{s.d(a,{$:()=>d,r:()=>l});var r=s(60687),t=s(43210),i=s(8730),n=s(24224),o=s(4780);let l=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-all duration-200 ease-in-out focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 active:scale-95",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90 hover:shadow-md hover:-translate-y-0.5",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90 hover:shadow-md hover:-translate-y-0.5",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground hover:shadow-sm hover:-translate-y-0.5",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80 hover:shadow-sm hover:-translate-y-0.5",ghost:"hover:bg-accent hover:text-accent-foreground hover:shadow-sm",link:"text-primary underline-offset-4 hover:underline",gradient:"btn-gradient-primary hover:shadow-lg","gradient-accent":"btn-gradient-accent hover:shadow-lg",success:"bg-green-600 text-white hover:bg-green-700 hover:shadow-md hover:-translate-y-0.5",warning:"bg-yellow-600 text-white hover:bg-yellow-700 hover:shadow-md hover:-translate-y-0.5",info:"bg-blue-600 text-white hover:bg-blue-700 hover:shadow-md hover:-translate-y-0.5"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3 text-xs",lg:"h-11 rounded-md px-8 text-base",xl:"h-12 rounded-lg px-10 text-lg",icon:"h-10 w-10","icon-sm":"h-8 w-8","icon-lg":"h-12 w-12"}},defaultVariants:{variant:"default",size:"default"}}),d=t.forwardRef(({className:e,variant:a,size:s,asChild:t=!1,...n},d)=>{let c=t?i.DX:"button";return(0,r.jsx)(c,{className:(0,o.cn)(l({variant:a,size:s,className:e})),ref:d,...n})});d.displayName="Button"},32584:(e,a,s)=>{s.d(a,{BK:()=>l,eu:()=>o,q5:()=>d});var r=s(60687),t=s(43210),i=s(92951),n=s(4780);let o=t.forwardRef(({className:e,...a},s)=>(0,r.jsx)(i.bL,{ref:s,className:(0,n.cn)("relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full",e),...a}));o.displayName=i.bL.displayName;let l=t.forwardRef(({className:e,...a},s)=>(0,r.jsx)(i._V,{ref:s,className:(0,n.cn)("aspect-square h-full w-full",e),...a}));l.displayName=i._V.displayName;let d=t.forwardRef(({className:e,...a},s)=>(0,r.jsx)(i.H4,{ref:s,className:(0,n.cn)("flex h-full w-full items-center justify-center rounded-full bg-muted",e),...a}));d.displayName=i.H4.displayName},70440:(e,a,s)=>{s.r(a),s.d(a,{default:()=>t});var r=s(31658);let t=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},85706:(e,a,s)=>{s.d(a,{default:()=>C});var r=s(60687),t=s(85814),i=s.n(t),n=s(97905),o=s(35849),l=s(76311),d=s(11003),c=s(99196),m=s(58369),h=s(49497),p=s(13914),f=s(71032),x=s(29523),u=s(78850),g=s(43210),v=s(29398),y=s(74158),b=s(58450),w=s(73256),N=s(4780);let j=v.bL,z=v.l9;v.YJ,v.ZL,v.Pb,v.z6,g.forwardRef(({className:e,inset:a,children:s,...t},i)=>(0,r.jsxs)(v.ZP,{ref:i,className:(0,N.cn)("flex cursor-default gap-2 select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",a&&"pl-8",e),...t,children:[s,(0,r.jsx)(y.A,{className:"ml-auto"})]})).displayName=v.ZP.displayName,g.forwardRef(({className:e,...a},s)=>(0,r.jsx)(v.G5,{ref:s,className:(0,N.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...a})).displayName=v.G5.displayName;let P=g.forwardRef(({className:e,sideOffset:a=4,...s},t)=>(0,r.jsx)(v.ZL,{children:(0,r.jsx)(v.UC,{ref:t,sideOffset:a,className:(0,N.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...s})}));P.displayName=v.UC.displayName;let A=g.forwardRef(({className:e,inset:a,...s},t)=>(0,r.jsx)(v.q7,{ref:t,className:(0,N.cn)("relative flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",a&&"pl-8",e),...s}));A.displayName=v.q7.displayName,g.forwardRef(({className:e,children:a,checked:s,...t},i)=>(0,r.jsxs)(v.H_,{ref:i,className:(0,N.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),checked:s,...t,children:[(0,r.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,r.jsx)(v.VF,{children:(0,r.jsx)(b.A,{className:"h-4 w-4"})})}),a]})).displayName=v.H_.displayName,g.forwardRef(({className:e,children:a,...s},t)=>(0,r.jsxs)(v.hN,{ref:t,className:(0,N.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...s,children:[(0,r.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,r.jsx)(v.VF,{children:(0,r.jsx)(w.A,{className:"h-2 w-2 fill-current"})})}),a]})).displayName=v.hN.displayName;let R=g.forwardRef(({className:e,inset:a,...s},t)=>(0,r.jsx)(v.JU,{ref:t,className:(0,N.cn)("px-2 py-1.5 text-sm font-semibold",a&&"pl-8",e),...s}));R.displayName=v.JU.displayName;let _=g.forwardRef(({className:e,...a},s)=>(0,r.jsx)(v.wv,{ref:s,className:(0,N.cn)("-mx-1 my-1 h-px bg-muted",e),...a}));_.displayName=v.wv.displayName;var k=s(32584);function C({title:e,balancesVisible:a,onToggleBalances:s}){let{currentUser:t,signOut:g,loading:v}=(0,u.A)();return(0,r.jsx)(n.P.header,{initial:{y:-100,opacity:0},animate:{y:0,opacity:1},transition:{duration:.5,ease:"easeOut"},className:"bg-card/95 backdrop-blur-md shadow-sm sticky top-0 z-40 border-b border-border/50",children:(0,r.jsxs)("div",{className:"container mx-auto flex items-center justify-between py-3 px-4 sm:px-6",children:[(0,r.jsx)(n.P.div,{whileHover:{scale:1.05},whileTap:{scale:.95},children:(0,r.jsxs)(i(),{href:"/",className:"flex items-center gap-2 group",children:[(0,r.jsx)(n.P.div,{animate:{rotate:[0,10,-10,0]},transition:{duration:2,repeat:1/0,repeatDelay:5},className:"p-1 rounded-full bg-gradient-to-r from-primary/20 to-primary/10 group-hover:from-primary/30 group-hover:to-primary/20 transition-all duration-300",children:(0,r.jsx)(o.A,{className:"h-7 w-7 text-primary"})}),(0,r.jsx)("h1",{className:"text-xl sm:text-2xl font-headline font-bold bg-gradient-to-r from-foreground to-foreground/80 bg-clip-text text-transparent",children:e})]})}),(0,r.jsxs)("nav",{className:"flex items-center gap-2 sm:gap-3",children:[s&&(0,r.jsx)(n.P.div,{whileHover:{scale:1.1},whileTap:{scale:.9},children:(0,r.jsxs)(x.$,{variant:"ghost",size:"icon",onClick:s,className:"text-foreground hover:text-foreground/80 h-8 w-8 sm:h-9 sm:w-9 hover:bg-primary/10 transition-all duration-200",children:[(0,r.jsx)(n.P.div,{initial:!1,animate:{rotate:180*!a},transition:{duration:.3},children:a?(0,r.jsx)(l.A,{className:"h-4 w-4 sm:h-5 sm:w-5"}):(0,r.jsx)(d.A,{className:"h-4 w-4 sm:h-5 sm:w-5"})}),(0,r.jsx)("span",{className:"sr-only",children:a?"Hide Balances":"Show Balances"})]})}),(0,r.jsx)(n.P.div,{whileHover:{scale:1.05},whileTap:{scale:.95},children:(0,r.jsxs)(i(),{href:"/tips",className:"text-sm sm:text-base text-primary hover:text-primary/80 transition-all duration-200 hidden sm:flex items-center gap-1 px-3 py-2 rounded-md hover:bg-primary/10",children:[(0,r.jsx)(c.A,{className:"h-4 w-4"}),"Budgeting Tips"]})}),(0,r.jsx)(n.P.div,{whileHover:{scale:1.1},whileTap:{scale:.9},className:"sm:hidden",children:(0,r.jsx)(x.$,{variant:"ghost",size:"icon",asChild:!0,className:"text-primary hover:text-primary/80 h-8 w-8 hover:bg-primary/10",children:(0,r.jsxs)(i(),{href:"/tips",children:[(0,r.jsx)(c.A,{className:"h-5 w-5"}),(0,r.jsx)("span",{className:"sr-only",children:"Budgeting Tips"})]})})}),v?(0,r.jsx)(n.P.div,{initial:{opacity:0},animate:{opacity:1},className:"h-8 w-20 bg-muted rounded animate-pulse"}):t?(0,r.jsx)(n.P.div,{initial:{opacity:0,scale:.8},animate:{opacity:1,scale:1},transition:{delay:.2},children:(0,r.jsxs)(j,{children:[(0,r.jsx)(z,{asChild:!0,children:(0,r.jsx)(n.P.div,{whileHover:{scale:1.1},whileTap:{scale:.9},children:(0,r.jsx)(x.$,{variant:"ghost",className:"relative h-8 w-8 rounded-full hover:bg-primary/10 transition-all duration-200",children:(0,r.jsxs)(k.eu,{className:"h-8 w-8 ring-2 ring-primary/20 hover:ring-primary/40 transition-all duration-200",children:[(0,r.jsx)(k.BK,{src:t.photoURL||void 0,alt:t.displayName||t.email||"User"}),(0,r.jsx)(k.q5,{className:"bg-gradient-to-r from-primary/20 to-accent/20 text-foreground font-semibold",children:((e,a)=>{if(a){let e=a.split(" ").filter(Boolean);return e.length>1?(e[0][0]+e[1][0]).toUpperCase():a.substring(0,2).toUpperCase()}return e?e.substring(0,2).toUpperCase():"U"})(t.email,t.displayName)})]})})})}),(0,r.jsxs)(P,{className:"w-56 glass",align:"end",forceMount:!0,children:[(0,r.jsx)(R,{className:"font-normal",children:(0,r.jsxs)("div",{className:"flex flex-col space-y-1",children:[(0,r.jsx)("p",{className:"text-sm font-medium leading-none",children:t.displayName||t.email?.split("@")[0]}),(0,r.jsx)("p",{className:"text-xs leading-none text-muted-foreground",children:t.email})]})}),(0,r.jsx)(_,{}),(0,r.jsx)(A,{asChild:!0,className:"cursor-pointer hover:bg-primary/10 transition-colors",children:(0,r.jsxs)(i(),{href:"/profile",children:[(0,r.jsx)(m.A,{className:"mr-2 h-4 w-4"}),(0,r.jsx)("span",{children:"Profile Settings"})]})}),(0,r.jsx)(_,{}),(0,r.jsxs)(A,{onClick:g,className:"cursor-pointer hover:bg-destructive/10 text-destructive transition-colors",children:[(0,r.jsx)(h.A,{className:"mr-2 h-4 w-4"}),(0,r.jsx)("span",{children:"Log out"})]})]})]})}):(0,r.jsxs)(n.P.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},transition:{delay:.3},className:"flex items-center gap-2",children:[(0,r.jsx)(n.P.div,{whileHover:{scale:1.05},whileTap:{scale:.95},children:(0,r.jsx)(x.$,{variant:"ghost",asChild:!0,size:"sm",className:"hover:bg-primary/10",children:(0,r.jsxs)(i(),{href:"/login",children:[(0,r.jsx)(p.A,{className:"mr-1 h-4 w-4 sm:mr-2"})," Login"]})})}),(0,r.jsx)(n.P.div,{whileHover:{scale:1.05},whileTap:{scale:.95},children:(0,r.jsx)(x.$,{asChild:!0,size:"sm",className:"bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70",children:(0,r.jsxs)(i(),{href:"/register",children:[(0,r.jsx)(f.A,{className:"mr-1 h-4 w-4 sm:mr-2"})," Sign Up"]})})})]})]})]})})}}};