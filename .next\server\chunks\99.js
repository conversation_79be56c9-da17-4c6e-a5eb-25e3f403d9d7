exports.id=99,exports.ids=[99],exports.modules={2015:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getNamedMiddlewareRegex:function(){return m},getNamedRouteRegex:function(){return p},getRouteRegex:function(){return d},parseParameter:function(){return l}});let n=r(46143),i=r(71437),o=r(53293),a=r(72887),s=/^([^[]*)\[((?:\[[^\]]*\])|[^\]]+)\](.*)$/;function l(e){let t=e.match(s);return t?u(t[2]):u(e)}function u(e){let t=e.startsWith("[")&&e.endsWith("]");t&&(e=e.slice(1,-1));let r=e.startsWith("...");return r&&(e=e.slice(3)),{key:e,repeat:r,optional:t}}function c(e,t,r){let n={},l=1,c=[];for(let d of(0,a.removeTrailingSlash)(e).slice(1).split("/")){let e=i.INTERCEPTION_ROUTE_MARKERS.find(e=>d.startsWith(e)),a=d.match(s);if(e&&a&&a[2]){let{key:t,optional:r,repeat:i}=u(a[2]);n[t]={pos:l++,repeat:i,optional:r},c.push("/"+(0,o.escapeStringRegexp)(e)+"([^/]+?)")}else if(a&&a[2]){let{key:e,repeat:t,optional:i}=u(a[2]);n[e]={pos:l++,repeat:t,optional:i},r&&a[1]&&c.push("/"+(0,o.escapeStringRegexp)(a[1]));let s=t?i?"(?:/(.+?))?":"/(.+?)":"/([^/]+?)";r&&a[1]&&(s=s.substring(1)),c.push(s)}else c.push("/"+(0,o.escapeStringRegexp)(d));t&&a&&a[3]&&c.push((0,o.escapeStringRegexp)(a[3]))}return{parameterizedRoute:c.join(""),groups:n}}function d(e,t){let{includeSuffix:r=!1,includePrefix:n=!1,excludeOptionalTrailingSlash:i=!1}=void 0===t?{}:t,{parameterizedRoute:o,groups:a}=c(e,r,n),s=o;return i||(s+="(?:/)?"),{re:RegExp("^"+s+"$"),groups:a}}function h(e){let t,{interceptionMarker:r,getSafeRouteKey:n,segment:i,routeKeys:a,keyPrefix:s,backreferenceDuplicateKeys:l}=e,{key:c,optional:d,repeat:h}=u(i),f=c.replace(/\W/g,"");s&&(f=""+s+f);let p=!1;(0===f.length||f.length>30)&&(p=!0),isNaN(parseInt(f.slice(0,1)))||(p=!0),p&&(f=n());let m=f in a;s?a[f]=""+s+c:a[f]=c;let g=r?(0,o.escapeStringRegexp)(r):"";return t=m&&l?"\\k<"+f+">":h?"(?<"+f+">.+?)":"(?<"+f+">[^/]+?)",d?"(?:/"+g+t+")?":"/"+g+t}function f(e,t,r,l,u){let c;let d=(c=0,()=>{let e="",t=++c;for(;t>0;)e+=String.fromCharCode(97+(t-1)%26),t=Math.floor((t-1)/26);return e}),f={},p=[];for(let c of(0,a.removeTrailingSlash)(e).slice(1).split("/")){let e=i.INTERCEPTION_ROUTE_MARKERS.some(e=>c.startsWith(e)),a=c.match(s);if(e&&a&&a[2])p.push(h({getSafeRouteKey:d,interceptionMarker:a[1],segment:a[2],routeKeys:f,keyPrefix:t?n.NEXT_INTERCEPTION_MARKER_PREFIX:void 0,backreferenceDuplicateKeys:u}));else if(a&&a[2]){l&&a[1]&&p.push("/"+(0,o.escapeStringRegexp)(a[1]));let e=h({getSafeRouteKey:d,segment:a[2],routeKeys:f,keyPrefix:t?n.NEXT_QUERY_PARAM_PREFIX:void 0,backreferenceDuplicateKeys:u});l&&a[1]&&(e=e.substring(1)),p.push(e)}else p.push("/"+(0,o.escapeStringRegexp)(c));r&&a&&a[3]&&p.push((0,o.escapeStringRegexp)(a[3]))}return{namedParameterizedRoute:p.join(""),routeKeys:f}}function p(e,t){var r,n,i;let o=f(e,t.prefixRouteKeys,null!=(r=t.includeSuffix)&&r,null!=(n=t.includePrefix)&&n,null!=(i=t.backreferenceDuplicateKeys)&&i),a=o.namedParameterizedRoute;return t.excludeOptionalTrailingSlash||(a+="(?:/)?"),{...d(e,t),namedRegex:"^"+a+"$",routeKeys:o.routeKeys}}function m(e,t){let{parameterizedRoute:r}=c(e,!1,!1),{catchAll:n=!0}=t;if("/"===r)return{namedRegex:"^/"+(n?".*":"")+"$"};let{namedParameterizedRoute:i}=f(e,!1,!1,!1,!1);return{namedRegex:"^"+i+(n?"(?:(/.*)?)":"")+"$"}}},2030:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isNavigatingToNewRootLayout",{enumerable:!0,get:function(){return function e(t,r){let n=t[0],i=r[0];if(Array.isArray(n)&&Array.isArray(i)){if(n[0]!==i[0]||n[2]!==i[2])return!0}else if(n!==i)return!0;if(t[4])return!r[4];if(r[4])return!0;let o=Object.values(t[1])[0],a=Object.values(r[1])[0];return!o||!a||e(o,a)}}}),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},2255:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"pathHasPrefix",{enumerable:!0,get:function(){return i}});let n=r(19169);function i(e,t){if("string"!=typeof e)return!1;let{pathname:r}=(0,n.parsePath)(e);return r===t||r.startsWith(t+"/")}},5144:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"PromiseQueue",{enumerable:!0,get:function(){return u}});let n=r(51550),i=r(59656);var o=i._("_maxConcurrency"),a=i._("_runningCount"),s=i._("_queue"),l=i._("_processNext");class u{enqueue(e){let t,r;let i=new Promise((e,n)=>{t=e,r=n}),o=async()=>{try{n._(this,a)[a]++;let r=await e();t(r)}catch(e){r(e)}finally{n._(this,a)[a]--,n._(this,l)[l]()}};return n._(this,s)[s].push({promiseFn:i,task:o}),n._(this,l)[l](),i}bump(e){let t=n._(this,s)[s].findIndex(t=>t.promiseFn===e);if(t>-1){let e=n._(this,s)[s].splice(t,1)[0];n._(this,s)[s].unshift(e),n._(this,l)[l](!0)}}constructor(e=5){Object.defineProperty(this,l,{value:c}),Object.defineProperty(this,o,{writable:!0,value:void 0}),Object.defineProperty(this,a,{writable:!0,value:void 0}),Object.defineProperty(this,s,{writable:!0,value:void 0}),n._(this,o)[o]=e,n._(this,a)[a]=0,n._(this,s)[s]=[]}}function c(e){if(void 0===e&&(e=!1),(n._(this,a)[a]<n._(this,o)[o]||e)&&n._(this,s)[s].length>0){var t;null==(t=n._(this,s)[s].shift())||t.task()}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5334:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{STATIC_STALETIME_MS:function(){return f},createSeededPrefetchCacheEntry:function(){return u},getOrCreatePrefetchCacheEntry:function(){return l},prunePrefetchCache:function(){return d}});let n=r(59008),i=r(59154),o=r(75076);function a(e,t,r){let n=e.pathname;return(t&&(n+=e.search),r)?""+r+"%"+n:n}function s(e,t,r){return a(e,t===i.PrefetchKind.FULL,r)}function l(e){let{url:t,nextUrl:r,tree:n,prefetchCache:o,kind:s,allowAliasing:l=!0}=e,u=function(e,t,r,n,o){for(let s of(void 0===t&&(t=i.PrefetchKind.TEMPORARY),[r,null])){let r=a(e,!0,s),l=a(e,!1,s),u=e.search?r:l,c=n.get(u);if(c&&o){if(c.url.pathname===e.pathname&&c.url.search!==e.search)return{...c,aliased:!0};return c}let d=n.get(l);if(o&&e.search&&t!==i.PrefetchKind.FULL&&d&&!d.key.includes("%"))return{...d,aliased:!0}}if(t!==i.PrefetchKind.FULL&&o){for(let t of n.values())if(t.url.pathname===e.pathname&&!t.key.includes("%"))return{...t,aliased:!0}}}(t,s,r,o,l);return u?(u.status=p(u),u.kind!==i.PrefetchKind.FULL&&s===i.PrefetchKind.FULL&&u.data.then(e=>{if(!(Array.isArray(e.flightData)&&e.flightData.some(e=>e.isRootRender&&null!==e.seedData)))return c({tree:n,url:t,nextUrl:r,prefetchCache:o,kind:null!=s?s:i.PrefetchKind.TEMPORARY})}),s&&u.kind===i.PrefetchKind.TEMPORARY&&(u.kind=s),u):c({tree:n,url:t,nextUrl:r,prefetchCache:o,kind:s||i.PrefetchKind.TEMPORARY})}function u(e){let{nextUrl:t,tree:r,prefetchCache:n,url:o,data:a,kind:l}=e,u=a.couldBeIntercepted?s(o,l,t):s(o,l),c={treeAtTimeOfPrefetch:r,data:Promise.resolve(a),kind:l,prefetchTime:Date.now(),lastUsedTime:Date.now(),staleTime:-1,key:u,status:i.PrefetchCacheEntryStatus.fresh,url:o};return n.set(u,c),c}function c(e){let{url:t,kind:r,tree:a,nextUrl:l,prefetchCache:u}=e,c=s(t,r),d=o.prefetchQueue.enqueue(()=>(0,n.fetchServerResponse)(t,{flightRouterState:a,nextUrl:l,prefetchKind:r}).then(e=>{let r;if(e.couldBeIntercepted&&(r=function(e){let{url:t,nextUrl:r,prefetchCache:n,existingCacheKey:i}=e,o=n.get(i);if(!o)return;let a=s(t,o.kind,r);return n.set(a,{...o,key:a}),n.delete(i),a}({url:t,existingCacheKey:c,nextUrl:l,prefetchCache:u})),e.prerendered){let t=u.get(null!=r?r:c);t&&(t.kind=i.PrefetchKind.FULL,-1!==e.staleTime&&(t.staleTime=e.staleTime))}return e})),h={treeAtTimeOfPrefetch:a,data:d,kind:r,prefetchTime:Date.now(),lastUsedTime:null,staleTime:-1,key:c,status:i.PrefetchCacheEntryStatus.fresh,url:t};return u.set(c,h),h}function d(e){for(let[t,r]of e)p(r)===i.PrefetchCacheEntryStatus.expired&&e.delete(t)}let h=1e3*Number("0"),f=1e3*Number("300");function p(e){let{kind:t,prefetchTime:r,lastUsedTime:n,staleTime:o}=e;return -1!==o?Date.now()<r+o?i.PrefetchCacheEntryStatus.fresh:i.PrefetchCacheEntryStatus.stale:Date.now()<(null!=n?n:r)+h?n?i.PrefetchCacheEntryStatus.reusable:i.PrefetchCacheEntryStatus.fresh:t===i.PrefetchKind.AUTO&&Date.now()<r+f?i.PrefetchCacheEntryStatus.stale:t===i.PrefetchKind.FULL&&Date.now()<r+f?i.PrefetchCacheEntryStatus.reusable:i.PrefetchCacheEntryStatus.expired}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6341:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getUtils:function(){return g},interpolateDynamicPath:function(){return p},normalizeDynamicRouteParams:function(){return m},normalizeVercelUrl:function(){return f}});let n=r(79551),i=r(11959),o=r(12437),a=r(2015),s=r(78034),l=r(15526),u=r(72887),c=r(74722),d=r(46143),h=r(47912);function f(e,t,r){let i=(0,n.parse)(e.url,!0);for(let e of(delete i.search,Object.keys(i.query))){let n=e!==d.NEXT_QUERY_PARAM_PREFIX&&e.startsWith(d.NEXT_QUERY_PARAM_PREFIX),o=e!==d.NEXT_INTERCEPTION_MARKER_PREFIX&&e.startsWith(d.NEXT_INTERCEPTION_MARKER_PREFIX);(n||o||t.includes(e)||r&&Object.keys(r.groups).includes(e))&&delete i.query[e]}e.url=(0,n.format)(i)}function p(e,t,r){if(!r)return e;for(let n of Object.keys(r.groups)){let i;let{optional:o,repeat:a}=r.groups[n],s=`[${a?"...":""}${n}]`;o&&(s=`[${s}]`);let l=t[n];i=Array.isArray(l)?l.map(e=>e&&encodeURIComponent(e)).join("/"):l?encodeURIComponent(l):"",e=e.replaceAll(s,i)}return e}function m(e,t,r,n){let i={};for(let o of Object.keys(t.groups)){let a=e[o];"string"==typeof a?a=(0,c.normalizeRscURL)(a):Array.isArray(a)&&(a=a.map(c.normalizeRscURL));let s=r[o],l=t.groups[o].optional;if((Array.isArray(s)?s.some(e=>Array.isArray(a)?a.some(t=>t.includes(e)):null==a?void 0:a.includes(e)):null==a?void 0:a.includes(s))||void 0===a&&!(l&&n))return{params:{},hasValidParams:!1};l&&(!a||Array.isArray(a)&&1===a.length&&("index"===a[0]||a[0]===`[[...${o}]]`))&&(a=void 0,delete e[o]),a&&"string"==typeof a&&t.groups[o].repeat&&(a=a.split("/")),a&&(i[o]=a)}return{params:i,hasValidParams:!0}}function g({page:e,i18n:t,basePath:r,rewrites:n,pageIsDynamic:c,trailingSlash:d,caseSensitive:g}){let y,v,b;return c&&(y=(0,a.getNamedRouteRegex)(e,{prefixRouteKeys:!1}),b=(v=(0,s.getRouteMatcher)(y))(e)),{handleRewrites:function(a,s){let h={},f=s.pathname,p=n=>{let u=(0,o.getPathMatch)(n.source+(d?"(/)?":""),{removeUnnamedParams:!0,strict:!0,sensitive:!!g});if(!s.pathname)return!1;let p=u(s.pathname);if((n.has||n.missing)&&p){let e=(0,l.matchHas)(a,s.query,n.has,n.missing);e?Object.assign(p,e):p=!1}if(p){let{parsedDestination:o,destQuery:a}=(0,l.prepareDestination)({appendParamsToQuery:!0,destination:n.destination,params:p,query:s.query});if(o.protocol)return!0;if(Object.assign(h,a,p),Object.assign(s.query,o.query),delete o.query,Object.assign(s,o),!(f=s.pathname))return!1;if(r&&(f=f.replace(RegExp(`^${r}`),"")||"/"),t){let e=(0,i.normalizeLocalePath)(f,t.locales);f=e.pathname,s.query.nextInternalLocale=e.detectedLocale||p.nextInternalLocale}if(f===e)return!0;if(c&&v){let e=v(f);if(e)return s.query={...s.query,...e},!0}}return!1};for(let e of n.beforeFiles||[])p(e);if(f!==e){let t=!1;for(let e of n.afterFiles||[])if(t=p(e))break;if(!t&&!(()=>{let t=(0,u.removeTrailingSlash)(f||"");return t===(0,u.removeTrailingSlash)(e)||(null==v?void 0:v(t))})()){for(let e of n.fallback||[])if(t=p(e))break}}return h},defaultRouteRegex:y,dynamicRouteMatcher:v,defaultRouteMatches:b,getParamsFromRouteMatches:function(e){if(!y)return null;let{groups:t,routeKeys:r}=y,n=(0,s.getRouteMatcher)({re:{exec:e=>{let n=Object.fromEntries(new URLSearchParams(e));for(let[e,t]of Object.entries(n)){let r=(0,h.normalizeNextQueryParam)(e);r&&(n[r]=t,delete n[e])}let i={};for(let e of Object.keys(r)){let o=r[e];if(!o)continue;let a=t[o],s=n[e];if(!a.optional&&!s)return null;i[a.pos]=s}return i}},groups:t})(e);return n||null},normalizeDynamicRouteParams:(e,t)=>y&&b?m(e,y,b,t):{params:{},hasValidParams:!1},normalizeVercelUrl:(e,t)=>f(e,t,y),interpolateDynamicPath:(e,t)=>p(e,t,y)}}},6361:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"assignLocation",{enumerable:!0,get:function(){return i}});let n=r(96127);function i(e,t){if(e.startsWith(".")){let r=t.origin+t.pathname;return new URL((r.endsWith("/")?r:r+"/")+e)}return new URL((0,n.addBasePath)(e),t.href)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},7044:(e,t,r)=>{"use strict";r.d(t,{B:()=>n});let n="undefined"!=typeof window},8304:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{STATIC_METADATA_IMAGES:function(){return i},getExtensionRegexString:function(){return a},isMetadataRoute:function(){return c},isMetadataRouteFile:function(){return s},isStaticMetadataRoute:function(){return u},isStaticMetadataRouteFile:function(){return l}});let n=r(12958),i={icon:{filename:"icon",extensions:["ico","jpg","jpeg","png","svg"]},apple:{filename:"apple-icon",extensions:["jpg","jpeg","png"]},favicon:{filename:"favicon",extensions:["ico"]},openGraph:{filename:"opengraph-image",extensions:["jpg","jpeg","png","gif"]},twitter:{filename:"twitter-image",extensions:["jpg","jpeg","png","gif"]}},o=["js","jsx","ts","tsx"],a=(e,t)=>t?`(?:\\.(${e.join("|")})|((\\[\\])?\\.(${t.join("|")})))`:`\\.(?:${e.join("|")})`;function s(e,t,r){let o=[RegExp(`^[\\\\/]robots${r?`${a(t.concat("txt"),null)}$`:""}`),RegExp(`^[\\\\/]manifest${r?`${a(t.concat("webmanifest","json"),null)}$`:""}`),RegExp("^[\\\\/]favicon\\.ico$"),RegExp(`[\\\\/]sitemap${r?`${a(["xml"],t)}$`:""}`),RegExp(`[\\\\/]${i.icon.filename}\\d?${r?`${a(i.icon.extensions,t)}$`:""}`),RegExp(`[\\\\/]${i.apple.filename}\\d?${r?`${a(i.apple.extensions,t)}$`:""}`),RegExp(`[\\\\/]${i.openGraph.filename}\\d?${r?`${a(i.openGraph.extensions,t)}$`:""}`),RegExp(`[\\\\/]${i.twitter.filename}\\d?${r?`${a(i.twitter.extensions,t)}$`:""}`)],s=(0,n.normalizePathSep)(e);return o.some(e=>e.test(s))}function l(e){return s(e,[],!0)}function u(e){return"/robots"===e||"/manifest"===e||l(e)}function c(e){let t=e.replace(/^\/?app\//,"").replace(/\/route$/,"");return"/"!==t[0]&&(t="/"+t),!t.endsWith("/page")&&s(t,o,!1)}},8830:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"reducer",{enumerable:!0,get:function(){return n}}),r(59154),r(25232),r(29651),r(28627),r(78866),r(75076),r(97936),r(35429);let n=function(e,t){return e};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},9707:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{addSearchParamsToPageSegments:function(){return d},handleAliasedPrefetchEntry:function(){return c}});let n=r(83913),i=r(89752),o=r(86770),a=r(57391),s=r(33123),l=r(33898),u=r(59435);function c(e,t,r,c){let h,f=e.tree,p=e.cache,m=(0,a.createHrefFromUrl)(r);if("string"==typeof t)return!1;for(let e of t){if(!function e(t){if(!t)return!1;let r=t[2];if(t[3])return!0;for(let t in r)if(e(r[t]))return!0;return!1}(e.seedData))continue;let t=e.tree;t=d(t,Object.fromEntries(r.searchParams));let{seedData:a,isRootRender:u,pathToSegment:c}=e,g=["",...c];t=d(t,Object.fromEntries(r.searchParams));let y=(0,o.applyRouterStatePatchToTree)(g,f,t,m),v=(0,i.createEmptyCacheNode)();if(u&&a){let e=a[1];v.loading=a[3],v.rsc=e,function e(t,r,i,o){if(0!==Object.keys(i[1]).length)for(let a in i[1]){let l;let u=i[1][a],c=u[0],d=(0,s.createRouterCacheKey)(c),h=null!==o&&void 0!==o[2][a]?o[2][a]:null;if(null!==h){let e=h[1],t=h[3];l={lazyData:null,rsc:c.includes(n.PAGE_SEGMENT_KEY)?null:e,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:t}}else l={lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null};let f=t.parallelRoutes.get(a);f?f.set(d,l):t.parallelRoutes.set(a,new Map([[d,l]])),e(l,r,u,h)}}(v,p,t,a)}else v.rsc=p.rsc,v.prefetchRsc=p.prefetchRsc,v.loading=p.loading,v.parallelRoutes=new Map(p.parallelRoutes),(0,l.fillCacheWithNewSubTreeDataButOnlyLoading)(v,p,e);y&&(f=y,p=v,h=!0)}return!!h&&(c.patchedTree=f,c.cache=p,c.canonicalUrl=m,c.hashFragment=r.hash,(0,u.handleMutable)(e,c))}function d(e,t){let[r,i,...o]=e;if(r.includes(n.PAGE_SEGMENT_KEY))return[(0,n.addSearchParamsIfPageSegment)(r,t),i,...o];let a={};for(let[e,r]of Object.entries(i))a[e]=d(r,t);return[r,a,...o]}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},12157:(e,t,r)=>{"use strict";r.d(t,{L:()=>n});let n=(0,r(43210).createContext)({})},12437:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getPathMatch",{enumerable:!0,get:function(){return i}});let n=r(35362);function i(e,t){let r=[],i=(0,n.pathToRegexp)(e,r,{delimiter:"/",sensitive:"boolean"==typeof(null==t?void 0:t.sensitive)&&t.sensitive,strict:null==t?void 0:t.strict}),o=(0,n.regexpToFunction)((null==t?void 0:t.regexModifier)?new RegExp(t.regexModifier(i.source),i.flags):i,r);return(e,n)=>{if("string"!=typeof e)return!1;let i=o(e);if(!i)return!1;if(null==t?void 0:t.removeUnnamedParams)for(let e of r)"number"==typeof e.name&&delete i.params[e.name];return{...n,...i.params}}}},12958:(e,t)=>{"use strict";function r(e){return e.replace(/\\/g,"/")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"normalizePathSep",{enumerable:!0,get:function(){return r}})},15124:(e,t,r)=>{"use strict";r.d(t,{E:()=>i});var n=r(43210);let i=r(7044).B?n.useLayoutEffect:n.useEffect},15526:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{compileNonPath:function(){return d},matchHas:function(){return c},parseDestination:function(){return h},prepareDestination:function(){return f}});let n=r(35362),i=r(53293),o=r(76759),a=r(71437),s=r(9977),l=r(88212);function u(e){return e.replace(/__ESC_COLON_/gi,":")}function c(e,t,r,n){void 0===r&&(r=[]),void 0===n&&(n=[]);let i={},o=r=>{let n;let o=r.key;switch(r.type){case"header":o=o.toLowerCase(),n=e.headers[o];break;case"cookie":n="cookies"in e?e.cookies[r.key]:(0,l.getCookieParser)(e.headers)()[r.key];break;case"query":n=t[o];break;case"host":{let{host:t}=(null==e?void 0:e.headers)||{};n=null==t?void 0:t.split(":",1)[0].toLowerCase()}}if(!r.value&&n)return i[function(e){let t="";for(let r=0;r<e.length;r++){let n=e.charCodeAt(r);(n>64&&n<91||n>96&&n<123)&&(t+=e[r])}return t}(o)]=n,!0;if(n){let e=RegExp("^"+r.value+"$"),t=Array.isArray(n)?n.slice(-1)[0].match(e):n.match(e);if(t)return Array.isArray(t)&&(t.groups?Object.keys(t.groups).forEach(e=>{i[e]=t.groups[e]}):"host"===r.type&&t[0]&&(i.host=t[0])),!0}return!1};return!!r.every(e=>o(e))&&!n.some(e=>o(e))&&i}function d(e,t){if(!e.includes(":"))return e;for(let r of Object.keys(t))e.includes(":"+r)&&(e=e.replace(RegExp(":"+r+"\\*","g"),":"+r+"--ESCAPED_PARAM_ASTERISKS").replace(RegExp(":"+r+"\\?","g"),":"+r+"--ESCAPED_PARAM_QUESTION").replace(RegExp(":"+r+"\\+","g"),":"+r+"--ESCAPED_PARAM_PLUS").replace(RegExp(":"+r+"(?!\\w)","g"),"--ESCAPED_PARAM_COLON"+r));return e=e.replace(/(:|\*|\?|\+|\(|\)|\{|\})/g,"\\$1").replace(/--ESCAPED_PARAM_PLUS/g,"+").replace(/--ESCAPED_PARAM_COLON/g,":").replace(/--ESCAPED_PARAM_QUESTION/g,"?").replace(/--ESCAPED_PARAM_ASTERISKS/g,"*"),(0,n.compile)("/"+e,{validate:!1})(t).slice(1)}function h(e){let t=e.destination;for(let r of Object.keys({...e.params,...e.query}))if(r)t=t.replace(RegExp(":"+(0,i.escapeStringRegexp)(r),"g"),"__ESC_COLON_"+r);let r=(0,o.parseUrl)(t),n=r.pathname;n&&(n=u(n));let a=r.href;a&&(a=u(a));let s=r.hostname;s&&(s=u(s));let l=r.hash;return l&&(l=u(l)),{...r,pathname:n,hostname:s,href:a,hash:l}}function f(e){let t,r;let i=Object.assign({},e.query);delete i[s.NEXT_RSC_UNION_QUERY];let o=h(e),{hostname:l,query:c}=o,f=o.pathname;o.hash&&(f=""+f+o.hash);let p=[],m=[];for(let e of((0,n.pathToRegexp)(f,m),m))p.push(e.name);if(l){let e=[];for(let t of((0,n.pathToRegexp)(l,e),e))p.push(t.name)}let g=(0,n.compile)(f,{validate:!1});for(let[r,i]of(l&&(t=(0,n.compile)(l,{validate:!1})),Object.entries(c)))Array.isArray(i)?c[r]=i.map(t=>d(u(t),e.params)):"string"==typeof i&&(c[r]=d(u(i),e.params));let y=Object.keys(e.params).filter(e=>"nextInternalLocale"!==e);if(e.appendParamsToQuery&&!y.some(e=>p.includes(e)))for(let t of y)t in c||(c[t]=e.params[t]);if((0,a.isInterceptionRouteAppPath)(f))for(let t of f.split("/")){let r=a.INTERCEPTION_ROUTE_MARKERS.find(e=>t.startsWith(e));if(r){"(..)(..)"===r?(e.params["0"]="(..)",e.params["1"]="(..)"):e.params["0"]=r;break}}try{let[n,i]=(r=g(e.params)).split("#",2);t&&(o.hostname=t(e.params)),o.pathname=n,o.hash=(i?"#":"")+(i||""),delete o.search}catch(e){if(e.message.match(/Expected .*? to not repeat, but got an array/))throw Object.defineProperty(Error("To use a multi-match in the destination you must add `*` at the end of the param name to signify it should repeat. https://nextjs.org/docs/messages/invalid-multi-match"),"__NEXT_ERROR_CODE",{value:"E329",enumerable:!1,configurable:!0});throw e}return o.query={...i,...o.query},{newUrl:r,destQuery:c,parsedDestination:o}}},18468:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"invalidateCacheBelowFlightSegmentPath",{enumerable:!0,get:function(){return function e(t,r,o){let a=o.length<=2,[s,l]=o,u=(0,n.createRouterCacheKey)(l),c=r.parallelRoutes.get(s);if(!c)return;let d=t.parallelRoutes.get(s);if(d&&d!==c||(d=new Map(c),t.parallelRoutes.set(s,d)),a){d.delete(u);return}let h=c.get(u),f=d.get(u);f&&h&&(f===h&&(f={lazyData:f.lazyData,rsc:f.rsc,prefetchRsc:f.prefetchRsc,head:f.head,prefetchHead:f.prefetchHead,parallelRoutes:new Map(f.parallelRoutes)},d.set(u,f)),e(f,h,(0,i.getNextFlightSegmentPath)(o)))}}});let n=r(33123),i=r(74007);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},19169:(e,t)=>{"use strict";function r(e){let t=e.indexOf("#"),r=e.indexOf("?"),n=r>-1&&(t<0||r<t);return n||t>-1?{pathname:e.substring(0,n?r:t),query:n?e.substring(r,t>-1?t:void 0):"",hash:t>-1?e.slice(t):""}:{pathname:e,query:"",hash:""}}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"parsePath",{enumerable:!0,get:function(){return r}})},21279:(e,t,r)=>{"use strict";r.d(t,{t:()=>n});let n=(0,r(43210).createContext)(null)},22308:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{addRefreshMarkerToActiveParallelSegments:function(){return function e(t,r){let[n,i,,a]=t;for(let s in n.includes(o.PAGE_SEGMENT_KEY)&&"refresh"!==a&&(t[2]=r,t[3]="refresh"),i)e(i[s],r)}},refreshInactiveParallelSegments:function(){return a}});let n=r(56928),i=r(59008),o=r(83913);async function a(e){let t=new Set;await s({...e,rootTree:e.updatedTree,fetchedSegments:t})}async function s(e){let{state:t,updatedTree:r,updatedCache:o,includeNextUrl:a,fetchedSegments:l,rootTree:u=r,canonicalUrl:c}=e,[,d,h,f]=r,p=[];if(h&&h!==c&&"refresh"===f&&!l.has(h)){l.add(h);let e=(0,i.fetchServerResponse)(new URL(h,location.origin),{flightRouterState:[u[0],u[1],u[2],"refetch"],nextUrl:a?t.nextUrl:null}).then(e=>{let{flightData:t}=e;if("string"!=typeof t)for(let e of t)(0,n.applyFlightData)(o,o,e)});p.push(e)}for(let e in d){let r=s({state:t,updatedTree:d[e],updatedCache:o,includeNextUrl:a,fetchedSegments:l,rootTree:u,canonicalUrl:c});p.push(r)}await Promise.all(p)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},23736:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"parseRelativeUrl",{enumerable:!0,get:function(){return i}}),r(44827);let n=r(42785);function i(e,t,r){void 0===r&&(r=!0);let i=new URL("http://n"),o=t?new URL(t,i):e.startsWith(".")?new URL("http://n"):i,{pathname:a,searchParams:s,search:l,hash:u,href:c,origin:d}=new URL(e,o);if(d!==i.origin)throw Object.defineProperty(Error("invariant: invalid relative URL, router received "+e),"__NEXT_ERROR_CODE",{value:"E159",enumerable:!1,configurable:!0});return{pathname:a,query:r?(0,n.searchParamsToUrlQuery)(s):void 0,search:l,hash:u,href:c.slice(d.length)}}},24642:(e,t)=>{"use strict";function r(e){let t=parseInt(e.slice(0,2),16),r=t>>1&63,n=Array(6);for(let e=0;e<6;e++){let t=r>>5-e&1;n[e]=1===t}return{type:1==(t>>7&1)?"use-cache":"server-action",usedArgs:n,hasRestArgs:1==(1&t)}}function n(e,t){let r=Array(e.length);for(let n=0;n<e.length;n++)(n<6&&t.usedArgs[n]||n>=6&&t.hasRestArgs)&&(r[n]=e[n]);return r}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{extractInfoFromServerReferenceId:function(){return r},omitUnusedArgs:function(){return n}})},25232:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{handleExternalUrl:function(){return b},navigateReducer:function(){return function e(t,r){let{url:x,isExternalUrl:R,navigateType:E,shouldScroll:T,allowAliasing:_}=r,w={},{hash:S}=x,A=(0,i.createHrefFromUrl)(x),M="push"===E;if((0,g.prunePrefetchCache)(t.prefetchCache),w.preserveCustomHistoryState=!1,w.pendingPush=M,R)return b(t,w,x.toString(),M);if(document.getElementById("__next-page-redirect"))return b(t,w,A,M);let j=(0,g.getOrCreatePrefetchCacheEntry)({url:x,nextUrl:t.nextUrl,tree:t.tree,prefetchCache:t.prefetchCache,allowAliasing:_}),{treeAtTimeOfPrefetch:O,data:C}=j;return h.prefetchQueue.bump(C),C.then(h=>{let{flightData:g,canonicalUrl:R,postponed:E}=h,_=!1;if(j.lastUsedTime||(j.lastUsedTime=Date.now(),_=!0),j.aliased){let n=(0,v.handleAliasedPrefetchEntry)(t,g,x,w);return!1===n?e(t,{...r,allowAliasing:!1}):n}if("string"==typeof g)return b(t,w,g,M);let C=R?(0,i.createHrefFromUrl)(R):A;if(S&&t.canonicalUrl.split("#",1)[0]===C.split("#",1)[0])return w.onlyHashChange=!0,w.canonicalUrl=C,w.shouldScroll=T,w.hashFragment=S,w.scrollableSegments=[],(0,c.handleMutable)(t,w);let D=t.tree,k=t.cache,L=[];for(let e of g){let{pathToSegment:r,seedData:i,head:c,isHeadPartial:h,isRootRender:g}=e,v=e.tree,R=["",...r],T=(0,a.applyRouterStatePatchToTree)(R,D,v,A);if(null===T&&(T=(0,a.applyRouterStatePatchToTree)(R,O,v,A)),null!==T){if(i&&g&&E){let e=(0,m.startPPRNavigation)(k,D,v,i,c,h,!1,L);if(null!==e){if(null===e.route)return b(t,w,A,M);T=e.route;let r=e.node;null!==r&&(w.cache=r);let i=e.dynamicRequestTree;if(null!==i){let r=(0,n.fetchServerResponse)(x,{flightRouterState:i,nextUrl:t.nextUrl});(0,m.listenForDynamicRequest)(e,r)}}else T=v}else{if((0,l.isNavigatingToNewRootLayout)(D,T))return b(t,w,A,M);let n=(0,f.createEmptyCacheNode)(),i=!1;for(let t of(j.status!==u.PrefetchCacheEntryStatus.stale||_?i=(0,d.applyFlightData)(k,n,e,j):(i=function(e,t,r,n){let i=!1;for(let o of(e.rsc=t.rsc,e.prefetchRsc=t.prefetchRsc,e.loading=t.loading,e.parallelRoutes=new Map(t.parallelRoutes),P(n).map(e=>[...r,...e])))(0,y.clearCacheNodeDataForSegmentPath)(e,t,o),i=!0;return i}(n,k,r,v),j.lastUsedTime=Date.now()),(0,s.shouldHardNavigate)(R,D)?(n.rsc=k.rsc,n.prefetchRsc=k.prefetchRsc,(0,o.invalidateCacheBelowFlightSegmentPath)(n,k,r),w.cache=n):i&&(w.cache=n,k=n),P(v))){let e=[...r,...t];e[e.length-1]!==p.DEFAULT_SEGMENT_KEY&&L.push(e)}}D=T}}return w.patchedTree=D,w.canonicalUrl=C,w.scrollableSegments=L,w.hashFragment=S,w.shouldScroll=T,(0,c.handleMutable)(t,w)},()=>t)}}});let n=r(59008),i=r(57391),o=r(18468),a=r(86770),s=r(65951),l=r(2030),u=r(59154),c=r(59435),d=r(56928),h=r(75076),f=r(89752),p=r(83913),m=r(65956),g=r(5334),y=r(97464),v=r(9707);function b(e,t,r,n){return t.mpaNavigation=!0,t.canonicalUrl=r,t.pendingPush=n,t.scrollableSegments=void 0,(0,c.handleMutable)(e,t)}function P(e){let t=[],[r,n]=e;if(0===Object.keys(n).length)return[[r]];for(let[e,i]of Object.entries(n))for(let n of P(i))""===r?t.push([e,...n]):t.push([r,e,...n]);return t}r(50593),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},25942:(e,t,r)=>{"use strict";function n(e){return e}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"removeBasePath",{enumerable:!0,get:function(){return n}}),r(26736),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},26415:e=>{(()=>{"use strict";"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab=__dirname+"/");var t={};(()=>{t.parse=function(t,r){if("string"!=typeof t)throw TypeError("argument str must be a string");for(var i={},o=t.split(n),a=(r||{}).decode||e,s=0;s<o.length;s++){var l=o[s],u=l.indexOf("=");if(!(u<0)){var c=l.substr(0,u).trim(),d=l.substr(++u,l.length).trim();'"'==d[0]&&(d=d.slice(1,-1)),void 0==i[c]&&(i[c]=function(e,t){try{return t(e)}catch(t){return e}}(d,a))}}return i},t.serialize=function(e,t,n){var o=n||{},a=o.encode||r;if("function"!=typeof a)throw TypeError("option encode is invalid");if(!i.test(e))throw TypeError("argument name is invalid");var s=a(t);if(s&&!i.test(s))throw TypeError("argument val is invalid");var l=e+"="+s;if(null!=o.maxAge){var u=o.maxAge-0;if(isNaN(u)||!isFinite(u))throw TypeError("option maxAge is invalid");l+="; Max-Age="+Math.floor(u)}if(o.domain){if(!i.test(o.domain))throw TypeError("option domain is invalid");l+="; Domain="+o.domain}if(o.path){if(!i.test(o.path))throw TypeError("option path is invalid");l+="; Path="+o.path}if(o.expires){if("function"!=typeof o.expires.toUTCString)throw TypeError("option expires is invalid");l+="; Expires="+o.expires.toUTCString()}if(o.httpOnly&&(l+="; HttpOnly"),o.secure&&(l+="; Secure"),o.sameSite)switch("string"==typeof o.sameSite?o.sameSite.toLowerCase():o.sameSite){case!0:case"strict":l+="; SameSite=Strict";break;case"lax":l+="; SameSite=Lax";break;case"none":l+="; SameSite=None";break;default:throw TypeError("option sameSite is invalid")}return l};var e=decodeURIComponent,r=encodeURIComponent,n=/; */,i=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/})(),e.exports=t})()},26736:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"hasBasePath",{enumerable:!0,get:function(){return i}});let n=r(2255);function i(e){return(0,n.pathHasPrefix)(e,"")}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},28627:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"restoreReducer",{enumerable:!0,get:function(){return o}});let n=r(57391),i=r(70642);function o(e,t){var r;let{url:o,tree:a}=t,s=(0,n.createHrefFromUrl)(o),l=a||e.tree,u=e.cache;return{canonicalUrl:s,pushRef:{pendingPush:!1,mpaNavigation:!1,preserveCustomHistoryState:!0},focusAndScrollRef:e.focusAndScrollRef,cache:u,prefetchCache:e.prefetchCache,tree:l,nextUrl:null!=(r=(0,i.extractPathFromFlightRouterState)(l))?r:o.pathname}}r(65956),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},29651:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"serverPatchReducer",{enumerable:!0,get:function(){return c}});let n=r(57391),i=r(86770),o=r(2030),a=r(25232),s=r(56928),l=r(59435),u=r(89752);function c(e,t){let{serverResponse:{flightData:r,canonicalUrl:c}}=t,d={};if(d.preserveCustomHistoryState=!1,"string"==typeof r)return(0,a.handleExternalUrl)(e,d,r,e.pushRef.pendingPush);let h=e.tree,f=e.cache;for(let t of r){let{segmentPath:r,tree:l}=t,p=(0,i.applyRouterStatePatchToTree)(["",...r],h,l,e.canonicalUrl);if(null===p)return e;if((0,o.isNavigatingToNewRootLayout)(h,p))return(0,a.handleExternalUrl)(e,d,e.canonicalUrl,e.pushRef.pendingPush);let m=c?(0,n.createHrefFromUrl)(c):void 0;m&&(d.canonicalUrl=m);let g=(0,u.createEmptyCacheNode)();(0,s.applyFlightData)(f,g,t),d.patchedTree=p,d.cache=g,f=g,h=p}return(0,l.handleMutable)(e,d)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},30195:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{formatUrl:function(){return o},formatWithValidation:function(){return s},urlObjectKeys:function(){return a}});let n=r(40740)._(r(76715)),i=/https?|ftp|gopher|file/;function o(e){let{auth:t,hostname:r}=e,o=e.protocol||"",a=e.pathname||"",s=e.hash||"",l=e.query||"",u=!1;t=t?encodeURIComponent(t).replace(/%3A/i,":")+"@":"",e.host?u=t+e.host:r&&(u=t+(~r.indexOf(":")?"["+r+"]":r),e.port&&(u+=":"+e.port)),l&&"object"==typeof l&&(l=String(n.urlQueryToSearchParams(l)));let c=e.search||l&&"?"+l||"";return o&&!o.endsWith(":")&&(o+=":"),e.slashes||(!o||i.test(o))&&!1!==u?(u="//"+(u||""),a&&"/"!==a[0]&&(a="/"+a)):u||(u=""),s&&"#"!==s[0]&&(s="#"+s),c&&"?"!==c[0]&&(c="?"+c),""+o+u+(a=a.replace(/[?#]/g,encodeURIComponent))+(c=c.replace("#","%23"))+s}let a=["auth","hash","host","hostname","href","path","pathname","port","protocol","query","search","slashes"];function s(e){return o(e)}},30660:(e,t)=>{"use strict";function r(e){let t=5381;for(let r=0;r<e.length;r++)t=(t<<5)+t+e.charCodeAt(r)&0xffffffff;return t>>>0}function n(e){return r(e).toString(36).slice(0,5)}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{djb2Hash:function(){return r},hexHash:function(){return n}})},31658:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{fillMetadataSegment:function(){return h},normalizeMetadataPageToRoute:function(){return p},normalizeMetadataRoute:function(){return f}});let n=r(8304),i=function(e){return e&&e.__esModule?e:{default:e}}(r(78671)),o=r(6341),a=r(2015),s=r(30660),l=r(74722),u=r(12958),c=r(35499);function d(e){let t=i.default.dirname(e);if(e.endsWith("/sitemap"))return"";let r="";return t.split("/").some(e=>(0,c.isGroupSegment)(e)||(0,c.isParallelRouteSegment)(e))&&(r=(0,s.djb2Hash)(t).toString(36).slice(0,6)),r}function h(e,t,r){let n=(0,l.normalizeAppPath)(e),s=(0,a.getNamedRouteRegex)(n,{prefixRouteKeys:!1}),c=(0,o.interpolateDynamicPath)(n,t,s),{name:h,ext:f}=i.default.parse(r),p=d(i.default.posix.join(e,h)),m=p?`-${p}`:"";return(0,u.normalizePathSep)(i.default.join(c,`${h}${m}${f}`))}function f(e){if(!(0,n.isMetadataRoute)(e))return e;let t=e,r="";if("/robots"===e?t+=".txt":"/manifest"===e?t+=".webmanifest":r=d(e),!t.endsWith("/route")){let{dir:e,name:n,ext:o}=i.default.parse(t);t=i.default.posix.join(e,`${n}${r?`-${r}`:""}${o}`,"route")}return t}function p(e,t){let r=e.endsWith("/route"),n=r?e.slice(0,-6):e,i=n.endsWith("/sitemap")?".xml":"";return(t?`${n}/[__metadata_id__]`:`${n}${i}`)+(r?"/route":"")}},32582:(e,t,r)=>{"use strict";r.d(t,{Q:()=>n});let n=(0,r(43210).createContext)({transformPagePoint:e=>e,isStatic:!1,reducedMotion:"never"})},33898:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{fillCacheWithNewSubTreeData:function(){return l},fillCacheWithNewSubTreeDataButOnlyLoading:function(){return u}});let n=r(34400),i=r(41500),o=r(33123),a=r(83913);function s(e,t,r,s,l){let{segmentPath:u,seedData:c,tree:d,head:h}=r,f=e,p=t;for(let e=0;e<u.length;e+=2){let t=u[e],r=u[e+1],m=e===u.length-2,g=(0,o.createRouterCacheKey)(r),y=p.parallelRoutes.get(t);if(!y)continue;let v=f.parallelRoutes.get(t);v&&v!==y||(v=new Map(y),f.parallelRoutes.set(t,v));let b=y.get(g),P=v.get(g);if(m){if(c&&(!P||!P.lazyData||P===b)){let e=c[0],t=c[1],r=c[3];P={lazyData:null,rsc:l||e!==a.PAGE_SEGMENT_KEY?t:null,prefetchRsc:null,head:null,prefetchHead:null,loading:r,parallelRoutes:l&&b?new Map(b.parallelRoutes):new Map},b&&l&&(0,n.invalidateCacheByRouterState)(P,b,d),l&&(0,i.fillLazyItemsTillLeafWithHead)(P,b,d,c,h,s),v.set(g,P)}continue}P&&b&&(P===b&&(P={lazyData:P.lazyData,rsc:P.rsc,prefetchRsc:P.prefetchRsc,head:P.head,prefetchHead:P.prefetchHead,parallelRoutes:new Map(P.parallelRoutes),loading:P.loading},v.set(g,P)),f=P,p=b)}}function l(e,t,r,n){s(e,t,r,n,!0)}function u(e,t,r,n){s(e,t,r,n,!1)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},34400:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"invalidateCacheByRouterState",{enumerable:!0,get:function(){return i}});let n=r(33123);function i(e,t,r){for(let i in r[1]){let o=r[1][i][0],a=(0,n.createRouterCacheKey)(o),s=t.parallelRoutes.get(i);if(s){let t=new Map(s);t.delete(a),e.parallelRoutes.set(i,t)}}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},35362:e=>{(()=>{"use strict";"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab=__dirname+"/");var t={};(()=>{function e(e,t){void 0===t&&(t={});for(var r=function(e){for(var t=[],r=0;r<e.length;){var n=e[r];if("*"===n||"+"===n||"?"===n){t.push({type:"MODIFIER",index:r,value:e[r++]});continue}if("\\"===n){t.push({type:"ESCAPED_CHAR",index:r++,value:e[r++]});continue}if("{"===n){t.push({type:"OPEN",index:r,value:e[r++]});continue}if("}"===n){t.push({type:"CLOSE",index:r,value:e[r++]});continue}if(":"===n){for(var i="",o=r+1;o<e.length;){var a=e.charCodeAt(o);if(a>=48&&a<=57||a>=65&&a<=90||a>=97&&a<=122||95===a){i+=e[o++];continue}break}if(!i)throw TypeError("Missing parameter name at "+r);t.push({type:"NAME",index:r,value:i}),r=o;continue}if("("===n){var s=1,l="",o=r+1;if("?"===e[o])throw TypeError('Pattern cannot start with "?" at '+o);for(;o<e.length;){if("\\"===e[o]){l+=e[o++]+e[o++];continue}if(")"===e[o]){if(0==--s){o++;break}}else if("("===e[o]&&(s++,"?"!==e[o+1]))throw TypeError("Capturing groups are not allowed at "+o);l+=e[o++]}if(s)throw TypeError("Unbalanced pattern at "+r);if(!l)throw TypeError("Missing pattern at "+r);t.push({type:"PATTERN",index:r,value:l}),r=o;continue}t.push({type:"CHAR",index:r,value:e[r++]})}return t.push({type:"END",index:r,value:""}),t}(e),n=t.prefixes,o=void 0===n?"./":n,a="[^"+i(t.delimiter||"/#?")+"]+?",s=[],l=0,u=0,c="",d=function(e){if(u<r.length&&r[u].type===e)return r[u++].value},h=function(e){var t=d(e);if(void 0!==t)return t;var n=r[u];throw TypeError("Unexpected "+n.type+" at "+n.index+", expected "+e)},f=function(){for(var e,t="";e=d("CHAR")||d("ESCAPED_CHAR");)t+=e;return t};u<r.length;){var p=d("CHAR"),m=d("NAME"),g=d("PATTERN");if(m||g){var y=p||"";-1===o.indexOf(y)&&(c+=y,y=""),c&&(s.push(c),c=""),s.push({name:m||l++,prefix:y,suffix:"",pattern:g||a,modifier:d("MODIFIER")||""});continue}var v=p||d("ESCAPED_CHAR");if(v){c+=v;continue}if(c&&(s.push(c),c=""),d("OPEN")){var y=f(),b=d("NAME")||"",P=d("PATTERN")||"",x=f();h("CLOSE"),s.push({name:b||(P?l++:""),pattern:b&&!P?a:P,prefix:y,suffix:x,modifier:d("MODIFIER")||""});continue}h("END")}return s}function r(e,t){void 0===t&&(t={});var r=o(t),n=t.encode,i=void 0===n?function(e){return e}:n,a=t.validate,s=void 0===a||a,l=e.map(function(e){if("object"==typeof e)return RegExp("^(?:"+e.pattern+")$",r)});return function(t){for(var r="",n=0;n<e.length;n++){var o=e[n];if("string"==typeof o){r+=o;continue}var a=t?t[o.name]:void 0,u="?"===o.modifier||"*"===o.modifier,c="*"===o.modifier||"+"===o.modifier;if(Array.isArray(a)){if(!c)throw TypeError('Expected "'+o.name+'" to not repeat, but got an array');if(0===a.length){if(u)continue;throw TypeError('Expected "'+o.name+'" to not be empty')}for(var d=0;d<a.length;d++){var h=i(a[d],o);if(s&&!l[n].test(h))throw TypeError('Expected all "'+o.name+'" to match "'+o.pattern+'", but got "'+h+'"');r+=o.prefix+h+o.suffix}continue}if("string"==typeof a||"number"==typeof a){var h=i(String(a),o);if(s&&!l[n].test(h))throw TypeError('Expected "'+o.name+'" to match "'+o.pattern+'", but got "'+h+'"');r+=o.prefix+h+o.suffix;continue}if(!u){var f=c?"an array":"a string";throw TypeError('Expected "'+o.name+'" to be '+f)}}return r}}function n(e,t,r){void 0===r&&(r={});var n=r.decode,i=void 0===n?function(e){return e}:n;return function(r){var n=e.exec(r);if(!n)return!1;for(var o=n[0],a=n.index,s=Object.create(null),l=1;l<n.length;l++)!function(e){if(void 0!==n[e]){var r=t[e-1];"*"===r.modifier||"+"===r.modifier?s[r.name]=n[e].split(r.prefix+r.suffix).map(function(e){return i(e,r)}):s[r.name]=i(n[e],r)}}(l);return{path:o,index:a,params:s}}}function i(e){return e.replace(/([.+*?=^!:${}()[\]|/\\])/g,"\\$1")}function o(e){return e&&e.sensitive?"":"i"}function a(e,t,r){void 0===r&&(r={});for(var n=r.strict,a=void 0!==n&&n,s=r.start,l=r.end,u=r.encode,c=void 0===u?function(e){return e}:u,d="["+i(r.endsWith||"")+"]|$",h="["+i(r.delimiter||"/#?")+"]",f=void 0===s||s?"^":"",p=0;p<e.length;p++){var m=e[p];if("string"==typeof m)f+=i(c(m));else{var g=i(c(m.prefix)),y=i(c(m.suffix));if(m.pattern){if(t&&t.push(m),g||y){if("+"===m.modifier||"*"===m.modifier){var v="*"===m.modifier?"?":"";f+="(?:"+g+"((?:"+m.pattern+")(?:"+y+g+"(?:"+m.pattern+"))*)"+y+")"+v}else f+="(?:"+g+"("+m.pattern+")"+y+")"+m.modifier}else f+="("+m.pattern+")"+m.modifier}else f+="(?:"+g+y+")"+m.modifier}}if(void 0===l||l)a||(f+=h+"?"),f+=r.endsWith?"(?="+d+")":"$";else{var b=e[e.length-1],P="string"==typeof b?h.indexOf(b[b.length-1])>-1:void 0===b;a||(f+="(?:"+h+"(?="+d+"))?"),P||(f+="(?="+h+"|"+d+")")}return new RegExp(f,o(r))}function s(t,r,n){return t instanceof RegExp?function(e,t){if(!t)return e;var r=e.source.match(/\((?!\?)/g);if(r)for(var n=0;n<r.length;n++)t.push({name:n,prefix:"",suffix:"",modifier:"",pattern:""});return e}(t,r):Array.isArray(t)?RegExp("(?:"+t.map(function(e){return s(e,r,n).source}).join("|")+")",o(n)):a(e(t,n),r,n)}Object.defineProperty(t,"__esModule",{value:!0}),t.parse=e,t.compile=function(t,n){return r(e(t,n),n)},t.tokensToFunction=r,t.match=function(e,t){var r=[];return n(s(e,r,t),r,t)},t.regexpToFunction=n,t.tokensToRegexp=a,t.pathToRegexp=s})(),e.exports=t})()},35416:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{HTML_LIMITED_BOT_UA_RE:function(){return n.HTML_LIMITED_BOT_UA_RE},HTML_LIMITED_BOT_UA_RE_STRING:function(){return o},getBotType:function(){return l},isBot:function(){return s}});let n=r(95796),i=/Googlebot|Google-PageRenderer|AdsBot-Google|googleweblight|Storebot-Google/i,o=n.HTML_LIMITED_BOT_UA_RE.source;function a(e){return n.HTML_LIMITED_BOT_UA_RE.test(e)}function s(e){return i.test(e)||a(e)}function l(e){return i.test(e)?"dom":a(e)?"html":void 0}},35429:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"serverActionReducer",{enumerable:!0,get:function(){return M}});let n=r(11264),i=r(11448),o=r(91563),a=r(59154),s=r(6361),l=r(57391),u=r(25232),c=r(86770),d=r(2030),h=r(59435),f=r(41500),p=r(89752),m=r(68214),g=r(96493),y=r(22308),v=r(74007),b=r(36875),P=r(97860),x=r(5334),R=r(25942),E=r(26736),T=r(24642);r(50593);let{createFromFetch:_,createTemporaryReferenceSet:w,encodeReply:S}=r(19357);async function A(e,t,r){let a,l,{actionId:u,actionArgs:c}=r,d=w(),h=(0,T.extractInfoFromServerReferenceId)(u),f="use-cache"===h.type?(0,T.omitUnusedArgs)(c,h):c,p=await S(f,{temporaryReferences:d}),m=await fetch("",{method:"POST",headers:{Accept:o.RSC_CONTENT_TYPE_HEADER,[o.ACTION_HEADER]:u,[o.NEXT_ROUTER_STATE_TREE_HEADER]:encodeURIComponent(JSON.stringify(e.tree)),...t?{[o.NEXT_URL]:t}:{}},body:p}),g=m.headers.get("x-action-redirect"),[y,b]=(null==g?void 0:g.split(";"))||[];switch(b){case"push":a=P.RedirectType.push;break;case"replace":a=P.RedirectType.replace;break;default:a=void 0}let x=!!m.headers.get(o.NEXT_IS_PRERENDER_HEADER);try{let e=JSON.parse(m.headers.get("x-action-revalidated")||"[[],0,0]");l={paths:e[0]||[],tag:!!e[1],cookie:e[2]}}catch(e){l={paths:[],tag:!1,cookie:!1}}let R=y?(0,s.assignLocation)(y,new URL(e.canonicalUrl,window.location.href)):void 0,E=m.headers.get("content-type");if(null==E?void 0:E.startsWith(o.RSC_CONTENT_TYPE_HEADER)){let e=await _(Promise.resolve(m),{callServer:n.callServer,findSourceMapURL:i.findSourceMapURL,temporaryReferences:d});return y?{actionFlightData:(0,v.normalizeFlightData)(e.f),redirectLocation:R,redirectType:a,revalidatedParts:l,isPrerender:x}:{actionResult:e.a,actionFlightData:(0,v.normalizeFlightData)(e.f),redirectLocation:R,redirectType:a,revalidatedParts:l,isPrerender:x}}if(m.status>=400)throw Object.defineProperty(Error("text/plain"===E?await m.text():"An unexpected response was received from the server."),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return{redirectLocation:R,redirectType:a,revalidatedParts:l,isPrerender:x}}function M(e,t){let{resolve:r,reject:n}=t,i={},o=e.tree;i.preserveCustomHistoryState=!1;let s=e.nextUrl&&(0,m.hasInterceptionRouteInCurrentTree)(e.tree)?e.nextUrl:null;return A(e,s,t).then(async m=>{let v,{actionResult:T,actionFlightData:_,redirectLocation:w,redirectType:S,isPrerender:A,revalidatedParts:M}=m;if(w&&(S===P.RedirectType.replace?(e.pushRef.pendingPush=!1,i.pendingPush=!1):(e.pushRef.pendingPush=!0,i.pendingPush=!0),i.canonicalUrl=v=(0,l.createHrefFromUrl)(w,!1)),!_)return(r(T),w)?(0,u.handleExternalUrl)(e,i,w.href,e.pushRef.pendingPush):e;if("string"==typeof _)return r(T),(0,u.handleExternalUrl)(e,i,_,e.pushRef.pendingPush);let j=M.paths.length>0||M.tag||M.cookie;for(let n of _){let{tree:a,seedData:l,head:h,isRootRender:m}=n;if(!m)return console.log("SERVER ACTION APPLY FAILED"),r(T),e;let b=(0,c.applyRouterStatePatchToTree)([""],o,a,v||e.canonicalUrl);if(null===b)return r(T),(0,g.handleSegmentMismatch)(e,t,a);if((0,d.isNavigatingToNewRootLayout)(o,b))return r(T),(0,u.handleExternalUrl)(e,i,v||e.canonicalUrl,e.pushRef.pendingPush);if(null!==l){let t=l[1],r=(0,p.createEmptyCacheNode)();r.rsc=t,r.prefetchRsc=null,r.loading=l[3],(0,f.fillLazyItemsTillLeafWithHead)(r,void 0,a,l,h,void 0),i.cache=r,i.prefetchCache=new Map,j&&await (0,y.refreshInactiveParallelSegments)({state:e,updatedTree:b,updatedCache:r,includeNextUrl:!!s,canonicalUrl:i.canonicalUrl||e.canonicalUrl})}i.patchedTree=b,o=b}return w&&v?(j||((0,x.createSeededPrefetchCacheEntry)({url:w,data:{flightData:_,canonicalUrl:void 0,couldBeIntercepted:!1,prerendered:!1,postponed:!1,staleTime:-1},tree:e.tree,prefetchCache:e.prefetchCache,nextUrl:e.nextUrl,kind:A?a.PrefetchKind.FULL:a.PrefetchKind.AUTO}),i.prefetchCache=e.prefetchCache),n((0,b.getRedirectError)((0,E.hasBasePath)(v)?(0,R.removeBasePath)(v):v,S||P.RedirectType.push))):r(T),(0,h.handleMutable)(e,i)},t=>(n(t),e))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},38202:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{createMutableActionQueue:function(){return u},getCurrentAppRouterState:function(){return c}});let n=r(59154),i=r(8830),o=r(43210),a=r(91992);function s(e,t){null!==e.pending&&(e.pending=e.pending.next,null!==e.pending?l({actionQueue:e,action:e.pending,setState:t}):e.needsRefresh&&(e.needsRefresh=!1,e.dispatch({type:n.ACTION_REFRESH,origin:window.location.origin},t)))}async function l(e){let{actionQueue:t,action:r,setState:n}=e,i=t.state;t.pending=r;let o=r.payload,l=t.action(i,o);function u(e){!r.discarded&&(t.state=e,s(t,n),r.resolve(e))}(0,a.isThenable)(l)?l.then(u,e=>{s(t,n),r.reject(e)}):u(l)}function u(e){let t={state:e,dispatch:(e,r)=>(function(e,t,r){let i={resolve:r,reject:()=>{}};if(t.type!==n.ACTION_RESTORE){let e=new Promise((e,t)=>{i={resolve:e,reject:t}});(0,o.startTransition)(()=>{r(e)})}let a={payload:t,next:null,resolve:i.resolve,reject:i.reject};null===e.pending?(e.last=a,l({actionQueue:e,action:a,setState:r})):t.type===n.ACTION_NAVIGATE||t.type===n.ACTION_RESTORE?(e.pending.discarded=!0,a.next=e.pending.next,e.pending.payload.type===n.ACTION_SERVER_ACTION&&(e.needsRefresh=!0),l({actionQueue:e,action:a,setState:r})):(null!==e.last&&(e.last.next=a),e.last=a)})(t,e,r),action:async(e,t)=>(0,i.reducer)(e,t),pending:null,last:null};return t}function c(){return null}},41500:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"fillLazyItemsTillLeafWithHead",{enumerable:!0,get:function(){return function e(t,r,o,a,s,l){if(0===Object.keys(o[1]).length){t.head=s;return}for(let u in o[1]){let c;let d=o[1][u],h=d[0],f=(0,n.createRouterCacheKey)(h),p=null!==a&&void 0!==a[2][u]?a[2][u]:null;if(r){let n=r.parallelRoutes.get(u);if(n){let r;let o=(null==l?void 0:l.kind)==="auto"&&l.status===i.PrefetchCacheEntryStatus.reusable,a=new Map(n),c=a.get(f);r=null!==p?{lazyData:null,rsc:p[1],prefetchRsc:null,head:null,prefetchHead:null,loading:p[3],parallelRoutes:new Map(null==c?void 0:c.parallelRoutes)}:o&&c?{lazyData:c.lazyData,rsc:c.rsc,prefetchRsc:c.prefetchRsc,head:c.head,prefetchHead:c.prefetchHead,parallelRoutes:new Map(c.parallelRoutes),loading:c.loading}:{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map(null==c?void 0:c.parallelRoutes),loading:null},a.set(f,r),e(r,c,d,p||null,s,l),t.parallelRoutes.set(u,a);continue}}if(null!==p){let e=p[1],t=p[3];c={lazyData:null,rsc:e,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:t}}else c={lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null};let m=t.parallelRoutes.get(u);m?m.set(f,c):t.parallelRoutes.set(u,new Map([[f,c]])),e(c,void 0,d,p,s,l)}}}});let n=r(33123),i=r(59154);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},42785:(e,t)=>{"use strict";function r(e){let t={};for(let[r,n]of e.entries()){let e=t[r];void 0===e?t[r]=n:Array.isArray(e)?e.push(n):t[r]=[e,n]}return t}function n(e){return"string"==typeof e?e:("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}function i(e){let t=new URLSearchParams;for(let[r,i]of Object.entries(e))if(Array.isArray(i))for(let e of i)t.append(r,n(e));else t.set(r,n(i));return t}function o(e){for(var t=arguments.length,r=Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];for(let t of r){for(let r of t.keys())e.delete(r);for(let[r,n]of t.entries())e.append(r,n)}return e}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{assign:function(){return o},searchParamsToUrlQuery:function(){return r},urlQueryToSearchParams:function(){return i}})},44397:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"findHeadInCache",{enumerable:!0,get:function(){return i}});let n=r(33123);function i(e,t){return function e(t,r,i){if(0===Object.keys(r).length)return[t,i];if(r.children){let[o,a]=r.children,s=t.parallelRoutes.get("children");if(s){let t=(0,n.createRouterCacheKey)(o),r=s.get(t);if(r){let n=e(r,a,i+"/"+t);if(n)return n}}}for(let o in r){if("children"===o)continue;let[a,s]=r[o],l=t.parallelRoutes.get(o);if(!l)continue;let u=(0,n.createRouterCacheKey)(a),c=l.get(u);if(!c)continue;let d=e(c,s,i+"/"+u);if(d)return d}return null}(e,t,"")}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},44827:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{DecodeError:function(){return p},MiddlewareNotFoundError:function(){return v},MissingStaticPage:function(){return y},NormalizeError:function(){return m},PageNotFoundError:function(){return g},SP:function(){return h},ST:function(){return f},WEB_VITALS:function(){return r},execOnce:function(){return n},getDisplayName:function(){return l},getLocationOrigin:function(){return a},getURL:function(){return s},isAbsoluteUrl:function(){return o},isResSent:function(){return u},loadGetInitialProps:function(){return d},normalizeRepeatedSlashes:function(){return c},stringifyError:function(){return b}});let r=["CLS","FCP","FID","INP","LCP","TTFB"];function n(e){let t,r=!1;return function(){for(var n=arguments.length,i=Array(n),o=0;o<n;o++)i[o]=arguments[o];return r||(r=!0,t=e(...i)),t}}let i=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,o=e=>i.test(e);function a(){let{protocol:e,hostname:t,port:r}=window.location;return e+"//"+t+(r?":"+r:"")}function s(){let{href:e}=window.location,t=a();return e.substring(t.length)}function l(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function u(e){return e.finished||e.headersSent}function c(e){let t=e.split("?");return t[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(t[1]?"?"+t.slice(1).join("?"):"")}async function d(e,t){let r=t.res||t.ctx&&t.ctx.res;if(!e.getInitialProps)return t.ctx&&t.Component?{pageProps:await d(t.Component,t.ctx)}:{};let n=await e.getInitialProps(t);if(r&&u(r))return n;if(!n)throw Object.defineProperty(Error('"'+l(e)+'.getInitialProps()" should resolve to an object. But found "'+n+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return n}let h="undefined"!=typeof performance,f=h&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class p extends Error{}class m extends Error{}class g extends Error{constructor(e){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+e}}class y extends Error{constructor(e,t){super(),this.message="Failed to load static file for page: "+e+" "+t}}class v extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function b(e){return JSON.stringify({message:e.message,stack:e.stack})}},50593:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{NavigationResultTag:function(){return d},PrefetchPriority:function(){return h},bumpPrefetchTask:function(){return u},cancelPrefetchTask:function(){return l},createCacheKey:function(){return c},getCurrentCacheVersion:function(){return a},navigate:function(){return i},prefetch:function(){return n},revalidateEntireCache:function(){return o},schedulePrefetchTask:function(){return s}});let r=()=>{throw Object.defineProperty(Error("Segment Cache experiment is not enabled. This is a bug in Next.js."),"__NEXT_ERROR_CODE",{value:"E654",enumerable:!1,configurable:!0})},n=r,i=r,o=r,a=r,s=r,l=r,u=r,c=r;var d=function(e){return e[e.MPA=0]="MPA",e[e.Success=1]="Success",e[e.NoOp=2]="NoOp",e[e.Async=3]="Async",e}({}),h=function(e){return e[e.Intent=2]="Intent",e[e.Default=1]="Default",e[e.Background=0]="Background",e}({});("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},51550:(e,t,r)=>{"use strict";function n(e,t){if(!Object.prototype.hasOwnProperty.call(e,t))throw TypeError("attempted to use private field on non-instance");return e}r.r(t),r.d(t,{_:()=>n})},53038:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useMergedRef",{enumerable:!0,get:function(){return i}});let n=r(43210);function i(e,t){let r=(0,n.useRef)(null),i=(0,n.useRef)(null);return(0,n.useCallback)(n=>{if(null===n){let e=r.current;e&&(r.current=null,e());let t=i.current;t&&(i.current=null,t())}else e&&(r.current=o(e,n)),t&&(i.current=o(t,n))},[e,t])}function o(e,t){if("function"!=typeof e)return e.current=t,()=>{e.current=null};{let r=e(t);return"function"==typeof r?r:()=>e(null)}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},53293:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"escapeStringRegexp",{enumerable:!0,get:function(){return i}});let r=/[|\\{}()[\]^$+*?.-]/,n=/[|\\{}()[\]^$+*?.-]/g;function i(e){return r.test(e)?e.replace(n,"\\$&"):e}},54674:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"normalizePathTrailingSlash",{enumerable:!0,get:function(){return o}});let n=r(84949),i=r(19169),o=e=>{if(!e.startsWith("/"))return e;let{pathname:t,query:r,hash:o}=(0,i.parsePath)(e);return""+(0,n.removeTrailingSlash)(t)+r+o};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},56928:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"applyFlightData",{enumerable:!0,get:function(){return o}});let n=r(41500),i=r(33898);function o(e,t,r,o){let{tree:a,seedData:s,head:l,isRootRender:u}=r;if(null===s)return!1;if(u){let r=s[1];t.loading=s[3],t.rsc=r,t.prefetchRsc=null,(0,n.fillLazyItemsTillLeafWithHead)(t,e,a,s,l,o)}else t.rsc=e.rsc,t.prefetchRsc=e.prefetchRsc,t.parallelRoutes=new Map(e.parallelRoutes),t.loading=e.loading,(0,i.fillCacheWithNewSubTreeData)(t,e,r,o);return!0}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},59435:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"handleMutable",{enumerable:!0,get:function(){return o}});let n=r(70642);function i(e){return void 0!==e}function o(e,t){var r,o;let a=null==(r=t.shouldScroll)||r,s=e.nextUrl;if(i(t.patchedTree)){let r=(0,n.computeChangedPath)(e.tree,t.patchedTree);r?s=r:s||(s=e.canonicalUrl)}return{canonicalUrl:i(t.canonicalUrl)?t.canonicalUrl===e.canonicalUrl?e.canonicalUrl:t.canonicalUrl:e.canonicalUrl,pushRef:{pendingPush:i(t.pendingPush)?t.pendingPush:e.pushRef.pendingPush,mpaNavigation:i(t.mpaNavigation)?t.mpaNavigation:e.pushRef.mpaNavigation,preserveCustomHistoryState:i(t.preserveCustomHistoryState)?t.preserveCustomHistoryState:e.pushRef.preserveCustomHistoryState},focusAndScrollRef:{apply:!!a&&(!!i(null==t?void 0:t.scrollableSegments)||e.focusAndScrollRef.apply),onlyHashChange:t.onlyHashChange||!1,hashFragment:a?t.hashFragment&&""!==t.hashFragment?decodeURIComponent(t.hashFragment.slice(1)):e.focusAndScrollRef.hashFragment:null,segmentPaths:a?null!=(o=null==t?void 0:t.scrollableSegments)?o:e.focusAndScrollRef.segmentPaths:[]},cache:t.cache?t.cache:e.cache,prefetchCache:t.prefetchCache?t.prefetchCache:e.prefetchCache,tree:i(t.patchedTree)?t.patchedTree:e.tree,nextUrl:s}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},59656:(e,t,r)=>{"use strict";r.r(t),r.d(t,{_:()=>i});var n=0;function i(e){return"__private_"+n+++"_"+e}},61520:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useSyncDevRenderIndicator",{enumerable:!0,get:function(){return n}});let r=e=>e(),n=()=>r;("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},65951:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"shouldHardNavigate",{enumerable:!0,get:function(){return function e(t,r){let[o,a]=r,[s,l]=t;return(0,i.matchSegment)(s,o)?!(t.length<=2)&&e((0,n.getNextFlightSegmentPath)(t),a[l]):!!Array.isArray(s)}}});let n=r(74007),i=r(14077);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},65956:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{abortTask:function(){return f},listenForDynamicRequest:function(){return h},startPPRNavigation:function(){return l},updateCacheNodeOnPopstateRestoration:function(){return function e(t,r){let n=r[1],i=t.parallelRoutes,a=new Map(i);for(let t in n){let r=n[t],s=r[0],l=(0,o.createRouterCacheKey)(s),u=i.get(t);if(void 0!==u){let n=u.get(l);if(void 0!==n){let i=e(n,r),o=new Map(u);o.set(l,i),a.set(t,o)}}}let s=t.rsc,l=g(s)&&"pending"===s.status;return{lazyData:null,rsc:s,head:t.head,prefetchHead:l?t.prefetchHead:[null,null],prefetchRsc:l?t.prefetchRsc:null,loading:t.loading,parallelRoutes:a}}}});let n=r(83913),i=r(14077),o=r(33123),a=r(2030),s={route:null,node:null,dynamicRequestTree:null,children:null};function l(e,t,r,a,l,d,h,f){return function e(t,r,a,l,d,h,f,p,m,g){let y=r[1],v=a[1],b=null!==d?d[2]:null;l||!0!==a[4]||(l=!0);let P=t.parallelRoutes,x=new Map(P),R={},E=null,T=!1,_={};for(let t in v){let r;let a=v[t],c=y[t],d=P.get(t),w=null!==b?b[t]:null,S=a[0],A=m.concat([t,S]),M=(0,o.createRouterCacheKey)(S),j=void 0!==c?c[0]:void 0,O=void 0!==d?d.get(M):void 0;if(null!==(r=S===n.DEFAULT_SEGMENT_KEY?void 0!==c?{route:c,node:null,dynamicRequestTree:null,children:null}:u(c,a,l,void 0!==w?w:null,h,f,A,g):p&&0===Object.keys(a[1]).length?u(c,a,l,void 0!==w?w:null,h,f,A,g):void 0!==c&&void 0!==j&&(0,i.matchSegment)(S,j)&&void 0!==O&&void 0!==c?e(O,c,a,l,w,h,f,p,A,g):u(c,a,l,void 0!==w?w:null,h,f,A,g))){if(null===r.route)return s;null===E&&(E=new Map),E.set(t,r);let e=r.node;if(null!==e){let r=new Map(d);r.set(M,e),x.set(t,r)}let n=r.route;R[t]=n;let i=r.dynamicRequestTree;null!==i?(T=!0,_[t]=i):_[t]=n}else R[t]=a,_[t]=a}if(null===E)return null;let w={lazyData:null,rsc:t.rsc,prefetchRsc:t.prefetchRsc,head:t.head,prefetchHead:t.prefetchHead,loading:t.loading,parallelRoutes:x};return{route:c(a,R),node:w,dynamicRequestTree:T?c(a,_):null,children:E}}(e,t,r,!1,a,l,d,h,[],f)}function u(e,t,r,n,i,l,u,h){return!r&&(void 0===e||(0,a.isNavigatingToNewRootLayout)(e,t))?s:function e(t,r,n,i,a,s){if(null===r)return d(t,null,n,i,a,s);let l=t[1],u=r[4],h=0===Object.keys(l).length;if(u||i&&h)return d(t,r,n,i,a,s);let f=r[2],p=new Map,m=new Map,g={},y=!1;if(h)s.push(a);else for(let t in l){let r=l[t],u=null!==f?f[t]:null,c=r[0],d=a.concat([t,c]),h=(0,o.createRouterCacheKey)(c),v=e(r,u,n,i,d,s);p.set(t,v);let b=v.dynamicRequestTree;null!==b?(y=!0,g[t]=b):g[t]=r;let P=v.node;if(null!==P){let e=new Map;e.set(h,P),m.set(t,e)}}return{route:t,node:{lazyData:null,rsc:r[1],prefetchRsc:null,head:h?n:null,prefetchHead:null,loading:r[3],parallelRoutes:m},dynamicRequestTree:y?c(t,g):null,children:p}}(t,n,i,l,u,h)}function c(e,t){let r=[e[0],t];return 2 in e&&(r[2]=e[2]),3 in e&&(r[3]=e[3]),4 in e&&(r[4]=e[4]),r}function d(e,t,r,n,i,a){let s=c(e,e[1]);return s[3]="refetch",{route:e,node:function e(t,r,n,i,a,s){let l=t[1],u=null!==r?r[2]:null,c=new Map;for(let t in l){let r=l[t],d=null!==u?u[t]:null,h=r[0],f=a.concat([t,h]),p=(0,o.createRouterCacheKey)(h),m=e(r,void 0===d?null:d,n,i,f,s),g=new Map;g.set(p,m),c.set(t,g)}let d=0===c.size;d&&s.push(a);let h=null!==r?r[1]:null,f=null!==r?r[3]:null;return{lazyData:null,parallelRoutes:c,prefetchRsc:void 0!==h?h:null,prefetchHead:d?n:[null,null],loading:void 0!==f?f:null,rsc:y(),head:d?y():null}}(e,t,r,n,i,a),dynamicRequestTree:s,children:null}}function h(e,t){t.then(t=>{let{flightData:r}=t;if("string"!=typeof r){for(let t of r){let{segmentPath:r,tree:n,seedData:a,head:s}=t;a&&function(e,t,r,n,a){let s=e;for(let e=0;e<t.length;e+=2){let r=t[e],n=t[e+1],o=s.children;if(null!==o){let e=o.get(r);if(void 0!==e){let t=e.route[0];if((0,i.matchSegment)(n,t)){s=e;continue}}}return}(function e(t,r,n,a){if(null===t.dynamicRequestTree)return;let s=t.children,l=t.node;if(null===s){null!==l&&(function e(t,r,n,a,s){let l=r[1],u=n[1],c=a[2],d=t.parallelRoutes;for(let t in l){let r=l[t],n=u[t],a=c[t],h=d.get(t),f=r[0],m=(0,o.createRouterCacheKey)(f),g=void 0!==h?h.get(m):void 0;void 0!==g&&(void 0!==n&&(0,i.matchSegment)(f,n[0])&&null!=a?e(g,r,n,a,s):p(r,g,null))}let h=t.rsc,f=a[1];null===h?t.rsc=f:g(h)&&h.resolve(f);let m=t.head;g(m)&&m.resolve(s)}(l,t.route,r,n,a),t.dynamicRequestTree=null);return}let u=r[1],c=n[2];for(let t in r){let r=u[t],n=c[t],o=s.get(t);if(void 0!==o){let t=o.route[0];if((0,i.matchSegment)(r[0],t)&&null!=n)return e(o,r,n,a)}}})(s,r,n,a)}(e,r,n,a,s)}f(e,null)}},t=>{f(e,t)})}function f(e,t){let r=e.node;if(null===r)return;let n=e.children;if(null===n)p(e.route,r,t);else for(let e of n.values())f(e,t);e.dynamicRequestTree=null}function p(e,t,r){let n=e[1],i=t.parallelRoutes;for(let e in n){let t=n[e],a=i.get(e);if(void 0===a)continue;let s=t[0],l=(0,o.createRouterCacheKey)(s),u=a.get(l);void 0!==u&&p(t,u,r)}let a=t.rsc;g(a)&&(null===r?a.resolve(null):a.reject(r));let s=t.head;g(s)&&s.resolve(null)}let m=Symbol();function g(e){return e&&e.tag===m}function y(){let e,t;let r=new Promise((r,n)=>{e=r,t=n});return r.status="pending",r.resolve=t=>{"pending"===r.status&&(r.status="fulfilled",r.value=t,e(t))},r.reject=e=>{"pending"===r.status&&(r.status="rejected",r.reason=e,t(e))},r.tag=m,r}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},70642:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{computeChangedPath:function(){return c},extractPathFromFlightRouterState:function(){return u},getSelectedParams:function(){return function e(t,r){for(let n of(void 0===r&&(r={}),Object.values(t[1]))){let t=n[0],o=Array.isArray(t),a=o?t[1]:t;!(!a||a.startsWith(i.PAGE_SEGMENT_KEY))&&(o&&("c"===t[2]||"oc"===t[2])?r[t[0]]=t[1].split("/"):o&&(r[t[0]]=t[1]),r=e(n,r))}return r}}});let n=r(72859),i=r(83913),o=r(14077),a=e=>"/"===e[0]?e.slice(1):e,s=e=>"string"==typeof e?"children"===e?"":e:e[1];function l(e){return e.reduce((e,t)=>""===(t=a(t))||(0,i.isGroupSegment)(t)?e:e+"/"+t,"")||"/"}function u(e){var t;let r=Array.isArray(e[0])?e[0][1]:e[0];if(r===i.DEFAULT_SEGMENT_KEY||n.INTERCEPTION_ROUTE_MARKERS.some(e=>r.startsWith(e)))return;if(r.startsWith(i.PAGE_SEGMENT_KEY))return"";let o=[s(r)],a=null!=(t=e[1])?t:{},c=a.children?u(a.children):void 0;if(void 0!==c)o.push(c);else for(let[e,t]of Object.entries(a)){if("children"===e)continue;let r=u(t);void 0!==r&&o.push(r)}return l(o)}function c(e,t){let r=function e(t,r){let[i,a]=t,[l,c]=r,d=s(i),h=s(l);if(n.INTERCEPTION_ROUTE_MARKERS.some(e=>d.startsWith(e)||h.startsWith(e)))return"";if(!(0,o.matchSegment)(i,l)){var f;return null!=(f=u(r))?f:""}for(let t in a)if(c[t]){let r=e(a[t],c[t]);if(null!==r)return s(l)+"/"+r}return null}(e,t);return null==r||"/"===r?r:l(r.split("/"))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},71437:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{INTERCEPTION_ROUTE_MARKERS:function(){return i},extractInterceptionRouteInformation:function(){return a},isInterceptionRouteAppPath:function(){return o}});let n=r(74722),i=["(..)(..)","(.)","(..)","(...)"];function o(e){return void 0!==e.split("/").find(e=>i.find(t=>e.startsWith(t)))}function a(e){let t,r,o;for(let n of e.split("/"))if(r=i.find(e=>n.startsWith(e))){[t,o]=e.split(r,2);break}if(!t||!r||!o)throw Object.defineProperty(Error("Invalid interception route: "+e+". Must be in the format /<intercepting route>/(..|...|..)(..)/<intercepted route>"),"__NEXT_ERROR_CODE",{value:"E269",enumerable:!1,configurable:!0});switch(t=(0,n.normalizeAppPath)(t),r){case"(.)":o="/"===t?"/"+o:t+"/"+o;break;case"(..)":if("/"===t)throw Object.defineProperty(Error("Invalid interception route: "+e+". Cannot use (..) marker at the root level, use (.) instead."),"__NEXT_ERROR_CODE",{value:"E207",enumerable:!1,configurable:!0});o=t.split("/").slice(0,-1).concat(o).join("/");break;case"(...)":o="/"+o;break;case"(..)(..)":let a=t.split("/");if(a.length<=2)throw Object.defineProperty(Error("Invalid interception route: "+e+". Cannot use (..)(..) marker at the root level or one level up."),"__NEXT_ERROR_CODE",{value:"E486",enumerable:!1,configurable:!0});o=a.slice(0,-2).concat(o).join("/");break;default:throw Object.defineProperty(Error("Invariant: unexpected marker"),"__NEXT_ERROR_CODE",{value:"E112",enumerable:!1,configurable:!0})}return{interceptingRoute:t,interceptedRoute:o}}},72789:(e,t,r)=>{"use strict";r.d(t,{M:()=>i});var n=r(43210);function i(e){let t=(0,n.useRef)(null);return null===t.current&&(t.current=e()),t.current}},73406:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{mountLinkInstance:function(){return u},onLinkVisibilityChanged:function(){return d},onNavigationIntent:function(){return h},pingVisibleLinks:function(){return p},unmountLinkInstance:function(){return c}}),r(38202);let n=r(89752),i=r(59154),o=r(50593),a="function"==typeof WeakMap?new WeakMap:new Map,s=new Set,l="function"==typeof IntersectionObserver?new IntersectionObserver(function(e){for(let t of e){let e=t.intersectionRatio>0;d(t.target,e)}},{rootMargin:"200px"}):null;function u(e,t,r,i){let o=null;try{if(o=(0,n.createPrefetchURL)(t),null===o)return}catch(e){("function"==typeof reportError?reportError:console.error)("Cannot prefetch '"+t+"' because it cannot be converted to a URL.");return}let s={prefetchHref:o.href,router:r,kind:i,isVisible:!1,wasHoveredOrTouched:!1,prefetchTask:null,cacheVersion:-1};void 0!==a.get(e)&&c(e),a.set(e,s),null!==l&&l.observe(e)}function c(e){let t=a.get(e);if(void 0!==t){a.delete(e),s.delete(t);let r=t.prefetchTask;null!==r&&(0,o.cancelPrefetchTask)(r)}null!==l&&l.unobserve(e)}function d(e,t){let r=a.get(e);void 0!==r&&(r.isVisible=t,t?s.add(r):s.delete(r),f(r))}function h(e){let t=a.get(e);void 0!==t&&void 0!==t&&(t.wasHoveredOrTouched=!0,f(t))}function f(e){let t=e.prefetchTask;if(!e.isVisible){null!==t&&(0,o.cancelPrefetchTask)(t);return}}function p(e,t){let r=(0,o.getCurrentCacheVersion)();for(let n of s){let a=n.prefetchTask;if(null!==a&&n.cacheVersion===r&&a.key.nextUrl===e&&a.treeAtTimeOfPrefetch===t)continue;null!==a&&(0,o.cancelPrefetchTask)(a);let s=(0,o.createCacheKey)(n.prefetchHref,e),l=n.wasHoveredOrTouched?o.PrefetchPriority.Intent:o.PrefetchPriority.Default;n.prefetchTask=(0,o.schedulePrefetchTask)(s,t,n.kind===i.PrefetchKind.FULL,l),n.cacheVersion=(0,o.getCurrentCacheVersion)()}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},74722:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{normalizeAppPath:function(){return o},normalizeRscURL:function(){return a}});let n=r(85531),i=r(35499);function o(e){return(0,n.ensureLeadingSlash)(e.split("/").reduce((e,t,r,n)=>!t||(0,i.isGroupSegment)(t)||"@"===t[0]||("page"===t||"route"===t)&&r===n.length-1?e:e+"/"+t,""))}function a(e){return e.replace(/\.rsc($|\?)/,"$1")}},75076:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{prefetchQueue:function(){return o},prefetchReducer:function(){return a}});let n=r(5144),i=r(5334),o=new n.PromiseQueue(5),a=function(e,t){(0,i.prunePrefetchCache)(e.prefetchCache);let{url:r}=t;return(0,i.getOrCreatePrefetchCacheEntry)({url:r,nextUrl:e.nextUrl,prefetchCache:e.prefetchCache,kind:t.kind,tree:e.tree,allowAliasing:!0}),e};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},76715:(e,t)=>{"use strict";function r(e){let t={};for(let[r,n]of e.entries()){let e=t[r];void 0===e?t[r]=n:Array.isArray(e)?e.push(n):t[r]=[e,n]}return t}function n(e){return"string"==typeof e?e:("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}function i(e){let t=new URLSearchParams;for(let[r,i]of Object.entries(e))if(Array.isArray(i))for(let e of i)t.append(r,n(e));else t.set(r,n(i));return t}function o(e){for(var t=arguments.length,r=Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];for(let t of r){for(let r of t.keys())e.delete(r);for(let[r,n]of t.entries())e.append(r,n)}return e}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{assign:function(){return o},searchParamsToUrlQuery:function(){return r},urlQueryToSearchParams:function(){return i}})},76759:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"parseUrl",{enumerable:!0,get:function(){return o}});let n=r(42785),i=r(23736);function o(e){if(e.startsWith("/"))return(0,i.parseRelativeUrl)(e);let t=new URL(e);return{hash:t.hash,hostname:t.hostname,href:t.href,pathname:t.pathname,port:t.port,protocol:t.protocol,query:(0,n.searchParamsToUrlQuery)(t.searchParams),search:t.search}}},77022:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"AppRouterAnnouncer",{enumerable:!0,get:function(){return a}});let n=r(43210),i=r(51215),o="next-route-announcer";function a(e){let{tree:t}=e,[r,a]=(0,n.useState)(null);(0,n.useEffect)(()=>(a(function(){var e;let t=document.getElementsByName(o)[0];if(null==t?void 0:null==(e=t.shadowRoot)?void 0:e.childNodes[0])return t.shadowRoot.childNodes[0];{let e=document.createElement(o);e.style.cssText="position:absolute";let t=document.createElement("div");return t.ariaLive="assertive",t.id="__next-route-announcer__",t.role="alert",t.style.cssText="position:absolute;border:0;height:1px;margin:-1px;padding:0;width:1px;clip:rect(0 0 0 0);overflow:hidden;white-space:nowrap;word-wrap:normal",e.attachShadow({mode:"open"}).appendChild(t),document.body.appendChild(e),t}}()),()=>{let e=document.getElementsByTagName(o)[0];(null==e?void 0:e.isConnected)&&document.body.removeChild(e)}),[]);let[s,l]=(0,n.useState)(""),u=(0,n.useRef)(void 0);return(0,n.useEffect)(()=>{let e="";if(document.title)e=document.title;else{let t=document.querySelector("h1");t&&(e=t.innerText||t.textContent||"")}void 0!==u.current&&u.current!==e&&l(e),u.current=e},[t]),r?(0,i.createPortal)(s,r):null}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},78034:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getRouteMatcher",{enumerable:!0,get:function(){return i}});let n=r(44827);function i(e){let{re:t,groups:r}=e;return e=>{let i=t.exec(e);if(!i)return!1;let o=e=>{try{return decodeURIComponent(e)}catch(e){throw Object.defineProperty(new n.DecodeError("failed to decode param"),"__NEXT_ERROR_CODE",{value:"E528",enumerable:!1,configurable:!0})}},a={};for(let[e,t]of Object.entries(r)){let r=i[t.pos];void 0!==r&&(t.repeat?a[e]=r.split("/").map(e=>o(e)):a[e]=o(r))}return a}}},78866:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"refreshReducer",{enumerable:!0,get:function(){return p}});let n=r(59008),i=r(57391),o=r(86770),a=r(2030),s=r(25232),l=r(59435),u=r(41500),c=r(89752),d=r(96493),h=r(68214),f=r(22308);function p(e,t){let{origin:r}=t,p={},m=e.canonicalUrl,g=e.tree;p.preserveCustomHistoryState=!1;let y=(0,c.createEmptyCacheNode)(),v=(0,h.hasInterceptionRouteInCurrentTree)(e.tree);return y.lazyData=(0,n.fetchServerResponse)(new URL(m,r),{flightRouterState:[g[0],g[1],g[2],"refetch"],nextUrl:v?e.nextUrl:null}),y.lazyData.then(async r=>{let{flightData:n,canonicalUrl:c}=r;if("string"==typeof n)return(0,s.handleExternalUrl)(e,p,n,e.pushRef.pendingPush);for(let r of(y.lazyData=null,n)){let{tree:n,seedData:l,head:h,isRootRender:b}=r;if(!b)return console.log("REFRESH FAILED"),e;let P=(0,o.applyRouterStatePatchToTree)([""],g,n,e.canonicalUrl);if(null===P)return(0,d.handleSegmentMismatch)(e,t,n);if((0,a.isNavigatingToNewRootLayout)(g,P))return(0,s.handleExternalUrl)(e,p,m,e.pushRef.pendingPush);let x=c?(0,i.createHrefFromUrl)(c):void 0;if(c&&(p.canonicalUrl=x),null!==l){let e=l[1],t=l[3];y.rsc=e,y.prefetchRsc=null,y.loading=t,(0,u.fillLazyItemsTillLeafWithHead)(y,void 0,n,l,h,void 0),p.prefetchCache=new Map}await (0,f.refreshInactiveParallelSegments)({state:e,updatedTree:P,updatedCache:y,includeNextUrl:v,canonicalUrl:p.canonicalUrl||e.canonicalUrl}),p.cache=y,p.patchedTree=P,g=P}return(0,l.handleMutable)(e,p)},()=>e)}r(50593),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},79289:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{DecodeError:function(){return p},MiddlewareNotFoundError:function(){return v},MissingStaticPage:function(){return y},NormalizeError:function(){return m},PageNotFoundError:function(){return g},SP:function(){return h},ST:function(){return f},WEB_VITALS:function(){return r},execOnce:function(){return n},getDisplayName:function(){return l},getLocationOrigin:function(){return a},getURL:function(){return s},isAbsoluteUrl:function(){return o},isResSent:function(){return u},loadGetInitialProps:function(){return d},normalizeRepeatedSlashes:function(){return c},stringifyError:function(){return b}});let r=["CLS","FCP","FID","INP","LCP","TTFB"];function n(e){let t,r=!1;return function(){for(var n=arguments.length,i=Array(n),o=0;o<n;o++)i[o]=arguments[o];return r||(r=!0,t=e(...i)),t}}let i=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,o=e=>i.test(e);function a(){let{protocol:e,hostname:t,port:r}=window.location;return e+"//"+t+(r?":"+r:"")}function s(){let{href:e}=window.location,t=a();return e.substring(t.length)}function l(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function u(e){return e.finished||e.headersSent}function c(e){let t=e.split("?");return t[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(t[1]?"?"+t.slice(1).join("?"):"")}async function d(e,t){let r=t.res||t.ctx&&t.ctx.res;if(!e.getInitialProps)return t.ctx&&t.Component?{pageProps:await d(t.Component,t.ctx)}:{};let n=await e.getInitialProps(t);if(r&&u(r))return n;if(!n)throw Object.defineProperty(Error('"'+l(e)+'.getInitialProps()" should resolve to an object. But found "'+n+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return n}let h="undefined"!=typeof performance,f=h&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class p extends Error{}class m extends Error{}class g extends Error{constructor(e){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+e}}class y extends Error{constructor(e,t){super(),this.message="Failed to load static file for page: "+e+" "+t}}class v extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function b(e){return JSON.stringify({message:e.message,stack:e.stack})}},84545:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{useReducer:function(){return s},useUnwrapState:function(){return a}});let n=r(40740)._(r(43210)),i=r(91992),o=r(61520);function a(e){return(0,i.isThenable)(e)?(0,n.use)(e):e}function s(e){let[t,r]=n.default.useState(e.state),i=(0,o.useSyncDevRenderIndicator)();return[t,(0,n.useCallback)(t=>{i(()=>{e.dispatch(t,r)})},[e,i])]}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},84949:(e,t)=>{"use strict";function r(e){return e.replace(/\/$/,"")||"/"}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"removeTrailingSlash",{enumerable:!0,get:function(){return r}})},85531:(e,t)=>{"use strict";function r(e){return e.startsWith("/")?e:"/"+e}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ensureLeadingSlash",{enumerable:!0,get:function(){return r}})},85814:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return p}});let n=r(14985),i=r(60687),o=n._(r(43210)),a=r(30195),s=r(22142),l=r(59154),u=r(53038),c=r(79289),d=r(96127);r(50148);let h=r(73406);function f(e){return"string"==typeof e?e:(0,a.formatUrl)(e)}let p=o.default.forwardRef(function(e,t){let r,n;let{href:a,as:p,children:m,prefetch:g=null,passHref:y,replace:v,shallow:b,scroll:P,onClick:x,onMouseEnter:R,onTouchStart:E,legacyBehavior:T=!1,..._}=e;r=m,T&&("string"==typeof r||"number"==typeof r)&&(r=(0,i.jsx)("a",{children:r}));let w=o.default.useContext(s.AppRouterContext),S=!1!==g,A=null===g?l.PrefetchKind.AUTO:l.PrefetchKind.FULL,{href:M,as:j}=o.default.useMemo(()=>{let e=f(a);return{href:e,as:p?f(p):e}},[a,p]);T&&(n=o.default.Children.only(r));let O=T?n&&"object"==typeof n&&n.ref:t,C=o.default.useCallback(e=>(S&&null!==w&&(0,h.mountLinkInstance)(e,M,w,A),()=>{(0,h.unmountLinkInstance)(e)}),[S,M,w,A]),D={ref:(0,u.useMergedRef)(C,O),onClick(e){T||"function"!=typeof x||x(e),T&&n.props&&"function"==typeof n.props.onClick&&n.props.onClick(e),w&&!e.defaultPrevented&&!function(e,t,r,n,i,a,s){let{nodeName:l}=e.currentTarget;!("A"===l.toUpperCase()&&function(e){let t=e.currentTarget.getAttribute("target");return t&&"_self"!==t||e.metaKey||e.ctrlKey||e.shiftKey||e.altKey||e.nativeEvent&&2===e.nativeEvent.which}(e))&&(e.preventDefault(),o.default.startTransition(()=>{let e=null==s||s;"beforePopState"in t?t[i?"replace":"push"](r,n,{shallow:a,scroll:e}):t[i?"replace":"push"](n||r,{scroll:e})}))}(e,w,M,j,v,b,P)},onMouseEnter(e){T||"function"!=typeof R||R(e),T&&n.props&&"function"==typeof n.props.onMouseEnter&&n.props.onMouseEnter(e),w&&S&&(0,h.onNavigationIntent)(e.currentTarget)},onTouchStart:function(e){T||"function"!=typeof E||E(e),T&&n.props&&"function"==typeof n.props.onTouchStart&&n.props.onTouchStart(e),w&&S&&(0,h.onNavigationIntent)(e.currentTarget)}};return(0,c.isAbsoluteUrl)(j)?D.href=j:T&&!y&&("a"!==n.type||"href"in n.props)||(D.href=(0,d.addBasePath)(j)),T?o.default.cloneElement(n,D):(0,i.jsx)("a",{..._,...D,children:r})});("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},86044:(e,t,r)=>{"use strict";r.d(t,{xQ:()=>o});var n=r(43210),i=r(21279);function o(e=!0){let t=(0,n.useContext)(i.t);if(null===t)return[!0,null];let{isPresent:r,onExitComplete:a,register:s}=t,l=(0,n.useId)();(0,n.useEffect)(()=>{e&&s(l)},[e]);let u=(0,n.useCallback)(()=>e&&a&&a(l),[l,a,e]);return!r&&a?[!1,u]:[!0]}},86770:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"applyRouterStatePatchToTree",{enumerable:!0,get:function(){return function e(t,r,n,l){let u;let[c,d,h,f,p]=r;if(1===t.length){let e=s(r,n);return(0,a.addRefreshMarkerToActiveParallelSegments)(e,l),e}let[m,g]=t;if(!(0,o.matchSegment)(m,c))return null;if(2===t.length)u=s(d[g],n);else if(null===(u=e((0,i.getNextFlightSegmentPath)(t),d[g],n,l)))return null;let y=[t[0],{...d,[g]:u},h,f];return p&&(y[4]=!0),(0,a.addRefreshMarkerToActiveParallelSegments)(y,l),y}}});let n=r(83913),i=r(74007),o=r(14077),a=r(22308);function s(e,t){let[r,i]=e,[a,l]=t;if(a===n.DEFAULT_SEGMENT_KEY&&r!==n.DEFAULT_SEGMENT_KEY)return e;if((0,o.matchSegment)(r,a)){let t={};for(let e in i)void 0!==l[e]?t[e]=s(i[e],l[e]):t[e]=i[e];for(let e in l)!t[e]&&(t[e]=l[e]);let n=[r,t];return e[2]&&(n[2]=e[2]),e[3]&&(n[3]=e[3]),e[4]&&(n[4]=e[4]),n}return t}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},88212:(e,t,r)=>{"use strict";function n(e){return function(){let{cookie:t}=e;if(!t)return{};let{parse:n}=r(26415);return n(Array.isArray(t)?t.join("; "):t)}}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getCookieParser",{enumerable:!0,get:function(){return n}})},89752:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{createEmptyCacheNode:function(){return j},createPrefetchURL:function(){return A},default:function(){return k}});let n=r(40740),i=r(60687),o=n._(r(43210)),a=r(22142),s=r(59154),l=r(57391),u=r(10449),c=r(84545),d=n._(r(35656)),h=r(35416),f=r(96127),p=r(77022),m=r(67086),g=r(44397),y=r(89330),v=r(25942),b=r(26736),P=r(70642),x=r(12776),R=r(11264);r(50593);let E=r(36875),T=r(97860),_=r(75076);r(73406);let w={};function S(e){return e.origin!==window.location.origin}function A(e){let t;if((0,h.isBot)(window.navigator.userAgent))return null;try{t=new URL((0,f.addBasePath)(e),window.location.href)}catch(t){throw Object.defineProperty(Error("Cannot prefetch '"+e+"' because it cannot be converted to a URL."),"__NEXT_ERROR_CODE",{value:"E234",enumerable:!1,configurable:!0})}return S(t)?null:t}function M(e){let{appRouterState:t}=e;return(0,o.useInsertionEffect)(()=>{let{tree:e,pushRef:r,canonicalUrl:n}=t,i={...r.preserveCustomHistoryState?window.history.state:{},__NA:!0,__PRIVATE_NEXTJS_INTERNALS_TREE:e};r.pendingPush&&(0,l.createHrefFromUrl)(new URL(window.location.href))!==n?(r.pendingPush=!1,window.history.pushState(i,"",n)):window.history.replaceState(i,"",n)},[t]),(0,o.useEffect)(()=>{},[t.nextUrl,t.tree]),null}function j(){return{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null}}function O(e){null==e&&(e={});let t=window.history.state,r=null==t?void 0:t.__NA;r&&(e.__NA=r);let n=null==t?void 0:t.__PRIVATE_NEXTJS_INTERNALS_TREE;return n&&(e.__PRIVATE_NEXTJS_INTERNALS_TREE=n),e}function C(e){let{headCacheNode:t}=e,r=null!==t?t.head:null,n=null!==t?t.prefetchHead:null,i=null!==n?n:r;return(0,o.useDeferredValue)(r,i)}function D(e){let t,{actionQueue:r,assetPrefix:n,globalError:l}=e,[h,x]=(0,c.useReducer)(r),{canonicalUrl:j}=(0,c.useUnwrapState)(h),{searchParams:D,pathname:k}=(0,o.useMemo)(()=>{let e=new URL(j,"http://n");return{searchParams:e.searchParams,pathname:(0,b.hasBasePath)(e.pathname)?(0,v.removeBasePath)(e.pathname):e.pathname}},[j]),L=(0,o.useCallback)(e=>{let{previousTree:t,serverResponse:r}=e;(0,o.startTransition)(()=>{x({type:s.ACTION_SERVER_PATCH,previousTree:t,serverResponse:r})})},[x]),N=(0,o.useCallback)((e,t,r)=>{let n=new URL((0,f.addBasePath)(e),location.href);return x({type:s.ACTION_NAVIGATE,url:n,isExternalUrl:S(n),locationSearch:location.search,shouldScroll:null==r||r,navigateType:t,allowAliasing:!0})},[x]);(0,R.useServerActionDispatcher)(x);let I=(0,o.useMemo)(()=>({back:()=>window.history.back(),forward:()=>window.history.forward(),prefetch:(e,t)=>{let n=A(e);if(null!==n){var i;(0,_.prefetchReducer)(r.state,{type:s.ACTION_PREFETCH,url:n,kind:null!=(i=null==t?void 0:t.kind)?i:s.PrefetchKind.FULL})}},replace:(e,t)=>{void 0===t&&(t={}),(0,o.startTransition)(()=>{var r;N(e,"replace",null==(r=t.scroll)||r)})},push:(e,t)=>{void 0===t&&(t={}),(0,o.startTransition)(()=>{var r;N(e,"push",null==(r=t.scroll)||r)})},refresh:()=>{(0,o.startTransition)(()=>{x({type:s.ACTION_REFRESH,origin:window.location.origin})})},hmrRefresh:()=>{throw Object.defineProperty(Error("hmrRefresh can only be used in development mode. Please use refresh instead."),"__NEXT_ERROR_CODE",{value:"E485",enumerable:!1,configurable:!0})}}),[r,x,N]);(0,o.useEffect)(()=>{window.next&&(window.next.router=I)},[I]),(0,o.useEffect)(()=>{function e(e){var t;e.persisted&&(null==(t=window.history.state)?void 0:t.__PRIVATE_NEXTJS_INTERNALS_TREE)&&(w.pendingMpaPath=void 0,x({type:s.ACTION_RESTORE,url:new URL(window.location.href),tree:window.history.state.__PRIVATE_NEXTJS_INTERNALS_TREE}))}return window.addEventListener("pageshow",e),()=>{window.removeEventListener("pageshow",e)}},[x]),(0,o.useEffect)(()=>{function e(e){let t="reason"in e?e.reason:e.error;if((0,T.isRedirectError)(t)){e.preventDefault();let r=(0,E.getURLFromRedirectError)(t);(0,E.getRedirectTypeFromError)(t)===T.RedirectType.push?I.push(r,{}):I.replace(r,{})}}return window.addEventListener("error",e),window.addEventListener("unhandledrejection",e),()=>{window.removeEventListener("error",e),window.removeEventListener("unhandledrejection",e)}},[I]);let{pushRef:V}=(0,c.useUnwrapState)(h);if(V.mpaNavigation){if(w.pendingMpaPath!==j){let e=window.location;V.pendingPush?e.assign(j):e.replace(j),w.pendingMpaPath=j}(0,o.use)(y.unresolvedThenable)}(0,o.useEffect)(()=>{let e=window.history.pushState.bind(window.history),t=window.history.replaceState.bind(window.history),r=e=>{var t;let r=window.location.href,n=null==(t=window.history.state)?void 0:t.__PRIVATE_NEXTJS_INTERNALS_TREE;(0,o.startTransition)(()=>{x({type:s.ACTION_RESTORE,url:new URL(null!=e?e:r,r),tree:n})})};window.history.pushState=function(t,n,i){return(null==t?void 0:t.__NA)||(null==t?void 0:t._N)||(t=O(t),i&&r(i)),e(t,n,i)},window.history.replaceState=function(e,n,i){return(null==e?void 0:e.__NA)||(null==e?void 0:e._N)||(e=O(e),i&&r(i)),t(e,n,i)};let n=e=>{if(e.state){if(!e.state.__NA){window.location.reload();return}(0,o.startTransition)(()=>{x({type:s.ACTION_RESTORE,url:new URL(window.location.href),tree:e.state.__PRIVATE_NEXTJS_INTERNALS_TREE})})}};return window.addEventListener("popstate",n),()=>{window.history.pushState=e,window.history.replaceState=t,window.removeEventListener("popstate",n)}},[x]);let{cache:F,tree:B,nextUrl:$,focusAndScrollRef:H}=(0,c.useUnwrapState)(h),z=(0,o.useMemo)(()=>(0,g.findHeadInCache)(F,B[1]),[F,B]),W=(0,o.useMemo)(()=>(0,P.getSelectedParams)(B),[B]),K=(0,o.useMemo)(()=>({parentTree:B,parentCacheNode:F,parentSegmentPath:null,url:j}),[B,F,j]),q=(0,o.useMemo)(()=>({changeByServerResponse:L,tree:B,focusAndScrollRef:H,nextUrl:$}),[L,B,H,$]);if(null!==z){let[e,r]=z;t=(0,i.jsx)(C,{headCacheNode:e},r)}else t=null;let X=(0,i.jsxs)(m.RedirectBoundary,{children:[t,F.rsc,(0,i.jsx)(p.AppRouterAnnouncer,{tree:B})]});return X=(0,i.jsx)(d.ErrorBoundary,{errorComponent:l[0],errorStyles:l[1],children:X}),(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(M,{appRouterState:(0,c.useUnwrapState)(h)}),(0,i.jsx)(U,{}),(0,i.jsx)(u.PathParamsContext.Provider,{value:W,children:(0,i.jsx)(u.PathnameContext.Provider,{value:k,children:(0,i.jsx)(u.SearchParamsContext.Provider,{value:D,children:(0,i.jsx)(a.GlobalLayoutRouterContext.Provider,{value:q,children:(0,i.jsx)(a.AppRouterContext.Provider,{value:I,children:(0,i.jsx)(a.LayoutRouterContext.Provider,{value:K,children:X})})})})})})]})}function k(e){let{actionQueue:t,globalErrorComponentAndStyles:[r,n],assetPrefix:o}=e;return(0,x.useNavFailureHandler)(),(0,i.jsx)(d.ErrorBoundary,{errorComponent:d.default,children:(0,i.jsx)(D,{actionQueue:t,assetPrefix:o,globalError:[r,n]})})}let L=new Set,N=new Set;function U(){let[,e]=o.default.useState(0),t=L.size;return(0,o.useEffect)(()=>{let r=()=>e(e=>e+1);return N.add(r),t!==L.size&&r(),()=>{N.delete(r)}},[t,e]),[...L].map((e,t)=>(0,i.jsx)("link",{rel:"stylesheet",href:""+e,precedence:"next"},t))}globalThis._N_E_STYLE_LOAD=function(e){let t=L.size;return L.add(e),L.size!==t&&N.forEach(e=>e()),Promise.resolve()},("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},91992:(e,t)=>{"use strict";function r(e){return null!==e&&"object"==typeof e&&"then"in e&&"function"==typeof e.then}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isThenable",{enumerable:!0,get:function(){return r}})},95796:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"HTML_LIMITED_BOT_UA_RE",{enumerable:!0,get:function(){return r}});let r=/Mediapartners-Google|Slurp|DuckDuckBot|baiduspider|yandex|sogou|bitlybot|tumblr|vkShare|quora link preview|redditbot|ia_archiver|Bingbot|BingPreview|applebot|facebookexternalhit|facebookcatalog|Twitterbot|LinkedInBot|Slackbot|Discordbot|WhatsApp|SkypeUriPreview/i},96127:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addBasePath",{enumerable:!0,get:function(){return o}});let n=r(98834),i=r(54674);function o(e,t){return(0,i.normalizePathTrailingSlash)((0,n.addPathPrefix)(e,""))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},96493:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"handleSegmentMismatch",{enumerable:!0,get:function(){return i}});let n=r(25232);function i(e,t,r){return(0,n.handleExternalUrl)(e,{},e.canonicalUrl,!0)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},97464:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"clearCacheNodeDataForSegmentPath",{enumerable:!0,get:function(){return function e(t,r,o){let a=o.length<=2,[s,l]=o,u=(0,i.createRouterCacheKey)(l),c=r.parallelRoutes.get(s),d=t.parallelRoutes.get(s);d&&d!==c||(d=new Map(c),t.parallelRoutes.set(s,d));let h=null==c?void 0:c.get(u),f=d.get(u);if(a){f&&f.lazyData&&f!==h||d.set(u,{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null});return}if(!f||!h){f||d.set(u,{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null});return}return f===h&&(f={lazyData:f.lazyData,rsc:f.rsc,prefetchRsc:f.prefetchRsc,head:f.head,prefetchHead:f.prefetchHead,parallelRoutes:new Map(f.parallelRoutes),loading:f.loading},d.set(u,f)),e(f,h,(0,n.getNextFlightSegmentPath)(o))}}});let n=r(74007),i=r(33123);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},97905:(e,t,r)=>{"use strict";let n;function i(e){return null!==e&&"object"==typeof e&&"function"==typeof e.start}r.d(t,{P:()=>om});let o=e=>Array.isArray(e);function a(e,t){if(!Array.isArray(t))return!1;let r=t.length;if(r!==e.length)return!1;for(let n=0;n<r;n++)if(t[n]!==e[n])return!1;return!0}function s(e){return"string"==typeof e||Array.isArray(e)}function l(e){let t=[{},{}];return null==e||e.values.forEach((e,r)=>{t[0][r]=e.get(),t[1][r]=e.getVelocity()}),t}function u(e,t,r,n){if("function"==typeof t){let[i,o]=l(n);t=t(void 0!==r?r:e.custom,i,o)}if("string"==typeof t&&(t=e.variants&&e.variants[t]),"function"==typeof t){let[i,o]=l(n);t=t(void 0!==r?r:e.custom,i,o)}return t}function c(e,t,r){let n=e.getProps();return u(n,t,void 0!==r?r:n.custom,e)}let d=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],h=["initial",...d];function f(e){let t;return()=>(void 0===t&&(t=e()),t)}let p=f(()=>void 0!==window.ScrollTimeline);class m{constructor(e){this.stop=()=>this.runAll("stop"),this.animations=e.filter(Boolean)}get finished(){return Promise.all(this.animations.map(e=>"finished"in e?e.finished:e))}getAll(e){return this.animations[0][e]}setAll(e,t){for(let r=0;r<this.animations.length;r++)this.animations[r][e]=t}attachTimeline(e,t){let r=this.animations.map(r=>p()&&r.attachTimeline?r.attachTimeline(e):"function"==typeof t?t(r):void 0);return()=>{r.forEach((e,t)=>{e&&e(),this.animations[t].stop()})}}get time(){return this.getAll("time")}set time(e){this.setAll("time",e)}get speed(){return this.getAll("speed")}set speed(e){this.setAll("speed",e)}get startTime(){return this.getAll("startTime")}get duration(){let e=0;for(let t=0;t<this.animations.length;t++)e=Math.max(e,this.animations[t].duration);return e}runAll(e){this.animations.forEach(t=>t[e]())}flatten(){this.runAll("flatten")}play(){this.runAll("play")}pause(){this.runAll("pause")}cancel(){this.runAll("cancel")}complete(){this.runAll("complete")}}class g extends m{then(e,t){return Promise.all(this.animations).then(e).catch(t)}}function y(e,t){return e?e[t]||e.default||e:void 0}function v(e){let t=0,r=e.next(t);for(;!r.done&&t<2e4;)t+=50,r=e.next(t);return t>=2e4?1/0:t}function b(e){return"function"==typeof e}function P(e,t){e.timeline=t,e.onfinish=null}let x=e=>Array.isArray(e)&&"number"==typeof e[0],R={linearEasing:void 0},E=function(e,t){let r=f(e);return()=>{var e;return null!==(e=R[t])&&void 0!==e?e:r()}}(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(e){return!1}return!0},"linearEasing"),T=(e,t,r)=>{let n=t-e;return 0===n?1:(r-e)/n},_=(e,t,r=10)=>{let n="",i=Math.max(Math.round(t/r),2);for(let t=0;t<i;t++)n+=e(T(0,i-1,t))+", ";return`linear(${n.substring(0,n.length-2)})`},w=([e,t,r,n])=>`cubic-bezier(${e}, ${t}, ${r}, ${n})`,S={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:w([0,.65,.55,1]),circOut:w([.55,0,1,.45]),backIn:w([.31,.01,.66,-.59]),backOut:w([.33,1.53,.69,.99])},A={x:!1,y:!1};function M(e,t){let r=function(e,t,r){var n;if(e instanceof Element)return[e];if("string"==typeof e){let t=document,r=(n=void 0,t.querySelectorAll(e));return r?Array.from(r):[]}return Array.from(e)}(e),n=new AbortController;return[r,{passive:!0,...t,signal:n.signal},()=>n.abort()]}function j(e){return t=>{"touch"===t.pointerType||A.x||A.y||e(t)}}let O=(e,t)=>!!t&&(e===t||O(e,t.parentElement)),C=e=>"mouse"===e.pointerType?"number"!=typeof e.button||e.button<=0:!1!==e.isPrimary,D=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]),k=new WeakSet;function L(e){return t=>{"Enter"===t.key&&e(t)}}function N(e,t){e.dispatchEvent(new PointerEvent("pointer"+t,{isPrimary:!0,bubbles:!0}))}let U=(e,t)=>{let r=e.currentTarget;if(!r)return;let n=L(()=>{if(k.has(r))return;N(r,"down");let e=L(()=>{N(r,"up")});r.addEventListener("keyup",e,t),r.addEventListener("blur",()=>N(r,"cancel"),t)});r.addEventListener("keydown",n,t),r.addEventListener("blur",()=>r.removeEventListener("keydown",n),t)};function I(e){return C(e)&&!(A.x||A.y)}let V=e=>1e3*e,F=e=>e/1e3,B=e=>e,$=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],H=new Set($),z=new Set(["width","height","top","left","right","bottom",...$]),W=e=>!!(e&&"object"==typeof e&&e.mix&&e.toValue),K=e=>o(e)?e[e.length-1]||0:e,q={skipAnimations:!1,useManualTiming:!1},X=["read","resolveKeyframes","update","preRender","render","postRender"];function Y(e,t){let r=!1,n=!0,i={delta:0,timestamp:0,isProcessing:!1},o=()=>r=!0,a=X.reduce((e,t)=>(e[t]=function(e){let t=new Set,r=new Set,n=!1,i=!1,o=new WeakSet,a={delta:0,timestamp:0,isProcessing:!1};function s(t){o.has(t)&&(l.schedule(t),e()),t(a)}let l={schedule:(e,i=!1,a=!1)=>{let s=a&&n?t:r;return i&&o.add(e),s.has(e)||s.add(e),e},cancel:e=>{r.delete(e),o.delete(e)},process:e=>{if(a=e,n){i=!0;return}n=!0,[t,r]=[r,t],t.forEach(s),t.clear(),n=!1,i&&(i=!1,l.process(e))}};return l}(o),e),{}),{read:s,resolveKeyframes:l,update:u,preRender:c,render:d,postRender:h}=a,f=()=>{let o=q.useManualTiming?i.timestamp:performance.now();r=!1,i.delta=n?1e3/60:Math.max(Math.min(o-i.timestamp,40),1),i.timestamp=o,i.isProcessing=!0,s.process(i),l.process(i),u.process(i),c.process(i),d.process(i),h.process(i),i.isProcessing=!1,r&&t&&(n=!1,e(f))},p=()=>{r=!0,n=!0,i.isProcessing||e(f)};return{schedule:X.reduce((e,t)=>{let n=a[t];return e[t]=(e,t=!1,i=!1)=>(r||p(),n.schedule(e,t,i)),e},{}),cancel:e=>{for(let t=0;t<X.length;t++)a[X[t]].cancel(e)},state:i,steps:a}}let{schedule:G,cancel:Q,state:Z,steps:J}=Y("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:B,!0);function ee(){n=void 0}let et={now:()=>(void 0===n&&et.set(Z.isProcessing||q.useManualTiming?Z.timestamp:performance.now()),n),set:e=>{n=e,queueMicrotask(ee)}};function er(e,t){-1===e.indexOf(t)&&e.push(t)}function en(e,t){let r=e.indexOf(t);r>-1&&e.splice(r,1)}class ei{constructor(){this.subscriptions=[]}add(e){return er(this.subscriptions,e),()=>en(this.subscriptions,e)}notify(e,t,r){let n=this.subscriptions.length;if(n){if(1===n)this.subscriptions[0](e,t,r);else for(let i=0;i<n;i++){let n=this.subscriptions[i];n&&n(e,t,r)}}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}let eo=e=>!isNaN(parseFloat(e)),ea={current:void 0};class es{constructor(e,t={}){this.version="11.18.2",this.canTrackVelocity=null,this.events={},this.updateAndNotify=(e,t=!0)=>{let r=et.now();this.updatedAt!==r&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(e),this.current!==this.prev&&this.events.change&&this.events.change.notify(this.current),t&&this.events.renderRequest&&this.events.renderRequest.notify(this.current)},this.hasAnimated=!1,this.setCurrent(e),this.owner=t.owner}setCurrent(e){this.current=e,this.updatedAt=et.now(),null===this.canTrackVelocity&&void 0!==e&&(this.canTrackVelocity=eo(this.current))}setPrevFrameValue(e=this.current){this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt}onChange(e){return this.on("change",e)}on(e,t){this.events[e]||(this.events[e]=new ei);let r=this.events[e].add(t);return"change"===e?()=>{r(),G.read(()=>{this.events.change.getSize()||this.stop()})}:r}clearListeners(){for(let e in this.events)this.events[e].clear()}attach(e,t){this.passiveEffect=e,this.stopPassiveEffect=t}set(e,t=!0){t&&this.passiveEffect?this.passiveEffect(e,this.updateAndNotify):this.updateAndNotify(e,t)}setWithVelocity(e,t,r){this.set(t),this.prev=void 0,this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt-r}jump(e,t=!0){this.updateAndNotify(e),this.prev=e,this.prevUpdatedAt=this.prevFrameValue=void 0,t&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}get(){return ea.current&&ea.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){var e;let t=et.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||t-this.updatedAt>30)return 0;let r=Math.min(this.updatedAt-this.prevUpdatedAt,30);return e=parseFloat(this.current)-parseFloat(this.prevFrameValue),r?1e3/r*e:0}start(e){return this.stop(),new Promise(t=>{this.hasAnimated=!0,this.animation=e(t),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function el(e,t){return new es(e,t)}let eu=e=>!!(e&&e.getVelocity);function ec(e,t){let r=e.getValue("willChange");if(eu(r)&&r.add)return r.add(t)}let ed=e=>e.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),eh="data-"+ed("framerAppearId"),ef={current:!1},ep=(e,t,r)=>(((1-3*r+3*t)*e+(3*r-6*t))*e+3*t)*e;function em(e,t,r,n){if(e===t&&r===n)return B;let i=t=>(function(e,t,r,n,i){let o,a;let s=0;do(o=ep(a=t+(r-t)/2,n,i)-e)>0?r=a:t=a;while(Math.abs(o)>1e-7&&++s<12);return a})(t,0,1,e,r);return e=>0===e||1===e?e:ep(i(e),t,n)}let eg=e=>t=>t<=.5?e(2*t)/2:(2-e(2*(1-t)))/2,ey=e=>t=>1-e(1-t),ev=em(.33,1.53,.69,.99),eb=ey(ev),eP=eg(eb),ex=e=>(e*=2)<1?.5*eb(e):.5*(2-Math.pow(2,-10*(e-1))),eR=e=>1-Math.sin(Math.acos(e)),eE=ey(eR),eT=eg(eR),e_=e=>/^0[^.\s]+$/u.test(e),ew=(e,t,r)=>r>t?t:r<e?e:r,eS={test:e=>"number"==typeof e,parse:parseFloat,transform:e=>e},eA={...eS,transform:e=>ew(0,1,e)},eM={...eS,default:1},ej=e=>Math.round(1e5*e)/1e5,eO=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu,eC=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,eD=(e,t)=>r=>!!("string"==typeof r&&eC.test(r)&&r.startsWith(e)||t&&null!=r&&Object.prototype.hasOwnProperty.call(r,t)),ek=(e,t,r)=>n=>{if("string"!=typeof n)return n;let[i,o,a,s]=n.match(eO);return{[e]:parseFloat(i),[t]:parseFloat(o),[r]:parseFloat(a),alpha:void 0!==s?parseFloat(s):1}},eL=e=>ew(0,255,e),eN={...eS,transform:e=>Math.round(eL(e))},eU={test:eD("rgb","red"),parse:ek("red","green","blue"),transform:({red:e,green:t,blue:r,alpha:n=1})=>"rgba("+eN.transform(e)+", "+eN.transform(t)+", "+eN.transform(r)+", "+ej(eA.transform(n))+")"},eI={test:eD("#"),parse:function(e){let t="",r="",n="",i="";return e.length>5?(t=e.substring(1,3),r=e.substring(3,5),n=e.substring(5,7),i=e.substring(7,9)):(t=e.substring(1,2),r=e.substring(2,3),n=e.substring(3,4),i=e.substring(4,5),t+=t,r+=r,n+=n,i+=i),{red:parseInt(t,16),green:parseInt(r,16),blue:parseInt(n,16),alpha:i?parseInt(i,16)/255:1}},transform:eU.transform},eV=e=>({test:t=>"string"==typeof t&&t.endsWith(e)&&1===t.split(" ").length,parse:parseFloat,transform:t=>`${t}${e}`}),eF=eV("deg"),eB=eV("%"),e$=eV("px"),eH=eV("vh"),ez=eV("vw"),eW={...eB,parse:e=>eB.parse(e)/100,transform:e=>eB.transform(100*e)},eK={test:eD("hsl","hue"),parse:ek("hue","saturation","lightness"),transform:({hue:e,saturation:t,lightness:r,alpha:n=1})=>"hsla("+Math.round(e)+", "+eB.transform(ej(t))+", "+eB.transform(ej(r))+", "+ej(eA.transform(n))+")"},eq={test:e=>eU.test(e)||eI.test(e)||eK.test(e),parse:e=>eU.test(e)?eU.parse(e):eK.test(e)?eK.parse(e):eI.parse(e),transform:e=>"string"==typeof e?e:e.hasOwnProperty("red")?eU.transform(e):eK.transform(e)},eX=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu,eY="number",eG="color",eQ=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function eZ(e){let t=e.toString(),r=[],n={color:[],number:[],var:[]},i=[],o=0,a=t.replace(eQ,e=>(eq.test(e)?(n.color.push(o),i.push(eG),r.push(eq.parse(e))):e.startsWith("var(")?(n.var.push(o),i.push("var"),r.push(e)):(n.number.push(o),i.push(eY),r.push(parseFloat(e))),++o,"${}")).split("${}");return{values:r,split:a,indexes:n,types:i}}function eJ(e){return eZ(e).values}function e0(e){let{split:t,types:r}=eZ(e),n=t.length;return e=>{let i="";for(let o=0;o<n;o++)if(i+=t[o],void 0!==e[o]){let t=r[o];t===eY?i+=ej(e[o]):t===eG?i+=eq.transform(e[o]):i+=e[o]}return i}}let e1=e=>"number"==typeof e?0:e,e2={test:function(e){var t,r;return isNaN(e)&&"string"==typeof e&&((null===(t=e.match(eO))||void 0===t?void 0:t.length)||0)+((null===(r=e.match(eX))||void 0===r?void 0:r.length)||0)>0},parse:eJ,createTransformer:e0,getAnimatableNone:function(e){let t=eJ(e);return e0(e)(t.map(e1))}},e3=new Set(["brightness","contrast","saturate","opacity"]);function e5(e){let[t,r]=e.slice(0,-1).split("(");if("drop-shadow"===t)return e;let[n]=r.match(eO)||[];if(!n)return e;let i=r.replace(n,""),o=+!!e3.has(t);return n!==r&&(o*=100),t+"("+o+i+")"}let e9=/\b([a-z-]*)\(.*?\)/gu,e4={...e2,getAnimatableNone:e=>{let t=e.match(e9);return t?t.map(e5).join(" "):e}},e7={...eS,transform:Math.round},e6={borderWidth:e$,borderTopWidth:e$,borderRightWidth:e$,borderBottomWidth:e$,borderLeftWidth:e$,borderRadius:e$,radius:e$,borderTopLeftRadius:e$,borderTopRightRadius:e$,borderBottomRightRadius:e$,borderBottomLeftRadius:e$,width:e$,maxWidth:e$,height:e$,maxHeight:e$,top:e$,right:e$,bottom:e$,left:e$,padding:e$,paddingTop:e$,paddingRight:e$,paddingBottom:e$,paddingLeft:e$,margin:e$,marginTop:e$,marginRight:e$,marginBottom:e$,marginLeft:e$,backgroundPositionX:e$,backgroundPositionY:e$,rotate:eF,rotateX:eF,rotateY:eF,rotateZ:eF,scale:eM,scaleX:eM,scaleY:eM,scaleZ:eM,skew:eF,skewX:eF,skewY:eF,distance:e$,translateX:e$,translateY:e$,translateZ:e$,x:e$,y:e$,z:e$,perspective:e$,transformPerspective:e$,opacity:eA,originX:eW,originY:eW,originZ:e$,zIndex:e7,size:e$,fillOpacity:eA,strokeOpacity:eA,numOctaves:e7},e8={...e6,color:eq,backgroundColor:eq,outlineColor:eq,fill:eq,stroke:eq,borderColor:eq,borderTopColor:eq,borderRightColor:eq,borderBottomColor:eq,borderLeftColor:eq,filter:e4,WebkitFilter:e4},te=e=>e8[e];function tt(e,t){let r=te(e);return r!==e4&&(r=e2),r.getAnimatableNone?r.getAnimatableNone(t):void 0}let tr=new Set(["auto","none","0"]),tn=e=>e===eS||e===e$,ti=(e,t)=>parseFloat(e.split(", ")[t]),to=(e,t)=>(r,{transform:n})=>{if("none"===n||!n)return 0;let i=n.match(/^matrix3d\((.+)\)$/u);if(i)return ti(i[1],t);{let t=n.match(/^matrix\((.+)\)$/u);return t?ti(t[1],e):0}},ta=new Set(["x","y","z"]),ts=$.filter(e=>!ta.has(e)),tl={width:({x:e},{paddingLeft:t="0",paddingRight:r="0"})=>e.max-e.min-parseFloat(t)-parseFloat(r),height:({y:e},{paddingTop:t="0",paddingBottom:r="0"})=>e.max-e.min-parseFloat(t)-parseFloat(r),top:(e,{top:t})=>parseFloat(t),left:(e,{left:t})=>parseFloat(t),bottom:({y:e},{top:t})=>parseFloat(t)+(e.max-e.min),right:({x:e},{left:t})=>parseFloat(t)+(e.max-e.min),x:to(4,13),y:to(5,14)};tl.translateX=tl.x,tl.translateY=tl.y;let tu=new Set,tc=!1,td=!1;function th(){if(td){let e=Array.from(tu).filter(e=>e.needsMeasurement),t=new Set(e.map(e=>e.element)),r=new Map;t.forEach(e=>{let t=function(e){let t=[];return ts.forEach(r=>{let n=e.getValue(r);void 0!==n&&(t.push([r,n.get()]),n.set(+!!r.startsWith("scale")))}),t}(e);t.length&&(r.set(e,t),e.render())}),e.forEach(e=>e.measureInitialState()),t.forEach(e=>{e.render();let t=r.get(e);t&&t.forEach(([t,r])=>{var n;null===(n=e.getValue(t))||void 0===n||n.set(r)})}),e.forEach(e=>e.measureEndState()),e.forEach(e=>{void 0!==e.suspendedScrollY&&window.scrollTo(0,e.suspendedScrollY)})}td=!1,tc=!1,tu.forEach(e=>e.complete()),tu.clear()}function tf(){tu.forEach(e=>{e.readKeyframes(),e.needsMeasurement&&(td=!0)})}class tp{constructor(e,t,r,n,i,o=!1){this.isComplete=!1,this.isAsync=!1,this.needsMeasurement=!1,this.isScheduled=!1,this.unresolvedKeyframes=[...e],this.onComplete=t,this.name=r,this.motionValue=n,this.element=i,this.isAsync=o}scheduleResolve(){this.isScheduled=!0,this.isAsync?(tu.add(this),tc||(tc=!0,G.read(tf),G.resolveKeyframes(th))):(this.readKeyframes(),this.complete())}readKeyframes(){let{unresolvedKeyframes:e,name:t,element:r,motionValue:n}=this;for(let i=0;i<e.length;i++)if(null===e[i]){if(0===i){let i=null==n?void 0:n.get(),o=e[e.length-1];if(void 0!==i)e[0]=i;else if(r&&t){let n=r.readValue(t,o);null!=n&&(e[0]=n)}void 0===e[0]&&(e[0]=o),n&&void 0===i&&n.set(e[0])}else e[i]=e[i-1]}}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(){this.isComplete=!0,this.onComplete(this.unresolvedKeyframes,this.finalKeyframe),tu.delete(this)}cancel(){this.isComplete||(this.isScheduled=!1,tu.delete(this))}resume(){this.isComplete||this.scheduleResolve()}}let tm=e=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(e),tg=e=>t=>"string"==typeof t&&t.startsWith(e),ty=tg("--"),tv=tg("var(--"),tb=e=>!!tv(e)&&tP.test(e.split("/*")[0].trim()),tP=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,tx=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u,tR=e=>t=>t.test(e),tE=[eS,e$,eB,eF,ez,eH,{test:e=>"auto"===e,parse:e=>e}],tT=e=>tE.find(tR(e));class t_ extends tp{constructor(e,t,r,n,i){super(e,t,r,n,i,!0)}readKeyframes(){let{unresolvedKeyframes:e,element:t,name:r}=this;if(!t||!t.current)return;super.readKeyframes();for(let r=0;r<e.length;r++){let n=e[r];if("string"==typeof n&&tb(n=n.trim())){let i=function e(t,r,n=1){B(n<=4,`Max CSS variable fallback depth detected in property "${t}". This may indicate a circular fallback dependency.`);let[i,o]=function(e){let t=tx.exec(e);if(!t)return[,];let[,r,n,i]=t;return[`--${null!=r?r:n}`,i]}(t);if(!i)return;let a=window.getComputedStyle(r).getPropertyValue(i);if(a){let e=a.trim();return tm(e)?parseFloat(e):e}return tb(o)?e(o,r,n+1):o}(n,t.current);void 0!==i&&(e[r]=i),r===e.length-1&&(this.finalKeyframe=n)}}if(this.resolveNoneKeyframes(),!z.has(r)||2!==e.length)return;let[n,i]=e,o=tT(n),a=tT(i);if(o!==a){if(tn(o)&&tn(a))for(let t=0;t<e.length;t++){let r=e[t];"string"==typeof r&&(e[t]=parseFloat(r))}else this.needsMeasurement=!0}}resolveNoneKeyframes(){let{unresolvedKeyframes:e,name:t}=this,r=[];for(let t=0;t<e.length;t++){var n;("number"==typeof(n=e[t])?0===n:null===n||"none"===n||"0"===n||e_(n))&&r.push(t)}r.length&&function(e,t,r){let n,i=0;for(;i<e.length&&!n;){let t=e[i];"string"==typeof t&&!tr.has(t)&&eZ(t).values.length&&(n=e[i]),i++}if(n&&r)for(let i of t)e[i]=tt(r,n)}(e,r,t)}measureInitialState(){let{element:e,unresolvedKeyframes:t,name:r}=this;if(!e||!e.current)return;"height"===r&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=tl[r](e.measureViewportBox(),window.getComputedStyle(e.current)),t[0]=this.measuredOrigin;let n=t[t.length-1];void 0!==n&&e.getValue(r,n).jump(n,!1)}measureEndState(){var e;let{element:t,name:r,unresolvedKeyframes:n}=this;if(!t||!t.current)return;let i=t.getValue(r);i&&i.jump(this.measuredOrigin,!1);let o=n.length-1,a=n[o];n[o]=tl[r](t.measureViewportBox(),window.getComputedStyle(t.current)),null!==a&&void 0===this.finalKeyframe&&(this.finalKeyframe=a),(null===(e=this.removedTransforms)||void 0===e?void 0:e.length)&&this.removedTransforms.forEach(([e,r])=>{t.getValue(e).set(r)}),this.resolveNoneKeyframes()}}let tw=(e,t)=>"zIndex"!==t&&!!("number"==typeof e||Array.isArray(e)||"string"==typeof e&&(e2.test(e)||"0"===e)&&!e.startsWith("url(")),tS=e=>null!==e;function tA(e,{repeat:t,repeatType:r="loop"},n){let i=e.filter(tS),o=t&&"loop"!==r&&t%2==1?0:i.length-1;return o&&void 0!==n?n:i[o]}class tM{constructor({autoplay:e=!0,delay:t=0,type:r="keyframes",repeat:n=0,repeatDelay:i=0,repeatType:o="loop",...a}){this.isStopped=!1,this.hasAttemptedResolve=!1,this.createdAt=et.now(),this.options={autoplay:e,delay:t,type:r,repeat:n,repeatDelay:i,repeatType:o,...a},this.updateFinishedPromise()}calcStartTime(){return this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt}get resolved(){return this._resolved||this.hasAttemptedResolve||(tf(),th()),this._resolved}onKeyframesResolved(e,t){this.resolvedAt=et.now(),this.hasAttemptedResolve=!0;let{name:r,type:n,velocity:i,delay:o,onComplete:a,onUpdate:s,isGenerator:l}=this.options;if(!l&&!function(e,t,r,n){let i=e[0];if(null===i)return!1;if("display"===t||"visibility"===t)return!0;let o=e[e.length-1],a=tw(i,t),s=tw(o,t);return B(a===s,`You are trying to animate ${t} from "${i}" to "${o}". ${i} is not an animatable value - to enable this animation set ${i} to a value animatable to ${o} via the \`style\` property.`),!!a&&!!s&&(function(e){let t=e[0];if(1===e.length)return!0;for(let r=0;r<e.length;r++)if(e[r]!==t)return!0}(e)||("spring"===r||b(r))&&n)}(e,r,n,i)){if(ef.current||!o){s&&s(tA(e,this.options,t)),a&&a(),this.resolveFinishedPromise();return}this.options.duration=0}let u=this.initPlayback(e,t);!1!==u&&(this._resolved={keyframes:e,finalKeyframe:t,...u},this.onPostResolved())}onPostResolved(){}then(e,t){return this.currentFinishedPromise.then(e,t)}flatten(){this.options.type="keyframes",this.options.ease="linear"}updateFinishedPromise(){this.currentFinishedPromise=new Promise(e=>{this.resolveFinishedPromise=e})}}let tj=(e,t,r)=>e+(t-e)*r;function tO(e,t,r){return(r<0&&(r+=1),r>1&&(r-=1),r<1/6)?e+(t-e)*6*r:r<.5?t:r<2/3?e+(t-e)*(2/3-r)*6:e}function tC(e,t){return r=>r>0?t:e}let tD=(e,t,r)=>{let n=e*e,i=r*(t*t-n)+n;return i<0?0:Math.sqrt(i)},tk=[eI,eU,eK],tL=e=>tk.find(t=>t.test(e));function tN(e){let t=tL(e);if(B(!!t,`'${e}' is not an animatable color. Use the equivalent color code instead.`),!t)return!1;let r=t.parse(e);return t===eK&&(r=function({hue:e,saturation:t,lightness:r,alpha:n}){e/=360,r/=100;let i=0,o=0,a=0;if(t/=100){let n=r<.5?r*(1+t):r+t-r*t,s=2*r-n;i=tO(s,n,e+1/3),o=tO(s,n,e),a=tO(s,n,e-1/3)}else i=o=a=r;return{red:Math.round(255*i),green:Math.round(255*o),blue:Math.round(255*a),alpha:n}}(r)),r}let tU=(e,t)=>{let r=tN(e),n=tN(t);if(!r||!n)return tC(e,t);let i={...r};return e=>(i.red=tD(r.red,n.red,e),i.green=tD(r.green,n.green,e),i.blue=tD(r.blue,n.blue,e),i.alpha=tj(r.alpha,n.alpha,e),eU.transform(i))},tI=(e,t)=>r=>t(e(r)),tV=(...e)=>e.reduce(tI),tF=new Set(["none","hidden"]);function tB(e,t){return r=>tj(e,t,r)}function t$(e){return"number"==typeof e?tB:"string"==typeof e?tb(e)?tC:eq.test(e)?tU:tW:Array.isArray(e)?tH:"object"==typeof e?eq.test(e)?tU:tz:tC}function tH(e,t){let r=[...e],n=r.length,i=e.map((e,r)=>t$(e)(e,t[r]));return e=>{for(let t=0;t<n;t++)r[t]=i[t](e);return r}}function tz(e,t){let r={...e,...t},n={};for(let i in r)void 0!==e[i]&&void 0!==t[i]&&(n[i]=t$(e[i])(e[i],t[i]));return e=>{for(let t in n)r[t]=n[t](e);return r}}let tW=(e,t)=>{let r=e2.createTransformer(t),n=eZ(e),i=eZ(t);return n.indexes.var.length===i.indexes.var.length&&n.indexes.color.length===i.indexes.color.length&&n.indexes.number.length>=i.indexes.number.length?tF.has(e)&&!i.values.length||tF.has(t)&&!n.values.length?function(e,t){return tF.has(e)?r=>r<=0?e:t:r=>r>=1?t:e}(e,t):tV(tH(function(e,t){var r;let n=[],i={color:0,var:0,number:0};for(let o=0;o<t.values.length;o++){let a=t.types[o],s=e.indexes[a][i[a]],l=null!==(r=e.values[s])&&void 0!==r?r:0;n[o]=l,i[a]++}return n}(n,i),i.values),r):(B(!0,`Complex values '${e}' and '${t}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`),tC(e,t))};function tK(e,t,r){return"number"==typeof e&&"number"==typeof t&&"number"==typeof r?tj(e,t,r):t$(e)(e,t)}function tq(e,t,r){var n,i;let o=Math.max(t-5,0);return n=r-e(o),(i=t-o)?1e3/i*n:0}let tX={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1};function tY(e,t){return e*Math.sqrt(1-t*t)}let tG=["duration","bounce"],tQ=["stiffness","damping","mass"];function tZ(e,t){return t.some(t=>void 0!==e[t])}function tJ(e=tX.visualDuration,t=tX.bounce){let r;let n="object"!=typeof e?{visualDuration:e,keyframes:[0,1],bounce:t}:e,{restSpeed:i,restDelta:o}=n,a=n.keyframes[0],s=n.keyframes[n.keyframes.length-1],l={done:!1,value:a},{stiffness:u,damping:c,mass:d,duration:h,velocity:f,isResolvedFromDuration:p}=function(e){let t={velocity:tX.velocity,stiffness:tX.stiffness,damping:tX.damping,mass:tX.mass,isResolvedFromDuration:!1,...e};if(!tZ(e,tQ)&&tZ(e,tG)){if(e.visualDuration){let r=2*Math.PI/(1.2*e.visualDuration),n=r*r,i=2*ew(.05,1,1-(e.bounce||0))*Math.sqrt(n);t={...t,mass:tX.mass,stiffness:n,damping:i}}else{let r=function({duration:e=tX.duration,bounce:t=tX.bounce,velocity:r=tX.velocity,mass:n=tX.mass}){let i,o;B(e<=V(tX.maxDuration),"Spring duration must be 10 seconds or less");let a=1-t;a=ew(tX.minDamping,tX.maxDamping,a),e=ew(tX.minDuration,tX.maxDuration,F(e)),a<1?(i=t=>{let n=t*a,i=n*e;return .001-(n-r)/tY(t,a)*Math.exp(-i)},o=t=>{let n=t*a*e,o=Math.pow(a,2)*Math.pow(t,2)*e,s=Math.exp(-n),l=tY(Math.pow(t,2),a);return(n*r+r-o)*s*(-i(t)+.001>0?-1:1)/l}):(i=t=>-.001+Math.exp(-t*e)*((t-r)*e+1),o=t=>e*e*(r-t)*Math.exp(-t*e));let s=function(e,t,r){let n=r;for(let r=1;r<12;r++)n-=e(n)/t(n);return n}(i,o,5/e);if(e=V(e),isNaN(s))return{stiffness:tX.stiffness,damping:tX.damping,duration:e};{let t=Math.pow(s,2)*n;return{stiffness:t,damping:2*a*Math.sqrt(n*t),duration:e}}}(e);(t={...t,...r,mass:tX.mass}).isResolvedFromDuration=!0}}return t}({...n,velocity:-F(n.velocity||0)}),m=f||0,g=c/(2*Math.sqrt(u*d)),y=s-a,b=F(Math.sqrt(u/d)),P=5>Math.abs(y);if(i||(i=P?tX.restSpeed.granular:tX.restSpeed.default),o||(o=P?tX.restDelta.granular:tX.restDelta.default),g<1){let e=tY(b,g);r=t=>s-Math.exp(-g*b*t)*((m+g*b*y)/e*Math.sin(e*t)+y*Math.cos(e*t))}else if(1===g)r=e=>s-Math.exp(-b*e)*(y+(m+b*y)*e);else{let e=b*Math.sqrt(g*g-1);r=t=>{let r=Math.exp(-g*b*t),n=Math.min(e*t,300);return s-r*((m+g*b*y)*Math.sinh(n)+e*y*Math.cosh(n))/e}}let x={calculatedDuration:p&&h||null,next:e=>{let t=r(e);if(p)l.done=e>=h;else{let n=0;g<1&&(n=0===e?V(m):tq(r,e,t));let a=Math.abs(s-t)<=o;l.done=Math.abs(n)<=i&&a}return l.value=l.done?s:t,l},toString:()=>{let e=Math.min(v(x),2e4),t=_(t=>x.next(e*t).value,e,30);return e+"ms "+t}};return x}function t0({keyframes:e,velocity:t=0,power:r=.8,timeConstant:n=325,bounceDamping:i=10,bounceStiffness:o=500,modifyTarget:a,min:s,max:l,restDelta:u=.5,restSpeed:c}){let d,h;let f=e[0],p={done:!1,value:f},m=e=>void 0!==s&&e<s||void 0!==l&&e>l,g=e=>void 0===s?l:void 0===l?s:Math.abs(s-e)<Math.abs(l-e)?s:l,y=r*t,v=f+y,b=void 0===a?v:a(v);b!==v&&(y=b-f);let P=e=>-y*Math.exp(-e/n),x=e=>b+P(e),R=e=>{let t=P(e),r=x(e);p.done=Math.abs(t)<=u,p.value=p.done?b:r},E=e=>{m(p.value)&&(d=e,h=tJ({keyframes:[p.value,g(p.value)],velocity:tq(x,e,p.value),damping:i,stiffness:o,restDelta:u,restSpeed:c}))};return E(0),{calculatedDuration:null,next:e=>{let t=!1;return(h||void 0!==d||(t=!0,R(e),E(e)),void 0!==d&&e>=d)?h.next(e-d):(t||R(e),p)}}}let t1=em(.42,0,1,1),t2=em(0,0,.58,1),t3=em(.42,0,.58,1),t5=e=>Array.isArray(e)&&"number"!=typeof e[0],t9={linear:B,easeIn:t1,easeInOut:t3,easeOut:t2,circIn:eR,circInOut:eT,circOut:eE,backIn:eb,backInOut:eP,backOut:ev,anticipate:ex},t4=e=>{if(x(e)){B(4===e.length,"Cubic bezier arrays must contain four numerical values.");let[t,r,n,i]=e;return em(t,r,n,i)}return"string"==typeof e?(B(void 0!==t9[e],`Invalid easing type '${e}'`),t9[e]):e};function t7({duration:e=300,keyframes:t,times:r,ease:n="easeInOut"}){let i=t5(n)?n.map(t4):t4(n),o={done:!1,value:t[0]},a=function(e,t,{clamp:r=!0,ease:n,mixer:i}={}){let o=e.length;if(B(o===t.length,"Both input and output ranges must be the same length"),1===o)return()=>t[0];if(2===o&&t[0]===t[1])return()=>t[1];let a=e[0]===e[1];e[0]>e[o-1]&&(e=[...e].reverse(),t=[...t].reverse());let s=function(e,t,r){let n=[],i=r||tK,o=e.length-1;for(let r=0;r<o;r++){let o=i(e[r],e[r+1]);t&&(o=tV(Array.isArray(t)?t[r]||B:t,o)),n.push(o)}return n}(t,n,i),l=s.length,u=r=>{if(a&&r<e[0])return t[0];let n=0;if(l>1)for(;n<e.length-2&&!(r<e[n+1]);n++);let i=T(e[n],e[n+1],r);return s[n](i)};return r?t=>u(ew(e[0],e[o-1],t)):u}((r&&r.length===t.length?r:function(e){let t=[0];return function(e,t){let r=e[e.length-1];for(let n=1;n<=t;n++){let i=T(0,t,n);e.push(tj(r,1,i))}}(t,e.length-1),t}(t)).map(t=>t*e),t,{ease:Array.isArray(i)?i:t.map(()=>i||t3).splice(0,t.length-1)});return{calculatedDuration:e,next:t=>(o.value=a(t),o.done=t>=e,o)}}let t6=e=>{let t=({timestamp:t})=>e(t);return{start:()=>G.update(t,!0),stop:()=>Q(t),now:()=>Z.isProcessing?Z.timestamp:et.now()}},t8={decay:t0,inertia:t0,tween:t7,keyframes:t7,spring:tJ},re=e=>e/100;class rt extends tM{constructor(e){super(e),this.holdTime=null,this.cancelTime=null,this.currentTime=0,this.playbackSpeed=1,this.pendingPlayState="running",this.startTime=null,this.state="idle",this.stop=()=>{if(this.resolver.cancel(),this.isStopped=!0,"idle"===this.state)return;this.teardown();let{onStop:e}=this.options;e&&e()};let{name:t,motionValue:r,element:n,keyframes:i}=this.options,o=(null==n?void 0:n.KeyframeResolver)||tp;this.resolver=new o(i,(e,t)=>this.onKeyframesResolved(e,t),t,r,n),this.resolver.scheduleResolve()}flatten(){super.flatten(),this._resolved&&Object.assign(this._resolved,this.initPlayback(this._resolved.keyframes))}initPlayback(e){let t,r;let{type:n="keyframes",repeat:i=0,repeatDelay:o=0,repeatType:a,velocity:s=0}=this.options,l=b(n)?n:t8[n]||t7;l!==t7&&"number"!=typeof e[0]&&(t=tV(re,tK(e[0],e[1])),e=[0,100]);let u=l({...this.options,keyframes:e});"mirror"===a&&(r=l({...this.options,keyframes:[...e].reverse(),velocity:-s})),null===u.calculatedDuration&&(u.calculatedDuration=v(u));let{calculatedDuration:c}=u,d=c+o;return{generator:u,mirroredGenerator:r,mapPercentToKeyframes:t,calculatedDuration:c,resolvedDuration:d,totalDuration:d*(i+1)-o}}onPostResolved(){let{autoplay:e=!0}=this.options;this.play(),"paused"!==this.pendingPlayState&&e?this.state=this.pendingPlayState:this.pause()}tick(e,t=!1){let{resolved:r}=this;if(!r){let{keyframes:e}=this.options;return{done:!0,value:e[e.length-1]}}let{finalKeyframe:n,generator:i,mirroredGenerator:o,mapPercentToKeyframes:a,keyframes:s,calculatedDuration:l,totalDuration:u,resolvedDuration:c}=r;if(null===this.startTime)return i.next(0);let{delay:d,repeat:h,repeatType:f,repeatDelay:p,onUpdate:m}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,e):this.speed<0&&(this.startTime=Math.min(e-u/this.speed,this.startTime)),t?this.currentTime=e:null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=Math.round(e-this.startTime)*this.speed;let g=this.currentTime-d*(this.speed>=0?1:-1),y=this.speed>=0?g<0:g>u;this.currentTime=Math.max(g,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=u);let v=this.currentTime,b=i;if(h){let e=Math.min(this.currentTime,u)/c,t=Math.floor(e),r=e%1;!r&&e>=1&&(r=1),1===r&&t--,(t=Math.min(t,h+1))%2&&("reverse"===f?(r=1-r,p&&(r-=p/c)):"mirror"===f&&(b=o)),v=ew(0,1,r)*c}let P=y?{done:!1,value:s[0]}:b.next(v);a&&(P.value=a(P.value));let{done:x}=P;y||null===l||(x=this.speed>=0?this.currentTime>=u:this.currentTime<=0);let R=null===this.holdTime&&("finished"===this.state||"running"===this.state&&x);return R&&void 0!==n&&(P.value=tA(s,this.options,n)),m&&m(P.value),R&&this.finish(),P}get duration(){let{resolved:e}=this;return e?F(e.calculatedDuration):0}get time(){return F(this.currentTime)}set time(e){e=V(e),this.currentTime=e,null!==this.holdTime||0===this.speed?this.holdTime=e:this.driver&&(this.startTime=this.driver.now()-e/this.speed)}get speed(){return this.playbackSpeed}set speed(e){let t=this.playbackSpeed!==e;this.playbackSpeed=e,t&&(this.time=F(this.currentTime))}play(){if(this.resolver.isScheduled||this.resolver.resume(),!this._resolved){this.pendingPlayState="running";return}if(this.isStopped)return;let{driver:e=t6,onPlay:t,startTime:r}=this.options;this.driver||(this.driver=e(e=>this.tick(e))),t&&t();let n=this.driver.now();null!==this.holdTime?this.startTime=n-this.holdTime:this.startTime?"finished"===this.state&&(this.startTime=n):this.startTime=null!=r?r:this.calcStartTime(),"finished"===this.state&&this.updateFinishedPromise(),this.cancelTime=this.startTime,this.holdTime=null,this.state="running",this.driver.start()}pause(){var e;if(!this._resolved){this.pendingPlayState="paused";return}this.state="paused",this.holdTime=null!==(e=this.currentTime)&&void 0!==e?e:0}complete(){"running"!==this.state&&this.play(),this.pendingPlayState=this.state="finished",this.holdTime=null}finish(){this.teardown(),this.state="finished";let{onComplete:e}=this.options;e&&e()}cancel(){null!==this.cancelTime&&this.tick(this.cancelTime),this.teardown(),this.updateFinishedPromise()}teardown(){this.state="idle",this.stopDriver(),this.resolveFinishedPromise(),this.updateFinishedPromise(),this.startTime=this.cancelTime=null,this.resolver.cancel()}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(e){return this.startTime=0,this.tick(e,!0)}}let rr=new Set(["opacity","clipPath","filter","transform"]),rn=f(()=>Object.hasOwnProperty.call(Element.prototype,"animate")),ri={anticipate:ex,backInOut:eP,circInOut:eT};class ro extends tM{constructor(e){super(e);let{name:t,motionValue:r,element:n,keyframes:i}=this.options;this.resolver=new t_(i,(e,t)=>this.onKeyframesResolved(e,t),t,r,n),this.resolver.scheduleResolve()}initPlayback(e,t){var r;let{duration:n=300,times:i,ease:o,type:a,motionValue:s,name:l,startTime:u}=this.options;if(!s.owner||!s.owner.current)return!1;if("string"==typeof o&&E()&&o in ri&&(o=ri[o]),b((r=this.options).type)||"spring"===r.type||!function e(t){return!!("function"==typeof t&&E()||!t||"string"==typeof t&&(t in S||E())||x(t)||Array.isArray(t)&&t.every(e))}(r.ease)){let{onComplete:t,onUpdate:r,motionValue:s,element:l,...u}=this.options,c=function(e,t){let r=new rt({...t,keyframes:e,repeat:0,delay:0,isGenerator:!0}),n={done:!1,value:e[0]},i=[],o=0;for(;!n.done&&o<2e4;)i.push((n=r.sample(o)).value),o+=10;return{times:void 0,keyframes:i,duration:o-10,ease:"linear"}}(e,u);1===(e=c.keyframes).length&&(e[1]=e[0]),n=c.duration,i=c.times,o=c.ease,a="keyframes"}let c=function(e,t,r,{delay:n=0,duration:i=300,repeat:o=0,repeatType:a="loop",ease:s="easeInOut",times:l}={}){let u={[t]:r};l&&(u.offset=l);let c=function e(t,r){if(t)return"function"==typeof t&&E()?_(t,r):x(t)?w(t):Array.isArray(t)?t.map(t=>e(t,r)||S.easeOut):S[t]}(s,i);return Array.isArray(c)&&(u.easing=c),e.animate(u,{delay:n,duration:i,easing:Array.isArray(c)?"linear":c,fill:"both",iterations:o+1,direction:"reverse"===a?"alternate":"normal"})}(s.owner.current,l,e,{...this.options,duration:n,times:i,ease:o});return c.startTime=null!=u?u:this.calcStartTime(),this.pendingTimeline?(P(c,this.pendingTimeline),this.pendingTimeline=void 0):c.onfinish=()=>{let{onComplete:r}=this.options;s.set(tA(e,this.options,t)),r&&r(),this.cancel(),this.resolveFinishedPromise()},{animation:c,duration:n,times:i,type:a,ease:o,keyframes:e}}get duration(){let{resolved:e}=this;if(!e)return 0;let{duration:t}=e;return F(t)}get time(){let{resolved:e}=this;if(!e)return 0;let{animation:t}=e;return F(t.currentTime||0)}set time(e){let{resolved:t}=this;if(!t)return;let{animation:r}=t;r.currentTime=V(e)}get speed(){let{resolved:e}=this;if(!e)return 1;let{animation:t}=e;return t.playbackRate}set speed(e){let{resolved:t}=this;if(!t)return;let{animation:r}=t;r.playbackRate=e}get state(){let{resolved:e}=this;if(!e)return"idle";let{animation:t}=e;return t.playState}get startTime(){let{resolved:e}=this;if(!e)return null;let{animation:t}=e;return t.startTime}attachTimeline(e){if(this._resolved){let{resolved:t}=this;if(!t)return B;let{animation:r}=t;P(r,e)}else this.pendingTimeline=e;return B}play(){if(this.isStopped)return;let{resolved:e}=this;if(!e)return;let{animation:t}=e;"finished"===t.playState&&this.updateFinishedPromise(),t.play()}pause(){let{resolved:e}=this;if(!e)return;let{animation:t}=e;t.pause()}stop(){if(this.resolver.cancel(),this.isStopped=!0,"idle"===this.state)return;this.resolveFinishedPromise(),this.updateFinishedPromise();let{resolved:e}=this;if(!e)return;let{animation:t,keyframes:r,duration:n,type:i,ease:o,times:a}=e;if("idle"===t.playState||"finished"===t.playState)return;if(this.time){let{motionValue:e,onUpdate:t,onComplete:s,element:l,...u}=this.options,c=new rt({...u,keyframes:r,duration:n,type:i,ease:o,times:a,isGenerator:!0}),d=V(this.time);e.setWithVelocity(c.sample(d-10).value,c.sample(d).value,10)}let{onStop:s}=this.options;s&&s(),this.cancel()}complete(){let{resolved:e}=this;e&&e.animation.finish()}cancel(){let{resolved:e}=this;e&&e.animation.cancel()}static supports(e){let{motionValue:t,name:r,repeatDelay:n,repeatType:i,damping:o,type:a}=e;if(!t||!t.owner||!(t.owner.current instanceof HTMLElement))return!1;let{onUpdate:s,transformTemplate:l}=t.owner.getProps();return rn()&&r&&rr.has(r)&&!s&&!l&&!n&&"mirror"!==i&&0!==o&&"inertia"!==a}}let ra={type:"spring",stiffness:500,damping:25,restSpeed:10},rs=e=>({type:"spring",stiffness:550,damping:0===e?2*Math.sqrt(550):30,restSpeed:10}),rl={type:"keyframes",duration:.8},ru={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},rc=(e,{keyframes:t})=>t.length>2?rl:H.has(e)?e.startsWith("scale")?rs(t[1]):ra:ru,rd=(e,t,r,n={},i,o)=>a=>{let s=y(n,e)||{},l=s.delay||n.delay||0,{elapsed:u=0}=n;u-=V(l);let c={keyframes:Array.isArray(r)?r:[null,r],ease:"easeOut",velocity:t.getVelocity(),...s,delay:-u,onUpdate:e=>{t.set(e),s.onUpdate&&s.onUpdate(e)},onComplete:()=>{a(),s.onComplete&&s.onComplete()},name:e,motionValue:t,element:o?void 0:i};!function({when:e,delay:t,delayChildren:r,staggerChildren:n,staggerDirection:i,repeat:o,repeatType:a,repeatDelay:s,from:l,elapsed:u,...c}){return!!Object.keys(c).length}(s)&&(c={...c,...rc(e,c)}),c.duration&&(c.duration=V(c.duration)),c.repeatDelay&&(c.repeatDelay=V(c.repeatDelay)),void 0!==c.from&&(c.keyframes[0]=c.from);let d=!1;if(!1!==c.type&&(0!==c.duration||c.repeatDelay)||(c.duration=0,0!==c.delay||(d=!0)),(ef.current||q.skipAnimations)&&(d=!0,c.duration=0,c.delay=0),d&&!o&&void 0!==t.get()){let e=tA(c.keyframes,s);if(void 0!==e)return G.update(()=>{c.onUpdate(e),c.onComplete()}),new g([])}return!o&&ro.supports(c)?new ro(c):new rt(c)};function rh(e,t,{delay:r=0,transitionOverride:n,type:i}={}){var o;let{transition:a=e.getDefaultTransition(),transitionEnd:s,...l}=t;n&&(a=n);let u=[],d=i&&e.animationState&&e.animationState.getState()[i];for(let t in l){let n=e.getValue(t,null!==(o=e.latestValues[t])&&void 0!==o?o:null),i=l[t];if(void 0===i||d&&function({protectedKeys:e,needsAnimating:t},r){let n=e.hasOwnProperty(r)&&!0!==t[r];return t[r]=!1,n}(d,t))continue;let s={delay:r,...y(a||{},t)},c=!1;if(window.MotionHandoffAnimation){let r=e.props[eh];if(r){let e=window.MotionHandoffAnimation(r,t,G);null!==e&&(s.startTime=e,c=!0)}}ec(e,t),n.start(rd(t,n,i,e.shouldReduceMotion&&z.has(t)?{type:!1}:s,e,c));let h=n.animation;h&&u.push(h)}return s&&Promise.all(u).then(()=>{G.update(()=>{s&&function(e,t){let{transitionEnd:r={},transition:n={},...i}=c(e,t)||{};for(let t in i={...i,...r}){let r=K(i[t]);e.hasValue(t)?e.getValue(t).set(r):e.addValue(t,el(r))}}(e,s)})}),u}function rf(e,t,r={}){var n;let i=c(e,t,"exit"===r.type?null===(n=e.presenceContext)||void 0===n?void 0:n.custom:void 0),{transition:o=e.getDefaultTransition()||{}}=i||{};r.transitionOverride&&(o=r.transitionOverride);let a=i?()=>Promise.all(rh(e,i,r)):()=>Promise.resolve(),s=e.variantChildren&&e.variantChildren.size?(n=0)=>{let{delayChildren:i=0,staggerChildren:a,staggerDirection:s}=o;return function(e,t,r=0,n=0,i=1,o){let a=[],s=(e.variantChildren.size-1)*n,l=1===i?(e=0)=>e*n:(e=0)=>s-e*n;return Array.from(e.variantChildren).sort(rp).forEach((e,n)=>{e.notify("AnimationStart",t),a.push(rf(e,t,{...o,delay:r+l(n)}).then(()=>e.notify("AnimationComplete",t)))}),Promise.all(a)}(e,t,i+n,a,s,r)}:()=>Promise.resolve(),{when:l}=o;if(!l)return Promise.all([a(),s(r.delay)]);{let[e,t]="beforeChildren"===l?[a,s]:[s,a];return e().then(()=>t())}}function rp(e,t){return e.sortNodePosition(t)}let rm=h.length,rg=[...d].reverse(),ry=d.length;function rv(e=!1){return{isActive:e,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function rb(){return{animate:rv(!0),whileInView:rv(),whileHover:rv(),whileTap:rv(),whileDrag:rv(),whileFocus:rv(),exit:rv()}}class rP{constructor(e){this.isMounted=!1,this.node=e}update(){}}class rx extends rP{constructor(e){super(e),e.animationState||(e.animationState=function(e){let t=t=>Promise.all(t.map(({animation:t,options:r})=>(function(e,t,r={}){let n;if(e.notify("AnimationStart",t),Array.isArray(t))n=Promise.all(t.map(t=>rf(e,t,r)));else if("string"==typeof t)n=rf(e,t,r);else{let i="function"==typeof t?c(e,t,r.custom):t;n=Promise.all(rh(e,i,r))}return n.then(()=>{e.notify("AnimationComplete",t)})})(e,t,r))),r=rb(),n=!0,l=t=>(r,n)=>{var i;let o=c(e,n,"exit"===t?null===(i=e.presenceContext)||void 0===i?void 0:i.custom:void 0);if(o){let{transition:e,transitionEnd:t,...n}=o;r={...r,...n,...t}}return r};function u(u){let{props:c}=e,d=function e(t){if(!t)return;if(!t.isControllingVariants){let r=t.parent&&e(t.parent)||{};return void 0!==t.props.initial&&(r.initial=t.props.initial),r}let r={};for(let e=0;e<rm;e++){let n=h[e],i=t.props[n];(s(i)||!1===i)&&(r[n]=i)}return r}(e.parent)||{},f=[],p=new Set,m={},g=1/0;for(let t=0;t<ry;t++){var y,v;let h=rg[t],b=r[h],P=void 0!==c[h]?c[h]:d[h],x=s(P),R=h===u?b.isActive:null;!1===R&&(g=t);let E=P===d[h]&&P!==c[h]&&x;if(E&&n&&e.manuallyAnimateOnMount&&(E=!1),b.protectedKeys={...m},!b.isActive&&null===R||!P&&!b.prevProp||i(P)||"boolean"==typeof P)continue;let T=(y=b.prevProp,"string"==typeof(v=P)?v!==y:!!Array.isArray(v)&&!a(v,y)),_=T||h===u&&b.isActive&&!E&&x||t>g&&x,w=!1,S=Array.isArray(P)?P:[P],A=S.reduce(l(h),{});!1===R&&(A={});let{prevResolvedValues:M={}}=b,j={...M,...A},O=t=>{_=!0,p.has(t)&&(w=!0,p.delete(t)),b.needsAnimating[t]=!0;let r=e.getValue(t);r&&(r.liveStyle=!1)};for(let e in j){let t=A[e],r=M[e];if(m.hasOwnProperty(e))continue;let n=!1;(o(t)&&o(r)?a(t,r):t===r)?void 0!==t&&p.has(e)?O(e):b.protectedKeys[e]=!0:null!=t?O(e):p.add(e)}b.prevProp=P,b.prevResolvedValues=A,b.isActive&&(m={...m,...A}),n&&e.blockInitialAnimation&&(_=!1);let C=!(E&&T)||w;_&&C&&f.push(...S.map(e=>({animation:e,options:{type:h}})))}if(p.size){let t={};p.forEach(r=>{let n=e.getBaseTarget(r),i=e.getValue(r);i&&(i.liveStyle=!0),t[r]=null!=n?n:null}),f.push({animation:t})}let b=!!f.length;return n&&(!1===c.initial||c.initial===c.animate)&&!e.manuallyAnimateOnMount&&(b=!1),n=!1,b?t(f):Promise.resolve()}return{animateChanges:u,setActive:function(t,n){var i;if(r[t].isActive===n)return Promise.resolve();null===(i=e.variantChildren)||void 0===i||i.forEach(e=>{var r;return null===(r=e.animationState)||void 0===r?void 0:r.setActive(t,n)}),r[t].isActive=n;let o=u(t);for(let e in r)r[e].protectedKeys={};return o},setAnimateFunction:function(r){t=r(e)},getState:()=>r,reset:()=>{r=rb(),n=!0}}}(e))}updateAnimationControlsSubscription(){let{animate:e}=this.node.getProps();i(e)&&(this.unmountControls=e.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:e}=this.node.getProps(),{animate:t}=this.node.prevProps||{};e!==t&&this.updateAnimationControlsSubscription()}unmount(){var e;this.node.animationState.reset(),null===(e=this.unmountControls)||void 0===e||e.call(this)}}let rR=0;class rE extends rP{constructor(){super(...arguments),this.id=rR++}update(){if(!this.node.presenceContext)return;let{isPresent:e,onExitComplete:t}=this.node.presenceContext,{isPresent:r}=this.node.prevPresenceContext||{};if(!this.node.animationState||e===r)return;let n=this.node.animationState.setActive("exit",!e);t&&!e&&n.then(()=>t(this.id))}mount(){let{register:e}=this.node.presenceContext||{};e&&(this.unmount=e(this.id))}unmount(){}}function rT(e,t,r,n={passive:!0}){return e.addEventListener(t,r,n),()=>e.removeEventListener(t,r)}function r_(e){return{point:{x:e.pageX,y:e.pageY}}}let rw=e=>t=>C(t)&&e(t,r_(t));function rS(e,t,r,n){return rT(e,t,rw(r),n)}let rA=(e,t)=>Math.abs(e-t);class rM{constructor(e,t,{transformPagePoint:r,contextWindow:n,dragSnapToOrigin:i=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let e=rC(this.lastMoveEventInfo,this.history),t=null!==this.startEvent,r=function(e,t){return Math.sqrt(rA(e.x,t.x)**2+rA(e.y,t.y)**2)}(e.offset,{x:0,y:0})>=3;if(!t&&!r)return;let{point:n}=e,{timestamp:i}=Z;this.history.push({...n,timestamp:i});let{onStart:o,onMove:a}=this.handlers;t||(o&&o(this.lastMoveEvent,e),this.startEvent=this.lastMoveEvent),a&&a(this.lastMoveEvent,e)},this.handlePointerMove=(e,t)=>{this.lastMoveEvent=e,this.lastMoveEventInfo=rj(t,this.transformPagePoint),G.update(this.updatePoint,!0)},this.handlePointerUp=(e,t)=>{this.end();let{onEnd:r,onSessionEnd:n,resumeAnimation:i}=this.handlers;if(this.dragSnapToOrigin&&i&&i(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let o=rC("pointercancel"===e.type?this.lastMoveEventInfo:rj(t,this.transformPagePoint),this.history);this.startEvent&&r&&r(e,o),n&&n(e,o)},!C(e))return;this.dragSnapToOrigin=i,this.handlers=t,this.transformPagePoint=r,this.contextWindow=n||window;let o=rj(r_(e),this.transformPagePoint),{point:a}=o,{timestamp:s}=Z;this.history=[{...a,timestamp:s}];let{onSessionStart:l}=t;l&&l(e,rC(o,this.history)),this.removeListeners=tV(rS(this.contextWindow,"pointermove",this.handlePointerMove),rS(this.contextWindow,"pointerup",this.handlePointerUp),rS(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(e){this.handlers=e}end(){this.removeListeners&&this.removeListeners(),Q(this.updatePoint)}}function rj(e,t){return t?{point:t(e.point)}:e}function rO(e,t){return{x:e.x-t.x,y:e.y-t.y}}function rC({point:e},t){return{point:e,delta:rO(e,rD(t)),offset:rO(e,t[0]),velocity:function(e,t){if(e.length<2)return{x:0,y:0};let r=e.length-1,n=null,i=rD(e);for(;r>=0&&(n=e[r],!(i.timestamp-n.timestamp>V(.1)));)r--;if(!n)return{x:0,y:0};let o=F(i.timestamp-n.timestamp);if(0===o)return{x:0,y:0};let a={x:(i.x-n.x)/o,y:(i.y-n.y)/o};return a.x===1/0&&(a.x=0),a.y===1/0&&(a.y=0),a}(t,.1)}}function rD(e){return e[e.length-1]}function rk(e){return e&&"object"==typeof e&&Object.prototype.hasOwnProperty.call(e,"current")}function rL(e){return e.max-e.min}function rN(e,t,r,n=.5){e.origin=n,e.originPoint=tj(t.min,t.max,e.origin),e.scale=rL(r)/rL(t),e.translate=tj(r.min,r.max,e.origin)-e.originPoint,(e.scale>=.9999&&e.scale<=1.0001||isNaN(e.scale))&&(e.scale=1),(e.translate>=-.01&&e.translate<=.01||isNaN(e.translate))&&(e.translate=0)}function rU(e,t,r,n){rN(e.x,t.x,r.x,n?n.originX:void 0),rN(e.y,t.y,r.y,n?n.originY:void 0)}function rI(e,t,r){e.min=r.min+t.min,e.max=e.min+rL(t)}function rV(e,t,r){e.min=t.min-r.min,e.max=e.min+rL(t)}function rF(e,t,r){rV(e.x,t.x,r.x),rV(e.y,t.y,r.y)}function rB(e,t,r){return{min:void 0!==t?e.min+t:void 0,max:void 0!==r?e.max+r-(e.max-e.min):void 0}}function r$(e,t){let r=t.min-e.min,n=t.max-e.max;return t.max-t.min<e.max-e.min&&([r,n]=[n,r]),{min:r,max:n}}function rH(e,t,r){return{min:rz(e,t),max:rz(e,r)}}function rz(e,t){return"number"==typeof e?e:e[t]||0}let rW=()=>({translate:0,scale:1,origin:0,originPoint:0}),rK=()=>({x:rW(),y:rW()}),rq=()=>({min:0,max:0}),rX=()=>({x:rq(),y:rq()});function rY(e){return[e("x"),e("y")]}function rG({top:e,left:t,right:r,bottom:n}){return{x:{min:t,max:r},y:{min:e,max:n}}}function rQ(e){return void 0===e||1===e}function rZ({scale:e,scaleX:t,scaleY:r}){return!rQ(e)||!rQ(t)||!rQ(r)}function rJ(e){return rZ(e)||r0(e)||e.z||e.rotate||e.rotateX||e.rotateY||e.skewX||e.skewY}function r0(e){var t,r;return(t=e.x)&&"0%"!==t||(r=e.y)&&"0%"!==r}function r1(e,t,r,n,i){return void 0!==i&&(e=n+i*(e-n)),n+r*(e-n)+t}function r2(e,t=0,r=1,n,i){e.min=r1(e.min,t,r,n,i),e.max=r1(e.max,t,r,n,i)}function r3(e,{x:t,y:r}){r2(e.x,t.translate,t.scale,t.originPoint),r2(e.y,r.translate,r.scale,r.originPoint)}function r5(e,t){e.min=e.min+t,e.max=e.max+t}function r9(e,t,r,n,i=.5){let o=tj(e.min,e.max,i);r2(e,t,r,o,n)}function r4(e,t){r9(e.x,t.x,t.scaleX,t.scale,t.originX),r9(e.y,t.y,t.scaleY,t.scale,t.originY)}function r7(e,t){return rG(function(e,t){if(!t)return e;let r=t({x:e.left,y:e.top}),n=t({x:e.right,y:e.bottom});return{top:r.y,left:r.x,bottom:n.y,right:n.x}}(e.getBoundingClientRect(),t))}let r6=({current:e})=>e?e.ownerDocument.defaultView:null,r8=new WeakMap;class ne{constructor(e){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=rX(),this.visualElement=e}start(e,{snapToCursor:t=!1}={}){let{presenceContext:r}=this.visualElement;if(r&&!1===r.isPresent)return;let{dragSnapToOrigin:n}=this.getProps();this.panSession=new rM(e,{onSessionStart:e=>{let{dragSnapToOrigin:r}=this.getProps();r?this.pauseAnimation():this.stopAnimation(),t&&this.snapToCursor(r_(e).point)},onStart:(e,t)=>{var r;let{drag:n,dragPropagation:i,onDragStart:o}=this.getProps();if(n&&!i&&(this.openDragLock&&this.openDragLock(),this.openDragLock="x"===(r=n)||"y"===r?A[r]?null:(A[r]=!0,()=>{A[r]=!1}):A.x||A.y?null:(A.x=A.y=!0,()=>{A.x=A.y=!1}),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),rY(e=>{let t=this.getAxisMotionValue(e).get()||0;if(eB.test(t)){let{projection:r}=this.visualElement;if(r&&r.layout){let n=r.layout.layoutBox[e];n&&(t=rL(n)*(parseFloat(t)/100))}}this.originPoint[e]=t}),o&&G.postRender(()=>o(e,t)),ec(this.visualElement,"transform");let{animationState:a}=this.visualElement;a&&a.setActive("whileDrag",!0)},onMove:(e,t)=>{let{dragPropagation:r,dragDirectionLock:n,onDirectionLock:i,onDrag:o}=this.getProps();if(!r&&!this.openDragLock)return;let{offset:a}=t;if(n&&null===this.currentDirection){this.currentDirection=function(e,t=10){let r=null;return Math.abs(e.y)>t?r="y":Math.abs(e.x)>t&&(r="x"),r}(a),null!==this.currentDirection&&i&&i(this.currentDirection);return}this.updateAxis("x",t.point,a),this.updateAxis("y",t.point,a),this.visualElement.render(),o&&o(e,t)},onSessionEnd:(e,t)=>this.stop(e,t),resumeAnimation:()=>rY(e=>{var t;return"paused"===this.getAnimationState(e)&&(null===(t=this.getAxisMotionValue(e).animation)||void 0===t?void 0:t.play())})},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:n,contextWindow:r6(this.visualElement)})}stop(e,t){let r=this.isDragging;if(this.cancel(),!r)return;let{velocity:n}=t;this.startAnimation(n);let{onDragEnd:i}=this.getProps();i&&G.postRender(()=>i(e,t))}cancel(){this.isDragging=!1;let{projection:e,animationState:t}=this.visualElement;e&&(e.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:r}=this.getProps();!r&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),t&&t.setActive("whileDrag",!1)}updateAxis(e,t,r){let{drag:n}=this.getProps();if(!r||!nt(e,n,this.currentDirection))return;let i=this.getAxisMotionValue(e),o=this.originPoint[e]+r[e];this.constraints&&this.constraints[e]&&(o=function(e,{min:t,max:r},n){return void 0!==t&&e<t?e=n?tj(t,e,n.min):Math.max(e,t):void 0!==r&&e>r&&(e=n?tj(r,e,n.max):Math.min(e,r)),e}(o,this.constraints[e],this.elastic[e])),i.set(o)}resolveConstraints(){var e;let{dragConstraints:t,dragElastic:r}=this.getProps(),n=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):null===(e=this.visualElement.projection)||void 0===e?void 0:e.layout,i=this.constraints;t&&rk(t)?this.constraints||(this.constraints=this.resolveRefConstraints()):t&&n?this.constraints=function(e,{top:t,left:r,bottom:n,right:i}){return{x:rB(e.x,r,i),y:rB(e.y,t,n)}}(n.layoutBox,t):this.constraints=!1,this.elastic=function(e=.35){return!1===e?e=0:!0===e&&(e=.35),{x:rH(e,"left","right"),y:rH(e,"top","bottom")}}(r),i!==this.constraints&&n&&this.constraints&&!this.hasMutatedConstraints&&rY(e=>{!1!==this.constraints&&this.getAxisMotionValue(e)&&(this.constraints[e]=function(e,t){let r={};return void 0!==t.min&&(r.min=t.min-e.min),void 0!==t.max&&(r.max=t.max-e.min),r}(n.layoutBox[e],this.constraints[e]))})}resolveRefConstraints(){var e;let{dragConstraints:t,onMeasureDragConstraints:r}=this.getProps();if(!t||!rk(t))return!1;let n=t.current;B(null!==n,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.");let{projection:i}=this.visualElement;if(!i||!i.layout)return!1;let o=function(e,t,r){let n=r7(e,r),{scroll:i}=t;return i&&(r5(n.x,i.offset.x),r5(n.y,i.offset.y)),n}(n,i.root,this.visualElement.getTransformPagePoint()),a={x:r$((e=i.layout.layoutBox).x,o.x),y:r$(e.y,o.y)};if(r){let e=r(function({x:e,y:t}){return{top:t.min,right:e.max,bottom:t.max,left:e.min}}(a));this.hasMutatedConstraints=!!e,e&&(a=rG(e))}return a}startAnimation(e){let{drag:t,dragMomentum:r,dragElastic:n,dragTransition:i,dragSnapToOrigin:o,onDragTransitionEnd:a}=this.getProps(),s=this.constraints||{};return Promise.all(rY(a=>{if(!nt(a,t,this.currentDirection))return;let l=s&&s[a]||{};o&&(l={min:0,max:0});let u={type:"inertia",velocity:r?e[a]:0,bounceStiffness:n?200:1e6,bounceDamping:n?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...i,...l};return this.startAxisValueAnimation(a,u)})).then(a)}startAxisValueAnimation(e,t){let r=this.getAxisMotionValue(e);return ec(this.visualElement,e),r.start(rd(e,r,0,t,this.visualElement,!1))}stopAnimation(){rY(e=>this.getAxisMotionValue(e).stop())}pauseAnimation(){rY(e=>{var t;return null===(t=this.getAxisMotionValue(e).animation)||void 0===t?void 0:t.pause()})}getAnimationState(e){var t;return null===(t=this.getAxisMotionValue(e).animation)||void 0===t?void 0:t.state}getAxisMotionValue(e){let t=`_drag${e.toUpperCase()}`,r=this.visualElement.getProps();return r[t]||this.visualElement.getValue(e,(r.initial?r.initial[e]:void 0)||0)}snapToCursor(e){rY(t=>{let{drag:r}=this.getProps();if(!nt(t,r,this.currentDirection))return;let{projection:n}=this.visualElement,i=this.getAxisMotionValue(t);if(n&&n.layout){let{min:r,max:o}=n.layout.layoutBox[t];i.set(e[t]-tj(r,o,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:e,dragConstraints:t}=this.getProps(),{projection:r}=this.visualElement;if(!rk(t)||!r||!this.constraints)return;this.stopAnimation();let n={x:0,y:0};rY(e=>{let t=this.getAxisMotionValue(e);if(t&&!1!==this.constraints){let r=t.get();n[e]=function(e,t){let r=.5,n=rL(e),i=rL(t);return i>n?r=T(t.min,t.max-n,e.min):n>i&&(r=T(e.min,e.max-i,t.min)),ew(0,1,r)}({min:r,max:r},this.constraints[e])}});let{transformTemplate:i}=this.visualElement.getProps();this.visualElement.current.style.transform=i?i({},""):"none",r.root&&r.root.updateScroll(),r.updateLayout(),this.resolveConstraints(),rY(t=>{if(!nt(t,e,null))return;let r=this.getAxisMotionValue(t),{min:i,max:o}=this.constraints[t];r.set(tj(i,o,n[t]))})}addListeners(){if(!this.visualElement.current)return;r8.set(this.visualElement,this);let e=rS(this.visualElement.current,"pointerdown",e=>{let{drag:t,dragListener:r=!0}=this.getProps();t&&r&&this.start(e)}),t=()=>{let{dragConstraints:e}=this.getProps();rk(e)&&e.current&&(this.constraints=this.resolveRefConstraints())},{projection:r}=this.visualElement,n=r.addEventListener("measure",t);r&&!r.layout&&(r.root&&r.root.updateScroll(),r.updateLayout()),G.read(t);let i=rT(window,"resize",()=>this.scalePositionWithinConstraints()),o=r.addEventListener("didUpdate",({delta:e,hasLayoutChanged:t})=>{this.isDragging&&t&&(rY(t=>{let r=this.getAxisMotionValue(t);r&&(this.originPoint[t]+=e[t].translate,r.set(r.get()+e[t].translate))}),this.visualElement.render())});return()=>{i(),e(),n(),o&&o()}}getProps(){let e=this.visualElement.getProps(),{drag:t=!1,dragDirectionLock:r=!1,dragPropagation:n=!1,dragConstraints:i=!1,dragElastic:o=.35,dragMomentum:a=!0}=e;return{...e,drag:t,dragDirectionLock:r,dragPropagation:n,dragConstraints:i,dragElastic:o,dragMomentum:a}}}function nt(e,t,r){return(!0===t||t===e)&&(null===r||r===e)}class nr extends rP{constructor(e){super(e),this.removeGroupControls=B,this.removeListeners=B,this.controls=new ne(e)}mount(){let{dragControls:e}=this.node.getProps();e&&(this.removeGroupControls=e.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||B}unmount(){this.removeGroupControls(),this.removeListeners()}}let nn=e=>(t,r)=>{e&&G.postRender(()=>e(t,r))};class ni extends rP{constructor(){super(...arguments),this.removePointerDownListener=B}onPointerDown(e){this.session=new rM(e,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:r6(this.node)})}createPanHandlers(){let{onPanSessionStart:e,onPanStart:t,onPan:r,onPanEnd:n}=this.node.getProps();return{onSessionStart:nn(e),onStart:nn(t),onMove:r,onEnd:(e,t)=>{delete this.session,n&&G.postRender(()=>n(e,t))}}}mount(){this.removePointerDownListener=rS(this.node.current,"pointerdown",e=>this.onPointerDown(e))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}var no,na,ns=r(60687),nl=r(43210),nu=r(86044),nc=r(12157);let nd=(0,nl.createContext)({}),nh={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function nf(e,t){return t.max===t.min?0:e/(t.max-t.min)*100}let np={correct:(e,t)=>{if(!t.target)return e;if("string"==typeof e){if(!e$.test(e))return e;e=parseFloat(e)}let r=nf(e,t.target.x),n=nf(e,t.target.y);return`${r}% ${n}%`}},nm={},{schedule:ng,cancel:ny}=Y(queueMicrotask,!1);class nv extends nl.Component{componentDidMount(){let{visualElement:e,layoutGroup:t,switchLayoutGroup:r,layoutId:n}=this.props,{projection:i}=e;Object.assign(nm,nP),i&&(t.group&&t.group.add(i),r&&r.register&&n&&r.register(i),i.root.didUpdate(),i.addEventListener("animationComplete",()=>{this.safeToRemove()}),i.setOptions({...i.options,onExitComplete:()=>this.safeToRemove()})),nh.hasEverUpdated=!0}getSnapshotBeforeUpdate(e){let{layoutDependency:t,visualElement:r,drag:n,isPresent:i}=this.props,o=r.projection;return o&&(o.isPresent=i,n||e.layoutDependency!==t||void 0===t?o.willUpdate():this.safeToRemove(),e.isPresent===i||(i?o.promote():o.relegate()||G.postRender(()=>{let e=o.getStack();e&&e.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:e}=this.props.visualElement;e&&(e.root.didUpdate(),ng.postRender(()=>{!e.currentAnimation&&e.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:e,layoutGroup:t,switchLayoutGroup:r}=this.props,{projection:n}=e;n&&(n.scheduleCheckAfterUnmount(),t&&t.group&&t.group.remove(n),r&&r.deregister&&r.deregister(n))}safeToRemove(){let{safeToRemove:e}=this.props;e&&e()}render(){return null}}function nb(e){let[t,r]=(0,nu.xQ)(),n=(0,nl.useContext)(nc.L);return(0,ns.jsx)(nv,{...e,layoutGroup:n,switchLayoutGroup:(0,nl.useContext)(nd),isPresent:t,safeToRemove:r})}let nP={borderRadius:{...np,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:np,borderTopRightRadius:np,borderBottomLeftRadius:np,borderBottomRightRadius:np,boxShadow:{correct:(e,{treeScale:t,projectionDelta:r})=>{let n=e2.parse(e);if(n.length>5)return e;let i=e2.createTransformer(e),o=+("number"!=typeof n[0]),a=r.x.scale*t.x,s=r.y.scale*t.y;n[0+o]/=a,n[1+o]/=s;let l=tj(a,s,.5);return"number"==typeof n[2+o]&&(n[2+o]/=l),"number"==typeof n[3+o]&&(n[3+o]/=l),i(n)}}},nx=(e,t)=>e.depth-t.depth;class nR{constructor(){this.children=[],this.isDirty=!1}add(e){er(this.children,e),this.isDirty=!0}remove(e){en(this.children,e),this.isDirty=!0}forEach(e){this.isDirty&&this.children.sort(nx),this.isDirty=!1,this.children.forEach(e)}}function nE(e){let t=eu(e)?e.get():e;return W(t)?t.toValue():t}let nT=["TopLeft","TopRight","BottomLeft","BottomRight"],n_=nT.length,nw=e=>"string"==typeof e?parseFloat(e):e,nS=e=>"number"==typeof e||e$.test(e);function nA(e,t){return void 0!==e[t]?e[t]:e.borderRadius}let nM=nO(0,.5,eE),nj=nO(.5,.95,B);function nO(e,t,r){return n=>n<e?0:n>t?1:r(T(e,t,n))}function nC(e,t){e.min=t.min,e.max=t.max}function nD(e,t){nC(e.x,t.x),nC(e.y,t.y)}function nk(e,t){e.translate=t.translate,e.scale=t.scale,e.originPoint=t.originPoint,e.origin=t.origin}function nL(e,t,r,n,i){return e-=t,e=n+1/r*(e-n),void 0!==i&&(e=n+1/i*(e-n)),e}function nN(e,t,[r,n,i],o,a){!function(e,t=0,r=1,n=.5,i,o=e,a=e){if(eB.test(t)&&(t=parseFloat(t),t=tj(a.min,a.max,t/100)-a.min),"number"!=typeof t)return;let s=tj(o.min,o.max,n);e===o&&(s-=t),e.min=nL(e.min,t,r,s,i),e.max=nL(e.max,t,r,s,i)}(e,t[r],t[n],t[i],t.scale,o,a)}let nU=["x","scaleX","originX"],nI=["y","scaleY","originY"];function nV(e,t,r,n){nN(e.x,t,nU,r?r.x:void 0,n?n.x:void 0),nN(e.y,t,nI,r?r.y:void 0,n?n.y:void 0)}function nF(e){return 0===e.translate&&1===e.scale}function nB(e){return nF(e.x)&&nF(e.y)}function n$(e,t){return e.min===t.min&&e.max===t.max}function nH(e,t){return Math.round(e.min)===Math.round(t.min)&&Math.round(e.max)===Math.round(t.max)}function nz(e,t){return nH(e.x,t.x)&&nH(e.y,t.y)}function nW(e){return rL(e.x)/rL(e.y)}function nK(e,t){return e.translate===t.translate&&e.scale===t.scale&&e.originPoint===t.originPoint}class nq{constructor(){this.members=[]}add(e){er(this.members,e),e.scheduleRender()}remove(e){if(en(this.members,e),e===this.prevLead&&(this.prevLead=void 0),e===this.lead){let e=this.members[this.members.length-1];e&&this.promote(e)}}relegate(e){let t;let r=this.members.findIndex(t=>e===t);if(0===r)return!1;for(let e=r;e>=0;e--){let r=this.members[e];if(!1!==r.isPresent){t=r;break}}return!!t&&(this.promote(t),!0)}promote(e,t){let r=this.lead;if(e!==r&&(this.prevLead=r,this.lead=e,e.show(),r)){r.instance&&r.scheduleRender(),e.scheduleRender(),e.resumeFrom=r,t&&(e.resumeFrom.preserveOpacity=!0),r.snapshot&&(e.snapshot=r.snapshot,e.snapshot.latestValues=r.animationValues||r.latestValues),e.root&&e.root.isUpdating&&(e.isLayoutDirty=!0);let{crossfade:n}=e.options;!1===n&&r.hide()}}exitAnimationComplete(){this.members.forEach(e=>{let{options:t,resumingFrom:r}=e;t.onExitComplete&&t.onExitComplete(),r&&r.options.onExitComplete&&r.options.onExitComplete()})}scheduleRender(){this.members.forEach(e=>{e.instance&&e.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}let nX={type:"projectionFrame",totalNodes:0,resolvedTargetDeltas:0,recalculatedProjection:0},nY="undefined"!=typeof window&&void 0!==window.MotionDebug,nG=["","X","Y","Z"],nQ={visibility:"hidden"},nZ=0;function nJ(e,t,r,n){let{latestValues:i}=t;i[e]&&(r[e]=i[e],t.setStaticValue(e,0),n&&(n[e]=0))}function n0({attachResizeListener:e,defaultParent:t,measureScroll:r,checkIsScrollRoot:n,resetTransform:i}){return class{constructor(e={},r=null==t?void 0:t()){this.id=nZ++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,nY&&(nX.totalNodes=nX.resolvedTargetDeltas=nX.recalculatedProjection=0),this.nodes.forEach(n3),this.nodes.forEach(ie),this.nodes.forEach(it),this.nodes.forEach(n5),nY&&window.MotionDebug.record(nX)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=e,this.root=r?r.root||r:this,this.path=r?[...r.path,r]:[],this.parent=r,this.depth=r?r.depth+1:0;for(let e=0;e<this.path.length;e++)this.path[e].shouldResetTransform=!0;this.root===this&&(this.nodes=new nR)}addEventListener(e,t){return this.eventHandlers.has(e)||this.eventHandlers.set(e,new ei),this.eventHandlers.get(e).add(t)}notifyListeners(e,...t){let r=this.eventHandlers.get(e);r&&r.notify(...t)}hasListeners(e){return this.eventHandlers.has(e)}mount(t,r=this.root.hasTreeAnimated){if(this.instance)return;this.isSVG=t instanceof SVGElement&&"svg"!==t.tagName,this.instance=t;let{layoutId:n,layout:i,visualElement:o}=this.options;if(o&&!o.current&&o.mount(t),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),r&&(i||n)&&(this.isLayoutDirty=!0),e){let r;let n=()=>this.root.updateBlockedByResize=!1;e(t,()=>{this.root.updateBlockedByResize=!0,r&&r(),r=function(e,t){let r=et.now(),n=({timestamp:i})=>{let o=i-r;o>=250&&(Q(n),e(o-t))};return G.read(n,!0),()=>Q(n)}(n,250),nh.hasAnimatedSinceResize&&(nh.hasAnimatedSinceResize=!1,this.nodes.forEach(n8))})}n&&this.root.registerSharedNode(n,this),!1!==this.options.animate&&o&&(n||i)&&this.addEventListener("didUpdate",({delta:e,hasLayoutChanged:t,hasRelativeTargetChanged:r,layout:n})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let i=this.options.transition||o.getDefaultTransition()||il,{onLayoutAnimationStart:a,onLayoutAnimationComplete:s}=o.getProps(),l=!this.targetLayout||!nz(this.targetLayout,n)||r,u=!t&&r;if(this.options.layoutRoot||this.resumeFrom&&this.resumeFrom.instance||u||t&&(l||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(e,u);let t={...y(i,"layout"),onPlay:a,onComplete:s};(o.shouldReduceMotion||this.options.layoutRoot)&&(t.delay=0,t.type=!1),this.startAnimation(t)}else t||n8(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=n})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let e=this.getStack();e&&e.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,Q(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(ir),this.animationId++)}getTransformTemplate(){let{visualElement:e}=this.options;return e&&e.getProps().transformTemplate}willUpdate(e=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&function e(t){if(t.hasCheckedOptimisedAppear=!0,t.root===t)return;let{visualElement:r}=t.options;if(!r)return;let n=r.props[eh];if(window.MotionHasOptimisedAnimation(n,"transform")){let{layout:e,layoutId:r}=t.options;window.MotionCancelOptimisedAnimation(n,"transform",G,!(e||r))}let{parent:i}=t;i&&!i.hasCheckedOptimisedAppear&&e(i)}(this),this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let e=0;e<this.path.length;e++){let t=this.path[e];t.shouldResetTransform=!0,t.updateScroll("snapshot"),t.options.layoutRoot&&t.willUpdate(!1)}let{layoutId:t,layout:r}=this.options;if(void 0===t&&!r)return;let n=this.getTransformTemplate();this.prevTransformTemplateValue=n?n(this.latestValues,""):void 0,this.updateSnapshot(),e&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(n4);return}this.isUpdating||this.nodes.forEach(n7),this.isUpdating=!1,this.nodes.forEach(n6),this.nodes.forEach(n1),this.nodes.forEach(n2),this.clearAllSnapshots();let e=et.now();Z.delta=ew(0,1e3/60,e-Z.timestamp),Z.timestamp=e,Z.isProcessing=!0,J.update.process(Z),J.preRender.process(Z),J.render.process(Z),Z.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,ng.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(n9),this.sharedNodes.forEach(ii)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,G.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){G.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure())}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let e=0;e<this.path.length;e++)this.path[e].updateScroll();let e=this.layout;this.layout=this.measure(!1),this.layoutCorrected=rX(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:t}=this.options;t&&t.notify("LayoutMeasure",this.layout.layoutBox,e?e.layoutBox:void 0)}updateScroll(e="measure"){let t=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===e&&(t=!1),t){let t=n(this.instance);this.scroll={animationId:this.root.animationId,phase:e,isRoot:t,offset:r(this.instance),wasRoot:this.scroll?this.scroll.isRoot:t}}}resetTransform(){if(!i)return;let e=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,t=this.projectionDelta&&!nB(this.projectionDelta),r=this.getTransformTemplate(),n=r?r(this.latestValues,""):void 0,o=n!==this.prevTransformTemplateValue;e&&(t||rJ(this.latestValues)||o)&&(i(this.instance,n),this.shouldResetTransform=!1,this.scheduleRender())}measure(e=!0){var t;let r=this.measurePageBox(),n=this.removeElementScroll(r);return e&&(n=this.removeTransform(n)),id((t=n).x),id(t.y),{animationId:this.root.animationId,measuredBox:r,layoutBox:n,latestValues:{},source:this.id}}measurePageBox(){var e;let{visualElement:t}=this.options;if(!t)return rX();let r=t.measureViewportBox();if(!((null===(e=this.scroll)||void 0===e?void 0:e.wasRoot)||this.path.some(ip))){let{scroll:e}=this.root;e&&(r5(r.x,e.offset.x),r5(r.y,e.offset.y))}return r}removeElementScroll(e){var t;let r=rX();if(nD(r,e),null===(t=this.scroll)||void 0===t?void 0:t.wasRoot)return r;for(let t=0;t<this.path.length;t++){let n=this.path[t],{scroll:i,options:o}=n;n!==this.root&&i&&o.layoutScroll&&(i.wasRoot&&nD(r,e),r5(r.x,i.offset.x),r5(r.y,i.offset.y))}return r}applyTransform(e,t=!1){let r=rX();nD(r,e);for(let e=0;e<this.path.length;e++){let n=this.path[e];!t&&n.options.layoutScroll&&n.scroll&&n!==n.root&&r4(r,{x:-n.scroll.offset.x,y:-n.scroll.offset.y}),rJ(n.latestValues)&&r4(r,n.latestValues)}return rJ(this.latestValues)&&r4(r,this.latestValues),r}removeTransform(e){let t=rX();nD(t,e);for(let e=0;e<this.path.length;e++){let r=this.path[e];if(!r.instance||!rJ(r.latestValues))continue;rZ(r.latestValues)&&r.updateSnapshot();let n=rX();nD(n,r.measurePageBox()),nV(t,r.latestValues,r.snapshot?r.snapshot.layoutBox:void 0,n)}return rJ(this.latestValues)&&nV(t,this.latestValues),t}setTargetDelta(e){this.targetDelta=e,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(e){this.options={...this.options,...e,crossfade:void 0===e.crossfade||e.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==Z.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(e=!1){var t,r,n,i;let o=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=o.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=o.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=o.isSharedProjectionDirty);let a=!!this.resumingFrom||this!==o;if(!(e||a&&this.isSharedProjectionDirty||this.isProjectionDirty||(null===(t=this.parent)||void 0===t?void 0:t.isProjectionDirty)||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;let{layout:s,layoutId:l}=this.options;if(this.layout&&(s||l)){if(this.resolvedRelativeTargetAt=Z.timestamp,!this.targetDelta&&!this.relativeTarget){let e=this.getClosestProjectingParent();e&&e.layout&&1!==this.animationProgress?(this.relativeParent=e,this.forceRelativeParentToResolveTarget(),this.relativeTarget=rX(),this.relativeTargetOrigin=rX(),rF(this.relativeTargetOrigin,this.layout.layoutBox,e.layout.layoutBox),nD(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if((this.target||(this.target=rX(),this.targetWithTransforms=rX()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target)?(this.forceRelativeParentToResolveTarget(),r=this.target,n=this.relativeTarget,i=this.relativeParent.target,rI(r.x,n.x,i.x),rI(r.y,n.y,i.y)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):nD(this.target,this.layout.layoutBox),r3(this.target,this.targetDelta)):nD(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let e=this.getClosestProjectingParent();e&&!!e.resumingFrom==!!this.resumingFrom&&!e.options.layoutScroll&&e.target&&1!==this.animationProgress?(this.relativeParent=e,this.forceRelativeParentToResolveTarget(),this.relativeTarget=rX(),this.relativeTargetOrigin=rX(),rF(this.relativeTargetOrigin,this.target,e.target),nD(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}nY&&nX.resolvedTargetDeltas++}}}getClosestProjectingParent(){return!this.parent||rZ(this.parent.latestValues)||r0(this.parent.latestValues)?void 0:this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var e;let t=this.getLead(),r=!!this.resumingFrom||this!==t,n=!0;if((this.isProjectionDirty||(null===(e=this.parent)||void 0===e?void 0:e.isProjectionDirty))&&(n=!1),r&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(n=!1),this.resolvedRelativeTargetAt===Z.timestamp&&(n=!1),n)return;let{layout:i,layoutId:o}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(i||o))return;nD(this.layoutCorrected,this.layout.layoutBox);let a=this.treeScale.x,s=this.treeScale.y;(function(e,t,r,n=!1){let i,o;let a=r.length;if(a){t.x=t.y=1;for(let s=0;s<a;s++){o=(i=r[s]).projectionDelta;let{visualElement:a}=i.options;(!a||!a.props.style||"contents"!==a.props.style.display)&&(n&&i.options.layoutScroll&&i.scroll&&i!==i.root&&r4(e,{x:-i.scroll.offset.x,y:-i.scroll.offset.y}),o&&(t.x*=o.x.scale,t.y*=o.y.scale,r3(e,o)),n&&rJ(i.latestValues)&&r4(e,i.latestValues))}t.x<1.0000000000001&&t.x>.999999999999&&(t.x=1),t.y<1.0000000000001&&t.y>.999999999999&&(t.y=1)}})(this.layoutCorrected,this.treeScale,this.path,r),t.layout&&!t.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(t.target=t.layout.layoutBox,t.targetWithTransforms=rX());let{target:l}=t;if(!l){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}this.projectionDelta&&this.prevProjectionDelta?(nk(this.prevProjectionDelta.x,this.projectionDelta.x),nk(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),rU(this.projectionDelta,this.layoutCorrected,l,this.latestValues),this.treeScale.x===a&&this.treeScale.y===s&&nK(this.projectionDelta.x,this.prevProjectionDelta.x)&&nK(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",l)),nY&&nX.recalculatedProjection++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(e=!0){var t;if(null===(t=this.options.visualElement)||void 0===t||t.scheduleRender(),e){let e=this.getStack();e&&e.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=rK(),this.projectionDelta=rK(),this.projectionDeltaWithTransform=rK()}setAnimationOrigin(e,t=!1){let r;let n=this.snapshot,i=n?n.latestValues:{},o={...this.latestValues},a=rK();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!t;let s=rX(),l=(n?n.source:void 0)!==(this.layout?this.layout.source:void 0),u=this.getStack(),c=!u||u.members.length<=1,d=!!(l&&!c&&!0===this.options.crossfade&&!this.path.some(is));this.animationProgress=0,this.mixTargetDelta=t=>{let n=t/1e3;if(io(a.x,e.x,n),io(a.y,e.y,n),this.setTargetDelta(a),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var u,h,f,p,m,g;if(rF(s,this.layout.layoutBox,this.relativeParent.layout.layoutBox),f=this.relativeTarget,p=this.relativeTargetOrigin,m=s,g=n,ia(f.x,p.x,m.x,g),ia(f.y,p.y,m.y,g),r&&(u=this.relativeTarget,h=r,n$(u.x,h.x)&&n$(u.y,h.y)))this.isProjectionDirty=!1;r||(r=rX()),nD(r,this.relativeTarget)}l&&(this.animationValues=o,function(e,t,r,n,i,o){i?(e.opacity=tj(0,void 0!==r.opacity?r.opacity:1,nM(n)),e.opacityExit=tj(void 0!==t.opacity?t.opacity:1,0,nj(n))):o&&(e.opacity=tj(void 0!==t.opacity?t.opacity:1,void 0!==r.opacity?r.opacity:1,n));for(let i=0;i<n_;i++){let o=`border${nT[i]}Radius`,a=nA(t,o),s=nA(r,o);(void 0!==a||void 0!==s)&&(a||(a=0),s||(s=0),0===a||0===s||nS(a)===nS(s)?(e[o]=Math.max(tj(nw(a),nw(s),n),0),(eB.test(s)||eB.test(a))&&(e[o]+="%")):e[o]=s)}(t.rotate||r.rotate)&&(e.rotate=tj(t.rotate||0,r.rotate||0,n))}(o,i,this.latestValues,n,d,c)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=n},this.mixTargetDelta(1e3*!!this.options.layoutRoot)}startAnimation(e){this.notifyListeners("animationStart"),this.currentAnimation&&this.currentAnimation.stop(),this.resumingFrom&&this.resumingFrom.currentAnimation&&this.resumingFrom.currentAnimation.stop(),this.pendingAnimation&&(Q(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=G.update(()=>{nh.hasAnimatedSinceResize=!0,this.currentAnimation=function(e,t,r){let n=eu(0)?0:el(e);return n.start(rd("",n,1e3,r)),n.animation}(0,0,{...e,onUpdate:t=>{this.mixTargetDelta(t),e.onUpdate&&e.onUpdate(t)},onComplete:()=>{e.onComplete&&e.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let e=this.getStack();e&&e.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let e=this.getLead(),{targetWithTransforms:t,target:r,layout:n,latestValues:i}=e;if(t&&r&&n){if(this!==e&&this.layout&&n&&ih(this.options.animationType,this.layout.layoutBox,n.layoutBox)){r=this.target||rX();let t=rL(this.layout.layoutBox.x);r.x.min=e.target.x.min,r.x.max=r.x.min+t;let n=rL(this.layout.layoutBox.y);r.y.min=e.target.y.min,r.y.max=r.y.min+n}nD(t,r),r4(t,i),rU(this.projectionDeltaWithTransform,this.layoutCorrected,t,i)}}registerSharedNode(e,t){this.sharedNodes.has(e)||this.sharedNodes.set(e,new nq),this.sharedNodes.get(e).add(t);let r=t.options.initialPromotionConfig;t.promote({transition:r?r.transition:void 0,preserveFollowOpacity:r&&r.shouldPreserveFollowOpacity?r.shouldPreserveFollowOpacity(t):void 0})}isLead(){let e=this.getStack();return!e||e.lead===this}getLead(){var e;let{layoutId:t}=this.options;return t&&(null===(e=this.getStack())||void 0===e?void 0:e.lead)||this}getPrevLead(){var e;let{layoutId:t}=this.options;return t?null===(e=this.getStack())||void 0===e?void 0:e.prevLead:void 0}getStack(){let{layoutId:e}=this.options;if(e)return this.root.sharedNodes.get(e)}promote({needsReset:e,transition:t,preserveFollowOpacity:r}={}){let n=this.getStack();n&&n.promote(this,r),e&&(this.projectionDelta=void 0,this.needsReset=!0),t&&this.setOptions({transition:t})}relegate(){let e=this.getStack();return!!e&&e.relegate(this)}resetSkewAndRotation(){let{visualElement:e}=this.options;if(!e)return;let t=!1,{latestValues:r}=e;if((r.z||r.rotate||r.rotateX||r.rotateY||r.rotateZ||r.skewX||r.skewY)&&(t=!0),!t)return;let n={};r.z&&nJ("z",e,n,this.animationValues);for(let t=0;t<nG.length;t++)nJ(`rotate${nG[t]}`,e,n,this.animationValues),nJ(`skew${nG[t]}`,e,n,this.animationValues);for(let t in e.render(),n)e.setStaticValue(t,n[t]),this.animationValues&&(this.animationValues[t]=n[t]);e.scheduleRender()}getProjectionStyles(e){var t,r;if(!this.instance||this.isSVG)return;if(!this.isVisible)return nQ;let n={visibility:""},i=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,n.opacity="",n.pointerEvents=nE(null==e?void 0:e.pointerEvents)||"",n.transform=i?i(this.latestValues,""):"none",n;let o=this.getLead();if(!this.projectionDelta||!this.layout||!o.target){let t={};return this.options.layoutId&&(t.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,t.pointerEvents=nE(null==e?void 0:e.pointerEvents)||""),this.hasProjected&&!rJ(this.latestValues)&&(t.transform=i?i({},""):"none",this.hasProjected=!1),t}let a=o.animationValues||o.latestValues;this.applyTransformsToTarget(),n.transform=function(e,t,r){let n="",i=e.x.translate/t.x,o=e.y.translate/t.y,a=(null==r?void 0:r.z)||0;if((i||o||a)&&(n=`translate3d(${i}px, ${o}px, ${a}px) `),(1!==t.x||1!==t.y)&&(n+=`scale(${1/t.x}, ${1/t.y}) `),r){let{transformPerspective:e,rotate:t,rotateX:i,rotateY:o,skewX:a,skewY:s}=r;e&&(n=`perspective(${e}px) ${n}`),t&&(n+=`rotate(${t}deg) `),i&&(n+=`rotateX(${i}deg) `),o&&(n+=`rotateY(${o}deg) `),a&&(n+=`skewX(${a}deg) `),s&&(n+=`skewY(${s}deg) `)}let s=e.x.scale*t.x,l=e.y.scale*t.y;return(1!==s||1!==l)&&(n+=`scale(${s}, ${l})`),n||"none"}(this.projectionDeltaWithTransform,this.treeScale,a),i&&(n.transform=i(a,n.transform));let{x:s,y:l}=this.projectionDelta;for(let e in n.transformOrigin=`${100*s.origin}% ${100*l.origin}% 0`,o.animationValues?n.opacity=o===this?null!==(r=null!==(t=a.opacity)&&void 0!==t?t:this.latestValues.opacity)&&void 0!==r?r:1:this.preserveOpacity?this.latestValues.opacity:a.opacityExit:n.opacity=o===this?void 0!==a.opacity?a.opacity:"":void 0!==a.opacityExit?a.opacityExit:0,nm){if(void 0===a[e])continue;let{correct:t,applyTo:r}=nm[e],i="none"===n.transform?a[e]:t(a[e],o);if(r){let e=r.length;for(let t=0;t<e;t++)n[r[t]]=i}else n[e]=i}return this.options.layoutId&&(n.pointerEvents=o===this?nE(null==e?void 0:e.pointerEvents)||"":"none"),n}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(e=>{var t;return null===(t=e.currentAnimation)||void 0===t?void 0:t.stop()}),this.root.nodes.forEach(n4),this.root.sharedNodes.clear()}}}function n1(e){e.updateLayout()}function n2(e){var t;let r=(null===(t=e.resumeFrom)||void 0===t?void 0:t.snapshot)||e.snapshot;if(e.isLead()&&e.layout&&r&&e.hasListeners("didUpdate")){let{layoutBox:t,measuredBox:n}=e.layout,{animationType:i}=e.options,o=r.source!==e.layout.source;"size"===i?rY(e=>{let n=o?r.measuredBox[e]:r.layoutBox[e],i=rL(n);n.min=t[e].min,n.max=n.min+i}):ih(i,r.layoutBox,t)&&rY(n=>{let i=o?r.measuredBox[n]:r.layoutBox[n],a=rL(t[n]);i.max=i.min+a,e.relativeTarget&&!e.currentAnimation&&(e.isProjectionDirty=!0,e.relativeTarget[n].max=e.relativeTarget[n].min+a)});let a=rK();rU(a,t,r.layoutBox);let s=rK();o?rU(s,e.applyTransform(n,!0),r.measuredBox):rU(s,t,r.layoutBox);let l=!nB(a),u=!1;if(!e.resumeFrom){let n=e.getClosestProjectingParent();if(n&&!n.resumeFrom){let{snapshot:i,layout:o}=n;if(i&&o){let a=rX();rF(a,r.layoutBox,i.layoutBox);let s=rX();rF(s,t,o.layoutBox),nz(a,s)||(u=!0),n.options.layoutRoot&&(e.relativeTarget=s,e.relativeTargetOrigin=a,e.relativeParent=n)}}}e.notifyListeners("didUpdate",{layout:t,snapshot:r,delta:s,layoutDelta:a,hasLayoutChanged:l,hasRelativeTargetChanged:u})}else if(e.isLead()){let{onExitComplete:t}=e.options;t&&t()}e.options.transition=void 0}function n3(e){nY&&nX.totalNodes++,e.parent&&(e.isProjecting()||(e.isProjectionDirty=e.parent.isProjectionDirty),e.isSharedProjectionDirty||(e.isSharedProjectionDirty=!!(e.isProjectionDirty||e.parent.isProjectionDirty||e.parent.isSharedProjectionDirty)),e.isTransformDirty||(e.isTransformDirty=e.parent.isTransformDirty))}function n5(e){e.isProjectionDirty=e.isSharedProjectionDirty=e.isTransformDirty=!1}function n9(e){e.clearSnapshot()}function n4(e){e.clearMeasurements()}function n7(e){e.isLayoutDirty=!1}function n6(e){let{visualElement:t}=e.options;t&&t.getProps().onBeforeLayoutMeasure&&t.notify("BeforeLayoutMeasure"),e.resetTransform()}function n8(e){e.finishAnimation(),e.targetDelta=e.relativeTarget=e.target=void 0,e.isProjectionDirty=!0}function ie(e){e.resolveTargetDelta()}function it(e){e.calcProjection()}function ir(e){e.resetSkewAndRotation()}function ii(e){e.removeLeadSnapshot()}function io(e,t,r){e.translate=tj(t.translate,0,r),e.scale=tj(t.scale,1,r),e.origin=t.origin,e.originPoint=t.originPoint}function ia(e,t,r,n){e.min=tj(t.min,r.min,n),e.max=tj(t.max,r.max,n)}function is(e){return e.animationValues&&void 0!==e.animationValues.opacityExit}let il={duration:.45,ease:[.4,0,.1,1]},iu=e=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(e),ic=iu("applewebkit/")&&!iu("chrome/")?Math.round:B;function id(e){e.min=ic(e.min),e.max=ic(e.max)}function ih(e,t,r){return"position"===e||"preserve-aspect"===e&&!(.2>=Math.abs(nW(t)-nW(r)))}function ip(e){var t;return e!==e.root&&(null===(t=e.scroll)||void 0===t?void 0:t.wasRoot)}let im=n0({attachResizeListener:(e,t)=>rT(e,"resize",t),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),ig={current:void 0},iy=n0({measureScroll:e=>({x:e.scrollLeft,y:e.scrollTop}),defaultParent:()=>{if(!ig.current){let e=new im({});e.mount(window),e.setOptions({layoutScroll:!0}),ig.current=e}return ig.current},resetTransform:(e,t)=>{e.style.transform=void 0!==t?t:"none"},checkIsScrollRoot:e=>"fixed"===window.getComputedStyle(e).position});function iv(e,t,r){let{props:n}=e;e.animationState&&n.whileHover&&e.animationState.setActive("whileHover","Start"===r);let i=n["onHover"+r];i&&G.postRender(()=>i(t,r_(t)))}class ib extends rP{mount(){let{current:e}=this.node;e&&(this.unmount=function(e,t,r={}){let[n,i,o]=M(e,r),a=j(e=>{let{target:r}=e,n=t(e);if("function"!=typeof n||!r)return;let o=j(e=>{n(e),r.removeEventListener("pointerleave",o)});r.addEventListener("pointerleave",o,i)});return n.forEach(e=>{e.addEventListener("pointerenter",a,i)}),o}(e,e=>(iv(this.node,e,"Start"),e=>iv(this.node,e,"End"))))}unmount(){}}class iP extends rP{constructor(){super(...arguments),this.isActive=!1}onFocus(){let e=!1;try{e=this.node.current.matches(":focus-visible")}catch(t){e=!0}e&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=tV(rT(this.node.current,"focus",()=>this.onFocus()),rT(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}function ix(e,t,r){let{props:n}=e;e.animationState&&n.whileTap&&e.animationState.setActive("whileTap","Start"===r);let i=n["onTap"+("End"===r?"":r)];i&&G.postRender(()=>i(t,r_(t)))}class iR extends rP{mount(){let{current:e}=this.node;e&&(this.unmount=function(e,t,r={}){let[n,i,o]=M(e,r),a=e=>{let n=e.currentTarget;if(!I(e)||k.has(n))return;k.add(n);let o=t(e),a=(e,t)=>{window.removeEventListener("pointerup",s),window.removeEventListener("pointercancel",l),I(e)&&k.has(n)&&(k.delete(n),"function"==typeof o&&o(e,{success:t}))},s=e=>{a(e,r.useGlobalTarget||O(n,e.target))},l=e=>{a(e,!1)};window.addEventListener("pointerup",s,i),window.addEventListener("pointercancel",l,i)};return n.forEach(e=>{!D.has(e.tagName)&&-1===e.tabIndex&&null===e.getAttribute("tabindex")&&(e.tabIndex=0),(r.useGlobalTarget?window:e).addEventListener("pointerdown",a,i),e.addEventListener("focus",e=>U(e,i),i)}),o}(e,e=>(ix(this.node,e,"Start"),(e,{success:t})=>ix(this.node,e,t?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}let iE=new WeakMap,iT=new WeakMap,i_=e=>{let t=iE.get(e.target);t&&t(e)},iw=e=>{e.forEach(i_)},iS={some:0,all:1};class iA extends rP{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:e={}}=this.node.getProps(),{root:t,margin:r,amount:n="some",once:i}=e,o={root:t?t.current:void 0,rootMargin:r,threshold:"number"==typeof n?n:iS[n]};return function(e,t,r){let n=function({root:e,...t}){let r=e||document;iT.has(r)||iT.set(r,{});let n=iT.get(r),i=JSON.stringify(t);return n[i]||(n[i]=new IntersectionObserver(iw,{root:e,...t})),n[i]}(t);return iE.set(e,r),n.observe(e),()=>{iE.delete(e),n.unobserve(e)}}(this.node.current,o,e=>{let{isIntersecting:t}=e;if(this.isInView===t||(this.isInView=t,i&&!t&&this.hasEnteredView))return;t&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",t);let{onViewportEnter:r,onViewportLeave:n}=this.node.getProps(),o=t?r:n;o&&o(e)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:e,prevProps:t}=this.node;["amount","margin","root"].some(function({viewport:e={}},{viewport:t={}}={}){return r=>e[r]!==t[r]}(e,t))&&this.startObserver()}unmount(){}}let iM=(0,nl.createContext)({strict:!1});var ij=r(32582);let iO=(0,nl.createContext)({});function iC(e){return i(e.animate)||h.some(t=>s(e[t]))}function iD(e){return!!(iC(e)||e.variants)}function ik(e){return Array.isArray(e)?e.join(" "):e}var iL=r(7044);let iN={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},iU={};for(let e in iN)iU[e]={isEnabled:t=>iN[e].some(e=>!!t[e])};let iI=Symbol.for("motionComponentSymbol");var iV=r(21279),iF=r(15124);let iB=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function i$(e){if("string"!=typeof e||e.includes("-"));else if(iB.indexOf(e)>-1||/[A-Z]/u.test(e))return!0;return!1}var iH=r(72789);let iz=e=>(t,r)=>{let n=(0,nl.useContext)(iO),o=(0,nl.useContext)(iV.t),a=()=>(function({scrapeMotionValuesFromProps:e,createRenderState:t,onUpdate:r},n,o,a){let s={latestValues:function(e,t,r,n){let o={},a=n(e,{});for(let e in a)o[e]=nE(a[e]);let{initial:s,animate:l}=e,c=iC(e),d=iD(e);t&&d&&!c&&!1!==e.inherit&&(void 0===s&&(s=t.initial),void 0===l&&(l=t.animate));let h=!!r&&!1===r.initial,f=(h=h||!1===s)?l:s;if(f&&"boolean"!=typeof f&&!i(f)){let t=Array.isArray(f)?f:[f];for(let r=0;r<t.length;r++){let n=u(e,t[r]);if(n){let{transitionEnd:e,transition:t,...r}=n;for(let e in r){let t=r[e];if(Array.isArray(t)){let e=h?t.length-1:0;t=t[e]}null!==t&&(o[e]=t)}for(let t in e)o[t]=e[t]}}}return o}(n,o,a,e),renderState:t()};return r&&(s.onMount=e=>r({props:n,current:e,...s}),s.onUpdate=e=>r(e)),s})(e,t,n,o);return r?a():(0,iH.M)(a)},iW=(e,t)=>t&&"number"==typeof e?t.transform(e):e,iK={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},iq=$.length;function iX(e,t,r){let{style:n,vars:i,transformOrigin:o}=e,a=!1,s=!1;for(let e in t){let r=t[e];if(H.has(e)){a=!0;continue}if(ty(e)){i[e]=r;continue}{let t=iW(r,e6[e]);e.startsWith("origin")?(s=!0,o[e]=t):n[e]=t}}if(!t.transform&&(a||r?n.transform=function(e,t,r){let n="",i=!0;for(let o=0;o<iq;o++){let a=$[o],s=e[a];if(void 0===s)continue;let l=!0;if(!(l="number"==typeof s?s===+!!a.startsWith("scale"):0===parseFloat(s))||r){let e=iW(s,e6[a]);if(!l){i=!1;let t=iK[a]||a;n+=`${t}(${e}) `}r&&(t[a]=e)}}return n=n.trim(),r?n=r(t,i?"":n):i&&(n="none"),n}(t,e.transform,r):n.transform&&(n.transform="none")),s){let{originX:e="50%",originY:t="50%",originZ:r=0}=o;n.transformOrigin=`${e} ${t} ${r}`}}let iY={offset:"stroke-dashoffset",array:"stroke-dasharray"},iG={offset:"strokeDashoffset",array:"strokeDasharray"};function iQ(e,t,r){return"string"==typeof e?e:e$.transform(t+r*e)}function iZ(e,{attrX:t,attrY:r,attrScale:n,originX:i,originY:o,pathLength:a,pathSpacing:s=1,pathOffset:l=0,...u},c,d){if(iX(e,u,d),c){e.style.viewBox&&(e.attrs.viewBox=e.style.viewBox);return}e.attrs=e.style,e.style={};let{attrs:h,style:f,dimensions:p}=e;h.transform&&(p&&(f.transform=h.transform),delete h.transform),p&&(void 0!==i||void 0!==o||f.transform)&&(f.transformOrigin=function(e,t,r){let n=iQ(t,e.x,e.width),i=iQ(r,e.y,e.height);return`${n} ${i}`}(p,void 0!==i?i:.5,void 0!==o?o:.5)),void 0!==t&&(h.x=t),void 0!==r&&(h.y=r),void 0!==n&&(h.scale=n),void 0!==a&&function(e,t,r=1,n=0,i=!0){e.pathLength=1;let o=i?iY:iG;e[o.offset]=e$.transform(-n);let a=e$.transform(t),s=e$.transform(r);e[o.array]=`${a} ${s}`}(h,a,s,l,!1)}let iJ=()=>({style:{},transform:{},transformOrigin:{},vars:{}}),i0=()=>({...iJ(),attrs:{}}),i1=e=>"string"==typeof e&&"svg"===e.toLowerCase();function i2(e,{style:t,vars:r},n,i){for(let o in Object.assign(e.style,t,i&&i.getProjectionStyles(n)),r)e.style.setProperty(o,r[o])}let i3=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function i5(e,t,r,n){for(let r in i2(e,t,void 0,n),t.attrs)e.setAttribute(i3.has(r)?r:ed(r),t.attrs[r])}function i9(e,{layout:t,layoutId:r}){return H.has(e)||e.startsWith("origin")||(t||void 0!==r)&&(!!nm[e]||"opacity"===e)}function i4(e,t,r){var n;let{style:i}=e,o={};for(let a in i)(eu(i[a])||t.style&&eu(t.style[a])||i9(a,e)||(null===(n=null==r?void 0:r.getValue(a))||void 0===n?void 0:n.liveStyle)!==void 0)&&(o[a]=i[a]);return o}function i7(e,t,r){let n=i4(e,t,r);for(let r in e)(eu(e[r])||eu(t[r]))&&(n[-1!==$.indexOf(r)?"attr"+r.charAt(0).toUpperCase()+r.substring(1):r]=e[r]);return n}let i6=["x","y","width","height","cx","cy","r"],i8={useVisualState:iz({scrapeMotionValuesFromProps:i7,createRenderState:i0,onUpdate:({props:e,prevProps:t,current:r,renderState:n,latestValues:i})=>{if(!r)return;let o=!!e.drag;if(!o){for(let e in i)if(H.has(e)){o=!0;break}}if(!o)return;let a=!t;if(t)for(let r=0;r<i6.length;r++){let n=i6[r];e[n]!==t[n]&&(a=!0)}a&&G.read(()=>{(function(e,t){try{t.dimensions="function"==typeof e.getBBox?e.getBBox():e.getBoundingClientRect()}catch(e){t.dimensions={x:0,y:0,width:0,height:0}}})(r,n),G.render(()=>{iZ(n,i,i1(r.tagName),e.transformTemplate),i5(r,n)})})}})},oe={useVisualState:iz({scrapeMotionValuesFromProps:i4,createRenderState:iJ})};function ot(e,t,r){for(let n in t)eu(t[n])||i9(n,r)||(e[n]=t[n])}let or=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function on(e){return e.startsWith("while")||e.startsWith("drag")&&"draggable"!==e||e.startsWith("layout")||e.startsWith("onTap")||e.startsWith("onPan")||e.startsWith("onLayout")||or.has(e)}let oi=e=>!on(e);try{!function(e){e&&(oi=t=>t.startsWith("on")?!on(t):e(t))}(require("@emotion/is-prop-valid").default)}catch(e){}let oo={current:null},oa={current:!1},os=[...tE,eq,e2],ol=e=>os.find(tR(e)),ou=new WeakMap,oc=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class od{scrapeMotionValuesFromProps(e,t,r){return{}}constructor({parent:e,props:t,presenceContext:r,reducedMotionConfig:n,blockInitialAnimation:i,visualState:o},a={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=tp,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{let e=et.now();this.renderScheduledAt<e&&(this.renderScheduledAt=e,G.render(this.render,!1,!0))};let{latestValues:s,renderState:l,onUpdate:u}=o;this.onUpdate=u,this.latestValues=s,this.baseTarget={...s},this.initialValues=t.initial?{...s}:{},this.renderState=l,this.parent=e,this.props=t,this.presenceContext=r,this.depth=e?e.depth+1:0,this.reducedMotionConfig=n,this.options=a,this.blockInitialAnimation=!!i,this.isControllingVariants=iC(t),this.isVariantNode=iD(t),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(e&&e.current);let{willChange:c,...d}=this.scrapeMotionValuesFromProps(t,{},this);for(let e in d){let t=d[e];void 0!==s[e]&&eu(t)&&t.set(s[e],!1)}}mount(e){this.current=e,ou.set(e,this),this.projection&&!this.projection.instance&&this.projection.mount(e),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((e,t)=>this.bindToMotionValue(t,e)),oa.current||function(){if(oa.current=!0,iL.B){if(window.matchMedia){let e=window.matchMedia("(prefers-reduced-motion)"),t=()=>oo.current=e.matches;e.addListener(t),t()}else oo.current=!1}}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||oo.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){for(let e in ou.delete(this.current),this.projection&&this.projection.unmount(),Q(this.notifyUpdate),Q(this.render),this.valueSubscriptions.forEach(e=>e()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this),this.events)this.events[e].clear();for(let e in this.features){let t=this.features[e];t&&(t.unmount(),t.isMounted=!1)}this.current=null}bindToMotionValue(e,t){let r;this.valueSubscriptions.has(e)&&this.valueSubscriptions.get(e)();let n=H.has(e),i=t.on("change",t=>{this.latestValues[e]=t,this.props.onUpdate&&G.preRender(this.notifyUpdate),n&&this.projection&&(this.projection.isTransformDirty=!0)}),o=t.on("renderRequest",this.scheduleRender);window.MotionCheckAppearSync&&(r=window.MotionCheckAppearSync(this,e,t)),this.valueSubscriptions.set(e,()=>{i(),o(),r&&r(),t.owner&&t.stop()})}sortNodePosition(e){return this.current&&this.sortInstanceNodePosition&&this.type===e.type?this.sortInstanceNodePosition(this.current,e.current):0}updateFeatures(){let e="animation";for(e in iU){let t=iU[e];if(!t)continue;let{isEnabled:r,Feature:n}=t;if(!this.features[e]&&n&&r(this.props)&&(this.features[e]=new n(this)),this.features[e]){let t=this.features[e];t.isMounted?t.update():(t.mount(),t.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):rX()}getStaticValue(e){return this.latestValues[e]}setStaticValue(e,t){this.latestValues[e]=t}update(e,t){(e.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=e,this.prevPresenceContext=this.presenceContext,this.presenceContext=t;for(let t=0;t<oc.length;t++){let r=oc[t];this.propEventSubscriptions[r]&&(this.propEventSubscriptions[r](),delete this.propEventSubscriptions[r]);let n=e["on"+r];n&&(this.propEventSubscriptions[r]=this.on(r,n))}this.prevMotionValues=function(e,t,r){for(let n in t){let i=t[n],o=r[n];if(eu(i))e.addValue(n,i);else if(eu(o))e.addValue(n,el(i,{owner:e}));else if(o!==i){if(e.hasValue(n)){let t=e.getValue(n);!0===t.liveStyle?t.jump(i):t.hasAnimated||t.set(i)}else{let t=e.getStaticValue(n);e.addValue(n,el(void 0!==t?t:i,{owner:e}))}}}for(let n in r)void 0===t[n]&&e.removeValue(n);return t}(this,this.scrapeMotionValuesFromProps(e,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue(),this.onUpdate&&this.onUpdate(this)}getProps(){return this.props}getVariant(e){return this.props.variants?this.props.variants[e]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(e){let t=this.getClosestVariantNode();if(t)return t.variantChildren&&t.variantChildren.add(e),()=>t.variantChildren.delete(e)}addValue(e,t){let r=this.values.get(e);t!==r&&(r&&this.removeValue(e),this.bindToMotionValue(e,t),this.values.set(e,t),this.latestValues[e]=t.get())}removeValue(e){this.values.delete(e);let t=this.valueSubscriptions.get(e);t&&(t(),this.valueSubscriptions.delete(e)),delete this.latestValues[e],this.removeValueFromRenderState(e,this.renderState)}hasValue(e){return this.values.has(e)}getValue(e,t){if(this.props.values&&this.props.values[e])return this.props.values[e];let r=this.values.get(e);return void 0===r&&void 0!==t&&(r=el(null===t?void 0:t,{owner:this}),this.addValue(e,r)),r}readValue(e,t){var r;let n=void 0===this.latestValues[e]&&this.current?null!==(r=this.getBaseTargetFromProps(this.props,e))&&void 0!==r?r:this.readValueFromInstance(this.current,e,this.options):this.latestValues[e];return null!=n&&("string"==typeof n&&(tm(n)||e_(n))?n=parseFloat(n):!ol(n)&&e2.test(t)&&(n=tt(e,t)),this.setBaseTarget(e,eu(n)?n.get():n)),eu(n)?n.get():n}setBaseTarget(e,t){this.baseTarget[e]=t}getBaseTarget(e){var t;let r;let{initial:n}=this.props;if("string"==typeof n||"object"==typeof n){let i=u(this.props,n,null===(t=this.presenceContext)||void 0===t?void 0:t.custom);i&&(r=i[e])}if(n&&void 0!==r)return r;let i=this.getBaseTargetFromProps(this.props,e);return void 0===i||eu(i)?void 0!==this.initialValues[e]&&void 0===r?void 0:this.baseTarget[e]:i}on(e,t){return this.events[e]||(this.events[e]=new ei),this.events[e].add(t)}notify(e,...t){this.events[e]&&this.events[e].notify(...t)}}class oh extends od{constructor(){super(...arguments),this.KeyframeResolver=t_}sortInstanceNodePosition(e,t){return 2&e.compareDocumentPosition(t)?1:-1}getBaseTargetFromProps(e,t){return e.style?e.style[t]:void 0}removeValueFromRenderState(e,{vars:t,style:r}){delete t[e],delete r[e]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:e}=this.props;eu(e)&&(this.childSubscription=e.on("change",e=>{this.current&&(this.current.textContent=`${e}`)}))}}class of extends oh{constructor(){super(...arguments),this.type="html",this.renderInstance=i2}readValueFromInstance(e,t){if(H.has(t)){let e=te(t);return e&&e.default||0}{let r=window.getComputedStyle(e),n=(ty(t)?r.getPropertyValue(t):r[t])||0;return"string"==typeof n?n.trim():n}}measureInstanceViewportBox(e,{transformPagePoint:t}){return r7(e,t)}build(e,t,r){iX(e,t,r.transformTemplate)}scrapeMotionValuesFromProps(e,t,r){return i4(e,t,r)}}class op extends oh{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=rX}getBaseTargetFromProps(e,t){return e[t]}readValueFromInstance(e,t){if(H.has(t)){let e=te(t);return e&&e.default||0}return t=i3.has(t)?t:ed(t),e.getAttribute(t)}scrapeMotionValuesFromProps(e,t,r){return i7(e,t,r)}build(e,t,r){iZ(e,t,this.isSVGTag,r.transformTemplate)}renderInstance(e,t,r,n){i5(e,t,r,n)}mount(e){this.isSVGTag=i1(e.tagName),super.mount(e)}}let om=function(e){if("undefined"==typeof Proxy)return e;let t=new Map;return new Proxy((...t)=>e(...t),{get:(r,n)=>"create"===n?e:(t.has(n)||t.set(n,e(n)),t.get(n))})}((no={animation:{Feature:rx},exit:{Feature:rE},inView:{Feature:iA},tap:{Feature:iR},focus:{Feature:iP},hover:{Feature:ib},pan:{Feature:ni},drag:{Feature:nr,ProjectionNode:iy,MeasureLayout:nb},layout:{ProjectionNode:iy,MeasureLayout:nb}},na=(e,t)=>i$(e)?new op(t):new of(t,{allowProjection:e!==nl.Fragment}),function(e,{forwardMotionProps:t}={forwardMotionProps:!1}){return function({preloadedFeatures:e,createVisualElement:t,useRender:r,useVisualState:n,Component:i}){var o,a;function l(e,o){var a,l,u;let c;let d={...(0,nl.useContext)(ij.Q),...e,layoutId:function({layoutId:e}){let t=(0,nl.useContext)(nc.L).id;return t&&void 0!==e?t+"-"+e:e}(e)},{isStatic:h}=d,f=function(e){let{initial:t,animate:r}=function(e,t){if(iC(e)){let{initial:t,animate:r}=e;return{initial:!1===t||s(t)?t:void 0,animate:s(r)?r:void 0}}return!1!==e.inherit?t:{}}(e,(0,nl.useContext)(iO));return(0,nl.useMemo)(()=>({initial:t,animate:r}),[ik(t),ik(r)])}(e),p=n(e,h);if(!h&&iL.B){l=0,u=0,(0,nl.useContext)(iM).strict;let e=function(e){let{drag:t,layout:r}=iU;if(!t&&!r)return{};let n={...t,...r};return{MeasureLayout:(null==t?void 0:t.isEnabled(e))||(null==r?void 0:r.isEnabled(e))?n.MeasureLayout:void 0,ProjectionNode:n.ProjectionNode}}(d);c=e.MeasureLayout,f.visualElement=function(e,t,r,n,i){var o,a;let{visualElement:s}=(0,nl.useContext)(iO),l=(0,nl.useContext)(iM),u=(0,nl.useContext)(iV.t),c=(0,nl.useContext)(ij.Q).reducedMotion,d=(0,nl.useRef)(null);n=n||l.renderer,!d.current&&n&&(d.current=n(e,{visualState:t,parent:s,props:r,presenceContext:u,blockInitialAnimation:!!u&&!1===u.initial,reducedMotionConfig:c}));let h=d.current,f=(0,nl.useContext)(nd);h&&!h.projection&&i&&("html"===h.type||"svg"===h.type)&&function(e,t,r,n){let{layoutId:i,layout:o,drag:a,dragConstraints:s,layoutScroll:l,layoutRoot:u}=t;e.projection=new r(e.latestValues,t["data-framer-portal-id"]?void 0:function e(t){if(t)return!1!==t.options.allowProjection?t.projection:e(t.parent)}(e.parent)),e.projection.setOptions({layoutId:i,layout:o,alwaysMeasureLayout:!!a||s&&rk(s),visualElement:e,animationType:"string"==typeof o?o:"both",initialPromotionConfig:n,layoutScroll:l,layoutRoot:u})}(d.current,r,i,f);let p=(0,nl.useRef)(!1);(0,nl.useInsertionEffect)(()=>{h&&p.current&&h.update(r,u)});let m=r[eh],g=(0,nl.useRef)(!!m&&!(null===(o=window.MotionHandoffIsComplete)||void 0===o?void 0:o.call(window,m))&&(null===(a=window.MotionHasOptimisedAnimation)||void 0===a?void 0:a.call(window,m)));return(0,iF.E)(()=>{h&&(p.current=!0,window.MotionIsMounted=!0,h.updateFeatures(),ng.render(h.render),g.current&&h.animationState&&h.animationState.animateChanges())}),(0,nl.useEffect)(()=>{h&&(!g.current&&h.animationState&&h.animationState.animateChanges(),g.current&&(queueMicrotask(()=>{var e;null===(e=window.MotionHandoffMarkAsComplete)||void 0===e||e.call(window,m)}),g.current=!1))}),h}(i,p,d,t,e.ProjectionNode)}return(0,ns.jsxs)(iO.Provider,{value:f,children:[c&&f.visualElement?(0,ns.jsx)(c,{visualElement:f.visualElement,...d}):null,r(i,e,(a=f.visualElement,(0,nl.useCallback)(e=>{e&&p.onMount&&p.onMount(e),a&&(e?a.mount(e):a.unmount()),o&&("function"==typeof o?o(e):rk(o)&&(o.current=e))},[a])),p,h,f.visualElement)]})}e&&function(e){for(let t in e)iU[t]={...iU[t],...e[t]}}(e),l.displayName=`motion.${"string"==typeof i?i:`create(${null!==(a=null!==(o=i.displayName)&&void 0!==o?o:i.name)&&void 0!==a?a:""})`}`;let u=(0,nl.forwardRef)(l);return u[iI]=i,u}({...i$(e)?i8:oe,preloadedFeatures:no,useRender:function(e=!1){return(t,r,n,{latestValues:i},o)=>{let a=(i$(t)?function(e,t,r,n){let i=(0,nl.useMemo)(()=>{let r=i0();return iZ(r,t,i1(n),e.transformTemplate),{...r.attrs,style:{...r.style}}},[t]);if(e.style){let t={};ot(t,e.style,e),i.style={...t,...i.style}}return i}:function(e,t){let r={},n=function(e,t){let r=e.style||{},n={};return ot(n,r,e),Object.assign(n,function({transformTemplate:e},t){return(0,nl.useMemo)(()=>{let r=iJ();return iX(r,t,e),Object.assign({},r.vars,r.style)},[t])}(e,t)),n}(e,t);return e.drag&&!1!==e.dragListener&&(r.draggable=!1,n.userSelect=n.WebkitUserSelect=n.WebkitTouchCallout="none",n.touchAction=!0===e.drag?"none":`pan-${"x"===e.drag?"y":"x"}`),void 0===e.tabIndex&&(e.onTap||e.onTapStart||e.whileTap)&&(r.tabIndex=0),r.style=n,r})(r,i,o,t),s=function(e,t,r){let n={};for(let i in e)("values"!==i||"object"!=typeof e.values)&&(oi(i)||!0===r&&on(i)||!t&&!on(i)||e.draggable&&i.startsWith("onDrag"))&&(n[i]=e[i]);return n}(r,"string"==typeof t,e),l=t!==nl.Fragment?{...s,...a,ref:n}:{},{children:u}=r,c=(0,nl.useMemo)(()=>eu(u)?u.get():u,[u]);return(0,nl.createElement)(t,{...l,children:c})}}(t),createVisualElement:na,Component:e})}))},97936:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"hmrRefreshReducer",{enumerable:!0,get:function(){return n}}),r(59008),r(57391),r(86770),r(2030),r(25232),r(59435),r(56928),r(89752),r(96493),r(68214);let n=function(e,t){return e};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},98834:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addPathPrefix",{enumerable:!0,get:function(){return i}});let n=r(19169);function i(e,t){if(!e.startsWith("/")||!t)return e;let{pathname:r,query:i,hash:o}=(0,n.parsePath)(e);return""+t+r+i+o}}};