"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.start = void 0;
const server_1 = require("@genkit-ai/tools-common/server");
const utils_1 = require("@genkit-ai/tools-common/utils");
const child_process_1 = require("child_process");
const commander_1 = require("commander");
const get_port_1 = __importStar(require("get-port"));
const open_1 = __importDefault(require("open"));
const manager_utils_1 = require("../utils/manager-utils");
exports.start = new commander_1.Command('start')
    .description('runs a command in Genkit dev mode')
    .option('-n, --noui', 'do not start the Dev UI', false)
    .option('-p, --port <port>', 'port for the Dev UI')
    .option('-o, --open', 'Open the browser on UI start up')
    .action(async (options) => {
    let managerPromise = (0, manager_utils_1.startManager)(true);
    if (!options.noui) {
        let port;
        if (options.port) {
            port = Number(options.port);
            if (isNaN(port) || port < 0) {
                utils_1.logger.error(`"${options.port}" is not a valid port number`);
                return;
            }
        }
        else {
            port = await (0, get_port_1.default)({ port: (0, get_port_1.makeRange)(4000, 4099) });
        }
        managerPromise = managerPromise.then((manager) => {
            (0, server_1.startServer)(manager, port);
            return manager;
        });
        if (options.open) {
            (0, open_1.default)(`http://localhost:${port}`);
        }
    }
    await managerPromise.then((manager) => {
        const telemetryServerUrl = manager?.telemetryServerUrl;
        return startRuntime(telemetryServerUrl);
    });
});
async function startRuntime(telemetryServerUrl) {
    if (exports.start.args.length > 0) {
        return new Promise((urlResolver, reject) => {
            const appProcess = (0, child_process_1.spawn)(exports.start.args[0], exports.start.args.slice(1), {
                env: {
                    ...process.env,
                    GENKIT_TELEMETRY_SERVER: telemetryServerUrl,
                    GENKIT_ENV: 'dev',
                },
                shell: process.platform === 'win32',
            });
            const originalStdIn = process.stdin;
            appProcess.stderr?.pipe(process.stderr);
            appProcess.stdout?.pipe(process.stdout);
            process.stdin?.pipe(appProcess.stdin);
            appProcess.on('error', (error) => {
                console.log(`Error in app process: ${error}`);
                reject(error);
                process.exitCode = 1;
            });
            appProcess.on('exit', (code) => {
                process.stdin?.pipe(originalStdIn);
                if (code === 0) {
                    urlResolver(undefined);
                }
                else {
                    reject(new Error(`app process exited with code ${code}`));
                }
            });
        });
    }
    return new Promise(() => { });
}
//# sourceMappingURL=start.js.map