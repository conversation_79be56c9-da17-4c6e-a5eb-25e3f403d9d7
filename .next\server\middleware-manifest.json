{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/src/middleware.js"], "name": "src/middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "x0c3sykpJO3PGlE7-78XV", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "Bo+sQ9ioaiyiLUwLjzd1udTV8v8fmaLeb82li3vq6m4=", "__NEXT_PREVIEW_MODE_ID": "b62792f2c67a87f5d82d4d461df049d0", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "04d6b931b1fa5c13d029035515507f7519a6bdd84417a41f4d365d0a70f6ede7", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "635e59b6081d57e26b9615b7916bc01b3ab00a49a16df8677fbf29eb92ba0ac2"}}}, "functions": {}, "sortedMiddleware": ["/"]}