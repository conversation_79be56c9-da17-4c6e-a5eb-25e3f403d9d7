{"name": "@opentelemetry/instrumentation", "version": "0.52.1", "description": "Base class for node which OpenTelemetry instrumentation modules extend", "author": "OpenTelemetry Authors", "homepage": "https://github.com/open-telemetry/opentelemetry-js/tree/main/experimental/packages/opentelemetry-instrumentation", "license": "Apache-2.0", "main": "build/src/index.js", "module": "build/esm/index.js", "esnext": "build/esnext/index.js", "types": "build/src/index.d.ts", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/open-telemetry/opentelemetry-js.git"}, "browser": {"./src/platform/index.ts": "./src/platform/browser/index.ts", "./build/esm/platform/index.js": "./build/esm/platform/browser/index.js", "./build/esnext/platform/index.js": "./build/esnext/platform/browser/index.js", "./build/src/platform/index.js": "./build/src/platform/browser/index.js"}, "files": ["build/esm/**/*.js", "build/esm/**/*.js.map", "build/esm/**/*.d.ts", "build/esnext/**/*.js", "build/esnext/**/*.js.map", "build/esnext/**/*.d.ts", "build/src/**/*.js", "build/src/**/*.js.map", "build/src/**/*.d.ts", "hook.mjs", "doc", "LICENSE", "README.md"], "scripts": {"prepublishOnly": "npm run compile", "compile": "tsc --build tsconfig.json tsconfig.esm.json tsconfig.esnext.json", "clean": "tsc --build --clean tsconfig.json tsconfig.esm.json tsconfig.esnext.json", "codecov": "nyc report --reporter=json && codecov -f coverage/*.json -p ../../../", "codecov:browser": "nyc report --reporter=json && codecov -f coverage/*.json -p ../../../", "lint": "eslint . --ext .ts", "lint:fix": "eslint . --ext .ts --fix", "tdd": "npm run tdd:node", "tdd:node": "npm run test -- --watch-extensions ts --watch", "tdd:browser": "karma start", "test:cjs": "nyc ts-mocha -p tsconfig.json 'test/**/*.test.ts' --exclude 'test/browser/**/*.ts'", "test:esm": "nyc node --experimental-loader=./hook.mjs ../../../node_modules/mocha/bin/mocha 'test/node/*.test.mjs' test/node/*.test.mjs", "test": "npm run test:cjs && npm run test:esm", "test:browser": "karma start --single-run", "version": "node ../../../scripts/version-update.js", "watch": "tsc --build --watch tsconfig.json tsconfig.esm.json tsconfig.esnext.json", "precompile": "cross-var lerna run version --scope $npm_package_name --include-dependencies", "prewatch": "node ../../../scripts/version-update.js", "peer-api-check": "node ../../../scripts/peer-api-check.js", "align-api-deps": "node ../../../scripts/align-api-deps.js"}, "keywords": ["opentelemetry", "nodejs", "browser", "tracing", "profiling", "metrics", "stats"], "bugs": {"url": "https://github.com/open-telemetry/opentelemetry-js/issues"}, "dependencies": {"@opentelemetry/api-logs": "0.52.1", "@types/shimmer": "^1.0.2", "import-in-the-middle": "^1.8.1", "require-in-the-middle": "^7.1.1", "semver": "^7.5.2", "shimmer": "^1.2.1"}, "peerDependencies": {"@opentelemetry/api": "^1.3.0"}, "devDependencies": {"@babel/core": "7.24.7", "@babel/preset-env": "7.24.7", "@opentelemetry/api": "1.9.0", "@opentelemetry/sdk-metrics": "1.25.1", "@types/mocha": "10.0.6", "@types/node": "18.6.5", "@types/semver": "7.5.8", "@types/sinon": "17.0.3", "@types/webpack-env": "1.16.3", "babel-loader": "8.3.0", "babel-plugin-istanbul": "6.1.1", "codecov": "3.8.3", "cpx2": "2.0.0", "cross-var": "1.1.0", "karma": "6.4.3", "karma-chrome-launcher": "3.1.0", "karma-coverage": "2.2.1", "karma-mocha": "2.0.1", "karma-spec-reporter": "0.0.36", "karma-webpack": "5.0.1", "lerna": "6.6.2", "mocha": "10.2.0", "nyc": "15.1.0", "sinon": "15.1.2", "ts-loader": "9.5.1", "ts-mocha": "10.0.0", "typescript": "4.4.4", "webpack": "5.89.0", "webpack-cli": "5.1.4", "webpack-merge": "5.10.0"}, "engines": {"node": ">=14"}, "sideEffects": false, "gitHead": "0608f405573901e54db01e44c533009cf28be262"}