1:"$Sreact.fragment"
2:I[48031,["73","static/chunks/36b4fd63-79743096f62d8164.js","671","static/chunks/671-f8fa9487493500f2.js","422","static/chunks/422-6e657bb89aca75b1.js","904","static/chunks/904-d05ec2f0d259015b.js","177","static/chunks/app/layout-8bd7f2e92b779de5.js"],"SpeedInsights"]
3:I[90925,["73","static/chunks/36b4fd63-79743096f62d8164.js","671","static/chunks/671-f8fa9487493500f2.js","422","static/chunks/422-6e657bb89aca75b1.js","904","static/chunks/904-d05ec2f0d259015b.js","177","static/chunks/app/layout-8bd7f2e92b779de5.js"],"AuthProvider"]
4:I[87555,[],""]
5:I[31295,[],""]
6:I[52558,["73","static/chunks/36b4fd63-79743096f62d8164.js","671","static/chunks/671-f8fa9487493500f2.js","422","static/chunks/422-6e657bb89aca75b1.js","904","static/chunks/904-d05ec2f0d259015b.js","177","static/chunks/app/layout-8bd7f2e92b779de5.js"],"Toaster"]
7:I[44321,["73","static/chunks/36b4fd63-79743096f62d8164.js","671","static/chunks/671-f8fa9487493500f2.js","440","static/chunks/440-a001a8ca762bad5a.js","422","static/chunks/422-6e657bb89aca75b1.js","59","static/chunks/59-0d7423cfe06b94f9.js","321","static/chunks/321-bbffef8ce0446962.js","273","static/chunks/app/tips/page-1c7d92e1ee427e89.js"],"default"]
8:I[29100,["73","static/chunks/36b4fd63-79743096f62d8164.js","671","static/chunks/671-f8fa9487493500f2.js","440","static/chunks/440-a001a8ca762bad5a.js","422","static/chunks/422-6e657bb89aca75b1.js","59","static/chunks/59-0d7423cfe06b94f9.js","321","static/chunks/321-bbffef8ce0446962.js","273","static/chunks/app/tips/page-1c7d92e1ee427e89.js"],"Separator"]
9:I[59665,[],"OutletBoundary"]
c:I[59665,[],"ViewportBoundary"]
e:I[59665,[],"MetadataBoundary"]
10:I[26614,[],""]
:HL["/_next/static/css/85be4710a241beeb.css","style"]
0:{"P":null,"b":"x0c3sykpJO3PGlE7-78XV","p":"","c":["","tips"],"i":false,"f":[[["",{"children":["tips",{"children":["__PAGE__",{}]}]},"$undefined","$undefined",true],["",["$","$1","c",{"children":[[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/85be4710a241beeb.css","precedence":"next","crossOrigin":"$undefined","nonce":"$undefined"}]],["$","html",null,{"lang":"en","children":[["$","head",null,{"children":[["$","link",null,{"rel":"preconnect","href":"https://fonts.googleapis.com"}],["$","link",null,{"rel":"preconnect","href":"https://fonts.gstatic.com","crossOrigin":"anonymous"}],["$","link",null,{"href":"https://fonts.googleapis.com/css2?family=PT+Sans:wght@400;700&display=swap","rel":"stylesheet"}],["$","$L2",null,{}]]}],["$","body",null,{"className":"font-body antialiased","children":["$","$L3",null,{"children":[["$","$L4",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L5",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":[[["$","title",null,{"children":"404: This page could not be found."}],["$","div",null,{"style":{"fontFamily":"system-ui,\"Segoe UI\",Roboto,Helvetica,Arial,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\"","height":"100vh","textAlign":"center","display":"flex","flexDirection":"column","alignItems":"center","justifyContent":"center"},"children":["$","div",null,{"children":[["$","style",null,{"dangerouslySetInnerHTML":{"__html":"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}"}}],["$","h1",null,{"className":"next-error-h1","style":{"display":"inline-block","margin":"0 20px 0 0","padding":"0 23px 0 0","fontSize":24,"fontWeight":500,"verticalAlign":"top","lineHeight":"49px"},"children":404}],["$","div",null,{"style":{"display":"inline-block"},"children":["$","h2",null,{"style":{"fontSize":14,"fontWeight":400,"lineHeight":"49px","margin":0},"children":"This page could not be found."}]}]]}]}]],[]],"forbidden":"$undefined","unauthorized":"$undefined"}],["$","$L6",null,{}]]}]}]]}]]}],{"children":["tips",["$","$1","c",{"children":[null,["$","$L4",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L5",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","forbidden":"$undefined","unauthorized":"$undefined"}]]}],{"children":["__PAGE__",["$","$1","c",{"children":[["$","div",null,{"className":"flex flex-col min-h-screen bg-background","children":[["$","$L7",null,{"title":"BudgetWise"}],["$","main",null,{"className":"flex-grow container mx-auto py-4 sm:py-6 px-2 sm:px-4","children":[["$","h2",null,{"className":"text-2xl font-headline font-semibold mb-1 text-foreground","children":"Budgeting Tips"}],["$","p",null,{"className":"text-sm text-muted-foreground mb-4","children":"Helpful advice to manage your finances better."}],["$","$L8",null,{"className":"mb-6"}],["$","div",null,{"className":"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6","children":[["$","div","0",{"ref":"$undefined","className":"rounded-lg border bg-card text-card-foreground shadow-sm hover:shadow-md transition-shadow duration-200","children":[["$","div",null,{"ref":"$undefined","className":"flex flex-col space-y-1.5 p-6 pb-2","children":["$","div",null,{"ref":"$undefined","className":"font-semibold tracking-tight text-base flex items-center gap-2 font-headline","children":[["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-lightbulb h-4 w-4 text-accent","children":[["$","path","1gvzjb",{"d":"M15 14c.2-1 .7-1.7 1.5-2.5 1-.9 1.5-2.2 1.5-3.5A6 6 0 0 0 6 8c0 1 .2 2.2 1.5 ******* 1.3 1.5 1.5 2.5"}],["$","path","x1upvd",{"d":"M9 18h6"}],["$","path","ceow96",{"d":"M10 22h4"}],"$undefined"]}],"Create a Realistic Budget"]}]}],["$","div",null,{"ref":"$undefined","className":"p-6 pt-0","children":["$","p",null,{"className":"text-sm text-muted-foreground","children":"Track your income and expenses for a month to understand your spending habits. Use this information to create a budget that reflects your actual lifestyle, making it easier to stick to."}]}]]}],["$","div","1",{"ref":"$undefined","className":"rounded-lg border bg-card text-card-foreground shadow-sm hover:shadow-md transition-shadow duration-200","children":[["$","div",null,{"ref":"$undefined","className":"flex flex-col space-y-1.5 p-6 pb-2","children":["$","div",null,{"ref":"$undefined","className":"font-semibold tracking-tight text-base flex items-center gap-2 font-headline","children":[["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-lightbulb h-4 w-4 text-accent","children":[["$","path","1gvzjb",{"d":"M15 14c.2-1 .7-1.7 1.5-2.5 1-.9 1.5-2.2 1.5-3.5A6 6 0 0 0 6 8c0 1 .2 2.2 1.5 ******* 1.3 1.5 1.5 2.5"}],["$","path","x1upvd",{"d":"M9 18h6"}],["$","path","ceow96",{"d":"M10 22h4"}],"$undefined"]}],"The 50/30/20 Rule"]}]}],["$","div",null,{"ref":"$undefined","className":"p-6 pt-0","children":["$","p",null,{"className":"text-sm text-muted-foreground","children":"Allocate 50% of your income to needs (housing, food, transport), 30% to wants (entertainment, hobbies), and 20% to savings and debt repayment. Adjust percentages as needed."}]}]]}],["$","div","2",{"ref":"$undefined","className":"rounded-lg border bg-card text-card-foreground shadow-sm hover:shadow-md transition-shadow duration-200","children":[["$","div",null,{"ref":"$undefined","className":"flex flex-col space-y-1.5 p-6 pb-2","children":["$","div",null,{"ref":"$undefined","className":"font-semibold tracking-tight text-base flex items-center gap-2 font-headline","children":[["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-lightbulb h-4 w-4 text-accent","children":[["$","path","1gvzjb",{"d":"M15 14c.2-1 .7-1.7 1.5-2.5 1-.9 1.5-2.2 1.5-3.5A6 6 0 0 0 6 8c0 1 .2 2.2 1.5 ******* 1.3 1.5 1.5 2.5"}],["$","path","x1upvd",{"d":"M9 18h6"}],["$","path","ceow96",{"d":"M10 22h4"}],"$undefined"]}],"Set Financial Goals"]}]}],["$","div",null,{"ref":"$undefined","className":"p-6 pt-0","children":["$","p",null,{"className":"text-sm text-muted-foreground","children":"Define short-term (e.g., emergency fund) and long-term (e.g., retirement, house down payment) goals. Having clear goals provides motivation to manage your money effectively."}]}]]}],["$","div","3",{"ref":"$undefined","className":"rounded-lg border bg-card text-card-foreground shadow-sm hover:shadow-md transition-shadow duration-200","children":[["$","div",null,{"ref":"$undefined","className":"flex flex-col space-y-1.5 p-6 pb-2","children":["$","div",null,{"ref":"$undefined","className":"font-semibold tracking-tight text-base flex items-center gap-2 font-headline","children":[["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-lightbulb h-4 w-4 text-accent","children":[["$","path","1gvzjb",{"d":"M15 14c.2-1 .7-1.7 1.5-2.5 1-.9 1.5-2.2 1.5-3.5A6 6 0 0 0 6 8c0 1 .2 2.2 1.5 ******* 1.3 1.5 1.5 2.5"}],["$","path","x1upvd",{"d":"M9 18h6"}],["$","path","ceow96",{"d":"M10 22h4"}],"$undefined"]}],"Automate Your Savings"]}]}],["$","div",null,{"ref":"$undefined","className":"p-6 pt-0","children":["$","p",null,{"className":"text-sm text-muted-foreground","children":"Set up automatic transfers from your checking account to your savings account each payday. Treating savings like a bill ensures you consistently put money aside."}]}]]}],["$","div","4",{"ref":"$undefined","className":"rounded-lg border bg-card text-card-foreground shadow-sm hover:shadow-md transition-shadow duration-200","children":[["$","div",null,{"ref":"$undefined","className":"flex flex-col space-y-1.5 p-6 pb-2","children":["$","div",null,{"ref":"$undefined","className":"font-semibold tracking-tight text-base flex items-center gap-2 font-headline","children":[["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-lightbulb h-4 w-4 text-accent","children":[["$","path","1gvzjb",{"d":"M15 14c.2-1 .7-1.7 1.5-2.5 1-.9 1.5-2.2 1.5-3.5A6 6 0 0 0 6 8c0 1 .2 2.2 1.5 ******* 1.3 1.5 1.5 2.5"}],["$","path","x1upvd",{"d":"M9 18h6"}],["$","path","ceow96",{"d":"M10 22h4"}],"$undefined"]}],"Review Your Budget Regularly"]}]}],["$","div",null,{"ref":"$undefined","className":"p-6 pt-0","children":["$","p",null,{"className":"text-sm text-muted-foreground","children":"Life changes, and so should your budget. Review it monthly or quarterly to make adjustments for new income, expenses, or financial goals."}]}]]}],["$","div","5",{"ref":"$undefined","className":"rounded-lg border bg-card text-card-foreground shadow-sm hover:shadow-md transition-shadow duration-200","children":[["$","div",null,{"ref":"$undefined","className":"flex flex-col space-y-1.5 p-6 pb-2","children":["$","div",null,{"ref":"$undefined","className":"font-semibold tracking-tight text-base flex items-center gap-2 font-headline","children":[["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-lightbulb h-4 w-4 text-accent","children":[["$","path","1gvzjb",{"d":"M15 14c.2-1 .7-1.7 1.5-2.5 1-.9 1.5-2.2 1.5-3.5A6 6 0 0 0 6 8c0 1 .2 2.2 1.5 ******* 1.3 1.5 1.5 2.5"}],["$","path","x1upvd",{"d":"M9 18h6"}],["$","path","ceow96",{"d":"M10 22h4"}],"$undefined"]}],"Cut Unnecessary Expenses"]}]}],["$","div",null,{"ref":"$undefined","className":"p-6 pt-0","children":["$","p",null,{"className":"text-sm text-muted-foreground","children":"Identify non-essential spending like unused subscriptions or frequent dining out. Reducing these can free up significant cash for savings or debt."}]}]]}],["$","div","6",{"ref":"$undefined","className":"rounded-lg border bg-card text-card-foreground shadow-sm hover:shadow-md transition-shadow duration-200","children":[["$","div",null,{"ref":"$undefined","className":"flex flex-col space-y-1.5 p-6 pb-2","children":["$","div",null,{"ref":"$undefined","className":"font-semibold tracking-tight text-base flex items-center gap-2 font-headline","children":[["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-lightbulb h-4 w-4 text-accent","children":[["$","path","1gvzjb",{"d":"M15 14c.2-1 .7-1.7 1.5-2.5 1-.9 1.5-2.2 1.5-3.5A6 6 0 0 0 6 8c0 1 .2 2.2 1.5 ******* 1.3 1.5 1.5 2.5"}],["$","path","x1upvd",{"d":"M9 18h6"}],["$","path","ceow96",{"d":"M10 22h4"}],"$undefined"]}],"Plan for Irregular Expenses"]}]}],["$","div",null,{"ref":"$undefined","className":"p-6 pt-0","children":["$","p",null,{"className":"text-sm text-muted-foreground","children":"Don't forget annual or semi-annual bills like insurance premiums, car registration, or holiday gifts. Divide these by 12 and save that amount monthly."}]}]]}],["$","div","7",{"ref":"$undefined","className":"rounded-lg border bg-card text-card-foreground shadow-sm hover:shadow-md transition-shadow duration-200","children":[["$","div",null,{"ref":"$undefined","className":"flex flex-col space-y-1.5 p-6 pb-2","children":["$","div",null,{"ref":"$undefined","className":"font-semibold tracking-tight text-base flex items-center gap-2 font-headline","children":[["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-lightbulb h-4 w-4 text-accent","children":[["$","path","1gvzjb",{"d":"M15 14c.2-1 .7-1.7 1.5-2.5 1-.9 1.5-2.2 1.5-3.5A6 6 0 0 0 6 8c0 1 .2 2.2 1.5 ******* 1.3 1.5 1.5 2.5"}],["$","path","x1upvd",{"d":"M9 18h6"}],["$","path","ceow96",{"d":"M10 22h4"}],"$undefined"]}],"Use Cash for Certain Categories"]}]}],["$","div",null,{"ref":"$undefined","className":"p-6 pt-0","children":["$","p",null,{"className":"text-sm text-muted-foreground","children":"For categories where you tend to overspend (like entertainment or dining out), try the envelope system. Withdraw a set amount of cash for that category and stop spending when it's gone."}]}]]}],["$","div","8",{"ref":"$undefined","className":"rounded-lg border bg-card text-card-foreground shadow-sm hover:shadow-md transition-shadow duration-200","children":[["$","div",null,{"ref":"$undefined","className":"flex flex-col space-y-1.5 p-6 pb-2","children":["$","div",null,{"ref":"$undefined","className":"font-semibold tracking-tight text-base flex items-center gap-2 font-headline","children":[["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-lightbulb h-4 w-4 text-accent","children":[["$","path","1gvzjb",{"d":"M15 14c.2-1 .7-1.7 1.5-2.5 1-.9 1.5-2.2 1.5-3.5A6 6 0 0 0 6 8c0 1 .2 2.2 1.5 ******* 1.3 1.5 1.5 2.5"}],["$","path","x1upvd",{"d":"M9 18h6"}],["$","path","ceow96",{"d":"M10 22h4"}],"$undefined"]}],"Build an Emergency Fund"]}]}],["$","div",null,{"ref":"$undefined","className":"p-6 pt-0","children":["$","p",null,{"className":"text-sm text-muted-foreground","children":"Aim to save 3-6 months' worth of living expenses in an easily accessible account. This fund can cover unexpected job loss, medical bills, or urgent repairs without derailing your budget."}]}]]}],["$","div","9",{"ref":"$undefined","className":"rounded-lg border bg-card text-card-foreground shadow-sm hover:shadow-md transition-shadow duration-200","children":[["$","div",null,{"ref":"$undefined","className":"flex flex-col space-y-1.5 p-6 pb-2","children":["$","div",null,{"ref":"$undefined","className":"font-semibold tracking-tight text-base flex items-center gap-2 font-headline","children":[["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-lightbulb h-4 w-4 text-accent","children":[["$","path","1gvzjb",{"d":"M15 14c.2-1 .7-1.7 1.5-2.5 1-.9 1.5-2.2 1.5-3.5A6 6 0 0 0 6 8c0 1 .2 2.2 1.5 ******* 1.3 1.5 1.5 2.5"}],["$","path","x1upvd",{"d":"M9 18h6"}],["$","path","ceow96",{"d":"M10 22h4"}],"$undefined"]}],"Track Your Spending Diligently"]}]}],["$","div",null,{"ref":"$undefined","className":"p-6 pt-0","children":["$","p",null,{"className":"text-sm text-muted-foreground","children":"Use apps, spreadsheets, or a notebook to record every expense. This awareness helps you see where your money is going and identify areas for improvement."}]}]]}]]}]]}]]}],"$undefined",null,["$","$L9",null,{"children":["$La","$Lb",null]}]]}],{},null,false]},null,false]},null,false],["$","$1","h",{"children":[null,["$","$1","3CBtXyShNNtm9J3KtT65q",{"children":[["$","$Lc",null,{"children":"$Ld"}],null]}],["$","$Le",null,{"children":"$Lf"}]]}],false]],"m":"$undefined","G":["$10","$undefined"],"s":false,"S":true}
d:[["$","meta","0",{"charSet":"utf-8"}],["$","meta","1",{"name":"viewport","content":"width=device-width, initial-scale=1"}]]
a:null
b:null
f:[["$","title","0",{"children":"BudgetWise"}],["$","meta","1",{"name":"description","content":"Personal budget allocation made easy."}],["$","link","2",{"rel":"icon","href":"/favicon.ico","type":"image/x-icon","sizes":"16x16"}]]
