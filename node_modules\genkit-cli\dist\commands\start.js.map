{"version": 3, "file": "start.js", "sourceRoot": "", "sources": ["../../src/commands/start.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiBA,2DAA6D;AAC7D,yDAAuD;AACvD,iDAAsC;AACtC,yCAAoC;AACpC,qDAA8C;AAC9C,gDAAwB;AACxB,0DAAsD;AASzC,QAAA,KAAK,GAAG,IAAI,mBAAO,CAAC,OAAO,CAAC;KACtC,WAAW,CAAC,mCAAmC,CAAC;KAChD,MAAM,CAAC,YAAY,EAAE,yBAAyB,EAAE,KAAK,CAAC;KACtD,MAAM,CAAC,mBAAmB,EAAE,qBAAqB,CAAC;KAClD,MAAM,CAAC,YAAY,EAAE,iCAAiC,CAAC;KACvD,MAAM,CAAC,KAAK,EAAE,OAAmB,EAAE,EAAE;IAEpC,IAAI,cAAc,GAA4B,IAAA,4BAAY,EAAC,IAAI,CAAC,CAAC;IACjE,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;QAClB,IAAI,IAAY,CAAC;QACjB,IAAI,OAAO,CAAC,IAAI,EAAE,CAAC;YACjB,IAAI,GAAG,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YAC5B,IAAI,KAAK,CAAC,IAAI,CAAC,IAAI,IAAI,GAAG,CAAC,EAAE,CAAC;gBAC5B,cAAM,CAAC,KAAK,CAAC,IAAI,OAAO,CAAC,IAAI,8BAA8B,CAAC,CAAC;gBAC7D,OAAO;YACT,CAAC;QACH,CAAC;aAAM,CAAC;YACN,IAAI,GAAG,MAAM,IAAA,kBAAO,EAAC,EAAE,IAAI,EAAE,IAAA,oBAAS,EAAC,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;QACxD,CAAC;QACD,cAAc,GAAG,cAAc,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,EAAE;YAC/C,IAAA,oBAAW,EAAC,OAAO,EAAE,IAAI,CAAC,CAAC;YAC3B,OAAO,OAAO,CAAC;QACjB,CAAC,CAAC,CAAC;QACH,IAAI,OAAO,CAAC,IAAI,EAAE,CAAC;YACjB,IAAA,cAAI,EAAC,oBAAoB,IAAI,EAAE,CAAC,CAAC;QACnC,CAAC;IACH,CAAC;IACD,MAAM,cAAc,CAAC,IAAI,CAAC,CAAC,OAAuB,EAAE,EAAE;QACpD,MAAM,kBAAkB,GAAG,OAAO,EAAE,kBAAkB,CAAC;QACvD,OAAO,YAAY,CAAC,kBAAkB,CAAC,CAAC;IAC1C,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AAEL,KAAK,UAAU,YAAY,CAAC,kBAA2B;IACrD,IAAI,aAAK,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QAC1B,OAAO,IAAI,OAAO,CAAC,CAAC,WAAW,EAAE,MAAM,EAAE,EAAE;YACzC,MAAM,UAAU,GAAG,IAAA,qBAAK,EAAC,aAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,aAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE;gBAC3D,GAAG,EAAE;oBACH,GAAG,OAAO,CAAC,GAAG;oBACd,uBAAuB,EAAE,kBAAkB;oBAC3C,UAAU,EAAE,KAAK;iBAClB;gBACD,KAAK,EAAE,OAAO,CAAC,QAAQ,KAAK,OAAO;aACpC,CAAC,CAAC;YAEH,MAAM,aAAa,GAAG,OAAO,CAAC,KAAK,CAAC;YACpC,UAAU,CAAC,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;YACxC,UAAU,CAAC,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;YACxC,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;YAEtC,UAAU,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,KAAK,EAAQ,EAAE;gBACrC,OAAO,CAAC,GAAG,CAAC,yBAAyB,KAAK,EAAE,CAAC,CAAC;gBAC9C,MAAM,CAAC,KAAK,CAAC,CAAC;gBACd,OAAO,CAAC,QAAQ,GAAG,CAAC,CAAC;YACvB,CAAC,CAAC,CAAC;YACH,UAAU,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,EAAE;gBAC7B,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;gBACnC,IAAI,IAAI,KAAK,CAAC,EAAE,CAAC;oBACf,WAAW,CAAC,SAAS,CAAC,CAAC;gBACzB,CAAC;qBAAM,CAAC;oBACN,MAAM,CAAC,IAAI,KAAK,CAAC,gCAAgC,IAAI,EAAE,CAAC,CAAC,CAAC;gBAC5D,CAAC;YACH,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;IACD,OAAO,IAAI,OAAO,CAAC,GAAG,EAAE,GAAE,CAAC,CAAC,CAAC;AAC/B,CAAC"}