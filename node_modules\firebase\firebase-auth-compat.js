((e,t)=>{"object"==typeof exports&&"undefined"!=typeof module?t(require("@firebase/app-compat"),require("@firebase/app")):"function"==typeof define&&define.amd?define(["@firebase/app-compat","@firebase/app"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).firebase,e.firebase.INTERNAL.modularAPIs)})(this,function(Gi,Ki){try{!(function(){function t(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var V=t(Gi);let r=()=>{},x={byteToCharMap_:null,charToByteMap_:null,byteToCharMapWebSafe_:null,charToByteMapWebSafe_:null,ENCODED_VALS_BASE:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789",get ENCODED_VALS(){return this.ENCODED_VALS_BASE+"+/="},get ENCODED_VALS_WEBSAFE(){return this.ENCODED_VALS_BASE+"-_."},HAS_NATIVE_SUPPORT:"function"==typeof atob,encodeByteArray(r,e){if(!Array.isArray(r))throw Error("encodeByteArray takes an array as a parameter");this.init_();var n=e?this.byteToCharMapWebSafe_:this.byteToCharMap_,i=[];for(let d=0;d<r.length;d+=3){var s=r[d],a=d+1<r.length,o=a?r[d+1]:0,l=d+2<r.length,c=l?r[d+2]:0;let e=(15&o)<<2|c>>6,t=63&c;l||(t=64,a)||(e=64),i.push(n[s>>2],n[(3&s)<<4|o>>4],n[e],n[t])}return i.join("")},encodeString(e,t){return this.HAS_NATIVE_SUPPORT&&!t?btoa(e):this.encodeByteArray((t=>{var r=[];let n=0;for(let i=0;i<t.length;i++){let e=t.charCodeAt(i);e<128?r[n++]=e:(e<2048?r[n++]=e>>6|192:(55296==(64512&e)&&i+1<t.length&&56320==(64512&t.charCodeAt(i+1))?(e=65536+((1023&e)<<10)+(1023&t.charCodeAt(++i)),r[n++]=e>>18|240,r[n++]=e>>12&63|128):r[n++]=e>>12|224,r[n++]=e>>6&63|128),r[n++]=63&e|128)}return r})(e),t)},decodeString(r,n){if(this.HAS_NATIVE_SUPPORT&&!n)return atob(r);{var i=this.decodeStringToByteArray(r,n);var s=[];let e=0,t=0;for(;e<i.length;){var a,o,l,c=i[e++];c<128?s[t++]=String.fromCharCode(c):191<c&&c<224?(a=i[e++],s[t++]=String.fromCharCode((31&c)<<6|63&a)):239<c&&c<365?(a=((7&c)<<18|(63&i[e++])<<12|(63&i[e++])<<6|63&i[e++])-65536,s[t++]=String.fromCharCode(55296+(a>>10)),s[t++]=String.fromCharCode(56320+(1023&a))):(o=i[e++],l=i[e++],s[t++]=String.fromCharCode((15&c)<<12|(63&o)<<6|63&l))}return s.join("");return}},decodeStringToByteArray(e,t){this.init_();var r=t?this.charToByteMapWebSafe_:this.charToByteMap_,n=[];for(let l=0;l<e.length;){var i=r[e.charAt(l++)],s=l<e.length?r[e.charAt(l)]:0,a=++l<e.length?r[e.charAt(l)]:64,o=++l<e.length?r[e.charAt(l)]:64;if(++l,null==i||null==s||null==a||null==o)throw new j;n.push(i<<2|s>>4),64!==a&&(n.push(s<<4&240|a>>2),64!==o)&&n.push(a<<6&192|o)}return n},init_(){if(!this.byteToCharMap_){this.byteToCharMap_={},this.charToByteMap_={},this.byteToCharMapWebSafe_={},this.charToByteMapWebSafe_={};for(let e=0;e<this.ENCODED_VALS.length;e++)this.byteToCharMap_[e]=this.ENCODED_VALS.charAt(e),this.charToByteMap_[this.byteToCharMap_[e]]=e,this.byteToCharMapWebSafe_[e]=this.ENCODED_VALS_WEBSAFE.charAt(e),(this.charToByteMapWebSafe_[this.byteToCharMapWebSafe_[e]]=e)>=this.ENCODED_VALS_BASE.length&&(this.charToByteMap_[this.ENCODED_VALS_WEBSAFE.charAt(e)]=e,this.charToByteMapWebSafe_[this.ENCODED_VALS.charAt(e)]=e)}}};class j extends Error{constructor(){super(...arguments),this.name="DecodeBase64StringError"}}let H=function(e){try{return x.decodeString(e,!0)}catch(e){console.error("base64Decode failed: ",e)}return null};let W=()=>(()=>{if("undefined"!=typeof self)return self;if("undefined"!=typeof window)return window;if("undefined"!=typeof global)return global;throw new Error("Unable to locate global object.")})().__FIREBASE_DEFAULTS__,q=()=>{var e;return"undefined"!=typeof process&&void 0!==process.env&&(e=process.env.__FIREBASE_DEFAULTS__)?JSON.parse(e):void 0},B=()=>{if("undefined"!=typeof document){let e;try{e=document.cookie.match(/__FIREBASE_DEFAULTS__=([^;]+)/)}catch(e){return}var t=e&&H(e[1]);return t&&JSON.parse(t)}},z=()=>{try{return r()||W()||q()||B()}catch(e){console.info("Unable to get __FIREBASE_DEFAULTS__ due to: "+e)}};var n;function G(e){return e.endsWith(".cloudworkstations.dev")}let K={};let J=!1;function Y(e,t){if("undefined"!=typeof window&&"undefined"!=typeof document&&G(window.location.host)&&K[e]!==t&&!K[e]&&!J){K[e]=t;let c="__firebase__banner";let d=0<(()=>{var e,t={prod:[],emulator:[]};for(e of Object.keys(K))(K[e]?t.emulator:t.prod).push(e);return t})().prod.length;function u(e){return"__firebase__banner__"+e}function h(){var e=document.createElement("span");return e.style.cursor="pointer",e.style.marginLeft="16px",e.style.fontSize="24px",e.innerHTML=" &times;",e.onclick=()=>{var e;J=!0,(e=document.getElementById(c))&&e.remove()},e}function r(){var e,t,r=(e=>{let t=document.getElementById(e),r=!1;return t||((t=document.createElement("div")).setAttribute("id",e),r=!0),{created:r,element:t}})(c),n=u("text"),i=document.getElementById(n)||document.createElement("span"),s=u("learnmore"),a=document.getElementById(s)||document.createElement("a"),o=u("preprendIcon"),l=document.getElementById(o)||document.createElementNS("http://www.w3.org/2000/svg","svg");r.created&&(r=r.element,(t=r).style.display="flex",t.style.background="#7faaf0",t.style.position="fixed",t.style.bottom="5px",t.style.left="5px",t.style.padding=".5em",t.style.borderRadius="5px",t.style.alignItems="center",(t=a).setAttribute("id",s),t.innerText="Learn more",t.href="https://firebase.google.com/docs/studio/preview-apps#preview-backend",t.setAttribute("target","__blank"),t.style.paddingLeft="5px",t.style.textDecoration="underline",s=h(),t=o,(e=l).setAttribute("width","24"),e.setAttribute("id",t),e.setAttribute("height","24"),e.setAttribute("viewBox","0 0 24 24"),e.setAttribute("fill","none"),e.style.marginLeft="-6px",r.append(l,i,a,s),document.body.appendChild(r)),d?(i.innerText="Preview backend disconnected.",l.innerHTML=`<g clip-path="url(#clip0_6013_33858)">
<path d="M4.8 17.6L12 5.6L19.2 17.6H4.8ZM6.91667 16.4H17.0833L12 7.93333L6.91667 16.4ZM12 15.6C12.1667 15.6 12.3056 15.5444 12.4167 15.4333C12.5389 15.3111 12.6 15.1667 12.6 15C12.6 14.8333 12.5389 14.6944 12.4167 14.5833C12.3056 14.4611 12.1667 14.4 12 14.4C11.8333 14.4 11.6889 14.4611 11.5667 14.5833C11.4556 14.6944 11.4 14.8333 11.4 15C11.4 15.1667 11.4556 15.3111 11.5667 15.4333C11.6889 15.5444 11.8333 15.6 12 15.6ZM11.4 13.6H12.6V10.4H11.4V13.6Z" fill="#212121"/>
</g>
<defs>
<clipPath id="clip0_6013_33858">
<rect width="24" height="24" fill="white"/>
</clipPath>
</defs>`):(l.innerHTML=`<g clip-path="url(#clip0_6083_34804)">
<path d="M11.4 15.2H12.6V11.2H11.4V15.2ZM12 10C12.1667 10 12.3056 9.94444 12.4167 9.83333C12.5389 9.71111 12.6 9.56667 12.6 9.4C12.6 9.23333 12.5389 9.09444 12.4167 8.98333C12.3056 8.86111 12.1667 8.8 12 8.8C11.8333 8.8 11.6889 8.86111 11.5667 8.98333C11.4556 9.09444 11.4 9.23333 11.4 9.4C11.4 9.56667 11.4556 9.71111 11.5667 9.83333C11.6889 9.94444 11.8333 10 12 10ZM12 18.4C11.1222 18.4 10.2944 18.2333 9.51667 17.9C8.73889 17.5667 8.05556 17.1111 7.46667 16.5333C6.88889 15.9444 6.43333 15.2611 6.1 14.4833C5.76667 13.7056 5.6 12.8778 5.6 12C5.6 11.1111 5.76667 10.2833 6.1 9.51667C6.43333 8.73889 6.88889 8.06111 7.46667 7.48333C8.05556 6.89444 8.73889 6.43333 9.51667 6.1C10.2944 5.76667 11.1222 5.6 12 5.6C12.8889 5.6 13.7167 5.76667 14.4833 6.1C15.2611 6.43333 15.9389 6.89444 16.5167 7.48333C17.1056 8.06111 17.5667 8.73889 17.9 9.51667C18.2333 10.2833 18.4 11.1111 18.4 12C18.4 12.8778 18.2333 13.7056 17.9 14.4833C17.5667 15.2611 17.1056 15.9444 16.5167 16.5333C15.9389 17.1111 15.2611 17.5667 14.4833 17.9C13.7167 18.2333 12.8889 18.4 12 18.4ZM12 17.2C13.4444 17.2 14.6722 16.6944 15.6833 15.6833C16.6944 14.6722 17.2 13.4444 17.2 12C17.2 10.5556 16.6944 9.32778 15.6833 8.31667C14.6722 7.30555 13.4444 6.8 12 6.8C10.5556 6.8 9.32778 7.30555 8.31667 8.31667C7.30556 9.32778 6.8 10.5556 6.8 12C6.8 13.4444 7.30556 14.6722 8.31667 15.6833C9.32778 16.6944 10.5556 17.2 12 17.2Z" fill="#212121"/>
</g>
<defs>
<clipPath id="clip0_6083_34804">
<rect width="24" height="24" fill="white"/>
</clipPath>
</defs>`,i.innerText="Preview backend running in this workspace."),i.setAttribute("id",n)}"loading"===document.readyState?window.addEventListener("DOMContentLoaded",r):r()}}function c(){return"undefined"!=typeof navigator&&"string"==typeof navigator.userAgent?navigator.userAgent:""}function $(){var e=null==(e=z())?void 0:e.forceEnvironment;if("node"===e)return!0;if("browser"===e)return!1;try{return"[object process]"===Object.prototype.toString.call(global.process)}catch(e){return!1}}function X(){var e="object"==typeof chrome?chrome.runtime:"object"==typeof browser?browser.runtime:void 0;return"object"==typeof e&&void 0!==e.id}function Z(){return"object"==typeof navigator&&"ReactNative"===navigator.product}function Q(){var e=c();return 0<=e.indexOf("MSIE ")||0<=e.indexOf("Trident/")}function ee(){try{return"object"==typeof indexedDB}catch(e){return!1}}class d extends Error{constructor(e,t,r){super(t),this.code=e,this.customData=r,this.name="FirebaseError",Object.setPrototypeOf(this,d.prototype),Error.captureStackTrace&&Error.captureStackTrace(this,te.prototype.create)}}class te{constructor(e,t,r){this.service=e,this.serviceName=t,this.errors=r}create(e,...t){var n,r=t[0]||{},i=this.service+"/"+e,s=this.errors[e],s=s?(n=r,s.replace(re,(e,t)=>{var r=n[t];return null!=r?String(r):`<${t}?>`})):"Error",s=this.serviceName+`: ${s} (${i}).`;return new d(i,s,r)}}let re=/\{\$([^}]+)}/g;function ne(e,t){if(e!==t){var r,n,i=Object.keys(e),s=Object.keys(t);for(r of i){if(!s.includes(r))return!1;var a=e[r],o=t[r];if(ie(a)&&ie(o)){if(!ne(a,o))return!1}else if(a!==o)return!1}for(n of s)if(!i.includes(n))return!1}return!0}function ie(e){return null!==e&&"object"==typeof e}function se(e){let t=[];for(let[r,n]of Object.entries(e))Array.isArray(n)?n.forEach(e=>{t.push(encodeURIComponent(r)+"="+encodeURIComponent(e))}):t.push(encodeURIComponent(r)+"="+encodeURIComponent(n));return t.length?"&"+t.join("&"):""}function ae(e){let n={};return e.replace(/^\?/,"").split("&").forEach(e=>{var t,r;e&&([t,r]=e.split("="),n[decodeURIComponent(t)]=decodeURIComponent(r))}),n}function oe(e){var t,r=e.indexOf("?");return r?(t=e.indexOf("#",r),e.substring(r,0<t?t:void 0)):""}class le{constructor(e,t){this.observers=[],this.unsubscribes=[],this.observerCount=0,this.task=Promise.resolve(),this.finalized=!1,this.onNoObservers=t,this.task.then(()=>{e(this)}).catch(e=>{this.error(e)})}next(t){this.forEachObserver(e=>{e.next(t)})}error(t){this.forEachObserver(e=>{e.error(t)}),this.close(t)}complete(){this.forEachObserver(e=>{e.complete()}),this.close()}subscribe(e,t,r){let n;if(void 0===e&&void 0===t&&void 0===r)throw new Error("Missing Observer.");void 0===(n=((e,t)=>{if("object"==typeof e&&null!==e)for(var r of t)if(r in e&&"function"==typeof e[r])return 1})(e,["next","error","complete"])?e:{next:e,error:t,complete:r}).next&&(n.next=ce),void 0===n.error&&(n.error=ce),void 0===n.complete&&(n.complete=ce);var i=this.unsubscribeOne.bind(this,this.observers.length);return this.finalized&&this.task.then(()=>{try{this.finalError?n.error(this.finalError):n.complete()}catch(e){}}),this.observers.push(n),i}unsubscribeOne(e){void 0!==this.observers&&void 0!==this.observers[e]&&(delete this.observers[e],--this.observerCount,0===this.observerCount)&&void 0!==this.onNoObservers&&this.onNoObservers(this)}forEachObserver(t){if(!this.finalized)for(let e=0;e<this.observers.length;e++)this.sendOne(e,t)}sendOne(e,t){this.task.then(()=>{if(void 0!==this.observers&&void 0!==this.observers[e])try{t(this.observers[e])}catch(e){"undefined"!=typeof console&&console.error&&console.error(e)}})}close(e){this.finalized||(this.finalized=!0,void 0!==e&&(this.finalError=e),this.task.then(()=>{this.observers=void 0,this.onNoObservers=void 0}))}}function ce(){}function o(e){return e&&e._delegate?e._delegate:e}(e=n=n||{})[e.DEBUG=0]="DEBUG",e[e.VERBOSE=1]="VERBOSE",e[e.INFO=2]="INFO",e[e.WARN=3]="WARN",e[e.ERROR=4]="ERROR",e[e.SILENT=5]="SILENT";let de={debug:n.DEBUG,verbose:n.VERBOSE,info:n.INFO,warn:n.WARN,error:n.ERROR,silent:n.SILENT},ue=n.INFO,he={[n.DEBUG]:"log",[n.VERBOSE]:"log",[n.INFO]:"info",[n.WARN]:"warn",[n.ERROR]:"error"},pe=(e,t,...r)=>{if(!(t<e.logLevel)){var n=(new Date).toISOString(),i=he[t];if(!i)throw new Error(`Attempted to log a message with an invalid logType (value: ${t})`);console[i](`[${n}]  ${e.name}:`,...r)}};function me(e,t){var r={};for(i in e)Object.prototype.hasOwnProperty.call(e,i)&&t.indexOf(i)<0&&(r[i]=e[i]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var n=0,i=Object.getOwnPropertySymbols(e);n<i.length;n++)t.indexOf(i[n])<0&&Object.prototype.propertyIsEnumerable.call(e,i[n])&&(r[i[n]]=e[i[n]]);return r}class ge{constructor(e,t,r){this.name=e,this.instanceFactory=t,this.type=r,this.multipleInstances=!1,this.serviceProps={},this.instantiationMode="LAZY",this.onInstanceCreated=null}setInstantiationMode(e){return this.instantiationMode=e,this}setMultipleInstances(e){return this.multipleInstances=e,this}setServiceProps(e){return this.serviceProps=e,this}setInstanceCreatedCallback(e){return this.onInstanceCreated=e,this}}let fe={FACEBOOK:"facebook.com",GITHUB:"github.com",GOOGLE:"google.com",PASSWORD:"password",PHONE:"phone",TWITTER:"twitter.com"},ve={EMAIL_SIGNIN:"EMAIL_SIGNIN",PASSWORD_RESET:"PASSWORD_RESET",RECOVER_EMAIL:"RECOVER_EMAIL",REVERT_SECOND_FACTOR_ADDITION:"REVERT_SECOND_FACTOR_ADDITION",VERIFY_AND_CHANGE_EMAIL:"VERIFY_AND_CHANGE_EMAIL",VERIFY_EMAIL:"VERIFY_EMAIL"};function _e(){return{"dependent-sdk-initialized-before-auth":"Another Firebase SDK was initialized and is trying to use Auth before Auth is initialized. Please be sure to call `initializeAuth` or `getAuth` before starting any other Firebase SDK."}}function ye(){return{"admin-restricted-operation":"This operation is restricted to administrators only.","argument-error":"","app-not-authorized":"This app, identified by the domain where it's hosted, is not authorized to use Firebase Authentication with the provided API key. Review your key configuration in the Google API console.","app-not-installed":"The requested mobile application corresponding to the identifier (Android package name or iOS bundle ID) provided is not installed on this device.","captcha-check-failed":"The reCAPTCHA response token provided is either invalid, expired, already used or the domain associated with it does not match the list of whitelisted domains.","code-expired":"The SMS code has expired. Please re-send the verification code to try again.","cordova-not-ready":"Cordova framework is not ready.","cors-unsupported":"This browser is not supported.","credential-already-in-use":"This credential is already associated with a different user account.","custom-token-mismatch":"The custom token corresponds to a different audience.","requires-recent-login":"This operation is sensitive and requires recent authentication. Log in again before retrying this request.","dependent-sdk-initialized-before-auth":"Another Firebase SDK was initialized and is trying to use Auth before Auth is initialized. Please be sure to call `initializeAuth` or `getAuth` before starting any other Firebase SDK.","dynamic-link-not-activated":"Please activate Dynamic Links in the Firebase Console and agree to the terms and conditions.","email-change-needs-verification":"Multi-factor users must always have a verified email.","email-already-in-use":"The email address is already in use by another account.","emulator-config-failed":'Auth instance has already been used to make a network call. Auth can no longer be configured to use the emulator. Try calling "connectAuthEmulator()" sooner.',"expired-action-code":"The action code has expired.","cancelled-popup-request":"This operation has been cancelled due to another conflicting popup being opened.","internal-error":"An internal AuthError has occurred.","invalid-app-credential":"The phone verification request contains an invalid application verifier. The reCAPTCHA token response is either invalid or expired.","invalid-app-id":"The mobile app identifier is not registered for the current project.","invalid-user-token":"This user's credential isn't valid for this project. This can happen if the user's token has been tampered with, or if the user isn't for the project associated with this API key.","invalid-auth-event":"An internal AuthError has occurred.","invalid-verification-code":"The SMS verification code used to create the phone auth credential is invalid. Please resend the verification code sms and be sure to use the verification code provided by the user.","invalid-continue-uri":"The continue URL provided in the request is invalid.","invalid-cordova-configuration":"The following Cordova plugins must be installed to enable OAuth sign-in: cordova-plugin-buildinfo, cordova-universal-links-plugin, cordova-plugin-browsertab, cordova-plugin-inappbrowser and cordova-plugin-customurlscheme.","invalid-custom-token":"The custom token format is incorrect. Please check the documentation.","invalid-dynamic-link-domain":"The provided dynamic link domain is not configured or authorized for the current project.","invalid-email":"The email address is badly formatted.","invalid-emulator-scheme":"Emulator URL must start with a valid scheme (http:// or https://).","invalid-api-key":"Your API key is invalid, please check you have copied it correctly.","invalid-cert-hash":"The SHA-1 certificate hash provided is invalid.","invalid-credential":"The supplied auth credential is incorrect, malformed or has expired.","invalid-message-payload":"The email template corresponding to this action contains invalid characters in its message. Please fix by going to the Auth email templates section in the Firebase Console.","invalid-multi-factor-session":"The request does not contain a valid proof of first factor successful sign-in.","invalid-oauth-provider":"EmailAuthProvider is not supported for this operation. This operation only supports OAuth providers.","invalid-oauth-client-id":"The OAuth client ID provided is either invalid or does not match the specified API key.","unauthorized-domain":"This domain is not authorized for OAuth operations for your Firebase project. Edit the list of authorized domains from the Firebase console.","invalid-action-code":"The action code is invalid. This can happen if the code is malformed, expired, or has already been used.","wrong-password":"The password is invalid or the user does not have a password.","invalid-persistence-type":"The specified persistence type is invalid. It can only be local, session or none.","invalid-phone-number":"The format of the phone number provided is incorrect. Please enter the phone number in a format that can be parsed into E.164 format. E.164 phone numbers are written in the format [+][country code][subscriber number including area code].","invalid-provider-id":"The specified provider ID is invalid.","invalid-recipient-email":"The email corresponding to this action failed to send as the provided recipient email address is invalid.","invalid-sender":"The email template corresponding to this action contains an invalid sender email or name. Please fix by going to the Auth email templates section in the Firebase Console.","invalid-verification-id":"The verification ID used to create the phone auth credential is invalid.","invalid-tenant-id":"The Auth instance's tenant ID is invalid.","login-blocked":"Login blocked by user-provided method: {$originalMessage}","missing-android-pkg-name":"An Android Package Name must be provided if the Android App is required to be installed.","auth-domain-config-required":"Be sure to include authDomain when calling firebase.initializeApp(), by following the instructions in the Firebase console.","missing-app-credential":"The phone verification request is missing an application verifier assertion. A reCAPTCHA response token needs to be provided.","missing-verification-code":"The phone auth credential was created with an empty SMS verification code.","missing-continue-uri":"A continue URL must be provided in the request.","missing-iframe-start":"An internal AuthError has occurred.","missing-ios-bundle-id":"An iOS Bundle ID must be provided if an App Store ID is provided.","missing-or-invalid-nonce":"The request does not contain a valid nonce. This can occur if the SHA-256 hash of the provided raw nonce does not match the hashed nonce in the ID token payload.","missing-password":"A non-empty password must be provided","missing-multi-factor-info":"No second factor identifier is provided.","missing-multi-factor-session":"The request is missing proof of first factor successful sign-in.","missing-phone-number":"To send verification codes, provide a phone number for the recipient.","missing-verification-id":"The phone auth credential was created with an empty verification ID.","app-deleted":"This instance of FirebaseApp has been deleted.","multi-factor-info-not-found":"The user does not have a second factor matching the identifier provided.","multi-factor-auth-required":"Proof of ownership of a second factor is required to complete sign-in.","account-exists-with-different-credential":"An account already exists with the same email address but different sign-in credentials. Sign in using a provider associated with this email address.","network-request-failed":"A network AuthError (such as timeout, interrupted connection or unreachable host) has occurred.","no-auth-event":"An internal AuthError has occurred.","no-such-provider":"User was not linked to an account with the given provider.","null-user":"A null user object was provided as the argument for an operation which requires a non-null user object.","operation-not-allowed":"The given sign-in provider is disabled for this Firebase project. Enable it in the Firebase console, under the sign-in method tab of the Auth section.","operation-not-supported-in-this-environment":'This operation is not supported in the environment this application is running on. "location.protocol" must be http, https or chrome-extension and web storage must be enabled.',"popup-blocked":"Unable to establish a connection with the popup. It may have been blocked by the browser.","popup-closed-by-user":"The popup has been closed by the user before finalizing the operation.","provider-already-linked":"User can only be linked to one identity for the given provider.","quota-exceeded":"The project's quota for this operation has been exceeded.","redirect-cancelled-by-user":"The redirect operation has been cancelled by the user before finalizing.","redirect-operation-pending":"A redirect sign-in operation is already pending.","rejected-credential":"The request contains malformed or mismatching credentials.","second-factor-already-in-use":"The second factor is already enrolled on this account.","maximum-second-factor-count-exceeded":"The maximum allowed number of second factors on a user has been exceeded.","tenant-id-mismatch":"The provided tenant ID does not match the Auth instance's tenant ID",timeout:"The operation has timed out.","user-token-expired":"The user's credential is no longer valid. The user must sign in again.","too-many-requests":"We have blocked all requests from this device due to unusual activity. Try again later.","unauthorized-continue-uri":"The domain of the continue URL is not whitelisted.  Please whitelist the domain in the Firebase console.","unsupported-first-factor":"Enrolling a second factor or signing in with a multi-factor account requires sign-in with a supported first factor.","unsupported-persistence-type":"The current environment does not support the specified persistence type.","unsupported-tenant-operation":"This operation is not supported in a multi-tenant context.","unverified-email":"The operation requires a verified email.","user-cancelled":"The user did not grant your application the permissions it requested.","user-not-found":"There is no user record corresponding to this identifier. The user may have been deleted.","user-disabled":"The user account has been disabled by an administrator.","user-mismatch":"The supplied credentials do not correspond to the previously signed in user.","user-signed-out":"","weak-password":"The password must be 6 characters long or more.","web-storage-unsupported":"This browser is not supported or 3rd party cookies and data may be disabled.","already-initialized":"initializeAuth() has already been called with different options. To avoid this error, call initializeAuth() with the same options as when it was originally called, or call getAuth() to return the already initialized instance.","missing-recaptcha-token":"The reCAPTCHA token is missing when sending request to the backend.","invalid-recaptcha-token":"The reCAPTCHA token is invalid when sending request to the backend.","invalid-recaptcha-action":"The reCAPTCHA action is invalid when sending request to the backend.","recaptcha-not-enabled":"reCAPTCHA Enterprise integration is not enabled for this project.","missing-client-type":"The reCAPTCHA client type is missing when sending request to the backend.","missing-recaptcha-version":"The reCAPTCHA version is missing when sending request to the backend.","invalid-req-type":"Invalid request parameters.","invalid-recaptcha-version":"The reCAPTCHA version is invalid when sending request to the backend.","unsupported-password-policy-schema-version":"The password policy received from the backend uses a schema version that is not supported by this version of the Firebase SDK.","password-does-not-meet-requirements":"The password does not meet the requirements.","invalid-hosting-link-domain":"The provided Hosting link domain is not configured in Firebase Hosting or is not owned by the current project. This cannot be a default Hosting domain (`web.app` or `firebaseapp.com`)."}}let Ie=_e,we=new te("auth","Firebase",_e()),Te=new class{constructor(e){this.name=e,this._logLevel=ue,this._logHandler=pe,this._userLogHandler=null}get logLevel(){return this._logLevel}set logLevel(e){if(!(e in n))throw new TypeError(`Invalid value "${e}" assigned to \`logLevel\``);this._logLevel=e}setLogLevel(e){this._logLevel="string"==typeof e?de[e]:e}get logHandler(){return this._logHandler}set logHandler(e){if("function"!=typeof e)throw new TypeError("Value assigned to `logHandler` must be a function");this._logHandler=e}get userLogHandler(){return this._userLogHandler}set userLogHandler(e){this._userLogHandler=e}debug(...e){this._userLogHandler&&this._userLogHandler(this,n.DEBUG,...e),this._logHandler(this,n.DEBUG,...e)}log(...e){this._userLogHandler&&this._userLogHandler(this,n.VERBOSE,...e),this._logHandler(this,n.VERBOSE,...e)}info(...e){this._userLogHandler&&this._userLogHandler(this,n.INFO,...e),this._logHandler(this,n.INFO,...e)}warn(...e){this._userLogHandler&&this._userLogHandler(this,n.WARN,...e),this._logHandler(this,n.WARN,...e)}error(...e){this._userLogHandler&&this._userLogHandler(this,n.ERROR,...e),this._logHandler(this,n.ERROR,...e)}}("@firebase/auth");function Ee(e,...t){Te.logLevel<=n.ERROR&&Te.error(`Auth (${Ki.SDK_VERSION}): `+e,...t)}function u(e,...t){throw Se(e,...t)}function h(e,...t){return Se(e,...t)}function be(e,t,r){var n=Object.assign(Object.assign({},Ie()),{[t]:r});return new te("auth","Firebase",n).create(t,{appName:e.name})}function l(e){return be(e,"operation-not-supported-in-this-environment","Operations that alter the current user are not supported in conjunction with FirebaseServerApp")}function ke(e,t,r){var n=r;if(!(t instanceof n))throw n.name!==t.constructor.name&&u(e,"argument-error"),be(e,"argument-error",`Type of ${t.constructor.name} does not match expected instance.`+"Did you pass a reference from a different Auth SDK?")}function Se(e,...t){var r,n;return"string"!=typeof e?(r=t[0],(n=[...t.slice(1)])[0]&&(n[0].appName=e.name),e._errorFactory.create(r,...n)):we.create(e,...t)}function g(e,t,...r){if(!e)throw Se(t,...r)}function i(e){var t="INTERNAL ASSERTION FAILED: "+e;throw Ee(t),new Error(t)}function a(e,t){e||i(t)}function Re(){var e;return"undefined"!=typeof self&&(null==(e=self.location)?void 0:e.href)||""}function Ae(){return"http:"===Pe()||"https:"===Pe()}function Pe(){var e;return"undefined"!=typeof self&&(null==(e=self.location)?void 0:e.protocol)||null}class Ce{constructor(e,t){a((this.shortDelay=e)<(this.longDelay=t),"Short delay should be less than long delay!"),this.isMobile="undefined"!=typeof window&&!!(window.cordova||window.phonegap||window.PhoneGap)&&/ios|iphone|ipod|ipad|android|blackberry|iemobile/i.test(c())||Z()}get(){return"undefined"!=typeof navigator&&navigator&&"onLine"in navigator&&"boolean"==typeof navigator.onLine&&(Ae()||X()||"connection"in navigator)&&!navigator.onLine?Math.min(5e3,this.shortDelay):this.isMobile?this.longDelay:this.shortDelay}}function Oe(e,t){a(e.emulator,"Emulator should always be set here");var r=e.emulator.url;return t?""+r+(t.startsWith("/")?t.slice(1):t):r}class Ne{static initialize(e,t,r){this.fetchImpl=e,t&&(this.headersImpl=t),r&&(this.responseImpl=r)}static fetch(){return this.fetchImpl||("undefined"!=typeof self&&"fetch"in self?self.fetch:"undefined"!=typeof globalThis&&globalThis.fetch?globalThis.fetch:"undefined"!=typeof fetch?fetch:void i("Could not find fetch implementation, make sure you call FetchProvider.initialize() with an appropriate polyfill"))}static headers(){return this.headersImpl||("undefined"!=typeof self&&"Headers"in self?self.Headers:"undefined"!=typeof globalThis&&globalThis.Headers?globalThis.Headers:"undefined"!=typeof Headers?Headers:void i("Could not find Headers implementation, make sure you call FetchProvider.initialize() with an appropriate polyfill"))}static response(){return this.responseImpl||("undefined"!=typeof self&&"Response"in self?self.Response:"undefined"!=typeof globalThis&&globalThis.Response?globalThis.Response:"undefined"!=typeof Response?Response:void i("Could not find Response implementation, make sure you call FetchProvider.initialize() with an appropriate polyfill"))}}let Le={CREDENTIAL_MISMATCH:"custom-token-mismatch",MISSING_CUSTOM_TOKEN:"internal-error",INVALID_IDENTIFIER:"invalid-email",MISSING_CONTINUE_URI:"internal-error",INVALID_PASSWORD:"wrong-password",MISSING_PASSWORD:"missing-password",INVALID_LOGIN_CREDENTIALS:"invalid-credential",EMAIL_EXISTS:"email-already-in-use",PASSWORD_LOGIN_DISABLED:"operation-not-allowed",INVALID_IDP_RESPONSE:"invalid-credential",INVALID_PENDING_TOKEN:"invalid-credential",FEDERATED_USER_ID_ALREADY_LINKED:"credential-already-in-use",MISSING_REQ_TYPE:"internal-error",EMAIL_NOT_FOUND:"user-not-found",RESET_PASSWORD_EXCEED_LIMIT:"too-many-requests",EXPIRED_OOB_CODE:"expired-action-code",INVALID_OOB_CODE:"invalid-action-code",MISSING_OOB_CODE:"internal-error",CREDENTIAL_TOO_OLD_LOGIN_AGAIN:"requires-recent-login",INVALID_ID_TOKEN:"invalid-user-token",TOKEN_EXPIRED:"user-token-expired",USER_NOT_FOUND:"user-token-expired",TOO_MANY_ATTEMPTS_TRY_LATER:"too-many-requests",PASSWORD_DOES_NOT_MEET_REQUIREMENTS:"password-does-not-meet-requirements",INVALID_CODE:"invalid-verification-code",INVALID_SESSION_INFO:"invalid-verification-id",INVALID_TEMPORARY_PROOF:"invalid-credential",MISSING_SESSION_INFO:"missing-verification-id",SESSION_EXPIRED:"code-expired",MISSING_ANDROID_PACKAGE_NAME:"missing-android-pkg-name",UNAUTHORIZED_DOMAIN:"unauthorized-continue-uri",INVALID_OAUTH_CLIENT_ID:"invalid-oauth-client-id",ADMIN_ONLY_OPERATION:"admin-restricted-operation",INVALID_MFA_PENDING_CREDENTIAL:"invalid-multi-factor-session",MFA_ENROLLMENT_NOT_FOUND:"multi-factor-info-not-found",MISSING_MFA_ENROLLMENT_ID:"missing-multi-factor-info",MISSING_MFA_PENDING_CREDENTIAL:"missing-multi-factor-session",SECOND_FACTOR_EXISTS:"second-factor-already-in-use",SECOND_FACTOR_LIMIT_EXCEEDED:"maximum-second-factor-count-exceeded",BLOCKING_FUNCTION_ERROR_RESPONSE:"internal-error",RECAPTCHA_NOT_ENABLED:"recaptcha-not-enabled",MISSING_RECAPTCHA_TOKEN:"missing-recaptcha-token",INVALID_RECAPTCHA_TOKEN:"invalid-recaptcha-token",INVALID_RECAPTCHA_ACTION:"invalid-recaptcha-action",MISSING_CLIENT_TYPE:"missing-client-type",MISSING_RECAPTCHA_VERSION:"missing-recaptcha-version",INVALID_RECAPTCHA_VERSION:"invalid-recaptcha-version",INVALID_REQ_TYPE:"invalid-req-type"},De=["/v1/accounts:signInWithCustomToken","/v1/accounts:signInWithEmailLink","/v1/accounts:signInWithIdp","/v1/accounts:signInWithPassword","/v1/accounts:signInWithPhoneNumber","/v1/token"],Me=new Ce(3e4,6e4);function p(e,t){return e.tenantId&&!t.tenantId?Object.assign(Object.assign({},t),{tenantId:e.tenantId}):t}async function m(i,s,a,o,e={}){return Ue(i,e,async()=>{let e={},t={};o&&("GET"===s?t=o:e={body:JSON.stringify(o)});var r=se(Object.assign({key:i.config.apiKey},t)).slice(1),n=await i._getAdditionalHeaders(),n=(n["Content-Type"]="application/json",i.languageCode&&(n["X-Firebase-Locale"]=i.languageCode),Object.assign({method:s,headers:n},e));return"undefined"!=typeof navigator&&"Cloudflare-Workers"===navigator.userAgent||(n.referrerPolicy="no-referrer"),i.emulatorConfig&&G(i.emulatorConfig.host)&&(n.credentials="include"),Ne.fetch()(await Fe(i,i.config.apiHost,a,r),n)})}async function Ue(t,e,r){t._canInitEmulator=!1;var n=Object.assign(Object.assign({},Le),e);try{var i=new Ve(t),s=await Promise.race([r(),i.promise]),a=(i.clearNetworkTimeout(),await s.json());if("needConfirmation"in a)throw xe(t,"account-exists-with-different-credential",a);if(s.ok&&!("errorMessage"in a))return a;var[o,l]=(s.ok?a.errorMessage:a.error.message).split(" : ");if("FEDERATED_USER_ID_ALREADY_LINKED"===o)throw xe(t,"credential-already-in-use",a);if("EMAIL_EXISTS"===o)throw xe(t,"email-already-in-use",a);if("USER_DISABLED"===o)throw xe(t,"user-disabled",a);var c=n[o]||o.toLowerCase().replace(/[_\s]+/g,"-");if(l)throw be(t,c,l);u(t,c)}catch(e){if(e instanceof d)throw e;u(t,"network-request-failed",{message:String(e)})}}async function s(e,t,r,n,i={}){var s=await m(e,t,r,n,i);return"mfaPendingCredential"in s&&u(e,"multi-factor-auth-required",{_serverResponse:s}),s}async function Fe(e,t,r,n){var i=""+t+r+"?"+n,s=e,i=s.config.emulator?Oe(e.config,i):e.config.apiScheme+"://"+i;if(De.includes(r)&&(await s._persistenceManagerAvailable,"COOKIE"===s._getPersistenceType()))return s._getPersistence()._getFinalTarget(i).toString();return i}class Ve{clearNetworkTimeout(){clearTimeout(this.timer)}constructor(e){this.auth=e,this.timer=null,this.promise=new Promise((e,t)=>{this.timer=setTimeout(()=>t(h(this.auth,"network-request-failed")),Me.get())})}}function xe(e,t,r){var n={appName:e.name},n=(r.email&&(n.email=r.email),r.phoneNumber&&(n.phoneNumber=r.phoneNumber),h(e,t,n));return n.customData._tokenResponse=r,n}function je(e){return void 0!==e&&void 0!==e.getResponse}function He(e){return void 0!==e&&void 0!==e.enterprise}class We{constructor(e){if(this.siteKey="",this.recaptchaEnforcementState=[],void 0===e.recaptchaKey)throw new Error("recaptchaKey undefined");this.siteKey=e.recaptchaKey.split("/")[3],this.recaptchaEnforcementState=e.recaptchaEnforcementState}getProviderEnforcementState(t){if(this.recaptchaEnforcementState&&0!==this.recaptchaEnforcementState.length)for(let e of this.recaptchaEnforcementState)if(e.provider&&e.provider===t){switch(e.enforcementState){case"ENFORCE":return"ENFORCE";case"AUDIT":return"AUDIT";case"OFF":return"OFF";default:return"ENFORCEMENT_STATE_UNSPECIFIED"}return}return null}isProviderEnabled(e){return"ENFORCE"===this.getProviderEnforcementState(e)||"AUDIT"===this.getProviderEnforcementState(e)}isAnyProviderEnabled(){return this.isProviderEnabled("EMAIL_PASSWORD_PROVIDER")||this.isProviderEnabled("PHONE_PROVIDER")}}async function qe(e,t){return m(e,"GET","/v2/recaptchaConfig",p(e,t))}async function Be(e,t){return m(e,"POST","/v1/accounts:lookup",t)}function ze(e){if(e)try{var t=new Date(Number(e));if(!isNaN(t.getTime()))return t.toUTCString()}catch(e){}}function Ge(e){return 1e3*Number(e)}function Ke(e){var[t,r,n]=e.split(".");if(void 0===t||void 0===r||void 0===n)return Ee("JWT malformed, contained fewer than 3 sections"),null;try{var i=H(r);return i?JSON.parse(i):(Ee("Failed to decode base64 JWT payload"),null)}catch(e){return Ee("Caught error parsing JWT payload as JSON",null==e?void 0:e.toString()),null}}function Je(e){var t=Ke(e);return g(t,"internal-error"),g(void 0!==t.exp,"internal-error"),g(void 0!==t.iat,"internal-error"),Number(t.exp)-Number(t.iat)}async function f(t,e,r=!1){if(r)return e;try{return await e}catch(e){throw e instanceof d&&(r=[e.code][0],"auth/user-disabled"===r||"auth/user-token-expired"===r)&&(t.auth.currentUser===t&&await t.auth.signOut()),e}}class Ye{constructor(e){this.user=e,this.isRunning=!1,this.timerId=null,this.errorBackoff=3e4}_start(){this.isRunning||(this.isRunning=!0,this.schedule())}_stop(){this.isRunning&&(this.isRunning=!1,null!==this.timerId)&&clearTimeout(this.timerId)}getInterval(e){var t;return e?(t=this.errorBackoff,this.errorBackoff=Math.min(2*this.errorBackoff,96e4),t):(this.errorBackoff=3e4,t=(null!=(t=this.user.stsTokenManager.expirationTime)?t:0)-Date.now()-3e5,Math.max(0,t))}schedule(e=!1){var t;this.isRunning&&(t=this.getInterval(e),this.timerId=setTimeout(async()=>{await this.iteration()},t))}async iteration(){try{await this.user.getIdToken(!0)}catch(e){return void("auth/network-request-failed"===(null==e?void 0:e.code)&&this.schedule(!0))}this.schedule()}}class $e{constructor(e,t){this.createdAt=e,this.lastLoginAt=t,this._initializeTime()}_initializeTime(){this.lastSignInTime=ze(this.lastLoginAt),this.creationTime=ze(this.createdAt)}_copy(e){this.createdAt=e.createdAt,this.lastLoginAt=e.lastLoginAt,this._initializeTime()}toJSON(){return{createdAt:this.createdAt,lastLoginAt:this.lastLoginAt}}}async function Xe(e){var t,r,n=e.auth,i=await e.getIdToken(),i=await f(e,Be(n,{idToken:i})),n=(g(null==i?void 0:i.users.length,n,"internal-error"),i.users[0]),i=(e._notifyReloadListener(n),null!=(i=n.providerUserInfo)&&i.length?Ze(n.providerUserInfo):[]),i=(t=e.providerData,r=i,[...t.filter(t=>!r.some(e=>e.providerId===t.providerId)),...r]),s=!(e.email&&n.passwordHash||null!==i&&i.length),s=!!e.isAnonymous&&s,i={uid:n.localId,displayName:n.displayName||null,photoURL:n.photoUrl||null,email:n.email||null,emailVerified:n.emailVerified||!1,phoneNumber:n.phoneNumber||null,tenantId:n.tenantId||null,providerData:i,metadata:new $e(n.createdAt,n.lastLoginAt),isAnonymous:s};Object.assign(e,i)}function Ze(e){return e.map(e=>{var t=e.providerId,r=me(e,["providerId"]);return{providerId:t,uid:r.rawId||"",displayName:r.displayName||null,email:r.email||null,phoneNumber:r.phoneNumber||null,photoURL:r.photoUrl||null}})}class Qe{constructor(){this.refreshToken=null,this.accessToken=null,this.expirationTime=null}get isExpired(){return!this.expirationTime||Date.now()>this.expirationTime-3e4}updateFromServerResponse(e){g(e.idToken,"internal-error"),g(void 0!==e.idToken,"internal-error"),g(void 0!==e.refreshToken,"internal-error");var t="expiresIn"in e&&void 0!==e.expiresIn?Number(e.expiresIn):Je(e.idToken);this.updateTokensAndExpiration(e.idToken,e.refreshToken,t)}updateFromIdToken(e){g(0!==e.length,"internal-error");var t=Je(e);this.updateTokensAndExpiration(e,null,t)}async getToken(e,t=!1){return t||!this.accessToken||this.isExpired?(g(this.refreshToken,e,"user-token-expired"),this.refreshToken?(await this.refresh(e,this.refreshToken),this.accessToken):null):this.accessToken}clearRefreshToken(){this.refreshToken=null}async refresh(e,t){i=t;var n,i,{accessToken:r,refreshToken:s,expiresIn:a}=await{accessToken:(r=await Ue(n=e,{},async()=>{var e=se({grant_type:"refresh_token",refresh_token:i}).slice(1),{tokenApiHost:t,apiKey:r}=n.config,t=await Fe(n,t,"/v1/token","key="+r),r=await n._getAdditionalHeaders();return r["Content-Type"]="application/x-www-form-urlencoded",Ne.fetch()(t,{method:"POST",headers:r,body:e})})).access_token,expiresIn:r.expires_in,refreshToken:r.refresh_token};this.updateTokensAndExpiration(r,s,Number(a))}updateTokensAndExpiration(e,t,r){this.refreshToken=t||null,this.accessToken=e||null,this.expirationTime=Date.now()+1e3*r}static fromJSON(e,t){var{refreshToken:r,accessToken:n,expirationTime:i}=t,s=new Qe;return r&&(g("string"==typeof r,"internal-error",{appName:e}),s.refreshToken=r),n&&(g("string"==typeof n,"internal-error",{appName:e}),s.accessToken=n),i&&(g("number"==typeof i,"internal-error",{appName:e}),s.expirationTime=i),s}toJSON(){return{refreshToken:this.refreshToken,accessToken:this.accessToken,expirationTime:this.expirationTime}}_assign(e){this.accessToken=e.accessToken,this.refreshToken=e.refreshToken,this.expirationTime=e.expirationTime}_clone(){return Object.assign(new Qe,this.toJSON())}_performRefresh(){return i("not implemented")}}function v(e,t){g("string"==typeof e||void 0===e,"internal-error",{appName:t})}class _{constructor(e){var{uid:t,auth:r,stsTokenManager:n}=e,i=me(e,["uid","auth","stsTokenManager"]);this.providerId="firebase",this.proactiveRefresh=new Ye(this),this.reloadUserInfo=null,this.reloadListener=null,this.uid=t,this.auth=r,this.stsTokenManager=n,this.accessToken=n.accessToken,this.displayName=i.displayName||null,this.email=i.email||null,this.emailVerified=i.emailVerified||!1,this.phoneNumber=i.phoneNumber||null,this.photoURL=i.photoURL||null,this.isAnonymous=i.isAnonymous||!1,this.tenantId=i.tenantId||null,this.providerData=i.providerData?[...i.providerData]:[],this.metadata=new $e(i.createdAt||void 0,i.lastLoginAt||void 0)}async getIdToken(e){var t=await f(this,this.stsTokenManager.getToken(this.auth,e));return g(t,this.auth,"internal-error"),this.accessToken!==t&&(this.accessToken=t,await this.auth._persistUserIfCurrent(this),this.auth._notifyListenersIfCurrent(this)),t}getIdTokenResult(e){return(async(e,t=!1)=>{var r=o(e),n=await r.getIdToken(t),i=Ke(n),s=(g(i&&i.exp&&i.auth_time&&i.iat,r.auth,"internal-error"),null==(r="object"==typeof i.firebase?i.firebase:void 0)?void 0:r.sign_in_provider);return{claims:i,token:n,authTime:ze(Ge(i.auth_time)),issuedAtTime:ze(Ge(i.iat)),expirationTime:ze(Ge(i.exp)),signInProvider:s||null,signInSecondFactor:(null==r?void 0:r.sign_in_second_factor)||null}})(this,e)}reload(){return(async e=>{var t=o(e);await Xe(t),await t.auth._persistUserIfCurrent(t),t.auth._notifyListenersIfCurrent(t)})(this)}_assign(e){this!==e&&(g(this.uid===e.uid,this.auth,"internal-error"),this.displayName=e.displayName,this.photoURL=e.photoURL,this.email=e.email,this.emailVerified=e.emailVerified,this.phoneNumber=e.phoneNumber,this.isAnonymous=e.isAnonymous,this.tenantId=e.tenantId,this.providerData=e.providerData.map(e=>Object.assign({},e)),this.metadata._copy(e.metadata),this.stsTokenManager._assign(e.stsTokenManager))}_clone(e){var t=new _(Object.assign(Object.assign({},this),{auth:e,stsTokenManager:this.stsTokenManager._clone()}));return t.metadata._copy(this.metadata),t}_onReload(e){g(!this.reloadListener,this.auth,"internal-error"),this.reloadListener=e,this.reloadUserInfo&&(this._notifyReloadListener(this.reloadUserInfo),this.reloadUserInfo=null)}_notifyReloadListener(e){this.reloadListener?this.reloadListener(e):this.reloadUserInfo=e}_startProactiveRefresh(){this.proactiveRefresh._start()}_stopProactiveRefresh(){this.proactiveRefresh._stop()}async _updateTokensIfNecessary(e,t=!1){let r=!1;e.idToken&&e.idToken!==this.stsTokenManager.accessToken&&(this.stsTokenManager.updateFromServerResponse(e),r=!0),t&&await Xe(this),await this.auth._persistUserIfCurrent(this),r&&this.auth._notifyListenersIfCurrent(this)}async delete(){var e;return Ki._isFirebaseServerApp(this.auth.app)?Promise.reject(l(this.auth)):(e=await this.getIdToken(),await f(this,(async(e,t)=>m(e,"POST","/v1/accounts:delete",t))(this.auth,{idToken:e})),this.stsTokenManager.clearRefreshToken(),this.auth.signOut())}toJSON(){return Object.assign(Object.assign({uid:this.uid,email:this.email||void 0,emailVerified:this.emailVerified,displayName:this.displayName||void 0,isAnonymous:this.isAnonymous,photoURL:this.photoURL||void 0,phoneNumber:this.phoneNumber||void 0,tenantId:this.tenantId||void 0,providerData:this.providerData.map(e=>Object.assign({},e)),stsTokenManager:this.stsTokenManager.toJSON(),_redirectEventId:this._redirectEventId},this.metadata.toJSON()),{apiKey:this.auth.config.apiKey,appName:this.auth.name})}get refreshToken(){return this.stsTokenManager.refreshToken||""}static _fromJSON(e,t){var r=null!=(r=t.displayName)?r:void 0,n=null!=(n=t.email)?n:void 0,i=null!=(i=t.phoneNumber)?i:void 0,s=null!=(s=t.photoURL)?s:void 0,a=null!=(a=t.tenantId)?a:void 0,o=null!=(o=t._redirectEventId)?o:void 0,l=null!=(l=t.createdAt)?l:void 0,c=null!=(c=t.lastLoginAt)?c:void 0,{uid:d,emailVerified:u,isAnonymous:h,providerData:p,stsTokenManager:m}=t,m=(g(d&&m,e,"internal-error"),Qe.fromJSON(this.name,m)),d=(g("string"==typeof d,e,"internal-error"),v(r,e.name),v(n,e.name),g("boolean"==typeof u,e,"internal-error"),g("boolean"==typeof h,e,"internal-error"),v(i,e.name),v(s,e.name),v(a,e.name),v(o,e.name),v(l,e.name),v(c,e.name),new _({uid:d,auth:e,email:n,emailVerified:u,displayName:r,isAnonymous:h,photoURL:s,phoneNumber:i,tenantId:a,stsTokenManager:m,createdAt:l,lastLoginAt:c}));return p&&Array.isArray(p)&&(d.providerData=p.map(e=>Object.assign({},e))),o&&(d._redirectEventId=o),d}static async _fromIdTokenResponse(e,t,r=!1){var n=new Qe,n=(n.updateFromServerResponse(t),new _({uid:t.localId,auth:e,stsTokenManager:n,isAnonymous:r}));return await Xe(n),n}static async _fromGetAccountInfoResponse(e,t,r){var n=t.users[0],i=(g(void 0!==n.localId,"internal-error"),void 0!==n.providerUserInfo?Ze(n.providerUserInfo):[]),s=!(n.email&&n.passwordHash||null!=i&&i.length),a=new Qe,a=(a.updateFromIdToken(r),new _({uid:n.localId,auth:e,stsTokenManager:a,isAnonymous:s})),s={uid:n.localId,displayName:n.displayName||null,photoURL:n.photoUrl||null,email:n.email||null,emailVerified:n.emailVerified||!1,phoneNumber:n.phoneNumber||null,tenantId:n.tenantId||null,providerData:i,metadata:new $e(n.createdAt,n.lastLoginAt),isAnonymous:!(n.email&&n.passwordHash||null!=i&&i.length)};return Object.assign(a,s),a}}let et=new Map;function y(e){a(e instanceof Function,"Expected a class definition");var t=et.get(e);return t?a(t instanceof e,"Instance stored in cache mismatched with class"):(t=new e,et.set(e,t)),t}class tt{constructor(){this.type="NONE",this.storage={}}async _isAvailable(){return!0}async _set(e,t){this.storage[e]=t}async _get(e){var t=this.storage[e];return void 0===t?null:t}async _remove(e){delete this.storage[e]}_addListener(e,t){}_removeListener(e,t){}}tt.type="NONE";let rt=tt;function I(e,t,r){return`firebase:${e}:${t}:`+r}class nt{constructor(e,t,r){this.persistence=e,this.auth=t,this.userKey=r;var{config:n,name:i}=this.auth;this.fullUserKey=I(this.userKey,n.apiKey,i),this.fullPersistenceKey=I("persistence",n.apiKey,i),this.boundEventHandler=t._onStorageEvent.bind(t),this.persistence._addListener(this.fullUserKey,this.boundEventHandler)}setCurrentUser(e){return this.persistence._set(this.fullUserKey,e.toJSON())}async getCurrentUser(){var e,t=await this.persistence._get(this.fullUserKey);return t?"string"==typeof t?(e=await Be(this.auth,{idToken:t}).catch(()=>{}))?_._fromGetAccountInfoResponse(this.auth,e,t):null:_._fromJSON(this.auth,t):null}removeCurrentUser(){return this.persistence._remove(this.fullUserKey)}savePersistenceForRedirect(){return this.persistence._set(this.fullPersistenceKey,this.persistence.type)}async setPersistence(e){var t;if(this.persistence!==e)return t=await this.getCurrentUser(),await this.removeCurrentUser(),this.persistence=e,t?this.setCurrentUser(t):void 0}delete(){this.persistence._removeListener(this.fullUserKey,this.boundEventHandler)}static async create(t,e,r="authUser"){if(!e.length)return new nt(y(rt),t,r);var n,i=(await Promise.all(e.map(async e=>{if(await e._isAvailable())return e}))).filter(e=>e);let s=i[0]||y(rt),a=I(r,t.config.apiKey,t.name),o=null;for(n of e)try{var l=await n._get(a);if(l){let e;if("string"==typeof l){var c=await Be(t,{idToken:l}).catch(()=>{});if(!c)break;e=await _._fromGetAccountInfoResponse(t,c,l)}else e=_._fromJSON(t,l);n!==s&&(o=e),s=n;break}}catch(e){}i=i.filter(e=>e._shouldAllowMigration);return s._shouldAllowMigration&&i.length&&(s=i[0],o&&await s._set(a,o.toJSON()),await Promise.all(e.map(async e=>{if(e!==s)try{await e._remove(a)}catch(e){}}))),new nt(s,t,r)}}function it(e){var t=e.toLowerCase();return t.includes("opera/")||t.includes("opr/")||t.includes("opios/")?"Opera":lt(t)?"IEMobile":t.includes("msie")||t.includes("trident/")?"IE":t.includes("edge/")?"Edge":st(t)?"Firefox":t.includes("silk/")?"Silk":dt(t)?"Blackberry":ut(t)?"Webos":at(t)?"Safari":!t.includes("chrome/")&&!ot(t)||t.includes("edge/")?ct(t)?"Android":2===(null==(t=e.match(/([a-zA-Z\d\.]+)\/[a-zA-Z\d\.]*$/))?void 0:t.length)?t[1]:"Other":"Chrome"}function st(e=c()){return/firefox\//i.test(e)}function at(e=c()){var t=e.toLowerCase();return t.includes("safari/")&&!t.includes("chrome/")&&!t.includes("crios/")&&!t.includes("android")}function ot(e=c()){return/crios\//i.test(e)}function lt(e=c()){return/iemobile/i.test(e)}function ct(e=c()){return/android/i.test(e)}function dt(e=c()){return/blackberry/i.test(e)}function ut(e=c()){return/webos/i.test(e)}function ht(e=c()){return/iphone|ipad|ipod/i.test(e)||/macintosh/i.test(e)&&/mobile/i.test(e)}function pt(e=c()){return ht(e)||ct(e)||ut(e)||dt(e)||/windows phone/i.test(e)||lt(e)}function mt(e,t=[]){let r;switch(e){case"Browser":r=it(c());break;case"Worker":r=it(c())+"-"+e;break;default:r=e}var n=t.length?t.join(","):"FirebaseCore-web";return`${r}/JsCore/${Ki.SDK_VERSION}/`+n}class gt{constructor(e){this.auth=e,this.queue=[]}pushCallback(n,e){var t=r=>new Promise((e,t)=>{try{e(n(r))}catch(e){t(e)}});t.onAbort=e,this.queue.push(t);let r=this.queue.length-1;return()=>{this.queue[r]=()=>Promise.resolve()}}async runMiddleware(e){if(this.auth.currentUser!==e){var t=[];try{for(var r of this.queue)await r(e),r.onAbort&&t.push(r.onAbort)}catch(e){t.reverse();for(var n of t)try{n()}catch(e){}throw this.auth._errorFactory.create("login-blocked",{originalMessage:null==e?void 0:e.message})}}}}class ft{constructor(e){var t,r=e.customStrengthOptions;this.customStrengthOptions={},this.customStrengthOptions.minPasswordLength=null!=(t=r.minPasswordLength)?t:6,r.maxPasswordLength&&(this.customStrengthOptions.maxPasswordLength=r.maxPasswordLength),void 0!==r.containsLowercaseCharacter&&(this.customStrengthOptions.containsLowercaseLetter=r.containsLowercaseCharacter),void 0!==r.containsUppercaseCharacter&&(this.customStrengthOptions.containsUppercaseLetter=r.containsUppercaseCharacter),void 0!==r.containsNumericCharacter&&(this.customStrengthOptions.containsNumericCharacter=r.containsNumericCharacter),void 0!==r.containsNonAlphanumericCharacter&&(this.customStrengthOptions.containsNonAlphanumericCharacter=r.containsNonAlphanumericCharacter),this.enforcementState=e.enforcementState,"ENFORCEMENT_STATE_UNSPECIFIED"===this.enforcementState&&(this.enforcementState="OFF"),this.allowedNonAlphanumericCharacters=null!=(r=null==(t=e.allowedNonAlphanumericCharacters)?void 0:t.join(""))?r:"",this.forceUpgradeOnSignin=null!=(t=e.forceUpgradeOnSignin)&&t,this.schemaVersion=e.schemaVersion}validatePassword(e){var t,r={isValid:!0,passwordPolicy:this};return this.validatePasswordLengthOptions(e,r),this.validatePasswordCharacterOptions(e,r),r.isValid&&(r.isValid=null==(t=r.meetsMinPasswordLength)||t),r.isValid&&(r.isValid=null==(t=r.meetsMaxPasswordLength)||t),r.isValid&&(r.isValid=null==(t=r.containsLowercaseLetter)||t),r.isValid&&(r.isValid=null==(t=r.containsUppercaseLetter)||t),r.isValid&&(r.isValid=null==(t=r.containsNumericCharacter)||t),r.isValid&&(r.isValid=null==(t=r.containsNonAlphanumericCharacter)||t),r}validatePasswordLengthOptions(e,t){var r=this.customStrengthOptions.minPasswordLength,n=this.customStrengthOptions.maxPasswordLength;r&&(t.meetsMinPasswordLength=e.length>=r),n&&(t.meetsMaxPasswordLength=e.length<=n)}validatePasswordCharacterOptions(e,t){var r;this.updatePasswordCharacterOptionsStatuses(t,!1,!1,!1,!1);for(let n=0;n<e.length;n++)r=e.charAt(n),this.updatePasswordCharacterOptionsStatuses(t,"a"<=r&&r<="z","A"<=r&&r<="Z","0"<=r&&r<="9",this.allowedNonAlphanumericCharacters.includes(r))}updatePasswordCharacterOptionsStatuses(e,t,r,n,i){this.customStrengthOptions.containsLowercaseLetter&&!e.containsLowercaseLetter&&(e.containsLowercaseLetter=t),this.customStrengthOptions.containsUppercaseLetter&&!e.containsUppercaseLetter&&(e.containsUppercaseLetter=r),this.customStrengthOptions.containsNumericCharacter&&!e.containsNumericCharacter&&(e.containsNumericCharacter=n),this.customStrengthOptions.containsNonAlphanumericCharacter&&!e.containsNonAlphanumericCharacter&&(e.containsNonAlphanumericCharacter=i)}}class vt{constructor(e,t,r,n){this.app=e,this.heartbeatServiceProvider=t,this.appCheckServiceProvider=r,this.config=n,this.currentUser=null,this.emulatorConfig=null,this.operations=Promise.resolve(),this.authStateSubscription=new _t(this),this.idTokenSubscription=new _t(this),this.beforeStateQueue=new gt(this),this.redirectUser=null,this.isProactiveRefreshEnabled=!1,this.EXPECTED_PASSWORD_POLICY_SCHEMA_VERSION=1,this._canInitEmulator=!0,this._isInitialized=!1,this._deleted=!1,this._initializationPromise=null,this._popupRedirectResolver=null,this._errorFactory=we,this._agentRecaptchaConfig=null,this._tenantRecaptchaConfigs={},this._projectPasswordPolicy=null,this._tenantPasswordPolicies={},this._resolvePersistenceManagerAvailable=void 0,this.lastNotifiedUid=void 0,this.languageCode=null,this.tenantId=null,this.settings={appVerificationDisabledForTesting:!1},this.frameworks=[],this.name=e.name,this.clientVersion=n.sdkClientVersion,this._persistenceManagerAvailable=new Promise(e=>this._resolvePersistenceManagerAvailable=e)}_initializeWithPersistence(t,r){return r&&(this._popupRedirectResolver=y(r)),this._initializationPromise=this.queue(async()=>{var e;if(!this._deleted&&(this.persistenceManager=await nt.create(this,t),null!=(e=this._resolvePersistenceManagerAvailable)&&e.call(this),!this._deleted)){if(null!=(e=this._popupRedirectResolver)&&e._shouldInitProactively)try{await this._popupRedirectResolver._initialize(this)}catch(e){}await this.initializeCurrentUser(r),this.lastNotifiedUid=(null==(e=this.currentUser)?void 0:e.uid)||null,this._deleted||(this._isInitialized=!0)}}),this._initializationPromise}async _onStorageEvent(){var e;!this._deleted&&(e=await this.assertedPersistence.getCurrentUser(),this.currentUser||e)&&(this.currentUser&&e&&this.currentUser.uid===e.uid?(this._currentUser._assign(e),await this.currentUser.getIdToken()):await this._updateCurrentUser(e,!0))}async initializeCurrentUserFromIdToken(e){try{var t=await Be(this,{idToken:e}),r=await _._fromGetAccountInfoResponse(this,t,e);await this.directlySetCurrentUser(r)}catch(e){console.warn("FirebaseServerApp could not login user with provided authIdToken: ",e),await this.directlySetCurrentUser(null)}}async initializeCurrentUser(e){if(Ki._isFirebaseServerApp(this.app)){let t=this.app.settings.authIdToken;return t?new Promise(e=>{setTimeout(()=>this.initializeCurrentUserFromIdToken(t).then(e,e))}):this.directlySetCurrentUser(null)}var t,r,n,i=await this.assertedPersistence.getCurrentUser();let s=i,a=!1;if(e&&this.config.authDomain&&(await this.getOrInitRedirectPersistenceManager(),t=null==(t=this.redirectUser)?void 0:t._redirectEventId,r=null==s?void 0:s._redirectEventId,n=await this.tryRedirectSignIn(e),t&&t!==r||null==n||!n.user||(s=n.user,a=!0)),!s)return this.directlySetCurrentUser(null);if(s._redirectEventId)return g(this._popupRedirectResolver,this,"argument-error"),await this.getOrInitRedirectPersistenceManager(),this.redirectUser&&this.redirectUser._redirectEventId===s._redirectEventId?this.directlySetCurrentUser(s):this.reloadAndSetCurrentUserOrClear(s);if(a)try{await this.beforeStateQueue.runMiddleware(s)}catch(e){s=i,this._popupRedirectResolver._overrideRedirectResult(this,()=>Promise.reject(e))}return s?this.reloadAndSetCurrentUserOrClear(s):this.directlySetCurrentUser(null)}async tryRedirectSignIn(e){let t=null;try{t=await this._popupRedirectResolver._completeRedirectFn(this,e,!0)}catch(e){await this._setRedirectUser(null)}return t}async reloadAndSetCurrentUserOrClear(e){try{await Xe(e)}catch(e){if("auth/network-request-failed"!==(null==e?void 0:e.code))return this.directlySetCurrentUser(null)}return this.directlySetCurrentUser(e)}useDeviceLanguage(){var e;this.languageCode="undefined"!=typeof navigator&&((e=navigator).languages&&e.languages[0]||e.language)||null}async _delete(){this._deleted=!0}async updateCurrentUser(e){var t;return Ki._isFirebaseServerApp(this.app)?Promise.reject(l(this)):((t=e?o(e):null)&&g(t.auth.config.apiKey===this.config.apiKey,this,"invalid-user-token"),this._updateCurrentUser(t&&t._clone(this)))}async _updateCurrentUser(e,t=!1){if(!this._deleted)return e&&g(this.tenantId===e.tenantId,this,"tenant-id-mismatch"),t||await this.beforeStateQueue.runMiddleware(e),this.queue(async()=>{await this.directlySetCurrentUser(e),this.notifyAuthListeners()})}async signOut(){return Ki._isFirebaseServerApp(this.app)?Promise.reject(l(this)):(await this.beforeStateQueue.runMiddleware(null),(this.redirectPersistenceManager||this._popupRedirectResolver)&&await this._setRedirectUser(null),this._updateCurrentUser(null,!0))}setPersistence(e){return Ki._isFirebaseServerApp(this.app)?Promise.reject(l(this)):this.queue(async()=>{await this.assertedPersistence.setPersistence(y(e))})}_getRecaptchaConfig(){return null==this.tenantId?this._agentRecaptchaConfig:this._tenantRecaptchaConfigs[this.tenantId]}async validatePassword(e){this._getPasswordPolicyInternal()||await this._updatePasswordPolicy();var t=this._getPasswordPolicyInternal();return t.schemaVersion!==this.EXPECTED_PASSWORD_POLICY_SCHEMA_VERSION?Promise.reject(this._errorFactory.create("unsupported-password-policy-schema-version",{})):t.validatePassword(e)}_getPasswordPolicyInternal(){return null===this.tenantId?this._projectPasswordPolicy:this._tenantPasswordPolicies[this.tenantId]}async _updatePasswordPolicy(){var e,t=await m(e=this,"GET","/v2/passwordPolicy",p(e,{})),t=new ft(t);null===this.tenantId?this._projectPasswordPolicy=t:this._tenantPasswordPolicies[this.tenantId]=t}_getPersistenceType(){return this.assertedPersistence.persistence.type}_getPersistence(){return this.assertedPersistence.persistence}_updateErrorMap(e){this._errorFactory=new te("auth","Firebase",e())}onAuthStateChanged(e,t,r){return this.registerStateListener(this.authStateSubscription,e,t,r)}beforeAuthStateChanged(e,t){return this.beforeStateQueue.pushCallback(e,t)}onIdTokenChanged(e,t,r){return this.registerStateListener(this.idTokenSubscription,e,t,r)}authStateReady(){return new Promise((t,r)=>{if(this.currentUser)t();else{let e=this.onAuthStateChanged(()=>{e(),t()},r)}})}async revokeAccessToken(e){var t;this.currentUser&&(t={providerId:"apple.com",tokenType:"ACCESS_TOKEN",token:e,idToken:await this.currentUser.getIdToken()},null!=this.tenantId&&(t.tenantId=this.tenantId),await m(e=this,"POST","/v2/accounts:revokeToken",p(e,t)))}toJSON(){var e;return{apiKey:this.config.apiKey,authDomain:this.config.authDomain,appName:this.name,currentUser:null==(e=this._currentUser)?void 0:e.toJSON()}}async _setRedirectUser(e,t){var r=await this.getOrInitRedirectPersistenceManager(t);return null===e?r.removeCurrentUser():r.setCurrentUser(e)}async getOrInitRedirectPersistenceManager(e){var t;return this.redirectPersistenceManager||(g(t=e&&y(e)||this._popupRedirectResolver,this,"argument-error"),this.redirectPersistenceManager=await nt.create(this,[y(t._redirectPersistence)],"redirectUser"),this.redirectUser=await this.redirectPersistenceManager.getCurrentUser()),this.redirectPersistenceManager}async _redirectUserForId(e){var t;return this._isInitialized&&await this.queue(async()=>{}),(null==(t=this._currentUser)?void 0:t._redirectEventId)===e?this._currentUser:(null==(t=this.redirectUser)?void 0:t._redirectEventId)===e?this.redirectUser:null}async _persistUserIfCurrent(e){if(e===this.currentUser)return this.queue(async()=>this.directlySetCurrentUser(e))}_notifyListenersIfCurrent(e){e===this.currentUser&&this.notifyAuthListeners()}_key(){return`${this.config.authDomain}:${this.config.apiKey}:`+this.name}_startProactiveRefresh(){this.isProactiveRefreshEnabled=!0,this.currentUser&&this._currentUser._startProactiveRefresh()}_stopProactiveRefresh(){this.isProactiveRefreshEnabled=!1,this.currentUser&&this._currentUser._stopProactiveRefresh()}get _currentUser(){return this.currentUser}notifyAuthListeners(){var e;this._isInitialized&&(this.idTokenSubscription.next(this.currentUser),e=null!=(e=null==(e=this.currentUser)?void 0:e.uid)?e:null,this.lastNotifiedUid!==e)&&(this.lastNotifiedUid=e,this.authStateSubscription.next(this.currentUser))}registerStateListener(t,r,n,i){if(this._deleted)return()=>{};let e="function"==typeof r?r:r.next.bind(r),s=!1;var a=this._isInitialized?Promise.resolve():this._initializationPromise;if(g(a,this,"internal-error"),a.then(()=>{s||e(this.currentUser)}),"function"==typeof r){let e=t.addObserver(r,n,i);return()=>{s=!0,e()}}{let e=t.addObserver(r);return()=>{s=!0,e()}}}async directlySetCurrentUser(e){this.currentUser&&this.currentUser!==e&&this._currentUser._stopProactiveRefresh(),e&&this.isProactiveRefreshEnabled&&e._startProactiveRefresh(),(this.currentUser=e)?await this.assertedPersistence.setCurrentUser(e):await this.assertedPersistence.removeCurrentUser()}queue(e){return this.operations=this.operations.then(e,e),this.operations}get assertedPersistence(){return g(this.persistenceManager,this,"internal-error"),this.persistenceManager}_logFramework(e){e&&!this.frameworks.includes(e)&&(this.frameworks.push(e),this.frameworks.sort(),this.clientVersion=mt(this.config.clientPlatform,this._getFrameworks()))}_getFrameworks(){return this.frameworks}async _getAdditionalHeaders(){var e={"X-Client-Version":this.clientVersion},t=(this.app.options.appId&&(e["X-Firebase-gmpid"]=this.app.options.appId),await(null==(t=this.heartbeatServiceProvider.getImmediate({optional:!0}))?void 0:t.getHeartbeatsHeader())),t=(t&&(e["X-Firebase-Client"]=t),await this._getAppCheckToken());return t&&(e["X-Firebase-AppCheck"]=t),e}async _getAppCheckToken(){var e,t,r;return Ki._isFirebaseServerApp(this.app)&&this.app.settings.appCheckToken?this.app.settings.appCheckToken:(null!=(e=await(null==(e=this.appCheckServiceProvider.getImmediate({optional:!0}))?void 0:e.getToken()))&&e.error&&(t="Error while retrieving App Check token: "+e.error,r=[],Te.logLevel<=n.WARN)&&Te.warn(`Auth (${Ki.SDK_VERSION}): `+t,...r),null==e?void 0:e.token)}}function w(e){return o(e)}class _t{constructor(e){var t,r;this.auth=e,this.observer=null,this.addObserver=(e=e=>this.observer=e,(r=new le(e,t)).subscribe.bind(r))}get next(){return g(this.observer,this.auth,"internal-error"),this.observer.next.bind(this.observer)}}let yt={async loadJS(){throw new Error("Unable to load external scripts")},recaptchaV2Script:"",recaptchaEnterpriseScript:"",gapiScript:""};function It(e){return yt.loadJS(e)}function wt(e){return"__"+e+Math.floor(1e6*Math.random())}class Tt{constructor(e){this.auth=e,this.counter=1e12,this._widgets=new Map}render(e,t){var r=this.counter;return this._widgets.set(r,new kt(e,this.auth.name,t||{})),this.counter++,r}reset(e){var t,r=e||1e12;null!=(t=this._widgets.get(r))&&t.delete(),this._widgets.delete(r)}getResponse(e){var t;return(null==(t=this._widgets.get(e||1e12))?void 0:t.getResponse())||""}async execute(e){var t;return null!=(t=this._widgets.get(e||1e12))&&t.execute(),""}}class Et{constructor(){this.enterprise=new bt}ready(e){e()}execute(e,t){return Promise.resolve("token")}render(e,t){return""}}class bt{ready(e){e()}execute(e,t){return Promise.resolve("token")}render(e,t){return""}}class kt{constructor(e,t,r){this.params=r,this.timerId=null,this.deleted=!1,this.responseToken=null,this.clickHandler=()=>{this.execute()};var n="string"==typeof e?document.getElementById(e):e;g(n,"argument-error",{appName:t}),this.container=n,this.isVisible="invisible"!==this.params.size,this.isVisible?this.execute():this.container.addEventListener("click",this.clickHandler)}getResponse(){return this.checkIfDeleted(),this.responseToken}delete(){this.checkIfDeleted(),this.deleted=!0,this.timerId&&(clearTimeout(this.timerId),this.timerId=null),this.container.removeEventListener("click",this.clickHandler)}execute(){this.checkIfDeleted(),this.timerId||(this.timerId=window.setTimeout(()=>{this.responseToken=(e=>{var t=[],r="1234567890abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ";for(let n=0;n<e;n++)t.push(r.charAt(Math.floor(Math.random()*r.length)));return t.join("")})(50);let{callback:e,"expired-callback":t}=this.params;if(e)try{e(this.responseToken)}catch(e){}this.timerId=window.setTimeout(()=>{if(this.timerId=null,this.responseToken=null,t)try{t()}catch(e){}this.isVisible&&this.execute()},6e4)},500))}checkIfDeleted(){if(this.deleted)throw new Error("reCAPTCHA mock was already deleted!")}}let St="NO_RECAPTCHA";class Rt{constructor(e){this.type="recaptcha-enterprise",this.auth=w(e)}async verify(i="verify",e=!1){function s(e,t,r){let n=window.grecaptcha;He(n)?n.enterprise.ready(()=>{n.enterprise.execute(e,{action:i}).then(e=>{t(e)}).catch(()=>{t(St)})}):r(Error("No reCAPTCHA enterprise script loaded."))}return this.auth.settings.appVerificationDisabledForTesting?(new Et).execute("siteKey",{action:"verify"}):new Promise((r,n)=>{(async i=>{if(!e){if(null==i.tenantId&&null!=i._agentRecaptchaConfig)return i._agentRecaptchaConfig.siteKey;if(null!=i.tenantId&&void 0!==i._tenantRecaptchaConfigs[i.tenantId])return i._tenantRecaptchaConfigs[i.tenantId].siteKey}return new Promise(async(r,n)=>{qe(i,{clientType:"CLIENT_TYPE_WEB",version:"RECAPTCHA_ENTERPRISE"}).then(e=>{var t;if(void 0!==e.recaptchaKey)return t=new We(e),null==i.tenantId?i._agentRecaptchaConfig=t:i._tenantRecaptchaConfigs[i.tenantId]=t,r(t.siteKey);n(new Error("recaptcha Enterprise site key undefined"))}).catch(e=>{n(e)})})})(this.auth).then(t=>{if(!e&&He(window.grecaptcha))s(t,r,n);else if("undefined"==typeof window)n(new Error("RecaptchaVerifier is only supported in browser"));else{let e=yt.recaptchaEnterpriseScript;0!==e.length&&(e+=t),It(e).then(()=>{s(t,r,n)}).catch(e=>{n(e)})}}).catch(e=>{n(e)})})}}async function At(e,t,r,n=!1,i=!1){var s=new Rt(e);let a;if(i)a=St;else try{a=await s.verify(r)}catch(e){a=await s.verify(r,!0)}var o,l,s=Object.assign({},t);return"mfaSmsEnrollment"===r||"mfaSmsSignIn"===r?"phoneEnrollmentInfo"in s?(l=s.phoneEnrollmentInfo.phoneNumber,o=s.phoneEnrollmentInfo.recaptchaToken,Object.assign(s,{phoneEnrollmentInfo:{phoneNumber:l,recaptchaToken:o,captchaResponse:a,clientType:"CLIENT_TYPE_WEB",recaptchaVersion:"RECAPTCHA_ENTERPRISE"}})):"phoneSignInInfo"in s&&(l=s.phoneSignInInfo.recaptchaToken,Object.assign(s,{phoneSignInInfo:{recaptchaToken:l,captchaResponse:a,clientType:"CLIENT_TYPE_WEB",recaptchaVersion:"RECAPTCHA_ENTERPRISE"}})):(n?Object.assign(s,{captchaResp:a}):Object.assign(s,{captchaResponse:a}),Object.assign(s,{clientType:"CLIENT_TYPE_WEB"}),Object.assign(s,{recaptchaVersion:"RECAPTCHA_ENTERPRISE"})),s}async function T(r,n,i,s,e){var t;return"EMAIL_PASSWORD_PROVIDER"===e?null!=(t=r._getRecaptchaConfig())&&t.isProviderEnabled("EMAIL_PASSWORD_PROVIDER")?(t=await At(r,n,i,"getOobCode"===i),s(r,t)):s(r,n).catch(async e=>{var t;return"auth/missing-recaptcha-token"===e.code?(console.log(i+" is protected by reCAPTCHA Enterprise for this project. Automatically triggering the reCAPTCHA flow and restarting the flow."),t=await At(r,n,i,"getOobCode"===i),s(r,t)):Promise.reject(e)}):"PHONE_PROVIDER"===e?null!=(t=r._getRecaptchaConfig())&&t.isProviderEnabled("PHONE_PROVIDER")?(t=await At(r,n,i),s(r,t).catch(async e=>{var t;if("AUDIT"===(null==(t=r._getRecaptchaConfig())?void 0:t.getProviderEnforcementState("PHONE_PROVIDER"))&&("auth/missing-recaptcha-token"===e.code||"auth/invalid-app-credential"===e.code))return console.log(`Failed to verify with reCAPTCHA Enterprise. Automatically triggering the reCAPTCHA v2 flow to complete the ${i} flow.`),t=await At(r,n,i,!1,!0),s(r,t);return Promise.reject(e)})):(t=await At(r,n,i,!1,!0),s(r,t)):Promise.reject(e+" provider is not supported.")}function Pt(e,t,r){var n=w(e),i=(g(/^https?:\/\//.test(t),n,"invalid-emulator-scheme"),!(null==r||!r.disableWarnings)),s=Ct(t),{host:a,port:o}=(e=>{var t,r=Ct(e);return(r=/(\/\/)?([^?#/]+)/.exec(e.substr(r.length)))?(r=r[2].split("@").pop()||"",(t=/^(\[[^\]]+\])(:|$)/.exec(r))?{host:t=t[1],port:Ot(r.substr(t.length+1))}:([t,r]=r.split(":"),{host:t,port:Ot(r)})):{host:"",port:null}})(t),l=null===o?"":":"+o,c={url:s+`//${a}${l}/`},o=Object.freeze({host:a,port:o,protocol:s.replace(":",""),options:Object.freeze({disableWarnings:i})});function d(){var e=document.createElement("p"),t=e.style;e.innerText="Running in emulator mode. Do not use with production credentials.",t.position="fixed",t.width="100%",t.backgroundColor="#ffffff",t.border=".1em solid #000000",t.color="#b50000",t.bottom="0px",t.left="0px",t.margin="0px",t.zIndex="10000",t.textAlign="center",e.classList.add("firebase-emulator-warning"),document.body.appendChild(e)}n._canInitEmulator?(n.config.emulator=c,n.emulatorConfig=o,n.settings.appVerificationDisabledForTesting=!0,G(a)?((async e=>(await fetch(e,{credentials:"include"})).ok)(s+"//"+a+l),Y("Auth",!0)):i||("undefined"!=typeof console&&"function"==typeof console.info&&console.info("WARNING: You are using the Auth Emulator, which is intended for local testing only.  Do not use with production credentials."),"undefined"!=typeof window&&"undefined"!=typeof document&&("loading"===document.readyState?window.addEventListener("DOMContentLoaded",d):d()))):(g(n.config.emulator&&n.emulatorConfig,n,"emulator-config-failed"),g(ne(c,n.config.emulator)&&ne(o,n.emulatorConfig),n,"emulator-config-failed"))}function Ct(e){var t=e.indexOf(":");return t<0?"":e.substr(0,t+1)}function Ot(e){var t;return!e||(t=Number(e),isNaN(t))?null:t}class Nt{constructor(e,t){this.providerId=e,this.signInMethod=t}toJSON(){return i("not implemented")}_getIdTokenResponse(e){return i("not implemented")}_linkToIdToken(e,t){return i("not implemented")}_getReauthenticationResolver(e){return i("not implemented")}}async function Lt(e,t){return m(e,"POST","/v1/accounts:resetPassword",p(e,t))}async function Dt(e,t){return m(e,"POST","/v1/accounts:signUp",t)}async function Mt(e,t){return s(e,"POST","/v1/accounts:signInWithPassword",p(e,t))}async function Ut(e,t){return m(e,"POST","/v1/accounts:sendOobCode",p(e,t))}async function Ft(e,t){return Ut(e,t)}async function Vt(e,t){return Ut(e,t)}class xt extends Nt{constructor(e,t,r,n=null){super("password",r),this._email=e,this._password=t,this._tenantId=n}static _fromEmailAndPassword(e,t){return new xt(e,t,"password")}static _fromEmailAndCode(e,t,r=null){return new xt(e,t,"emailLink",r)}toJSON(){return{email:this._email,password:this._password,signInMethod:this.signInMethod,tenantId:this._tenantId}}static fromJSON(e){var t="string"==typeof e?JSON.parse(e):e;if(null!=t&&t.email&&null!=t&&t.password){if("password"===t.signInMethod)return this._fromEmailAndPassword(t.email,t.password);if("emailLink"===t.signInMethod)return this._fromEmailAndCode(t.email,t.password,t.tenantId)}return null}async _getIdTokenResponse(e){switch(this.signInMethod){case"password":return T(e,{returnSecureToken:!0,email:this._email,password:this._password,clientType:"CLIENT_TYPE_WEB"},"signInWithPassword",Mt,"EMAIL_PASSWORD_PROVIDER");case"emailLink":return(async(e,t)=>s(e,"POST","/v1/accounts:signInWithEmailLink",p(e,t)))(e,{email:this._email,oobCode:this._password});default:u(e,"internal-error")}}async _linkToIdToken(e,t){switch(this.signInMethod){case"password":return T(e,{idToken:t,returnSecureToken:!0,email:this._email,password:this._password,clientType:"CLIENT_TYPE_WEB"},"signUpPassword",Dt,"EMAIL_PASSWORD_PROVIDER");case"emailLink":return(async(e,t)=>s(e,"POST","/v1/accounts:signInWithEmailLink",p(e,t)))(e,{idToken:t,email:this._email,oobCode:this._password});default:u(e,"internal-error")}}_getReauthenticationResolver(e){return this._getIdTokenResponse(e)}}async function E(e,t){return s(e,"POST","/v1/accounts:signInWithIdp",p(e,t))}class b extends Nt{constructor(){super(...arguments),this.pendingToken=null}static _fromParams(e){var t=new b(e.providerId,e.signInMethod);return e.idToken||e.accessToken?(e.idToken&&(t.idToken=e.idToken),e.accessToken&&(t.accessToken=e.accessToken),e.nonce&&!e.pendingToken&&(t.nonce=e.nonce),e.pendingToken&&(t.pendingToken=e.pendingToken)):e.oauthToken&&e.oauthTokenSecret?(t.accessToken=e.oauthToken,t.secret=e.oauthTokenSecret):u("argument-error"),t}toJSON(){return{idToken:this.idToken,accessToken:this.accessToken,secret:this.secret,nonce:this.nonce,pendingToken:this.pendingToken,providerId:this.providerId,signInMethod:this.signInMethod}}static fromJSON(e){var t="string"==typeof e?JSON.parse(e):e,{providerId:r,signInMethod:n}=t,t=me(t,["providerId","signInMethod"]);return r&&n?((r=new b(r,n)).idToken=t.idToken||void 0,r.accessToken=t.accessToken||void 0,r.secret=t.secret,r.nonce=t.nonce,r.pendingToken=t.pendingToken||null,r):null}_getIdTokenResponse(e){return E(e,this.buildRequest())}_linkToIdToken(e,t){var r=this.buildRequest();return r.idToken=t,E(e,r)}_getReauthenticationResolver(e){var t=this.buildRequest();return t.autoCreate=!1,E(e,t)}buildRequest(){var e,t={requestUri:"http://localhost",returnSecureToken:!0};return this.pendingToken?t.pendingToken=this.pendingToken:(e={},this.idToken&&(e.id_token=this.idToken),this.accessToken&&(e.access_token=this.accessToken),this.secret&&(e.oauth_token_secret=this.secret),e.providerId=this.providerId,this.nonce&&!this.pendingToken&&(e.nonce=this.nonce),t.postBody=se(e)),t}}async function jt(e,t){return m(e,"POST","/v1/accounts:sendVerificationCode",p(e,t))}let Ht={USER_NOT_FOUND:"user-not-found"};class Wt extends Nt{constructor(e){super("phone","phone"),this.params=e}static _fromVerification(e,t){return new Wt({verificationId:e,verificationCode:t})}static _fromTokenResponse(e,t){return new Wt({phoneNumber:e,temporaryProof:t})}_getIdTokenResponse(e){return(async(e,t)=>s(e,"POST","/v1/accounts:signInWithPhoneNumber",p(e,t)))(e,this._makeVerificationRequest())}_linkToIdToken(e,t){return(async(e,t)=>{var r=await s(e,"POST","/v1/accounts:signInWithPhoneNumber",p(e,t));if(r.temporaryProof)throw xe(e,"account-exists-with-different-credential",r);return r})(e,Object.assign({idToken:t},this._makeVerificationRequest()))}_getReauthenticationResolver(e){return(async(e,t)=>s(e,"POST","/v1/accounts:signInWithPhoneNumber",p(e,Object.assign(Object.assign({},t),{operation:"REAUTH"})),Ht))(e,this._makeVerificationRequest())}_makeVerificationRequest(){var{temporaryProof:e,phoneNumber:t,verificationId:r,verificationCode:n}=this.params;return e&&t?{temporaryProof:e,phoneNumber:t}:{sessionInfo:r,code:n}}toJSON(){var e={providerId:this.providerId};return this.params.phoneNumber&&(e.phoneNumber=this.params.phoneNumber),this.params.temporaryProof&&(e.temporaryProof=this.params.temporaryProof),this.params.verificationCode&&(e.verificationCode=this.params.verificationCode),this.params.verificationId&&(e.verificationId=this.params.verificationId),e}static fromJSON(e){var{verificationId:t,verificationCode:r,phoneNumber:n,temporaryProof:i}=e="string"==typeof e?JSON.parse(e):e;return r||t||n||i?new Wt({verificationId:t,verificationCode:r,phoneNumber:n,temporaryProof:i}):null}}class qt{constructor(e){var t=ae(oe(e)),r=null!=(r=t.apiKey)?r:null,n=null!=(n=t.oobCode)?n:null,i=(e=>{switch(e){case"recoverEmail":return"RECOVER_EMAIL";case"resetPassword":return"PASSWORD_RESET";case"signIn":return"EMAIL_SIGNIN";case"verifyEmail":return"VERIFY_EMAIL";case"verifyAndChangeEmail":return"VERIFY_AND_CHANGE_EMAIL";case"revertSecondFactorAddition":return"REVERT_SECOND_FACTOR_ADDITION";default:return null}})(null!=(i=t.mode)?i:null);g(r&&n&&i,"argument-error"),this.apiKey=r,this.operation=i,this.code=n,this.continueUrl=null!=(r=t.continueUrl)?r:null,this.languageCode=null!=(i=t.lang)?i:null,this.tenantId=null!=(n=t.tenantId)?n:null}static parseLink(e){t=ae(oe(e=e)).link,r=t?ae(oe(t)).deep_link_id:null;var t,r,n=((n=ae(oe(e)).deep_link_id)?ae(oe(n)).link:null)||n||r||t||e;try{return new qt(n)}catch(e){return null}}}class Bt{constructor(){this.providerId=Bt.PROVIDER_ID}static credential(e,t){return xt._fromEmailAndPassword(e,t)}static credentialWithLink(e,t){var r=qt.parseLink(t);return g(r,"argument-error"),xt._fromEmailAndCode(e,r.code,r.tenantId)}}Bt.PROVIDER_ID="password",Bt.EMAIL_PASSWORD_SIGN_IN_METHOD="password",Bt.EMAIL_LINK_SIGN_IN_METHOD="emailLink";class k{constructor(e){this.providerId=e,this.defaultLanguageCode=null,this.customParameters={}}setDefaultLanguage(e){this.defaultLanguageCode=e}setCustomParameters(e){return this.customParameters=e,this}getCustomParameters(){return this.customParameters}}class zt extends k{constructor(){super(...arguments),this.scopes=[]}addScope(e){return this.scopes.includes(e)||this.scopes.push(e),this}getScopes(){return[...this.scopes]}}class Gt extends zt{static credentialFromJSON(e){var t="string"==typeof e?JSON.parse(e):e;return g("providerId"in t&&"signInMethod"in t,"argument-error"),b._fromParams(t)}credential(e){return this._credential(Object.assign(Object.assign({},e),{nonce:e.rawNonce}))}_credential(e){return g(e.idToken||e.accessToken,"argument-error"),b._fromParams(Object.assign(Object.assign({},e),{providerId:this.providerId,signInMethod:this.providerId}))}static credentialFromResult(e){return Gt.oauthCredentialFromTaggedObject(e)}static credentialFromError(e){return Gt.oauthCredentialFromTaggedObject(e.customData||{})}static oauthCredentialFromTaggedObject({_tokenResponse:e}){if(!e)return null;var{oauthIdToken:t,oauthAccessToken:r,oauthTokenSecret:n,pendingToken:i,nonce:s,providerId:a}=e;if(!(r||n||t||i))return null;if(!a)return null;try{return new Gt(a)._credential({idToken:t,accessToken:r,nonce:s,pendingToken:i})}catch(e){return null}}}class S extends zt{constructor(){super("facebook.com")}static credential(e){return b._fromParams({providerId:S.PROVIDER_ID,signInMethod:S.FACEBOOK_SIGN_IN_METHOD,accessToken:e})}static credentialFromResult(e){return S.credentialFromTaggedObject(e)}static credentialFromError(e){return S.credentialFromTaggedObject(e.customData||{})}static credentialFromTaggedObject({_tokenResponse:e}){if(!(e&&"oauthAccessToken"in e))return null;if(!e.oauthAccessToken)return null;try{return S.credential(e.oauthAccessToken)}catch(e){return null}}}S.FACEBOOK_SIGN_IN_METHOD="facebook.com",S.PROVIDER_ID="facebook.com";class R extends zt{constructor(){super("google.com"),this.addScope("profile")}static credential(e,t){return b._fromParams({providerId:R.PROVIDER_ID,signInMethod:R.GOOGLE_SIGN_IN_METHOD,idToken:e,accessToken:t})}static credentialFromResult(e){return R.credentialFromTaggedObject(e)}static credentialFromError(e){return R.credentialFromTaggedObject(e.customData||{})}static credentialFromTaggedObject({_tokenResponse:e}){if(!e)return null;var{oauthIdToken:t,oauthAccessToken:r}=e;if(!t&&!r)return null;try{return R.credential(t,r)}catch(e){return null}}}R.GOOGLE_SIGN_IN_METHOD="google.com",R.PROVIDER_ID="google.com";class A extends zt{constructor(){super("github.com")}static credential(e){return b._fromParams({providerId:A.PROVIDER_ID,signInMethod:A.GITHUB_SIGN_IN_METHOD,accessToken:e})}static credentialFromResult(e){return A.credentialFromTaggedObject(e)}static credentialFromError(e){return A.credentialFromTaggedObject(e.customData||{})}static credentialFromTaggedObject({_tokenResponse:e}){if(!(e&&"oauthAccessToken"in e))return null;if(!e.oauthAccessToken)return null;try{return A.credential(e.oauthAccessToken)}catch(e){return null}}}A.GITHUB_SIGN_IN_METHOD="github.com",A.PROVIDER_ID="github.com";class Kt extends Nt{constructor(e,t){super(e,e),this.pendingToken=t}_getIdTokenResponse(e){return E(e,this.buildRequest())}_linkToIdToken(e,t){var r=this.buildRequest();return r.idToken=t,E(e,r)}_getReauthenticationResolver(e){var t=this.buildRequest();return t.autoCreate=!1,E(e,t)}toJSON(){return{signInMethod:this.signInMethod,providerId:this.providerId,pendingToken:this.pendingToken}}static fromJSON(e){var{providerId:t,signInMethod:r,pendingToken:n}="string"==typeof e?JSON.parse(e):e;return t&&r&&n&&t===r?new Kt(t,n):null}static _create(e,t){return new Kt(e,t)}buildRequest(){return{requestUri:"http://localhost",returnSecureToken:!0,pendingToken:this.pendingToken}}}class Jt extends k{constructor(e){g(e.startsWith("saml."),"argument-error"),super(e)}static credentialFromResult(e){return Jt.samlCredentialFromTaggedObject(e)}static credentialFromError(e){return Jt.samlCredentialFromTaggedObject(e.customData||{})}static credentialFromJSON(e){var t=Kt.fromJSON(e);return g(t,"argument-error"),t}static samlCredentialFromTaggedObject({_tokenResponse:e}){if(!e)return null;var{pendingToken:t,providerId:r}=e;if(!t||!r)return null;try{return Kt._create(r,t)}catch(e){return null}}}class P extends zt{constructor(){super("twitter.com")}static credential(e,t){return b._fromParams({providerId:P.PROVIDER_ID,signInMethod:P.TWITTER_SIGN_IN_METHOD,oauthToken:e,oauthTokenSecret:t})}static credentialFromResult(e){return P.credentialFromTaggedObject(e)}static credentialFromError(e){return P.credentialFromTaggedObject(e.customData||{})}static credentialFromTaggedObject({_tokenResponse:e}){if(!e)return null;var{oauthAccessToken:t,oauthTokenSecret:r}=e;if(!t||!r)return null;try{return P.credential(t,r)}catch(e){return null}}}async function Yt(e,t){return s(e,"POST","/v1/accounts:signUp",p(e,t))}P.TWITTER_SIGN_IN_METHOD="twitter.com",P.PROVIDER_ID="twitter.com";class C{constructor(e){this.user=e.user,this.providerId=e.providerId,this._tokenResponse=e._tokenResponse,this.operationType=e.operationType}static async _fromIdTokenResponse(e,t,r,n=!1){var i=await _._fromIdTokenResponse(e,r,n),s=$t(r);return new C({user:i,providerId:s,_tokenResponse:r,operationType:t})}static async _forOperation(e,t,r){await e._updateTokensIfNecessary(r,!0);var n=$t(r);return new C({user:e,providerId:n,_tokenResponse:r,operationType:t})}}function $t(e){return e.providerId||("phoneNumber"in e?"phone":null)}class Xt extends d{constructor(e,t,r,n){var i;super(t.code,t.message),this.operationType=r,this.user=n,Object.setPrototypeOf(this,Xt.prototype),this.customData={appName:e.name,tenantId:null!=(i=e.tenantId)?i:void 0,_serverResponse:t.customData._serverResponse,operationType:r}}static _fromErrorAndOperation(e,t,r,n){return new Xt(e,t,r,n)}}function Zt(t,r,e,n){return("reauthenticate"===r?e._getReauthenticationResolver(t):e._getIdTokenResponse(t)).catch(e=>{if("auth/multi-factor-auth-required"===e.code)throw Xt._fromErrorAndOperation(t,e,r,n);throw e})}function Qt(e){return new Set(e.map(({providerId:e})=>e).filter(e=>!!e))}async function er(e,t){var r=o(e),n=(await rr(!0,r,t),e=r.auth,t={idToken:await r.getIdToken(),deleteProvider:[t]},await m(e,"POST","/v1/accounts:update",t)).providerUserInfo;let i=Qt(n||[]);return r.providerData=r.providerData.filter(e=>i.has(e.providerId)),i.has("phone")||(r.phoneNumber=null),await r.auth._persistUserIfCurrent(r),r}async function tr(e,t,r=!1){var n=await f(e,t._linkToIdToken(e.auth,await e.getIdToken()),r);return C._forOperation(e,"link",n)}async function rr(e,t,r){await Xe(t);var n=!1===e?"provider-already-linked":"no-such-provider";g(Qt(t.providerData).has(r)===e,t.auth,n)}async function nr(e,t,r=!1){var n=e.auth;if(Ki._isFirebaseServerApp(n.app))return Promise.reject(l(n));var i="reauthenticate";try{var s=await f(e,Zt(n,i,t,e),r),a=(g(s.idToken,n,"internal-error"),Ke(s.idToken)),o=(g(a,n,"internal-error"),a).sub;return g(e.uid===o,n,"user-mismatch"),C._forOperation(e,i,s)}catch(e){throw"auth/user-not-found"===(null==e?void 0:e.code)&&u(n,"user-mismatch"),e}}async function ir(e,t,r=!1){var n;return Ki._isFirebaseServerApp(e.app)?Promise.reject(l(e)):(n=await Zt(e,"signIn",t),n=await C._fromIdTokenResponse(e,"signIn",n),r||await e._updateCurrentUser(n.user),n)}async function sr(e,t){return ir(w(e),t)}async function ar(e,t){var r=o(e);return await rr(!1,r,t.providerId),tr(r,t)}async function or(e,t){return nr(o(e),t)}async function lr(e,t){var r,n;return Ki._isFirebaseServerApp(e.app)?Promise.reject(l(e)):(n=await s(r=w(e),"POST","/v1/accounts:signInWithCustomToken",p(r,{token:t,returnSecureToken:!0})),n=await C._fromIdTokenResponse(r,"signIn",n),await r._updateCurrentUser(n.user),n)}class cr{constructor(e,t){this.factorId=e,this.uid=t.mfaEnrollmentId,this.enrollmentTime=new Date(t.enrolledAt).toUTCString(),this.displayName=t.displayName}static _fromServerResponse(e,t){return"phoneInfo"in t?dr._fromServerResponse(e,t):"totpInfo"in t?ur._fromServerResponse(e,t):u(e,"internal-error")}}class dr extends cr{constructor(e){super("phone",e),this.phoneNumber=e.phoneInfo}static _fromServerResponse(e,t){return new dr(t)}}class ur extends cr{constructor(e){super("totp",e)}static _fromServerResponse(e,t){return new ur(t)}}function hr(e,t,r){var n;g(0<(null==(n=r.url)?void 0:n.length),e,"invalid-continue-uri"),g(void 0===r.dynamicLinkDomain||0<r.dynamicLinkDomain.length,e,"invalid-dynamic-link-domain"),g(void 0===r.linkDomain||0<r.linkDomain.length,e,"invalid-hosting-link-domain"),t.continueUrl=r.url,t.dynamicLinkDomain=r.dynamicLinkDomain,t.linkDomain=r.linkDomain,t.canHandleCodeInApp=r.handleCodeInApp,r.iOS&&(g(0<r.iOS.bundleId.length,e,"missing-ios-bundle-id"),t.iOSBundleId=r.iOS.bundleId),r.android&&(g(0<r.android.packageName.length,e,"missing-android-pkg-name"),t.androidInstallApp=r.android.installApp,t.androidMinimumVersionCode=r.android.minimumVersion,t.androidPackageName=r.android.packageName)}async function pr(e){var t=w(e);t._getPasswordPolicyInternal()&&await t._updatePasswordPolicy()}async function mr(e,t){await m(e=o(e),"POST","/v1/accounts:update",p(e,{oobCode:t}))}async function gr(e,t){var r=o(e),n=await Lt(r,{oobCode:t}),i=n.requestType;switch(g(i,r,"internal-error"),i){case"EMAIL_SIGNIN":break;case"VERIFY_AND_CHANGE_EMAIL":g(n.newEmail,r,"internal-error");break;case"REVERT_SECOND_FACTOR_ADDITION":g(n.mfaInfo,r,"internal-error");default:g(n.email,r,"internal-error")}let s=null;return n.mfaInfo&&(s=cr._fromServerResponse(w(r),n.mfaInfo)),{data:{email:("VERIFY_AND_CHANGE_EMAIL"===n.requestType?n.newEmail:n.email)||null,previousEmail:("VERIFY_AND_CHANGE_EMAIL"===n.requestType?n.email:n.newEmail)||null,multiFactorInfo:s},operation:i}}async function fr(e,t){var r=Ae()?Re():"http://localhost",r=(await m(e=o(e),"POST","/v1/accounts:createAuthUri",p(e,{identifier:t,continueUri:r}))).signinMethods;return r||[]}async function vr(e,t){var r=o(e),n={requestType:"VERIFY_EMAIL",idToken:await e.getIdToken()},r=(t&&hr(r.auth,n,t),await Ut(r.auth,n)).email;r!==e.email&&await e.reload()}async function _r(e,t,r){var n=o(e),i={requestType:"VERIFY_AND_CHANGE_EMAIL",idToken:await e.getIdToken(),newEmail:t},n=(r&&hr(n.auth,i,r),await Ut(n.auth,i)).email;n!==e.email&&await e.reload()}async function yr(e,{displayName:t,photoURL:r}){var n,i,s;void 0===t&&void 0===r||(i=await(n=o(e)).getIdToken(),i=await f(n,(async(e,t)=>m(e,"POST","/v1/accounts:update",t))(n.auth,{idToken:i,displayName:t,photoUrl:r,returnSecureToken:!0})),n.displayName=i.displayName||null,n.photoURL=i.photoUrl||null,(s=n.providerData.find(({providerId:e})=>"password"===e))&&(s.displayName=n.displayName,s.photoURL=n.photoURL),await n._updateTokensIfNecessary(i))}async function Ir(e,t,r){var n=e.auth,i={idToken:await e.getIdToken(),returnSecureToken:!0},n=(t&&(i.email=t),r&&(i.password=r),await f(e,(async(e,t)=>m(e,"POST","/v1/accounts:update",t))(n,i)));await e._updateTokensIfNecessary(n,!0)}class wr{constructor(e,t,r={}){this.isNewUser=e,this.providerId=t,this.profile=r}}class Tr extends wr{constructor(e,t,r,n){super(e,t,r),this.username=n}}class Er extends wr{constructor(e,t){super(e,"facebook.com",t)}}class br extends Tr{constructor(e,t){super(e,"github.com",t,"string"==typeof(null==t?void 0:t.login)?null==t?void 0:t.login:null)}}class kr extends wr{constructor(e,t){super(e,"google.com",t)}}class Sr extends Tr{constructor(e,t,r){super(e,"twitter.com",t,r)}}function Rr(e){var{user:t,_tokenResponse:r}=e;if(t.isAnonymous&&!r)return{providerId:null,isNewUser:!1,profile:null};var n=r;if(!n)return null;var i=n.providerId,s=n.rawUserInfo?JSON.parse(n.rawUserInfo):{},a=n.isNewUser||"identitytoolkit#SignupNewUserResponse"===n.kind;if(!i&&null!=n&&n.idToken){t=null==(t=null==(t=Ke(n.idToken))?void 0:t.firebase)?void 0:t.sign_in_provider;if(t)return t="anonymous"!==t&&"custom"!==t?t:null,new wr(a,t)}if(!i)return null;switch(i){case"facebook.com":return new Er(a,s);case"github.com":return new br(a,s);case"google.com":return new kr(a,s);case"twitter.com":return new Sr(a,s,n.screenName||null);case"custom":case"anonymous":return new wr(a,null);default:return new wr(a,i,s)}}class Ar{constructor(e,t,r){this.type=e,this.credential=t,this.user=r}static _fromIdtoken(e,t){return new Ar("enroll",e,t)}static _fromMfaPendingCredential(e){return new Ar("signin",e)}toJSON(){return{multiFactorSession:{["enroll"===this.type?"idToken":"pendingCredential"]:this.credential}}}static fromJSON(e){var t;if(null!=e&&e.multiFactorSession){if(null!=(t=e.multiFactorSession)&&t.pendingCredential)return Ar._fromMfaPendingCredential(e.multiFactorSession.pendingCredential);if(null!=(t=e.multiFactorSession)&&t.idToken)return Ar._fromIdtoken(e.multiFactorSession.idToken)}return null}}class Pr{constructor(e,t,r){this.session=e,this.hints=t,this.signInResolver=r}static _fromError(e,i){let s=w(e),a=i.customData._serverResponse;var t=(a.mfaInfo||[]).map(e=>cr._fromServerResponse(s,e));g(a.mfaPendingCredential,s,"internal-error");let o=Ar._fromMfaPendingCredential(a.mfaPendingCredential);return new Pr(o,t,async e=>{var t=await e._process(s,o),r=(delete a.mfaInfo,delete a.mfaPendingCredential,Object.assign(Object.assign({},a),{idToken:t.idToken,refreshToken:t.refreshToken}));switch(i.operationType){case"signIn":var n=await C._fromIdTokenResponse(s,i.operationType,r);return await s._updateCurrentUser(n.user),n;case"reauthenticate":return g(i.user,s,"internal-error"),C._forOperation(i.user,i.operationType,r);default:u(s,"internal-error")}})}async resolveSignIn(e){return this.signInResolver(e)}}function Cr(e,t){return m(e,"POST","/v2/accounts/mfaEnrollment:start",p(e,t))}class Or{constructor(t){this.user=t,this.enrolledFactors=[],t._onReload(e=>{e.mfaInfo&&(this.enrolledFactors=e.mfaInfo.map(e=>cr._fromServerResponse(t.auth,e)))})}static _fromUser(e){return new Or(e)}async getSession(){return Ar._fromIdtoken(await this.user.getIdToken(),this.user)}async enroll(e,t){var r=e,n=await this.getSession(),r=await f(this.user,r._process(this.user.auth,n,t));return await this.user._updateTokensIfNecessary(r),this.user.reload()}async unenroll(e){let t="string"==typeof e?e:e.uid;var r,n,i=await this.user.getIdToken();try{var s=await f(this.user,(r=this.user.auth,n={idToken:i,mfaEnrollmentId:t},m(r,"POST","/v2/accounts/mfaEnrollment:withdraw",p(r,n))));this.enrolledFactors=this.enrolledFactors.filter(({uid:e})=>e!==t),await this.user._updateTokensIfNecessary(s),await this.user.reload()}catch(e){throw e}}}let Nr=new WeakMap;let Lr="__sak";class Dr{constructor(e,t){this.storageRetriever=e,this.type=t}_isAvailable(){try{return this.storage?(this.storage.setItem(Lr,"1"),this.storage.removeItem(Lr),Promise.resolve(!0)):Promise.resolve(!1)}catch(e){return Promise.resolve(!1)}}_set(e,t){return this.storage.setItem(e,JSON.stringify(t)),Promise.resolve()}_get(e){var t=this.storage.getItem(e);return Promise.resolve(t?JSON.parse(t):null)}_remove(e){return this.storage.removeItem(e),Promise.resolve()}get storage(){return this.storageRetriever()}}class Mr extends Dr{constructor(){super(()=>window.localStorage,"LOCAL"),this.boundEventHandler=(e,t)=>this.onStorageEvent(e,t),this.listeners={},this.localCache={},this.pollTimer=null,this.fallbackToPolling=pt(),this._shouldAllowMigration=!0}forAllChangedKeys(e){for(var t of Object.keys(this.listeners)){var r=this.storage.getItem(t),n=this.localCache[t];r!==n&&e(t,n,r)}}onStorageEvent(e,r=!1){if(e.key){let t=e.key;r?this.detachListener():this.stopPolling();var n=()=>{var e=this.storage.getItem(t);!r&&this.localCache[t]===e||this.notifyListeners(t,e)},i=this.storage.getItem(t);Q()&&10===document.documentMode&&i!==e.newValue&&e.newValue!==e.oldValue?setTimeout(n,10):n()}else this.forAllChangedKeys((e,t,r)=>{this.notifyListeners(e,r)})}notifyListeners(e,t){this.localCache[e]=t;var r=this.listeners[e];if(r)for(var n of Array.from(r))n(t&&JSON.parse(t))}startPolling(){this.stopPolling(),this.pollTimer=setInterval(()=>{this.forAllChangedKeys((e,t,r)=>{this.onStorageEvent(new StorageEvent("storage",{key:e,oldValue:t,newValue:r}),!0)})},1e3)}stopPolling(){this.pollTimer&&(clearInterval(this.pollTimer),this.pollTimer=null)}attachListener(){window.addEventListener("storage",this.boundEventHandler)}detachListener(){window.removeEventListener("storage",this.boundEventHandler)}_addListener(e,t){0===Object.keys(this.listeners).length&&(this.fallbackToPolling?this.startPolling():this.attachListener()),this.listeners[e]||(this.listeners[e]=new Set,this.localCache[e]=this.storage.getItem(e)),this.listeners[e].add(t)}_removeListener(e,t){this.listeners[e]&&(this.listeners[e].delete(t),0===this.listeners[e].size)&&delete this.listeners[e],0===Object.keys(this.listeners).length&&(this.detachListener(),this.stopPolling())}async _set(e,t){await super._set(e,t),this.localCache[e]=JSON.stringify(t)}async _get(e){var t=await super._get(e);return this.localCache[e]=JSON.stringify(t),t}async _remove(e){await super._remove(e),delete this.localCache[e]}}Mr.type="LOCAL";let Ur=Mr;class Fr extends Dr{constructor(){super(()=>window.sessionStorage,"SESSION")}_addListener(e,t){}_removeListener(e,t){}}Fr.type="SESSION";let Vr=Fr;class xr{constructor(e){this.eventTarget=e,this.handlersMap={},this.boundEventHandler=this.handleEvent.bind(this)}static _getInstance(t){var e=this.receivers.find(e=>e.isListeningto(t));return e||(e=new xr(t),this.receivers.push(e),e)}isListeningto(e){return this.eventTarget===e}async handleEvent(e){let t=e,{eventId:r,eventType:n,data:i}=t.data;var s=this.handlersMap[n];null!=s&&s.size&&(t.ports[0].postMessage({status:"ack",eventId:r,eventType:n}),s=Array.from(s).map(async e=>e(t.origin,i)),s=await Promise.all(s.map(async e=>{try{return{fulfilled:!0,value:await e}}catch(e){return{fulfilled:!1,reason:e}}})),t.ports[0].postMessage({status:"done",eventId:r,eventType:n,response:s}))}_subscribe(e,t){0===Object.keys(this.handlersMap).length&&this.eventTarget.addEventListener("message",this.boundEventHandler),this.handlersMap[e]||(this.handlersMap[e]=new Set),this.handlersMap[e].add(t)}_unsubscribe(e,t){this.handlersMap[e]&&t&&this.handlersMap[e].delete(t),t&&0!==this.handlersMap[e].size||delete this.handlersMap[e],0===Object.keys(this.handlersMap).length&&this.eventTarget.removeEventListener("message",this.boundEventHandler)}}function jr(e="",t=10){let r="";for(let n=0;n<t;n++)r+=Math.floor(10*Math.random());return e+r}xr.receivers=[];class Hr{constructor(e){this.target=e,this.handlers=new Set}removeMessageHandler(e){e.messageChannel&&(e.messageChannel.port1.removeEventListener("message",e.onMessage),e.messageChannel.port1.close()),this.handlers.delete(e)}async _send(e,t,a=50){let o="undefined"!=typeof MessageChannel?new MessageChannel:null;if(!o)throw new Error("connection_unavailable");let l,c;return new Promise((r,n)=>{let i=jr("",20),s=(o.port1.start(),setTimeout(()=>{n(new Error("unsupported_event"))},a));c={messageChannel:o,onMessage(e){var t=e;if(t.data.eventId===i)switch(t.data.status){case"ack":clearTimeout(s),l=setTimeout(()=>{n(new Error("timeout"))},3e3);break;case"done":clearTimeout(l),r(t.data.response);break;default:clearTimeout(s),clearTimeout(l),n(new Error("invalid_response"))}}},this.handlers.add(c),o.port1.addEventListener("message",c.onMessage),this.target.postMessage({eventType:e,eventId:i,data:t},[o.port2])}).finally(()=>{c&&this.removeMessageHandler(c)})}}function O(){return window}function Wr(){return void 0!==O().WorkerGlobalScope&&"function"==typeof O().importScripts}let qr="firebaseLocalStorageDb",Br="firebaseLocalStorage",zr="fbase_key";class Gr{constructor(e){this.request=e}toPromise(){return new Promise((e,t)=>{this.request.addEventListener("success",()=>{e(this.request.result)}),this.request.addEventListener("error",()=>{t(this.request.error)})})}}function Kr(e,t){return e.transaction([Br],t?"readwrite":"readonly").objectStore(Br)}function Jr(){let n=indexedDB.open(qr,1);return new Promise((t,r)=>{n.addEventListener("error",()=>{r(n.error)}),n.addEventListener("upgradeneeded",()=>{var e=n.result;try{e.createObjectStore(Br,{keyPath:zr})}catch(e){r(e)}}),n.addEventListener("success",async()=>{var e=n.result;e.objectStoreNames.contains(Br)?t(e):(e.close(),e=indexedDB.deleteDatabase(qr),await new Gr(e).toPromise(),t(await Jr()))})})}async function Yr(e,t,r){var n=Kr(e,!0).put({fbase_key:t,value:r});return new Gr(n).toPromise()}function $r(e,t){var r=Kr(e,!0).delete(t);return new Gr(r).toPromise()}class Xr{constructor(){this.type="LOCAL",this._shouldAllowMigration=!0,this.listeners={},this.localCache={},this.pollTimer=null,this.pendingWrites=0,this.receiver=null,this.sender=null,this.serviceWorkerReceiverAvailable=!1,this.activeServiceWorker=null,this._workerInitializationPromise=this.initializeServiceWorkerMessaging().then(()=>{},()=>{})}async _openDb(){return this.db||(this.db=await Jr()),this.db}async _withRetries(e){let t=0;for(;;)try{return await e(await this._openDb())}catch(e){if(3<t++)throw e;this.db&&(this.db.close(),this.db=void 0)}}async initializeServiceWorkerMessaging(){return Wr()?this.initializeReceiver():this.initializeSender()}async initializeReceiver(){this.receiver=xr._getInstance(Wr()?self:null),this.receiver._subscribe("keyChanged",async(e,t)=>({keyProcessed:(await this._poll()).includes(t.key)})),this.receiver._subscribe("ping",async(e,t)=>["keyChanged"])}async initializeSender(){var e,t;this.activeServiceWorker=await(async()=>{if(null==navigator||!navigator.serviceWorker)return null;try{return(await navigator.serviceWorker.ready).active}catch(e){return null}})(),this.activeServiceWorker&&(this.sender=new Hr(this.activeServiceWorker),t=await this.sender._send("ping",{},800))&&null!=(e=t[0])&&e.fulfilled&&null!=(e=t[0])&&e.value.includes("keyChanged")&&(this.serviceWorkerReceiverAvailable=!0)}async notifyServiceWorker(e){var t;if(this.sender&&this.activeServiceWorker&&((null==(t=null==navigator?void 0:navigator.serviceWorker)?void 0:t.controller)||null)===this.activeServiceWorker)try{await this.sender._send("keyChanged",{key:e},this.serviceWorkerReceiverAvailable?800:50)}catch(e){}}async _isAvailable(){try{var e;return indexedDB?(await Yr(e=await Jr(),Lr,"1"),await $r(e,Lr),!0):!1}catch(e){}return!1}async _withPendingWrite(e){this.pendingWrites++;try{await e()}finally{this.pendingWrites--}}async _set(t,r){return this._withPendingWrite(async()=>(await this._withRetries(e=>Yr(e,t,r)),this.localCache[t]=r,this.notifyServiceWorker(t)))}async _get(t){var e=await this._withRetries(e=>(async(e,t)=>{var r=Kr(e,!1).get(t);return void 0===(r=await new Gr(r).toPromise())?null:r.value})(e,t));return this.localCache[t]=e}async _remove(t){return this._withPendingWrite(async()=>(await this._withRetries(e=>$r(e,t)),delete this.localCache[t],this.notifyServiceWorker(t)))}async _poll(){var e=await this._withRetries(e=>{var t=Kr(e,!1).getAll();return new Gr(t).toPromise()});if(!e)return[];if(0!==this.pendingWrites)return[];var t,r=[],n=new Set;if(0!==e.length)for(var{fbase_key:i,value:s}of e)n.add(i),JSON.stringify(this.localCache[i])!==JSON.stringify(s)&&(this.notifyListeners(i,s),r.push(i));for(t of Object.keys(this.localCache))this.localCache[t]&&!n.has(t)&&(this.notifyListeners(t,null),r.push(t));return r}notifyListeners(e,t){this.localCache[e]=t;var r=this.listeners[e];if(r)for(var n of Array.from(r))n(t)}startPolling(){this.stopPolling(),this.pollTimer=setInterval(async()=>this._poll(),800)}stopPolling(){this.pollTimer&&(clearInterval(this.pollTimer),this.pollTimer=null)}_addListener(e,t){0===Object.keys(this.listeners).length&&this.startPolling(),this.listeners[e]||(this.listeners[e]=new Set,this._get(e)),this.listeners[e].add(t)}_removeListener(e,t){this.listeners[e]&&(this.listeners[e].delete(t),0===this.listeners[e].size)&&delete this.listeners[e],0===Object.keys(this.listeners).length&&this.stopPolling()}}Xr.type="LOCAL";let Zr=Xr;function Qr(e,t){return m(e,"POST","/v2/accounts/mfaSignIn:start",p(e,t))}let en=wt("rcb"),tn=new Ce(3e4,6e4);class rn{constructor(){var e;this.hostLanguage="",this.counter=0,this.librarySeparatelyLoaded=!(null==(e=O().grecaptcha)||!e.render)}load(i,s=""){var e;return g((e=s).length<=6&&/^\s*[a-zA-Z0-9\-]*\s*$/.test(e),i,"argument-error"),this.shouldResolveImmediately(s)&&je(O().grecaptcha)?Promise.resolve(O().grecaptcha):new Promise((t,r)=>{let n=O().setTimeout(()=>{r(h(i,"network-request-failed"))},tn.get());O()[en]=()=>{O().clearTimeout(n),delete O()[en];var e=O().grecaptcha;if(e&&je(e)){let n=e.render;e.render=(e,t)=>{var r=n(e,t);return this.counter++,r},this.hostLanguage=s,t(e)}else r(h(i,"internal-error"))},It(yt.recaptchaV2Script+"?"+se({onload:en,render:"explicit",hl:s})).catch(()=>{clearTimeout(n),r(h(i,"internal-error"))})})}clearedOneInstance(){this.counter--}shouldResolveImmediately(e){var t;return!(null==(t=O().grecaptcha)||!t.render)&&(e===this.hostLanguage||0<this.counter||this.librarySeparatelyLoaded)}}class nn{async load(e){return new Tt(e)}clearedOneInstance(){}}let sn="recaptcha",an={theme:"light",type:"image"};class on{constructor(e,t,r=Object.assign({},an)){this.parameters=r,this.type=sn,this.destroyed=!1,this.widgetId=null,this.tokenChangeListeners=new Set,this.renderPromise=null,this.recaptcha=null,this.auth=w(e),this.isInvisible="invisible"===this.parameters.size,g("undefined"!=typeof document,this.auth,"operation-not-supported-in-this-environment");var n="string"==typeof t?document.getElementById(t):t;g(n,this.auth,"argument-error"),this.container=n,this.parameters.callback=this.makeTokenCallback(this.parameters.callback),this._recaptchaLoader=new(this.auth.settings.appVerificationDisabledForTesting?nn:rn),this.validateStartingState()}async verify(){this.assertNotDestroyed();let e=await this.render(),n=this.getAssertedRecaptcha();var t=n.getResponse(e);return t||new Promise(t=>{let r=e=>{e&&(this.tokenChangeListeners.delete(r),t(e))};this.tokenChangeListeners.add(r),this.isInvisible&&n.execute(e)})}render(){try{this.assertNotDestroyed()}catch(e){return Promise.reject(e)}return this.renderPromise||(this.renderPromise=this.makeRenderPromise().catch(e=>{throw this.renderPromise=null,e})),this.renderPromise}_reset(){this.assertNotDestroyed(),null!==this.widgetId&&this.getAssertedRecaptcha().reset(this.widgetId)}clear(){this.assertNotDestroyed(),this.destroyed=!0,this._recaptchaLoader.clearedOneInstance(),this.isInvisible||this.container.childNodes.forEach(e=>{this.container.removeChild(e)})}validateStartingState(){g(!this.parameters.sitekey,this.auth,"argument-error"),g(this.isInvisible||!this.container.hasChildNodes(),this.auth,"argument-error"),g("undefined"!=typeof document,this.auth,"operation-not-supported-in-this-environment")}makeTokenCallback(r){return t=>{var e;this.tokenChangeListeners.forEach(e=>e(t)),"function"==typeof r?r(t):"string"==typeof r&&"function"==typeof(e=O()[r])&&e(t)}}assertNotDestroyed(){g(!this.destroyed,this.auth,"internal-error")}async makeRenderPromise(){if(await this.init(),!this.widgetId){let e=this.container;var t;this.isInvisible||(t=document.createElement("div"),e.appendChild(t),e=t),this.widgetId=this.getAssertedRecaptcha().render(e,this.parameters)}return this.widgetId}async init(){g(Ae()&&!Wr(),this.auth,"internal-error"),await(()=>{let t=null;return new Promise(e=>{"complete"===document.readyState?e():(t=()=>e(),window.addEventListener("load",t))}).catch(e=>{throw t&&window.removeEventListener("load",t),e})})(),this.recaptcha=await this._recaptchaLoader.load(this.auth,this.auth.languageCode||void 0);var e=await((await m(this.auth,"GET","/v1/recaptchaParams")).recaptchaSiteKey||"");g(e,this.auth,"internal-error"),this.parameters.sitekey=e}getAssertedRecaptcha(){return g(this.recaptcha,this.auth,"internal-error"),this.recaptcha}}class ln{constructor(e,t){this.verificationId=e,this.onConfirmation=t}confirm(e){var t=Wt._fromVerification(this.verificationId,e);return this.onConfirmation(t)}}async function cn(t,r,n){var i,e,s,a,o,l,c,d;if(!t._getRecaptchaConfig())try{e=w(t),s=await qe(e,{clientType:"CLIENT_TYPE_WEB",version:"RECAPTCHA_ENTERPRISE"}),s=new We(s),null==e.tenantId?e._agentRecaptchaConfig=s:e._tenantRecaptchaConfigs[e.tenantId]=s,await(!s.isAnyProviderEnabled()||!new Rt(e).verify())}catch(e){console.log("Failed to initialize reCAPTCHA Enterprise config. Triggering the reCAPTCHA v2 verification.")}try{let e;return("session"in(e="string"==typeof r?{phoneNumber:r}:r)?(a=e.session,"phoneNumber"in e?(g("enroll"===a.type,t,"internal-error"),o={idToken:a.credential,phoneEnrollmentInfo:{phoneNumber:e.phoneNumber,clientType:"CLIENT_TYPE_WEB"}},(await T(t,o,"mfaSmsEnrollment",async(e,t)=>t.phoneEnrollmentInfo.captchaResponse===St?(g((null==n?void 0:n.type)===sn,e,"argument-error"),Cr(e,await dn(e,t,n))):Cr(e,t),"PHONE_PROVIDER").catch(e=>Promise.reject(e))).phoneSessionInfo):(g("signin"===a.type,t,"internal-error"),g(l=(null==(i=e.multiFactorHint)?void 0:i.uid)||e.multiFactorUid,t,"missing-multi-factor-info"),c={mfaPendingCredential:a.credential,mfaEnrollmentId:l,phoneSignInInfo:{clientType:"CLIENT_TYPE_WEB"}},(await T(t,c,"mfaSmsSignIn",async(e,t)=>t.phoneSignInInfo.captchaResponse===St?(g((null==n?void 0:n.type)===sn,e,"argument-error"),Qr(e,await dn(e,t,n))):Qr(e,t),"PHONE_PROVIDER").catch(e=>Promise.reject(e))).phoneResponseInfo)):(d={phoneNumber:e.phoneNumber,clientType:"CLIENT_TYPE_WEB"},await T(t,d,"sendVerificationCode",async(e,t)=>t.captchaResponse===St?(g((null==n?void 0:n.type)===sn,e,"argument-error"),jt(e,await dn(e,t,n))):jt(e,t),"PHONE_PROVIDER").catch(e=>Promise.reject(e)))).sessionInfo}finally{null!=n&&n._reset()}}async function dn(e,t,r){g(r.type===sn,e,"argument-error");var n,i,s,a,o=await r.verify(),l=(g("string"==typeof o,e,"argument-error"),Object.assign({},t));return"phoneEnrollmentInfo"in l?(i=l.phoneEnrollmentInfo.phoneNumber,s=l.phoneEnrollmentInfo.captchaResponse,a=l.phoneEnrollmentInfo.clientType,n=l.phoneEnrollmentInfo.recaptchaVersion,Object.assign(l,{phoneEnrollmentInfo:{phoneNumber:i,recaptchaToken:o,captchaResponse:s,clientType:a,recaptchaVersion:n}})):"phoneSignInInfo"in l?(i=l.phoneSignInInfo.captchaResponse,s=l.phoneSignInInfo.clientType,a=l.phoneSignInInfo.recaptchaVersion,Object.assign(l,{phoneSignInInfo:{recaptchaToken:o,captchaResponse:i,clientType:s,recaptchaVersion:a}})):Object.assign(l,{recaptchaToken:o}),l}class N{constructor(e){this.providerId=N.PROVIDER_ID,this.auth=w(e)}verifyPhoneNumber(e,t){return cn(this.auth,e,o(t))}static credential(e,t){return Wt._fromVerification(e,t)}static credentialFromResult(e){var t=e;return N.credentialFromTaggedObject(t)}static credentialFromError(e){return N.credentialFromTaggedObject(e.customData||{})}static credentialFromTaggedObject({_tokenResponse:e}){var t,r;return e&&({phoneNumber:t,temporaryProof:r}=e,t)&&r?Wt._fromTokenResponse(t,r):null}}function un(e,t){return t?y(t):(g(e._popupRedirectResolver,e,"argument-error"),e._popupRedirectResolver)}N.PROVIDER_ID="phone",N.PHONE_SIGN_IN_METHOD="phone";class hn extends Nt{constructor(e){super("custom","custom"),this.params=e}_getIdTokenResponse(e){return E(e,this._buildIdpRequest())}_linkToIdToken(e,t){return E(e,this._buildIdpRequest(t))}_getReauthenticationResolver(e){return E(e,this._buildIdpRequest())}_buildIdpRequest(e){var t={requestUri:this.params.requestUri,sessionId:this.params.sessionId,postBody:this.params.postBody,tenantId:this.params.tenantId,pendingToken:this.params.pendingToken,returnSecureToken:!0,returnIdpCredential:!0};return e&&(t.idToken=e),t}}function pn(e){return ir(e.auth,new hn(e),e.bypassAuthState)}function mn(e){var{auth:t,user:r}=e;return g(r,t,"internal-error"),nr(r,new hn(e),e.bypassAuthState)}async function gn(e){var{auth:t,user:r}=e;return g(r,t,"internal-error"),tr(r,new hn(e),e.bypassAuthState)}class fn{constructor(e,t,r,n,i=!1){this.auth=e,this.resolver=r,this.user=n,this.bypassAuthState=i,this.pendingPromise=null,this.eventManager=null,this.filter=Array.isArray(t)?t:[t]}execute(){return new Promise(async(e,t)=>{this.pendingPromise={resolve:e,reject:t};try{this.eventManager=await this.resolver._initialize(this.auth),await this.onExecution(),this.eventManager.registerConsumer(this)}catch(e){this.reject(e)}})}async onAuthEvent(e){var{urlResponse:t,sessionId:r,postBody:n,tenantId:i,error:s,type:a}=e;if(s)this.reject(s);else{s={auth:this.auth,requestUri:t,sessionId:r,tenantId:i||void 0,postBody:n||void 0,user:this.user,bypassAuthState:this.bypassAuthState};try{this.resolve(await this.getIdpTask(a)(s))}catch(e){this.reject(e)}}}onError(e){this.reject(e)}getIdpTask(e){switch(e){case"signInViaPopup":case"signInViaRedirect":return pn;case"linkViaPopup":case"linkViaRedirect":return gn;case"reauthViaPopup":case"reauthViaRedirect":return mn;default:u(this.auth,"internal-error")}}resolve(e){a(this.pendingPromise,"Pending promise was never set"),this.pendingPromise.resolve(e),this.unregisterAndCleanUp()}reject(e){a(this.pendingPromise,"Pending promise was never set"),this.pendingPromise.reject(e),this.unregisterAndCleanUp()}unregisterAndCleanUp(){this.eventManager&&this.eventManager.unregisterConsumer(this),this.pendingPromise=null,this.cleanUp()}}let vn=new Ce(2e3,1e4);class L extends fn{constructor(e,t,r,n,i){super(e,t,n,i),this.provider=r,this.authWindow=null,this.pollId=null,L.currentPopupAction&&L.currentPopupAction.cancel(),L.currentPopupAction=this}async executeNotNull(){var e=await this.execute();return g(e,this.auth,"internal-error"),e}async onExecution(){a(1===this.filter.length,"Popup operations only handle one event");var e=jr();this.authWindow=await this.resolver._openPopup(this.auth,this.provider,this.filter[0],e),this.authWindow.associatedEvent=e,this.resolver._originValidation(this.auth).catch(e=>{this.reject(e)}),this.resolver._isIframeWebStorageSupported(this.auth,e=>{e||this.reject(h(this.auth,"web-storage-unsupported"))}),this.pollUserCancellation()}get eventId(){var e;return(null==(e=this.authWindow)?void 0:e.associatedEvent)||null}cancel(){this.reject(h(this.auth,"cancelled-popup-request"))}cleanUp(){this.authWindow&&this.authWindow.close(),this.pollId&&window.clearTimeout(this.pollId),this.authWindow=null,this.pollId=null,L.currentPopupAction=null}pollUserCancellation(){let t=()=>{var e;null!=(e=null==(e=this.authWindow)?void 0:e.window)&&e.closed?this.pollId=window.setTimeout(()=>{this.pollId=null,this.reject(h(this.auth,"popup-closed-by-user"))},8e3):this.pollId=window.setTimeout(t,vn.get())};t()}}L.currentPopupAction=null;let _n="pendingRedirect",yn=new Map;class In extends fn{constructor(e,t,r=!1){super(e,["signInViaRedirect","linkViaRedirect","reauthViaRedirect","unknown"],t,void 0,r),this.eventId=null}async execute(){let t=yn.get(this.auth._key());if(!t){try{let e=await(async(e,t)=>{var r,n=bn(t),i=En(e);return!!await i._isAvailable()&&(r="true"===await i._get(n),await i._remove(n),r)})(this.resolver,this.auth)?await super.execute():null;t=()=>Promise.resolve(e)}catch(e){t=()=>Promise.reject(e)}yn.set(this.auth._key(),t)}return this.bypassAuthState||yn.set(this.auth._key(),()=>Promise.resolve(null)),t()}async onAuthEvent(e){if("signInViaRedirect"===e.type)return super.onAuthEvent(e);if("unknown"===e.type)this.resolve(null);else if(e.eventId){var t=await this.auth._redirectUserForId(e.eventId);if(t)return this.user=t,super.onAuthEvent(e);this.resolve(null)}}async onExecution(){}cleanUp(){}}async function wn(e,t){return En(e)._set(bn(t),"true")}function Tn(e,t){yn.set(e._key(),t)}function En(e){return y(e._redirectPersistence)}function bn(e){return I(_n,e.config.apiKey,e.name)}function kn(e,t,r){return(async(e,t,r)=>{var n,i;return Ki._isFirebaseServerApp(e.app)?Promise.reject(l(e)):(n=w(e),ke(e,t,k),await n._initializationPromise,await wn(i=un(n,r),n),i._openRedirect(n,t,"signInViaRedirect"))})(e,t,r)}function Sn(e,t,r){return(async(e,t,r)=>{var n=o(e);if(ke(n.auth,t,k),Ki._isFirebaseServerApp(n.auth.app))return Promise.reject(l(n.auth));await n.auth._initializationPromise;var i=un(n.auth,r),s=(await wn(i,n.auth),await Pn(n));return i._openRedirect(n.auth,t,"reauthViaRedirect",s)})(e,t,r)}function Rn(e,t,r){return(async(e,t,r)=>{var n=o(e),i=(ke(n.auth,t,k),await n.auth._initializationPromise,un(n.auth,r)),s=(await rr(!1,n,t.providerId),await wn(i,n.auth),await Pn(n));return i._openRedirect(n.auth,t,"linkViaRedirect",s)})(e,t,r)}async function An(e,t,r=!1){var n,i;return Ki._isFirebaseServerApp(e.app)?Promise.reject(l(e)):(i=un(n=w(e),t),(i=await new In(n,i,r).execute())&&!r&&(delete i.user._redirectEventId,await n._persistUserIfCurrent(i.user),await n._setRedirectUser(null,t)),i)}async function Pn(e){var t=jr(e.uid+":::");return e._redirectEventId=t,await e.auth._setRedirectUser(e),await e.auth._persistUserIfCurrent(e),t}class Cn{constructor(e){this.auth=e,this.cachedEventUids=new Set,this.consumers=new Set,this.queuedRedirectEvent=null,this.hasHandledPotentialRedirect=!1,this.lastProcessedEventTime=Date.now()}registerConsumer(e){this.consumers.add(e),this.queuedRedirectEvent&&this.isEventForConsumer(this.queuedRedirectEvent,e)&&(this.sendToConsumer(this.queuedRedirectEvent,e),this.saveEventToCache(this.queuedRedirectEvent),this.queuedRedirectEvent=null)}unregisterConsumer(e){this.consumers.delete(e)}onEvent(t){if(this.hasEventBeenHandled(t))return!1;let r=!1;return this.consumers.forEach(e=>{this.isEventForConsumer(t,e)&&(r=!0,this.sendToConsumer(t,e),this.saveEventToCache(t))}),this.hasHandledPotentialRedirect||!(e=>{switch(e.type){case"signInViaRedirect":case"linkViaRedirect":case"reauthViaRedirect":return 1;case"unknown":return Nn(e);default:return}})(t)||(this.hasHandledPotentialRedirect=!0,r)||(this.queuedRedirectEvent=t,r=!0),r}sendToConsumer(e,t){var r;e.error&&!Nn(e)?(r=(null==(r=e.error.code)?void 0:r.split("auth/")[1])||"internal-error",t.onError(h(this.auth,r))):t.onAuthEvent(e)}isEventForConsumer(e,t){var r=null===t.eventId||!!e.eventId&&e.eventId===t.eventId;return t.filter.includes(e.type)&&r}hasEventBeenHandled(e){return 6e5<=Date.now()-this.lastProcessedEventTime&&this.cachedEventUids.clear(),this.cachedEventUids.has(On(e))}saveEventToCache(e){this.cachedEventUids.add(On(e)),this.lastProcessedEventTime=Date.now()}}function On(e){return[e.type,e.eventId,e.sessionId,e.tenantId].filter(e=>e).join("-")}function Nn({type:e,error:t}){return"unknown"===e&&"auth/no-auth-event"===(null==t?void 0:t.code)}async function Ln(e,t={}){return m(e,"GET","/v1/projects",t)}let Dn=/^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}$/,Mn=/^https?/;async function Un(e){if(!e.config.emulator){var t,r=(await Ln(e)).authorizedDomains;for(t of r)try{if((e=>{var t,r=Re(),{protocol:n,hostname:i}=new URL(r);return e.startsWith("chrome-extension://")?""===(t=new URL(e)).hostname&&""===i?"chrome-extension:"===n&&e.replace("chrome-extension://","")===r.replace("chrome-extension://",""):"chrome-extension:"===n&&t.hostname===i:Mn.test(n)&&(Dn.test(e)?i===e:(r=e.replace(/\./g,"\\."),(t=new RegExp("^(.+\\."+r+"|"+r+")$","i")).test(i)))})(t))return}catch(e){}u(e,"unauthorized-domain")}}let Fn=new Ce(3e4,6e4);function Vn(){var t=O().___jsl;if(null!=t&&t.H)for(var r of Object.keys(t.H))if(t.H[r].r=t.H[r].r||[],t.H[r].L=t.H[r].L||[],t.H[r].r=[...t.H[r].L],t.CP)for(let e=0;e<t.CP.length;e++)t.CP[e]=null}function xn(i){return new Promise((e,t)=>{var r;function n(){Vn(),gapi.load("gapi.iframes",{callback:()=>{e(gapi.iframes.getContext())},ontimeout:()=>{Vn(),t(h(i,"network-request-failed"))},timeout:Fn.get()})}if(null!=(r=null==(r=O().gapi)?void 0:r.iframes)&&r.Iframe)e(gapi.iframes.getContext());else{if(null==(r=O().gapi)||!r.load)return r=wt("iframefcb"),O()[r]=()=>{gapi.load?n():t(h(i,"network-request-failed"))},It(yt.gapiScript+"?onload="+r).catch(e=>t(e));n()}}).catch(e=>{throw jn=null,e})}let jn=null;let Hn=new Ce(5e3,15e3),Wn="__/auth/iframe",qn="emulator/auth/iframe",Bn={style:{position:"absolute",top:"-100px",width:"1px",height:"1px"},"aria-hidden":"true",tabindex:"-1"},zn=new Map([["identitytoolkit.googleapis.com","p"],["staging-identitytoolkit.sandbox.googleapis.com","s"],["test-identitytoolkit.sandbox.googleapis.com","t"]]);async function Gn(a){e=a;var e,t,r,n=await(jn=jn||xn(e)),i=O().gapi;return g(i,a,"internal-error"),n.open({where:document.body,url:(g((n=(e=a).config).authDomain,e,"auth-domain-config-required"),t=n.emulator?Oe(n,qn):`https://${e.config.authDomain}/`+Wn,n={apiKey:n.apiKey,appName:e.name,v:Ki.SDK_VERSION},(r=zn.get(e.config.apiHost))&&(n.eid=r),(r=e._getFrameworks()).length&&(n.fw=r.join(",")),t+"?"+se(n).slice(1)),messageHandlersFilter:i.iframes.CROSS_ORIGIN_IFRAMES_FILTER,attributes:Bn,dontclear:!0},s=>new Promise(async(e,t)=>{await s.restyle({setHideOnLeave:!1});let r=h(a,"network-request-failed"),n=O().setTimeout(()=>{t(r)},Hn.get());function i(){O().clearTimeout(n),e(s)}s.ping(i).then(i,()=>{t(r)})}))}let Kn={location:"yes",resizable:"yes",statusbar:"yes",toolbar:"no"};class Jn{constructor(e){this.window=e,this.associatedEvent=null}close(){if(this.window)try{this.window.close()}catch(e){}}}function Yn(e,t,r,n=500,i=600){var s=Math.max((window.screen.availHeight-i)/2,0).toString(),a=Math.max((window.screen.availWidth-n)/2,0).toString();let o="";var l,s=Object.assign(Object.assign({},Kn),{width:n.toString(),height:i.toString(),top:s,left:a}),a=c().toLowerCase(),s=(r&&(o=ot(a)?"_blank":r),st(a)&&(t=t||"http://localhost",s.scrollbars="yes"),Object.entries(s).reduce((e,[t,r])=>""+e+t+`=${r},`,""));if([n=c()]=[a],ht(n)&&null!=(a=window.navigator)&&a.standalone&&"_self"!==o)return i=t||"",r=o,(a=document.createElement("a")).href=i,a.target=r,(l=document.createEvent("MouseEvent")).initMouseEvent("click",!0,!0,window,1,0,0,0,0,!1,!1,!1,!1,1,null),a.dispatchEvent(l),new Jn(null);a=window.open(t||"",o,s);g(a,e,"popup-blocked");try{a.focus()}catch(e){}return new Jn(a)}let $n="__/auth/handler",Xn="emulator/auth/handler",Zn=encodeURIComponent("fac");async function Qn(e,t,r,n,i,s){g(e.config.authDomain,e,"auth-domain-config-required"),g(e.config.apiKey,e,"invalid-api-key");var a={apiKey:e.config.apiKey,appName:e.name,authType:r,redirectUrl:n,v:Ki.SDK_VERSION,eventId:i};if(t instanceof k){t.setDefaultLanguage(e.languageCode),a.providerId=t.providerId||"",(e=>{for(var t in e)if(Object.prototype.hasOwnProperty.call(e,t))return;return 1})(t.getCustomParameters())||(a.customParameters=JSON.stringify(t.getCustomParameters()));for(var[o,l]of Object.entries(s||{}))a[o]=l}t instanceof zt&&0<(u=t.getScopes().filter(e=>""!==e)).length&&(a.scopes=u.join(",")),e.tenantId&&(a.tid=e.tenantId);var c,d=a;for(c of Object.keys(d))void 0===d[c]&&delete d[c];var u=await e._getAppCheckToken(),u=u?`#${Zn}=`+encodeURIComponent(u):"";return`${r=[e.config][0],r.emulator?Oe(r,Xn):`https://${r.authDomain}/`+$n}?`+se(d).slice(1)+u}let ei="webStorageSupport";class ti{constructor(){this.eventManagers={},this.iframes={},this.originValidationPromises={},this._redirectPersistence=Vr,this._completeRedirectFn=An,this._overrideRedirectResult=Tn}async _openPopup(e,t,r,n){a(null==(i=this.eventManagers[e._key()])?void 0:i.manager,"_initialize() not called before _openPopup()");var i=await Qn(e,t,r,Re(),n);return Yn(e,i,jr())}async _openRedirect(e,t,r,n){await this._originValidation(e);var i=await Qn(e,t,r,Re(),n);return O().location.href=i,new Promise(()=>{})}_initialize(e){let r=e._key();if(this.eventManagers[r]){let{manager:e,promise:t}=this.eventManagers[r];return e?Promise.resolve(e):(a(t,"If manager is not set, promise should be"),t)}let t=this.initAndGetManager(e);return this.eventManagers[r]={promise:t},t.catch(()=>{delete this.eventManagers[r]}),t}async initAndGetManager(t){var e=await Gn(t);let r=new Cn(t);return e.register("authEvent",e=>(g(null==e?void 0:e.authEvent,t,"invalid-auth-event"),{status:r.onEvent(e.authEvent)?"ACK":"ERROR"}),gapi.iframes.CROSS_ORIGIN_IFRAMES_FILTER),this.eventManagers[t._key()]={manager:r},this.iframes[t._key()]=e,r}_isIframeWebStorageSupported(r,n){this.iframes[r._key()].send(ei,{type:ei},e=>{var t=null==(t=null==e?void 0:e[0])?void 0:t[ei];void 0!==t&&n(!!t),u(r,"internal-error")},gapi.iframes.CROSS_ORIGIN_IFRAMES_FILTER)}_originValidation(e){var t=e._key();return this.originValidationPromises[t]||(this.originValidationPromises[t]=Un(e)),this.originValidationPromises[t]}get _shouldInitProactively(){return pt()||at()||ht()}}let ri=ti;class ni extends class{constructor(e){this.factorId=e}_process(e,t,r){switch(t.type){case"enroll":return this._finalizeEnroll(e,t.credential,r);case"signin":return this._finalizeSignIn(e,t.credential);default:return i("unexpected MultiFactorSessionType")}}}{constructor(e){super("phone"),this.credential=e}static _fromCredential(e){return new ni(e)}_finalizeEnroll(e,t,r){return e=e,t={idToken:t,displayName:r,phoneVerificationInfo:this.credential._makeVerificationRequest()},m(e,"POST","/v2/accounts/mfaEnrollment:finalize",p(e,t))}_finalizeSignIn(e,t){return e=e,t={mfaPendingCredential:t,phoneVerificationInfo:this.credential._makeVerificationRequest()},m(e,"POST","/v2/accounts/mfaSignIn:finalize",p(e,t))}}class ii{constructor(){}static assertion(e){return ni._fromCredential(e)}}ii.FACTOR_ID="phone";var si="@firebase/auth";class ai{constructor(e){this.auth=e,this.internalListeners=new Map}getUid(){var e;return this.assertAuthConfigured(),(null==(e=this.auth.currentUser)?void 0:e.uid)||null}async getToken(e){return this.assertAuthConfigured(),await this.auth._initializationPromise,this.auth.currentUser?{accessToken:await this.auth.currentUser.getIdToken(e)}:null}addAuthTokenListener(t){var e;this.assertAuthConfigured(),this.internalListeners.has(t)||(e=this.auth.onIdTokenChanged(e=>{t((null==e?void 0:e.stsTokenManager.accessToken)||null)}),this.internalListeners.set(t,e),this.updateProactiveRefresh())}removeAuthTokenListener(e){this.assertAuthConfigured();var t=this.internalListeners.get(e);t&&(this.internalListeners.delete(e),t(),this.updateProactiveRefresh())}assertAuthConfigured(){g(this.auth._initializationPromise,"dependent-sdk-initialized-before-auth")}updateProactiveRefresh(){0<this.internalListeners.size?this.auth._startProactiveRefresh():this.auth._stopProactiveRefresh()}}var oi,li;function ci(){return window}e="authIdTokenMaxAge",null!=(oi=z())&&oi["_"+e],yt={loadJS(i){return new Promise((e,r)=>{var t,n=document.createElement("script");n.setAttribute("src",i),n.onload=e,n.onerror=e=>{var t=h("internal-error");t.customData=e,r(t)},n.type="text/javascript",n.charset="UTF-8",(null!=(t=null==(t=document.getElementsByTagName("head"))?void 0:t[0])?t:document).appendChild(n)})},gapiScript:"https://apis.google.com/js/api.js",recaptchaV2Script:"https://www.google.com/recaptcha/api.js",recaptchaEnterpriseScript:"https://www.google.com/recaptcha/enterprise.js?render="},li="Browser",Ki._registerComponent(new ge("auth",(e,{options:t})=>{var r=e.getProvider("app").getImmediate(),n=e.getProvider("heartbeat"),i=e.getProvider("app-check-internal"),{apiKey:s,authDomain:a}=r.options,s=(g(s&&!s.includes(":"),"invalid-api-key",{appName:r.name}),{apiKey:s,authDomain:a,clientPlatform:li,apiHost:"identitytoolkit.googleapis.com",tokenApiHost:"securetoken.googleapis.com",apiScheme:"https",sdkClientVersion:mt(li)}),a=new vt(r,n,i,s);return e=a,r=(null==(t=t)?void 0:t.persistence)||[],r=(Array.isArray(r)?r:[r]).map(y),null!=t&&t.errorMap&&e._updateErrorMap(t.errorMap),e._initializeWithPersistence(r,null==t?void 0:t.popupRedirectResolver),a},"PUBLIC").setInstantiationMode("EXPLICIT").setInstanceCreatedCallback((e,t,r)=>{e.getProvider("auth-internal").initialize()})),Ki._registerComponent(new ge("auth-internal",e=>{var t=w(e.getProvider("auth").getImmediate());return e=t,new ai(e)},"PRIVATE").setInstantiationMode("EXPLICIT")),Ki.registerVersion(si,"1.10.6",(e=>{switch(e){case"Node":return"node";case"ReactNative":return"rn";case"Worker":return"webworker";case"Cordova":return"cordova";case"WebExtension":return"web-extension";default:return}})(li)),Ki.registerVersion(si,"1.10.6","esm2017");async function di(e,t,r){var n=ci().BuildInfo,i=(a(t.sessionId,"AuthEvent did not contain a session ID"),i=(e=>{if(a(/[0-9a-zA-Z]+/.test(e),"Can only convert alpha-numeric strings"),"undefined"!=typeof TextEncoder)return(new TextEncoder).encode(e);var t=new ArrayBuffer(e.length),r=new Uint8Array(t);for(let n=0;n<e.length;n++)r[n]=e.charCodeAt(n);return r})(t.sessionId),i=await crypto.subtle.digest("SHA-256",i),await(i=Array.from(new Uint8Array(i))).map(e=>e.toString(16).padStart(2,"0")).join("")),s={};return ht()?s.ibi=n.packageName:ct()?s.apn=n.packageName:u(e,"operation-not-supported-in-this-environment"),n.displayName&&(s.appDisplayName=n.displayName),s.sessionId=i,Qn(e,r,t.type,void 0,null!=(n=t.eventId)?n:void 0,s)}function ui(n){let i=ci().cordova;return new Promise(r=>{i.plugins.browsertab.isAvailable(e=>{let t=null;e?i.plugins.browsertab.openUrl(n):t=i.InAppBrowser.open(n,(e=c(),/(iPad|iPhone|iPod).*OS 7_\d/i.test(e)||/(iPad|iPhone|iPod).*OS 8_\d/i.test(e)?"_blank":"_system"),"location=yes"),r(t)})})}let hi=20;class pi extends Cn{constructor(){super(...arguments),this.passiveListeners=new Set,this.initPromise=new Promise(e=>{this.resolveInitialized=e})}addPassiveListener(e){this.passiveListeners.add(e)}removePassiveListener(e){this.passiveListeners.delete(e)}resetRedirect(){this.queuedRedirectEvent=null,this.hasHandledPotentialRedirect=!1}onEvent(t){return this.resolveInitialized(),this.passiveListeners.forEach(e=>e(t)),super.onEvent(t)}async initialized(){await this.initPromise}}function mi(e,t,r=null){return{type:t,eventId:r,urlResponse:null,sessionId:(()=>{var e=[],t="1234567890abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ";for(let n=0;n<hi;n++){var r=Math.floor(Math.random()*t.length);e.push(t.charAt(r))}return e.join("")})(),postBody:null,tenantId:e.tenantId,error:h(e,"no-auth-event")}}async function gi(e){var t=await vi()._get(_i(e));return t&&await vi()._remove(_i(e)),t}function fi(e,t){i=yi(t=t),n=i.link?decodeURIComponent(i.link):void 0,r=yi(n).link,i=i.deep_link_id?decodeURIComponent(i.deep_link_id):void 0;var r,n,i=yi(i).link||i||r||n||t;return i.includes("/__/auth/callback")?(n=(r=null==(n=null==(r=null==(n=(r=yi(i)).firebaseError?(e=>{try{return JSON.parse(e)}catch(e){return null}})(decodeURIComponent(r.firebaseError)):null)?void 0:n.code)?void 0:r.split("auth/"))?void 0:n[1])?h(r):null)?{type:e.type,eventId:e.eventId,tenantId:e.tenantId,error:n,urlResponse:null,sessionId:null,postBody:null}:{type:e.type,eventId:e.eventId,tenantId:e.tenantId,sessionId:e.sessionId,urlResponse:i,postBody:null}:null}function vi(){return y(Ur)}function _i(e){return I("authEvent",e.config.apiKey,e.name)}function yi(e){var t,r;return null!=e&&e.includes("?")?([t,...r]=e.split("?"),ae(r.join("?"))):{}}class Ii{constructor(){this._redirectPersistence=Vr,this._shouldInitProactively=!0,this.eventManagers=new Map,this.originValidationPromises={},this._completeRedirectFn=An,this._overrideRedirectResult=Tn}async _initialize(e){var t=e._key();let r=this.eventManagers.get(t);return r||(r=new pi(e),this.eventManagers.set(t,r),this.attachCallbackListeners(e,r)),r}_openPopup(e){u(e,"operation-not-supported-in-this-environment")}async _openRedirect(e,t,r,n){i=e,a=ci(),g("function"==typeof(null==(s=null==a?void 0:a.universalLinks)?void 0:s.subscribe),i,"invalid-cordova-configuration",{missingPlugin:"cordova-universal-links-plugin-fix"}),g(void 0!==(null==(s=null==a?void 0:a.BuildInfo)?void 0:s.packageName),i,"invalid-cordova-configuration",{missingPlugin:"cordova-plugin-buildInfo"}),g("function"==typeof(null==(s=null==(s=null==(s=null==a?void 0:a.cordova)?void 0:s.plugins)?void 0:s.browsertab)?void 0:s.openUrl),i,"invalid-cordova-configuration",{missingPlugin:"cordova-plugin-browsertab"}),g("function"==typeof(null==(s=null==(s=null==(s=null==a?void 0:a.cordova)?void 0:s.plugins)?void 0:s.browsertab)?void 0:s.isAvailable),i,"invalid-cordova-configuration",{missingPlugin:"cordova-plugin-browsertab"}),g("function"==typeof(null==(a=null==(s=null==a?void 0:a.cordova)?void 0:s.InAppBrowser)?void 0:a.open),i,"invalid-cordova-configuration",{missingPlugin:"cordova-plugin-inappbrowser"});var i,s=await this._initialize(e),a=(await s.initialized(),s.resetRedirect(),yn.clear(),await this._originValidation(e),mi(e,r,n)),a=(i=e,r=a,await vi()._set(_i(i),r),await di(e,a,t));return(async(a,o,l)=>{let c=ci().cordova,d=()=>{};try{await new Promise((t,e)=>{let r=null;function n(){t();var e=null==(e=c.plugins.browsertab)?void 0:e.close;"function"==typeof e&&e(),"function"==typeof(null==l?void 0:l.close)&&l.close()}function i(){r=r||window.setTimeout(()=>{e(h(a,"redirect-cancelled-by-user"))},2e3)}function s(){"visible"===(null==document?void 0:document.visibilityState)&&i()}o.addPassiveListener(n),document.addEventListener("resume",i,!1),ct()&&document.addEventListener("visibilitychange",s,!1),d=()=>{o.removePassiveListener(n),document.removeEventListener("resume",i,!1),document.removeEventListener("visibilitychange",s,!1),r&&window.clearTimeout(r)}})}finally{d()}})(e,s,await ui(a))}_isIframeWebStorageSupported(e,t){throw new Error("Method not implemented.")}_originValidation(e){var t=e._key();return this.originValidationPromises[t]||(this.originValidationPromises[t]=(async e=>{var t=ci().BuildInfo,r={};ht()?r.iosBundleId=t.packageName:ct()?r.androidPackageName=t.packageName:u(e,"operation-not-supported-in-this-environment"),await Ln(e,r)})(e)),this.originValidationPromises[t]}attachCallbackListeners(n,i){var{universalLinks:e,handleOpenURL:t,BuildInfo:r}=ci();let s=setTimeout(async()=>{await gi(n),i.onEvent(Ti())},500),a=async e=>{clearTimeout(s);var t=await gi(n);let r=null;t&&null!=e&&e.url&&(r=fi(t,e.url)),i.onEvent(r||Ti())},o=(void 0!==e&&"function"==typeof e.subscribe&&e.subscribe(null,a),t),l=r.packageName.toLowerCase()+"://";ci().handleOpenURL=async e=>{if(e.toLowerCase().startsWith(l)&&a({url:e}),"function"==typeof o)try{o(e)}catch(e){console.error(e)}}}}let wi=Ii;function Ti(){return{type:"unknown",eventId:null,sessionId:null,urlResponse:null,postBody:null,tenantId:null,error:h("no-auth-event")}}var e;function Ei(){var e;return(null==(e=null==self?void 0:self.location)?void 0:e.protocol)||null}function bi(e=c()){return!("file:"!==Ei()&&"ionic:"!==Ei()&&"capacitor:"!==Ei()||!e.toLowerCase().match(/iphone|ipad|ipod|android/))}function ki(e=c()){return Q()&&11===(null==document?void 0:document.documentMode)||([e=c()]=[e],/Edge\/\d+/.test(e))}function Si(){try{var e=self.localStorage,t=jr();if(e)return e.setItem(t,"1"),e.removeItem(t),!ki()||ee()}catch(e){return Ri()&&ee()}return!1}function Ri(){return"undefined"!=typeof global&&"WorkerGlobalScope"in global&&"importScripts"in global}function Ai(){return("http:"===Ei()||"https:"===Ei()||X()||bi())&&!(Z()||$())&&Si()&&!Ri()}function Pi(){return bi()&&"undefined"!=typeof document}let D={LOCAL:"local",NONE:"none",SESSION:"session"},Ci=g,Oi="persistence";async function Ni(e){await e._initializationPromise;var t=Li(),r=I(Oi,e.config.apiKey,e.name);t&&t.setItem(r,e._getPersistenceType())}function Li(){var e;try{return(null===(e="undefined"!=typeof window?window:null)?void 0:e.sessionStorage)||null}catch(e){return null}}let Di=g;class M{constructor(){this.browserResolver=y(ri),this.cordovaResolver=y(wi),this.underlyingResolver=null,this._redirectPersistence=Vr,this._completeRedirectFn=An,this._overrideRedirectResult=Tn}async _initialize(e){return await this.selectUnderlyingResolver(),this.assertedUnderlyingResolver._initialize(e)}async _openPopup(e,t,r,n){return await this.selectUnderlyingResolver(),this.assertedUnderlyingResolver._openPopup(e,t,r,n)}async _openRedirect(e,t,r,n){return await this.selectUnderlyingResolver(),this.assertedUnderlyingResolver._openRedirect(e,t,r,n)}_isIframeWebStorageSupported(e,t){this.assertedUnderlyingResolver._isIframeWebStorageSupported(e,t)}_originValidation(e){return this.assertedUnderlyingResolver._originValidation(e)}get _shouldInitProactively(){return Pi()||this.browserResolver._shouldInitProactively}get assertedUnderlyingResolver(){return Di(this.underlyingResolver,"internal-error"),this.underlyingResolver}async selectUnderlyingResolver(){var e;this.underlyingResolver||(e=await(!!Pi()&&new Promise(e=>{let t=setTimeout(()=>{e(!1)},1e3);document.addEventListener("deviceready",()=>{clearTimeout(t),e(!0)})})),this.underlyingResolver=e?this.cordovaResolver:this.browserResolver)}}function Mi(e){return e.unwrap()}function Ui(e,t){var r,n,i,s,a=null==(a=t.customData)?void 0:a._tokenResponse;"auth/multi-factor-auth-required"===(null==t?void 0:t.code)?t.resolver=new xi(e,(r=t,i=o(e),g((s=r).customData.operationType,i,"argument-error"),g(null==(n=s.customData._serverResponse)?void 0:n.mfaPendingCredential,i,"argument-error"),Pr._fromError(i,s))):a&&(i=Fi(n=t))&&(n.credential=i,n.tenantId=a.tenantId||void 0,n.email=a.email||void 0,n.phoneNumber=a.phoneNumber||void 0)}function Fi(e){var t=(e instanceof d?e.customData:e)._tokenResponse;if(!t)return null;if(!(e instanceof d)&&"temporaryProof"in t&&"phoneNumber"in t)return N.credentialFromResult(e);var r=t.providerId;if(!r||r===fe.PASSWORD)return null;let n;switch(r){case fe.GOOGLE:n=R;break;case fe.FACEBOOK:n=S;break;case fe.GITHUB:n=A;break;case fe.TWITTER:n=P;break;default:var{oauthIdToken:i,oauthAccessToken:s,oauthTokenSecret:a,pendingToken:o,nonce:l}=t;return s||a||i||o?o?r.startsWith("saml.")?Kt._create(r,o):b._fromParams({providerId:r,signInMethod:r,pendingToken:o,idToken:i,accessToken:s}):new Gt(r).credential({idToken:i,accessToken:s,rawNonce:l}):null}return e instanceof d?n.credentialFromError(e):n.credentialFromResult(e)}function U(t,e){return e.catch(e=>{throw e instanceof d&&Ui(t,e),e}).then(e=>{var t=e.operationType,r=e.user;return{operationType:t,credential:Fi(e),additionalUserInfo:Rr(e),user:F.getOrCreate(r)}})}async function Vi(t,e){let r=await e;return{verificationId:r.verificationId,confirm:e=>U(t,r.confirm(e))}}class xi{constructor(e,t){this.resolver=t,this.auth=e.wrapped()}get session(){return this.resolver.session}get hints(){return this.resolver.hints}resolveSignIn(e){return U(Mi(this.auth),this.resolver.resolveSignIn(e))}}class F{constructor(e){var t;this._delegate=e,this.multiFactor=(t=o(e),Nr.has(t)||Nr.set(t,Or._fromUser(t)),Nr.get(t))}static getOrCreate(e){return F.USER_MAP.has(e)||F.USER_MAP.set(e,new F(e)),F.USER_MAP.get(e)}delete(){return this._delegate.delete()}reload(){return this._delegate.reload()}toJSON(){return this._delegate.toJSON()}getIdTokenResult(e){return this._delegate.getIdTokenResult(e)}getIdToken(e){return this._delegate.getIdToken(e)}linkAndRetrieveDataWithCredential(e){return this.linkWithCredential(e)}async linkWithCredential(e){return U(this.auth,ar(this._delegate,e))}async linkWithPhoneNumber(e,t){return Vi(this.auth,(async(e,t,r)=>{let n=o(e);await rr(!1,n,"phone");var i=await cn(n.auth,t,o(r));return new ln(i,e=>ar(n,e))})(this._delegate,e,t))}async linkWithPopup(e){return U(this.auth,(async(e,t,r)=>{var n=o(e),i=(ke(n.auth,t,k),un(n.auth,r));return new L(n.auth,"linkViaPopup",t,i,n).executeNotNull()})(this._delegate,e,M))}async linkWithRedirect(e){return await Ni(w(this.auth)),Rn(this._delegate,e,M)}reauthenticateAndRetrieveDataWithCredential(e){return this.reauthenticateWithCredential(e)}async reauthenticateWithCredential(e){return U(this.auth,or(this._delegate,e))}reauthenticateWithPhoneNumber(e,t){return Vi(this.auth,(async(e,t,r)=>{let n=o(e);var i;return Ki._isFirebaseServerApp(n.auth.app)?Promise.reject(l(n.auth)):(i=await cn(n.auth,t,o(r)),new ln(i,e=>or(n,e)))})(this._delegate,e,t))}reauthenticateWithPopup(e){return U(this.auth,(async(e,t,r)=>{var n=o(e);if(Ki._isFirebaseServerApp(n.auth.app))return Promise.reject(h(n.auth,"operation-not-supported-in-this-environment"));ke(n.auth,t,k);var i=un(n.auth,r);return new L(n.auth,"reauthViaPopup",t,i,n).executeNotNull()})(this._delegate,e,M))}async reauthenticateWithRedirect(e){return await Ni(w(this.auth)),Sn(this._delegate,e,M)}sendEmailVerification(e){return vr(this._delegate,e)}async unlink(e){return await er(this._delegate,e),this}updateEmail(e){return t=this._delegate,e=e,r=o(t),Ki._isFirebaseServerApp(r.auth.app)?Promise.reject(l(r.auth)):Ir(r,e,null);var t,r}updatePassword(e){return Ir(o(this._delegate),null,e)}updatePhoneNumber(e){return(async(e,t)=>{var r=o(e);if(Ki._isFirebaseServerApp(r.auth.app))return Promise.reject(l(r.auth));await tr(r,t)})(this._delegate,e)}updateProfile(e){return yr(this._delegate,e)}verifyBeforeUpdateEmail(e,t){return _r(this._delegate,e,t)}get emailVerified(){return this._delegate.emailVerified}get isAnonymous(){return this._delegate.isAnonymous}get metadata(){return this._delegate.metadata}get phoneNumber(){return this._delegate.phoneNumber}get providerData(){return this._delegate.providerData}get refreshToken(){return this._delegate.refreshToken}get tenantId(){return this._delegate.tenantId}get displayName(){return this._delegate.displayName}get email(){return this._delegate.email}get photoURL(){return this._delegate.photoURL}get providerId(){return this._delegate.providerId}get uid(){return this._delegate.uid}get auth(){return this._delegate.auth}}F.USER_MAP=new WeakMap;let ji=g;class Hi{constructor(e,t){var r,n;this.app=e,t.isInitialized()?this._delegate=t.getImmediate():(r=e.options.apiKey,ji(r,"invalid-api-key",{appName:e.name}),ji(r,"invalid-api-key",{appName:e.name}),n="undefined"!=typeof window?M:void 0,this._delegate=t.initialize({options:{persistence:((e,t)=>{var r=((e,t)=>{var r=Li();if(!r)return[];var n=I(Oi,e,t);switch(r.getItem(n)){case D.NONE:return[rt];case D.LOCAL:return[Zr,Vr];case D.SESSION:return[Vr];default:return[]}})(e,t);if("undefined"==typeof self||r.includes(Zr)||r.push(Zr),"undefined"!=typeof window)for(var n of[Ur,Vr])r.includes(n)||r.push(n);return r.includes(rt)||r.push(rt),r})(r,e.name),popupRedirectResolver:n}}),this._delegate._updateErrorMap(ye)),this.linkUnderlyingAuth()}get emulatorConfig(){return this._delegate.emulatorConfig}get currentUser(){return this._delegate.currentUser?F.getOrCreate(this._delegate.currentUser):null}get languageCode(){return this._delegate.languageCode}set languageCode(e){this._delegate.languageCode=e}get settings(){return this._delegate.settings}get tenantId(){return this._delegate.tenantId}set tenantId(e){this._delegate.tenantId=e}useDeviceLanguage(){this._delegate.useDeviceLanguage()}signOut(){return this._delegate.signOut()}useEmulator(e,t){Pt(this._delegate,e,t)}applyActionCode(e){return mr(this._delegate,e)}checkActionCode(e){return gr(this._delegate,e)}confirmPasswordReset(e,t){return(async(t,e,r)=>{await Lt(o(t),{oobCode:e,newPassword:r}).catch(async e=>{throw"auth/password-does-not-meet-requirements"===e.code&&pr(t),e})})(this._delegate,e,t)}async createUserWithEmailAndPassword(e,t){return U(this._delegate,(async(t,e,r)=>{var n,i;return Ki._isFirebaseServerApp(t.app)?Promise.reject(l(t)):(i=await T(n=w(t),{returnSecureToken:!0,email:e,password:r,clientType:"CLIENT_TYPE_WEB"},"signUpPassword",Yt,"EMAIL_PASSWORD_PROVIDER").catch(e=>{throw"auth/password-does-not-meet-requirements"===e.code&&pr(t),e}),i=await C._fromIdTokenResponse(n,"signIn",i),await n._updateCurrentUser(i.user),i)})(this._delegate,e,t))}fetchProvidersForEmail(e){return this.fetchSignInMethodsForEmail(e)}fetchSignInMethodsForEmail(e){return fr(this._delegate,e)}isSignInWithEmailLink(e){return this._delegate,e=e,"EMAIL_SIGNIN"===(null==(t=qt.parseLink(e))?void 0:t.operation);var t}async getRedirectResult(){ji(Ai(),this._delegate,"operation-not-supported-in-this-environment");e=this._delegate,t=M,await w(e)._initializationPromise;var e,t,r=await An(e,t,!1);return r?U(this._delegate,Promise.resolve(r)):{credential:null,user:null}}addFrameworkForLogging(e){w(this._delegate)._logFramework(e)}onAuthStateChanged(e,t,r){var{next:n,error:i,complete:s}=Wi(e,t,r);return this._delegate.onAuthStateChanged(n,i,s)}onIdTokenChanged(e,t,r){var{next:n,error:i,complete:s}=Wi(e,t,r);return this._delegate.onIdTokenChanged(n,i,s)}sendSignInLinkToEmail(e,t){return(async(e,t,r)=>{let n=w(e);var i={requestType:"EMAIL_SIGNIN",email:t,clientType:"CLIENT_TYPE_WEB"};e=i,g((t=r).handleCodeInApp,n,"argument-error"),t&&hr(n,e,t),await T(n,i,"getOobCode",Vt,"EMAIL_PASSWORD_PROVIDER")})(this._delegate,e,t)}sendPasswordResetEmail(e,t){return(async(e,t,r)=>{var n=w(e),i={requestType:"PASSWORD_RESET",email:t,clientType:"CLIENT_TYPE_WEB"};r&&hr(n,i,r),await T(n,i,"getOobCode",Ft,"EMAIL_PASSWORD_PROVIDER")})(this._delegate,e,t||void 0)}async setPersistence(e){var t,r;t=this._delegate,r=e,Ci(Object.values(D).includes(r),t,"invalid-persistence-type"),Z()?Ci(r!==D.SESSION,t,"unsupported-persistence-type"):$()?Ci(r===D.NONE,t,"unsupported-persistence-type"):Ri()?Ci(r===D.NONE||r===D.LOCAL&&ee(),t,"unsupported-persistence-type"):Ci(r===D.NONE||Si(),t,"unsupported-persistence-type");let n;switch(e){case D.SESSION:n=Vr;break;case D.LOCAL:var i=await y(Zr)._isAvailable();n=i?Zr:Ur;break;case D.NONE:n=rt;break;default:return u("argument-error",{appName:this._delegate.name})}return this._delegate.setPersistence(n)}signInAndRetrieveDataWithCredential(e){return this.signInWithCredential(e)}signInAnonymously(){return U(this._delegate,(async e=>{var t,r;return Ki._isFirebaseServerApp(e.app)?Promise.reject(l(e)):(await(t=w(e))._initializationPromise,null!=(r=t.currentUser)&&r.isAnonymous?new C({user:t.currentUser,providerId:null,operationType:"signIn"}):(r=await Yt(t,{returnSecureToken:!0}),r=await C._fromIdTokenResponse(t,"signIn",r,!0),await t._updateCurrentUser(r.user),r))})(this._delegate))}signInWithCredential(e){return U(this._delegate,sr(this._delegate,e))}signInWithCustomToken(e){return U(this._delegate,lr(this._delegate,e))}signInWithEmailAndPassword(e,t){return U(this._delegate,(r=this._delegate,e=e,t=t,Ki._isFirebaseServerApp(r.app)?Promise.reject(l(r)):sr(o(r),Bt.credential(e,t)).catch(async e=>{throw"auth/password-does-not-meet-requirements"===e.code&&pr(r),e})));var r}signInWithEmailLink(e,t){return U(this._delegate,(async(e,t,r)=>{var n,i;return Ki._isFirebaseServerApp(e.app)?Promise.reject(l(e)):(n=o(e),g((i=Bt.credentialWithLink(t,r||Re()))._tenantId===(n.tenantId||null),n,"tenant-id-mismatch"),sr(n,i))})(this._delegate,e,t))}signInWithPhoneNumber(e,t){return Vi(this._delegate,(async(e,t,r)=>{if(Ki._isFirebaseServerApp(e.app))return Promise.reject(l(e));let n=w(e);var i=await cn(n,t,o(r));return new ln(i,e=>sr(n,e))})(this._delegate,e,t))}async signInWithPopup(e){return ji(Ai(),this._delegate,"operation-not-supported-in-this-environment"),U(this._delegate,(async(e,t,r)=>{var n,i;return Ki._isFirebaseServerApp(e.app)?Promise.reject(h(e,"operation-not-supported-in-this-environment")):(n=w(e),ke(e,t,k),i=un(n,r),new L(n,"signInViaPopup",t,i).executeNotNull())})(this._delegate,e,M))}async signInWithRedirect(e){return ji(Ai(),this._delegate,"operation-not-supported-in-this-environment"),await Ni(this._delegate),kn(this._delegate,e,M)}updateCurrentUser(e){return this._delegate.updateCurrentUser(e)}verifyPasswordResetCode(e){return(async(e,t)=>{var r=(await gr(o(e),t)).data;return r.email})(this._delegate,e)}unwrap(){return this._delegate}_delete(){return this._delegate._delete()}linkUnderlyingAuth(){this._delegate.wrapped=()=>this}}function Wi(e,t,r){let n=e,i=("function"!=typeof e&&({next:n,error:t,complete:r}=e),n);return{next:e=>i(e&&F.getOrCreate(e)),error:t,complete:r}}Hi.Persistence=D;class qi{static credential(e,t){return N.credential(e,t)}constructor(){this.providerId="phone",this._delegate=new N(Mi(V.default.auth()))}verifyPhoneNumber(e,t){return this._delegate.verifyPhoneNumber(e,t)}unwrap(){return this._delegate}}qi.PHONE_SIGN_IN_METHOD=N.PHONE_SIGN_IN_METHOD,qi.PROVIDER_ID=N.PROVIDER_ID;let Bi=g;class zi{constructor(e,t,r=V.default.app()){var n;Bi(null==(n=r.options)?void 0:n.apiKey,"invalid-api-key",{appName:r.name}),this._delegate=new on(r.auth(),e,t),this.type=this._delegate.type}clear(){this._delegate.clear()}render(){return this._delegate.render()}verify(){return this._delegate.verify()}}(e=V.default).INTERNAL.registerComponent(new ge("auth-compat",e=>{var t=e.getProvider("app-compat").getImmediate(),r=e.getProvider("auth");return new Hi(t,r)},"PUBLIC").setServiceProps({ActionCodeInfo:{Operation:{EMAIL_SIGNIN:ve.EMAIL_SIGNIN,PASSWORD_RESET:ve.PASSWORD_RESET,RECOVER_EMAIL:ve.RECOVER_EMAIL,REVERT_SECOND_FACTOR_ADDITION:ve.REVERT_SECOND_FACTOR_ADDITION,VERIFY_AND_CHANGE_EMAIL:ve.VERIFY_AND_CHANGE_EMAIL,VERIFY_EMAIL:ve.VERIFY_EMAIL}},EmailAuthProvider:Bt,FacebookAuthProvider:S,GithubAuthProvider:A,GoogleAuthProvider:R,OAuthProvider:Gt,SAMLAuthProvider:Jt,PhoneAuthProvider:qi,PhoneMultiFactorGenerator:ii,RecaptchaVerifier:zi,TwitterAuthProvider:P,Auth:Hi,AuthCredential:Nt,Error:d}).setInstantiationMode("LAZY").setMultipleInstances(!1)),e.registerVersion("@firebase/auth-compat","0.5.26")}).apply(this,arguments)}catch(e){throw console.error(e),new Error("Cannot instantiate firebase-auth-compat.js - be sure to load firebase-app.js first.")}});
//# sourceMappingURL=firebase-auth-compat.js.map
