
'use client';

import { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { motion, AnimatePresence } from 'framer-motion';
import type { Category, SubCategory, BudgetData, FinancialGoal } from '@/lib/types';
import Header from '@/components/layout/Header';
import IncomeInput from '@/components/budget/IncomeInput';
import SummaryDisplay from '@/components/budget/SummaryDisplay';
import CategoryManager from '@/components/budget/CategoryManager';
import BudgetVisualizer from '@/components/budget/BudgetVisualizer';
import FinancialGoalManager from '@/components/budget/FinancialGoalManager';
import { SkeletonDashboard } from '@/components/ui/skeleton';
import { AchievementBadge, AchievementToast } from '@/components/ui/achievement-badge';
import { useToast } from "@/hooks/use-toast";
import { useAuth } from '@/context/AuthContext';
import { WalletCards, Target, Trophy } from 'lucide-react';

const ACHIEVEMENT_IDS = {
  BUDGET_STARTER: 'BUDGET_STARTER',
  GOAL_SETTER: 'GOAL_SETTER',
  GOAL_CRUSHER: 'GOAL_CRUSHER',
};

const ACHIEVEMENT_DETAILS: Record<string, { title: string; description: string; IconComponent: React.ElementType }> = {
  [ACHIEVEMENT_IDS.BUDGET_STARTER]: { title: "Budget Starter!", description: "You've set your income and added your first category!", IconComponent: WalletCards },
  [ACHIEVEMENT_IDS.GOAL_SETTER]: { title: "Goal Setter!", description: "You've set your first financial goal!", IconComponent: Target },
  [ACHIEVEMENT_IDS.GOAL_CRUSHER]: { title: "Goal Crusher!", description: "Congratulations! You've achieved your financial goal!", IconComponent: Trophy },
};


export default function BudgetPage() {
  const { currentUser, loading: authLoading } = useAuth();
  const router = useRouter();

  const [isMounted, setIsMounted] = useState(false);
  const [totalIncome, setTotalIncome] = useState<number>(0);
  const [categories, setCategories] = useState<Category[]>([]);
  const [financialGoal, setFinancialGoal] = useState<FinancialGoal | null>(null);
  const [achievements, setAchievements] = useState<string[]>([]);
  const [balancesVisible, setBalancesVisible] = useState<boolean>(true);
  const [newAchievement, setNewAchievement] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const { toast } = useToast();

  const getLocalStorageKey = useCallback(() => {
    if (currentUser) {
      return `budgetWiseData_${currentUser.uid}`;
    }
    return null; // Or a default key if you want non-authenticated users to have some data (not recommended for this app)
  }, [currentUser]);


  const awardAchievement = useCallback((achievementId: string) => {
    if (!achievements.includes(achievementId)) {
      setAchievements(prev => [...prev, achievementId]);
      setNewAchievement(achievementId);
      const details = ACHIEVEMENT_DETAILS[achievementId];
      if (details) {
        toast({
          title: (
            <div className="flex items-center gap-2">
              <details.IconComponent className="h-5 w-5 text-accent" />
              <span>{details.title}</span>
            </div>
          ),
          description: details.description,
        });
        // Clear the new achievement notification after 5 seconds
        setTimeout(() => setNewAchievement(null), 5000);
      }
    }
  }, [achievements, toast]);

  useEffect(() => {
    setIsMounted(true); // Component is mounted on client
  }, []);

  useEffect(() => {
    if (authLoading) return; // Wait for auth state to be determined

    if (!currentUser) {
      router.push('/login');
      return;
    }

    const storageKey = getLocalStorageKey();
    if (!storageKey) return; // Should not happen if currentUser is present

    if (isMounted) { // Ensure component is client-side mounted before accessing localStorage
      const storedData = localStorage.getItem(storageKey);
      if (storedData) {
        try {
          const parsedData: BudgetData = JSON.parse(storedData);
          setTotalIncome(parsedData.totalIncome || 0);
          setCategories(
            (parsedData.categories || []).map(cat => ({
              ...cat,
              isVisible: cat.isVisible !== undefined ? cat.isVisible : true,
              subCategories: cat.subCategories || [],
            }))
          );
          setFinancialGoal(parsedData.financialGoal || null);
          setAchievements(parsedData.achievements || []);
          setBalancesVisible(parsedData.balancesVisible !== undefined ? parsedData.balancesVisible : true);
        } catch (error) {
          console.error("Failed to parse budget data from localStorage", error);
          // Reset to defaults if parsing fails
          setTotalIncome(0);
          setCategories([]);
          setFinancialGoal(null);
          setAchievements([]);
          setBalancesVisible(true);
        }
      } else {
        // No data found, initialize with defaults
        setTotalIncome(0);
        setCategories([]);
        setFinancialGoal(null);
        setAchievements([]);
        setBalancesVisible(true);
      }
    }
  }, [currentUser, authLoading, router, isMounted, getLocalStorageKey]);

  useEffect(() => {
    const storageKey = getLocalStorageKey();
    if (!storageKey || !isMounted || authLoading || !currentUser) return; // Only save if user is logged in and component is mounted

    const budgetData: BudgetData = { totalIncome, categories, financialGoal, achievements, balancesVisible };
    localStorage.setItem(storageKey, JSON.stringify(budgetData));

    if (totalIncome > 0 && categories.length > 0) {
      awardAchievement(ACHIEVEMENT_IDS.BUDGET_STARTER);
    }
    if (financialGoal?.dateAchieved) {
      awardAchievement(ACHIEVEMENT_IDS.GOAL_CRUSHER);
    }
  }, [totalIncome, categories, financialGoal, achievements, balancesVisible, isMounted, awardAchievement, getLocalStorageKey, authLoading, currentUser]);

  const toggleBalancesVisibility = useCallback(() => {
    setBalancesVisible(prev => !prev);
  }, []);

  const toggleCategoryVisibility = useCallback((categoryId: string) => {
    setCategories(prevCategories =>
      prevCategories.map(cat =>
        cat.id === categoryId ? { ...cat, isVisible: !(cat.isVisible ?? true) } : cat
      )
    );
  }, []);

  const handleIncomeChange = useCallback((amount: number) => {
    setTotalIncome(amount);
  }, []);

  const addCategory = useCallback((name: string, budget: number) => {
    const newCategory: Category = {
      id: crypto.randomUUID(),
      name,
      budget,
      subCategories: [],
      isVisible: true,
    };
    setCategories((prev) => [...prev, newCategory]);
  }, []);

  const updateCategory = useCallback((id: string, newName: string, newBudget: number) => {
    setCategories((prev) =>
      prev.map((cat) =>
        cat.id === id ? { ...cat, name: newName, budget: newBudget } : cat
      )
    );
  }, []);

  const deleteCategory = useCallback((id: string) => {
    setCategories((prev) => prev.filter((cat) => cat.id !== id));
  }, []);

  const addSubCategory = useCallback((parentId: string, name: string, allocatedAmount: number): boolean => {
    let success = false;
    setCategories((prev) =>
      prev.map((cat) => {
        if (cat.id === parentId) {
          const currentSubTotal = cat.subCategories.reduce((sum, sc) => sum + sc.allocatedAmount, 0);
          if (currentSubTotal + allocatedAmount > cat.budget) {
            success = false;
            return cat;
          }
          const newSubCategory: SubCategory = {
            id: crypto.randomUUID(),
            name,
            allocatedAmount,
          };
          success = true;
          return { ...cat, subCategories: [...cat.subCategories, newSubCategory] };
        }
        return cat;
      })
    );
    return success;
  }, []);

  const updateSubCategory = useCallback((parentId: string, subId: string, newName: string, newAllocatedAmount: number): boolean => {
    let success = false;
    setCategories((prev) =>
      prev.map((cat) => {
        if (cat.id === parentId) {
          const otherSubCategoriesTotal = cat.subCategories
            .filter(sc => sc.id !== subId)
            .reduce((sum, sc) => sum + sc.allocatedAmount, 0);
          
          if (otherSubCategoriesTotal + newAllocatedAmount > cat.budget) {
            success = false;
            return cat;
          }
          success = true;
          return {
            ...cat,
            subCategories: cat.subCategories.map((sc) =>
              sc.id === subId ? { ...sc, name: newName, allocatedAmount: newAllocatedAmount } : sc
            ),
          };
        }
        return cat;
      })
    );
    return success;
  }, []);

  const deleteSubCategory = useCallback((parentId: string, subId: string) => {
    setCategories((prev) =>
      prev.map((cat) =>
        cat.id === parentId
          ? { ...cat, subCategories: cat.subCategories.filter((sc) => sc.id !== subId) }
          : cat
      )
    );
    toast({ title: "Subcategory Deleted", description: "Subcategory has been removed."});
  }, [toast]);

  const handleSetFinancialGoal = useCallback((name: string, targetAmount: number, icon?: string) => {
    const newGoal: FinancialGoal = {
      id: financialGoal?.id || crypto.randomUUID(),
      name,
      targetAmount,
      savedAmount: financialGoal?.id ? financialGoal.savedAmount : 0,
      icon,
      dateSet: financialGoal?.dateSet || new Date().toISOString(),
      dateAchieved: null,
    };
    setFinancialGoal(newGoal);
    if (!achievements.includes(ACHIEVEMENT_IDS.GOAL_SETTER) || !financialGoal) {
        awardAchievement(ACHIEVEMENT_IDS.GOAL_SETTER);
    }
    toast({ title: "Financial Goal Updated!", description: `Your goal "${name}" has been set/updated.`});
  }, [financialGoal, awardAchievement, toast, achievements]);

  const handleUpdateGoalProgress = useCallback((savedAmount: number) => {
    if (financialGoal) {
      const updatedGoal = { ...financialGoal, savedAmount };
      if (savedAmount >= financialGoal.targetAmount && !financialGoal.dateAchieved) {
        updatedGoal.dateAchieved = new Date().toISOString();
        awardAchievement(ACHIEVEMENT_IDS.GOAL_CRUSHER);
        toast({ title: "Goal Achieved!", description: `Congratulations on reaching your goal: ${financialGoal.name}!`, duration: 5000 });
      }
      setFinancialGoal(updatedGoal);
    }
  }, [financialGoal, awardAchievement, toast]);
  
  const handleClearGoal = useCallback(() => {
    setFinancialGoal(null);
    toast({ title: "Financial Goal Cleared", description: "Your financial goal has been removed."});
  }, [toast]);

  const overallTotalAllocated = categories.reduce((sum, cat) => sum + cat.budget, 0);
  const overallRemaining = totalIncome - overallTotalAllocated;

  if (authLoading || !isMounted || !currentUser) {
    return (
      <div className="flex flex-col min-h-screen">
        <Header title="BudgetWise" balancesVisible={balancesVisible} onToggleBalances={toggleBalancesVisibility} />
        <SkeletonDashboard />
      </div>
    );
  }

  return (
    <div className="flex flex-col min-h-screen bg-background">
      <Header title="BudgetWise" balancesVisible={balancesVisible} onToggleBalances={toggleBalancesVisibility} />

      {/* Achievement Notification */}
      <AnimatePresence>
        {newAchievement && (
          <motion.div
            initial={{ y: -100, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            exit={{ y: -100, opacity: 0 }}
            className="fixed top-20 right-4 z-50"
          >
            <AchievementToast
              title={ACHIEVEMENT_DETAILS[newAchievement]?.title || "Achievement Unlocked!"}
              description={ACHIEVEMENT_DETAILS[newAchievement]?.description || "You've earned a new achievement!"}
              icon="trophy"
              variant="gold"
              onClose={() => setNewAchievement(null)}
            />
          </motion.div>
        )}
      </AnimatePresence>

      <main className="flex-grow container mx-auto py-4 sm:py-6 px-2 sm:px-4">
        {/* Achievement Badges */}
        {achievements.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="mb-6 flex flex-wrap gap-2"
          >
            {achievements.map((achievementId) => (
              <AchievementBadge
                key={achievementId}
                title={ACHIEVEMENT_DETAILS[achievementId]?.title || "Achievement"}
                icon="trophy"
                variant="gold"
                size="sm"
                animated={true}
              />
            ))}
          </motion.div>
        )}

        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5 }}
          className="grid grid-cols-1 md:grid-cols-3 gap-4 sm:gap-6"
        >
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
            className="md:col-span-1 space-y-4 sm:space-y-6"
          >
            <IncomeInput totalIncome={totalIncome} onIncomeChange={handleIncomeChange} balancesVisible={balancesVisible} />
            <SummaryDisplay totalIncome={totalIncome} overallTotalAllocated={overallTotalAllocated} balancesVisible={balancesVisible} />
            <FinancialGoalManager
              goal={financialGoal}
              onSetGoal={handleSetFinancialGoal}
              onUpdateProgress={handleUpdateGoalProgress}
              onClearGoal={handleClearGoal}
              overallRemaining={overallRemaining}
              balancesVisible={balancesVisible}
            />
            <BudgetVisualizer totalIncome={totalIncome} categories={categories} balancesVisible={balancesVisible} />
          </motion.div>
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="md:col-span-2"
          >
            <CategoryManager
              categories={categories}
              onAddCategory={addCategory}
              onUpdateCategory={updateCategory}
              onDeleteCategory={deleteCategory}
              onAddSubCategory={addSubCategory}
              onUpdateSubCategory={updateSubCategory}
              onDeleteSubCategory={deleteSubCategory}
              onToggleCategoryVisibility={toggleCategoryVisibility}
              balancesVisible={balancesVisible}
            />
          </motion.div>
        </motion.div>
      </main>
    </div>
  );
}
