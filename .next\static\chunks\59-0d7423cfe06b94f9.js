"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[59],{519:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(40157).A)("PiggyBank",[["path",{d:"M19 5c-1.5 0-2.8 1.4-3 2-3.5-1.5-11-.3-11 5 0 1.8 0 3 2 4.5V20h4v-2h3v2h4v-4c1-.5 1.7-1 2-2h2v-4h-2c0-1-.5-1.5-1-2V5z",key:"1ivx2i"}],["path",{d:"M2 9v1c0 1.1.9 2 2 2h1",key:"nm575m"}],["path",{d:"M16 11h.01",key:"xkw8gn"}]])},4607:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(40157).A)("EyeOff",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},9449:(e,t,n)=>{n.d(t,{H_:()=>td,UC:()=>tl,YJ:()=>tu,q7:()=>ts,VF:()=>th,JU:()=>tc,ZL:()=>ta,z6:()=>tf,hN:()=>tp,bL:()=>to,wv:()=>tv,Pb:()=>tm,G5:()=>ty,ZP:()=>tg,l9:()=>ti});var r=n(12115),o=n(85185),i=n(6101),a=n(46081),l=n(5845),u=n(63655),c=n(82284),s=n(94315),d=n(19178),f=n(92293),p=n(25519),h=n(61285),v=n(38795),m=n(34378),g=n(28905),y=n(39033),w=n(95155),x="rovingFocusGroup.onEntryFocus",b={bubbles:!1,cancelable:!0},C="RovingFocusGroup",[E,R,A]=(0,c.N)(C),[k,M]=(0,a.A)(C,[A]),[S,T]=k(C),L=r.forwardRef((e,t)=>(0,w.jsx)(E.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,w.jsx)(E.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,w.jsx)(j,{...e,ref:t})})}));L.displayName=C;var j=r.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,orientation:a,loop:c=!1,dir:d,currentTabStopId:f,defaultCurrentTabStopId:p,onCurrentTabStopIdChange:h,onEntryFocus:v,preventScrollOnEntryFocus:m=!1,...g}=e,C=r.useRef(null),E=(0,i.s)(t,C),A=(0,s.jH)(d),[k=null,M]=(0,l.i)({prop:f,defaultProp:p,onChange:h}),[T,L]=r.useState(!1),j=(0,y.c)(v),P=R(n),D=r.useRef(!1),[N,O]=r.useState(0);return r.useEffect(()=>{let e=C.current;if(e)return e.addEventListener(x,j),()=>e.removeEventListener(x,j)},[j]),(0,w.jsx)(S,{scope:n,orientation:a,dir:A,loop:c,currentTabStopId:k,onItemFocus:r.useCallback(e=>M(e),[M]),onItemShiftTab:r.useCallback(()=>L(!0),[]),onFocusableItemAdd:r.useCallback(()=>O(e=>e+1),[]),onFocusableItemRemove:r.useCallback(()=>O(e=>e-1),[]),children:(0,w.jsx)(u.sG.div,{tabIndex:T||0===N?-1:0,"data-orientation":a,...g,ref:E,style:{outline:"none",...e.style},onMouseDown:(0,o.m)(e.onMouseDown,()=>{D.current=!0}),onFocus:(0,o.m)(e.onFocus,e=>{let t=!D.current;if(e.target===e.currentTarget&&t&&!T){let t=new CustomEvent(x,b);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=P().filter(e=>e.focusable);I([e.find(e=>e.active),e.find(e=>e.id===k),...e].filter(Boolean).map(e=>e.ref.current),m)}}D.current=!1}),onBlur:(0,o.m)(e.onBlur,()=>L(!1))})})}),P="RovingFocusGroupItem",D=r.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,focusable:i=!0,active:a=!1,tabStopId:l,...c}=e,s=(0,h.B)(),d=l||s,f=T(P,n),p=f.currentTabStopId===d,v=R(n),{onFocusableItemAdd:m,onFocusableItemRemove:g}=f;return r.useEffect(()=>{if(i)return m(),()=>g()},[i,m,g]),(0,w.jsx)(E.ItemSlot,{scope:n,id:d,focusable:i,active:a,children:(0,w.jsx)(u.sG.span,{tabIndex:p?0:-1,"data-orientation":f.orientation,...c,ref:t,onMouseDown:(0,o.m)(e.onMouseDown,e=>{i?f.onItemFocus(d):e.preventDefault()}),onFocus:(0,o.m)(e.onFocus,()=>f.onItemFocus(d)),onKeyDown:(0,o.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey){f.onItemShiftTab();return}if(e.target!==e.currentTarget)return;let t=function(e,t,n){var r;let o=(r=e.key,"rtl"!==n?r:"ArrowLeft"===r?"ArrowRight":"ArrowRight"===r?"ArrowLeft":r);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(o)))return N[o]}(e,f.orientation,f.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let n=v().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)n.reverse();else if("prev"===t||"next"===t){"prev"===t&&n.reverse();let r=n.indexOf(e.currentTarget);n=f.loop?function(e,t){return e.map((n,r)=>e[(t+r)%e.length])}(n,r+1):n.slice(r+1)}setTimeout(()=>I(n))}})})})});D.displayName=P;var N={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function I(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=document.activeElement;for(let r of e)if(r===n||(r.focus({preventScroll:t}),document.activeElement!==n))return}var O=n(99708),F=n(38168),_=n(31114),B=["Enter"," "],H=["ArrowUp","PageDown","End"],K=["ArrowDown","PageUp","Home",...H],W={ltr:[...B,"ArrowRight"],rtl:[...B,"ArrowLeft"]},G={ltr:["ArrowLeft"],rtl:["ArrowRight"]},z="Menu",[V,U,X]=(0,c.N)(z),[Y,q]=(0,a.A)(z,[X,v.Bk,M]),Z=(0,v.Bk)(),$=M(),[J,Q]=Y(z),[ee,et]=Y(z),en=e=>{let{__scopeMenu:t,open:n=!1,children:o,dir:i,onOpenChange:a,modal:l=!0}=e,u=Z(t),[c,d]=r.useState(null),f=r.useRef(!1),p=(0,y.c)(a),h=(0,s.jH)(i);return r.useEffect(()=>{let e=()=>{f.current=!0,document.addEventListener("pointerdown",t,{capture:!0,once:!0}),document.addEventListener("pointermove",t,{capture:!0,once:!0})},t=()=>f.current=!1;return document.addEventListener("keydown",e,{capture:!0}),()=>{document.removeEventListener("keydown",e,{capture:!0}),document.removeEventListener("pointerdown",t,{capture:!0}),document.removeEventListener("pointermove",t,{capture:!0})}},[]),(0,w.jsx)(v.bL,{...u,children:(0,w.jsx)(J,{scope:t,open:n,onOpenChange:p,content:c,onContentChange:d,children:(0,w.jsx)(ee,{scope:t,onClose:r.useCallback(()=>p(!1),[p]),isUsingKeyboardRef:f,dir:h,modal:l,children:o})})})};en.displayName=z;var er=r.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e,o=Z(n);return(0,w.jsx)(v.Mz,{...o,...r,ref:t})});er.displayName="MenuAnchor";var eo="MenuPortal",[ei,ea]=Y(eo,{forceMount:void 0}),el=e=>{let{__scopeMenu:t,forceMount:n,children:r,container:o}=e,i=Q(eo,t);return(0,w.jsx)(ei,{scope:t,forceMount:n,children:(0,w.jsx)(g.C,{present:n||i.open,children:(0,w.jsx)(m.Z,{asChild:!0,container:o,children:r})})})};el.displayName=eo;var eu="MenuContent",[ec,es]=Y(eu),ed=r.forwardRef((e,t)=>{let n=ea(eu,e.__scopeMenu),{forceMount:r=n.forceMount,...o}=e,i=Q(eu,e.__scopeMenu),a=et(eu,e.__scopeMenu);return(0,w.jsx)(V.Provider,{scope:e.__scopeMenu,children:(0,w.jsx)(g.C,{present:r||i.open,children:(0,w.jsx)(V.Slot,{scope:e.__scopeMenu,children:a.modal?(0,w.jsx)(ef,{...o,ref:t}):(0,w.jsx)(ep,{...o,ref:t})})})})}),ef=r.forwardRef((e,t)=>{let n=Q(eu,e.__scopeMenu),a=r.useRef(null),l=(0,i.s)(t,a);return r.useEffect(()=>{let e=a.current;if(e)return(0,F.Eq)(e)},[]),(0,w.jsx)(eh,{...e,ref:l,trapFocus:n.open,disableOutsidePointerEvents:n.open,disableOutsideScroll:!0,onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>n.onOpenChange(!1)})}),ep=r.forwardRef((e,t)=>{let n=Q(eu,e.__scopeMenu);return(0,w.jsx)(eh,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>n.onOpenChange(!1)})}),eh=r.forwardRef((e,t)=>{let{__scopeMenu:n,loop:a=!1,trapFocus:l,onOpenAutoFocus:u,onCloseAutoFocus:c,disableOutsidePointerEvents:s,onEntryFocus:h,onEscapeKeyDown:m,onPointerDownOutside:g,onFocusOutside:y,onInteractOutside:x,onDismiss:b,disableOutsideScroll:C,...E}=e,R=Q(eu,n),A=et(eu,n),k=Z(n),M=$(n),S=U(n),[T,j]=r.useState(null),P=r.useRef(null),D=(0,i.s)(t,P,R.onContentChange),N=r.useRef(0),I=r.useRef(""),F=r.useRef(0),B=r.useRef(null),W=r.useRef("right"),G=r.useRef(0),z=C?_.A:r.Fragment,V=C?{as:O.DX,allowPinchZoom:!0}:void 0,X=e=>{var t,n;let r=I.current+e,o=S().filter(e=>!e.disabled),i=document.activeElement,a=null===(t=o.find(e=>e.ref.current===i))||void 0===t?void 0:t.textValue,l=function(e,t,n){var r;let o=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,i=(r=Math.max(n?e.indexOf(n):-1,0),e.map((t,n)=>e[(r+n)%e.length]));1===o.length&&(i=i.filter(e=>e!==n));let a=i.find(e=>e.toLowerCase().startsWith(o.toLowerCase()));return a!==n?a:void 0}(o.map(e=>e.textValue),r,a),u=null===(n=o.find(e=>e.textValue===l))||void 0===n?void 0:n.ref.current;!function e(t){I.current=t,window.clearTimeout(N.current),""!==t&&(N.current=window.setTimeout(()=>e(""),1e3))}(r),u&&setTimeout(()=>u.focus())};r.useEffect(()=>()=>window.clearTimeout(N.current),[]),(0,f.Oh)();let Y=r.useCallback(e=>{var t,n;return W.current===(null===(t=B.current)||void 0===t?void 0:t.side)&&function(e,t){return!!t&&function(e,t){let{x:n,y:r}=e,o=!1;for(let e=0,i=t.length-1;e<t.length;i=e++){let a=t[e].x,l=t[e].y,u=t[i].x,c=t[i].y;l>r!=c>r&&n<(u-a)*(r-l)/(c-l)+a&&(o=!o)}return o}({x:e.clientX,y:e.clientY},t)}(e,null===(n=B.current)||void 0===n?void 0:n.area)},[]);return(0,w.jsx)(ec,{scope:n,searchRef:I,onItemEnter:r.useCallback(e=>{Y(e)&&e.preventDefault()},[Y]),onItemLeave:r.useCallback(e=>{var t;Y(e)||(null===(t=P.current)||void 0===t||t.focus(),j(null))},[Y]),onTriggerLeave:r.useCallback(e=>{Y(e)&&e.preventDefault()},[Y]),pointerGraceTimerRef:F,onPointerGraceIntentChange:r.useCallback(e=>{B.current=e},[]),children:(0,w.jsx)(z,{...V,children:(0,w.jsx)(p.n,{asChild:!0,trapped:l,onMountAutoFocus:(0,o.m)(u,e=>{var t;e.preventDefault(),null===(t=P.current)||void 0===t||t.focus({preventScroll:!0})}),onUnmountAutoFocus:c,children:(0,w.jsx)(d.qW,{asChild:!0,disableOutsidePointerEvents:s,onEscapeKeyDown:m,onPointerDownOutside:g,onFocusOutside:y,onInteractOutside:x,onDismiss:b,children:(0,w.jsx)(L,{asChild:!0,...M,dir:A.dir,orientation:"vertical",loop:a,currentTabStopId:T,onCurrentTabStopIdChange:j,onEntryFocus:(0,o.m)(h,e=>{A.isUsingKeyboardRef.current||e.preventDefault()}),preventScrollOnEntryFocus:!0,children:(0,w.jsx)(v.UC,{role:"menu","aria-orientation":"vertical","data-state":eW(R.open),"data-radix-menu-content":"",dir:A.dir,...k,...E,ref:D,style:{outline:"none",...E.style},onKeyDown:(0,o.m)(E.onKeyDown,e=>{let t=e.target.closest("[data-radix-menu-content]")===e.currentTarget,n=e.ctrlKey||e.altKey||e.metaKey,r=1===e.key.length;t&&("Tab"===e.key&&e.preventDefault(),!n&&r&&X(e.key));let o=P.current;if(e.target!==o||!K.includes(e.key))return;e.preventDefault();let i=S().filter(e=>!e.disabled).map(e=>e.ref.current);H.includes(e.key)&&i.reverse(),function(e){let t=document.activeElement;for(let n of e)if(n===t||(n.focus(),document.activeElement!==t))return}(i)}),onBlur:(0,o.m)(e.onBlur,e=>{e.currentTarget.contains(e.target)||(window.clearTimeout(N.current),I.current="")}),onPointerMove:(0,o.m)(e.onPointerMove,eV(e=>{let t=e.target,n=G.current!==e.clientX;e.currentTarget.contains(t)&&n&&(W.current=e.clientX>G.current?"right":"left",G.current=e.clientX)}))})})})})})})});ed.displayName=eu;var ev=r.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e;return(0,w.jsx)(u.sG.div,{role:"group",...r,ref:t})});ev.displayName="MenuGroup";var em=r.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e;return(0,w.jsx)(u.sG.div,{...r,ref:t})});em.displayName="MenuLabel";var eg="MenuItem",ey="menu.itemSelect",ew=r.forwardRef((e,t)=>{let{disabled:n=!1,onSelect:a,...l}=e,c=r.useRef(null),s=et(eg,e.__scopeMenu),d=es(eg,e.__scopeMenu),f=(0,i.s)(t,c),p=r.useRef(!1);return(0,w.jsx)(ex,{...l,ref:f,disabled:n,onClick:(0,o.m)(e.onClick,()=>{let e=c.current;if(!n&&e){let t=new CustomEvent(ey,{bubbles:!0,cancelable:!0});e.addEventListener(ey,e=>null==a?void 0:a(e),{once:!0}),(0,u.hO)(e,t),t.defaultPrevented?p.current=!1:s.onClose()}}),onPointerDown:t=>{var n;null===(n=e.onPointerDown)||void 0===n||n.call(e,t),p.current=!0},onPointerUp:(0,o.m)(e.onPointerUp,e=>{var t;p.current||null===(t=e.currentTarget)||void 0===t||t.click()}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{let t=""!==d.searchRef.current;!n&&(!t||" "!==e.key)&&B.includes(e.key)&&(e.currentTarget.click(),e.preventDefault())})})});ew.displayName=eg;var ex=r.forwardRef((e,t)=>{let{__scopeMenu:n,disabled:a=!1,textValue:l,...c}=e,s=es(eg,n),d=$(n),f=r.useRef(null),p=(0,i.s)(t,f),[h,v]=r.useState(!1),[m,g]=r.useState("");return r.useEffect(()=>{let e=f.current;if(e){var t;g((null!==(t=e.textContent)&&void 0!==t?t:"").trim())}},[c.children]),(0,w.jsx)(V.ItemSlot,{scope:n,disabled:a,textValue:null!=l?l:m,children:(0,w.jsx)(D,{asChild:!0,...d,focusable:!a,children:(0,w.jsx)(u.sG.div,{role:"menuitem","data-highlighted":h?"":void 0,"aria-disabled":a||void 0,"data-disabled":a?"":void 0,...c,ref:p,onPointerMove:(0,o.m)(e.onPointerMove,eV(e=>{a?s.onItemLeave(e):(s.onItemEnter(e),e.defaultPrevented||e.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:(0,o.m)(e.onPointerLeave,eV(e=>s.onItemLeave(e))),onFocus:(0,o.m)(e.onFocus,()=>v(!0)),onBlur:(0,o.m)(e.onBlur,()=>v(!1))})})})}),eb=r.forwardRef((e,t)=>{let{checked:n=!1,onCheckedChange:r,...i}=e;return(0,w.jsx)(eT,{scope:e.__scopeMenu,checked:n,children:(0,w.jsx)(ew,{role:"menuitemcheckbox","aria-checked":eG(n)?"mixed":n,...i,ref:t,"data-state":ez(n),onSelect:(0,o.m)(i.onSelect,()=>null==r?void 0:r(!!eG(n)||!n),{checkForDefaultPrevented:!1})})})});eb.displayName="MenuCheckboxItem";var eC="MenuRadioGroup",[eE,eR]=Y(eC,{value:void 0,onValueChange:()=>{}}),eA=r.forwardRef((e,t)=>{let{value:n,onValueChange:r,...o}=e,i=(0,y.c)(r);return(0,w.jsx)(eE,{scope:e.__scopeMenu,value:n,onValueChange:i,children:(0,w.jsx)(ev,{...o,ref:t})})});eA.displayName=eC;var ek="MenuRadioItem",eM=r.forwardRef((e,t)=>{let{value:n,...r}=e,i=eR(ek,e.__scopeMenu),a=n===i.value;return(0,w.jsx)(eT,{scope:e.__scopeMenu,checked:a,children:(0,w.jsx)(ew,{role:"menuitemradio","aria-checked":a,...r,ref:t,"data-state":ez(a),onSelect:(0,o.m)(r.onSelect,()=>{var e;return null===(e=i.onValueChange)||void 0===e?void 0:e.call(i,n)},{checkForDefaultPrevented:!1})})})});eM.displayName=ek;var eS="MenuItemIndicator",[eT,eL]=Y(eS,{checked:!1}),ej=r.forwardRef((e,t)=>{let{__scopeMenu:n,forceMount:r,...o}=e,i=eL(eS,n);return(0,w.jsx)(g.C,{present:r||eG(i.checked)||!0===i.checked,children:(0,w.jsx)(u.sG.span,{...o,ref:t,"data-state":ez(i.checked)})})});ej.displayName=eS;var eP=r.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e;return(0,w.jsx)(u.sG.div,{role:"separator","aria-orientation":"horizontal",...r,ref:t})});eP.displayName="MenuSeparator";var eD=r.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e,o=Z(n);return(0,w.jsx)(v.i3,{...o,...r,ref:t})});eD.displayName="MenuArrow";var eN="MenuSub",[eI,eO]=Y(eN),eF=e=>{let{__scopeMenu:t,children:n,open:o=!1,onOpenChange:i}=e,a=Q(eN,t),l=Z(t),[u,c]=r.useState(null),[s,d]=r.useState(null),f=(0,y.c)(i);return r.useEffect(()=>(!1===a.open&&f(!1),()=>f(!1)),[a.open,f]),(0,w.jsx)(v.bL,{...l,children:(0,w.jsx)(J,{scope:t,open:o,onOpenChange:f,content:s,onContentChange:d,children:(0,w.jsx)(eI,{scope:t,contentId:(0,h.B)(),triggerId:(0,h.B)(),trigger:u,onTriggerChange:c,children:n})})})};eF.displayName=eN;var e_="MenuSubTrigger",eB=r.forwardRef((e,t)=>{let n=Q(e_,e.__scopeMenu),a=et(e_,e.__scopeMenu),l=eO(e_,e.__scopeMenu),u=es(e_,e.__scopeMenu),c=r.useRef(null),{pointerGraceTimerRef:s,onPointerGraceIntentChange:d}=u,f={__scopeMenu:e.__scopeMenu},p=r.useCallback(()=>{c.current&&window.clearTimeout(c.current),c.current=null},[]);return r.useEffect(()=>p,[p]),r.useEffect(()=>{let e=s.current;return()=>{window.clearTimeout(e),d(null)}},[s,d]),(0,w.jsx)(er,{asChild:!0,...f,children:(0,w.jsx)(ex,{id:l.triggerId,"aria-haspopup":"menu","aria-expanded":n.open,"aria-controls":l.contentId,"data-state":eW(n.open),...e,ref:(0,i.t)(t,l.onTriggerChange),onClick:t=>{var r;null===(r=e.onClick)||void 0===r||r.call(e,t),e.disabled||t.defaultPrevented||(t.currentTarget.focus(),n.open||n.onOpenChange(!0))},onPointerMove:(0,o.m)(e.onPointerMove,eV(t=>{u.onItemEnter(t),t.defaultPrevented||e.disabled||n.open||c.current||(u.onPointerGraceIntentChange(null),c.current=window.setTimeout(()=>{n.onOpenChange(!0),p()},100))})),onPointerLeave:(0,o.m)(e.onPointerLeave,eV(e=>{var t,r;p();let o=null===(t=n.content)||void 0===t?void 0:t.getBoundingClientRect();if(o){let t=null===(r=n.content)||void 0===r?void 0:r.dataset.side,i="right"===t,a=o[i?"left":"right"],l=o[i?"right":"left"];u.onPointerGraceIntentChange({area:[{x:e.clientX+(i?-5:5),y:e.clientY},{x:a,y:o.top},{x:l,y:o.top},{x:l,y:o.bottom},{x:a,y:o.bottom}],side:t}),window.clearTimeout(s.current),s.current=window.setTimeout(()=>u.onPointerGraceIntentChange(null),300)}else{if(u.onTriggerLeave(e),e.defaultPrevented)return;u.onPointerGraceIntentChange(null)}})),onKeyDown:(0,o.m)(e.onKeyDown,t=>{let r=""!==u.searchRef.current;if(!e.disabled&&(!r||" "!==t.key)&&W[a.dir].includes(t.key)){var o;n.onOpenChange(!0),null===(o=n.content)||void 0===o||o.focus(),t.preventDefault()}})})})});eB.displayName=e_;var eH="MenuSubContent",eK=r.forwardRef((e,t)=>{let n=ea(eu,e.__scopeMenu),{forceMount:a=n.forceMount,...l}=e,u=Q(eu,e.__scopeMenu),c=et(eu,e.__scopeMenu),s=eO(eH,e.__scopeMenu),d=r.useRef(null),f=(0,i.s)(t,d);return(0,w.jsx)(V.Provider,{scope:e.__scopeMenu,children:(0,w.jsx)(g.C,{present:a||u.open,children:(0,w.jsx)(V.Slot,{scope:e.__scopeMenu,children:(0,w.jsx)(eh,{id:s.contentId,"aria-labelledby":s.triggerId,...l,ref:f,align:"start",side:"rtl"===c.dir?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:e=>{var t;c.isUsingKeyboardRef.current&&(null===(t=d.current)||void 0===t||t.focus()),e.preventDefault()},onCloseAutoFocus:e=>e.preventDefault(),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>{e.target!==s.trigger&&u.onOpenChange(!1)}),onEscapeKeyDown:(0,o.m)(e.onEscapeKeyDown,e=>{c.onClose(),e.preventDefault()}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{let t=e.currentTarget.contains(e.target),n=G[c.dir].includes(e.key);if(t&&n){var r;u.onOpenChange(!1),null===(r=s.trigger)||void 0===r||r.focus(),e.preventDefault()}})})})})})});function eW(e){return e?"open":"closed"}function eG(e){return"indeterminate"===e}function ez(e){return eG(e)?"indeterminate":e?"checked":"unchecked"}function eV(e){return t=>"mouse"===t.pointerType?e(t):void 0}eK.displayName=eH;var eU="DropdownMenu",[eX,eY]=(0,a.A)(eU,[q]),eq=q(),[eZ,e$]=eX(eU),eJ=e=>{let{__scopeDropdownMenu:t,children:n,dir:o,open:i,defaultOpen:a,onOpenChange:u,modal:c=!0}=e,s=eq(t),d=r.useRef(null),[f=!1,p]=(0,l.i)({prop:i,defaultProp:a,onChange:u});return(0,w.jsx)(eZ,{scope:t,triggerId:(0,h.B)(),triggerRef:d,contentId:(0,h.B)(),open:f,onOpenChange:p,onOpenToggle:r.useCallback(()=>p(e=>!e),[p]),modal:c,children:(0,w.jsx)(en,{...s,open:f,onOpenChange:p,dir:o,modal:c,children:n})})};eJ.displayName=eU;var eQ="DropdownMenuTrigger",e0=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,disabled:r=!1,...a}=e,l=e$(eQ,n),c=eq(n);return(0,w.jsx)(er,{asChild:!0,...c,children:(0,w.jsx)(u.sG.button,{type:"button",id:l.triggerId,"aria-haspopup":"menu","aria-expanded":l.open,"aria-controls":l.open?l.contentId:void 0,"data-state":l.open?"open":"closed","data-disabled":r?"":void 0,disabled:r,...a,ref:(0,i.t)(t,l.triggerRef),onPointerDown:(0,o.m)(e.onPointerDown,e=>{r||0!==e.button||!1!==e.ctrlKey||(l.onOpenToggle(),l.open||e.preventDefault())}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{!r&&(["Enter"," "].includes(e.key)&&l.onOpenToggle(),"ArrowDown"===e.key&&l.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(e.key)&&e.preventDefault())})})})});e0.displayName=eQ;var e1=e=>{let{__scopeDropdownMenu:t,...n}=e,r=eq(t);return(0,w.jsx)(el,{...r,...n})};e1.displayName="DropdownMenuPortal";var e2="DropdownMenuContent",e5=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...i}=e,a=e$(e2,n),l=eq(n),u=r.useRef(!1);return(0,w.jsx)(ed,{id:a.contentId,"aria-labelledby":a.triggerId,...l,...i,ref:t,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{var t;u.current||null===(t=a.triggerRef.current)||void 0===t||t.focus(),u.current=!1,e.preventDefault()}),onInteractOutside:(0,o.m)(e.onInteractOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey,r=2===t.button||n;(!a.modal||r)&&(u.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});e5.displayName=e2;var e4=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eq(n);return(0,w.jsx)(ev,{...o,...r,ref:t})});e4.displayName="DropdownMenuGroup";var e3=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eq(n);return(0,w.jsx)(em,{...o,...r,ref:t})});e3.displayName="DropdownMenuLabel";var e7=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eq(n);return(0,w.jsx)(ew,{...o,...r,ref:t})});e7.displayName="DropdownMenuItem";var e9=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eq(n);return(0,w.jsx)(eb,{...o,...r,ref:t})});e9.displayName="DropdownMenuCheckboxItem";var e6=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eq(n);return(0,w.jsx)(eA,{...o,...r,ref:t})});e6.displayName="DropdownMenuRadioGroup";var e8=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eq(n);return(0,w.jsx)(eM,{...o,...r,ref:t})});e8.displayName="DropdownMenuRadioItem";var te=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eq(n);return(0,w.jsx)(ej,{...o,...r,ref:t})});te.displayName="DropdownMenuItemIndicator";var tt=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eq(n);return(0,w.jsx)(eP,{...o,...r,ref:t})});tt.displayName="DropdownMenuSeparator",r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eq(n);return(0,w.jsx)(eD,{...o,...r,ref:t})}).displayName="DropdownMenuArrow";var tn=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eq(n);return(0,w.jsx)(eB,{...o,...r,ref:t})});tn.displayName="DropdownMenuSubTrigger";var tr=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eq(n);return(0,w.jsx)(eK,{...o,...r,ref:t,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});tr.displayName="DropdownMenuSubContent";var to=eJ,ti=e0,ta=e1,tl=e5,tu=e4,tc=e3,ts=e7,td=e9,tf=e6,tp=e8,th=te,tv=tt,tm=e=>{let{__scopeDropdownMenu:t,children:n,open:r,onOpenChange:o,defaultOpen:i}=e,a=eq(t),[u=!1,c]=(0,l.i)({prop:r,defaultProp:i,onChange:o});return(0,w.jsx)(eF,{...a,open:u,onOpenChange:c,children:n})},tg=tn,ty=tr},10518:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(40157).A)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},13896:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(40157).A)("UserPlus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]])},17104:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(40157).A)("LogIn",[["path",{d:"M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4",key:"u53s6r"}],["polyline",{points:"10 17 15 12 10 7",key:"1ail0h"}],["line",{x1:"15",x2:"3",y1:"12",y2:"12",key:"v6grx8"}]])},17607:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(40157).A)("Eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},18271:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(40157).A)("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},25519:(e,t,n)=>{n.d(t,{n:()=>d});var r=n(12115),o=n(6101),i=n(63655),a=n(39033),l=n(95155),u="focusScope.autoFocusOnMount",c="focusScope.autoFocusOnUnmount",s={bubbles:!1,cancelable:!0},d=r.forwardRef((e,t)=>{let{loop:n=!1,trapped:d=!1,onMountAutoFocus:m,onUnmountAutoFocus:g,...y}=e,[w,x]=r.useState(null),b=(0,a.c)(m),C=(0,a.c)(g),E=r.useRef(null),R=(0,o.s)(t,e=>x(e)),A=r.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;r.useEffect(()=>{if(d){let e=function(e){if(A.paused||!w)return;let t=e.target;w.contains(t)?E.current=t:h(E.current,{select:!0})},t=function(e){if(A.paused||!w)return;let t=e.relatedTarget;null===t||w.contains(t)||h(E.current,{select:!0})};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&h(w)});return w&&n.observe(w,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[d,w,A.paused]),r.useEffect(()=>{if(w){v.add(A);let e=document.activeElement;if(!w.contains(e)){let t=new CustomEvent(u,s);w.addEventListener(u,b),w.dispatchEvent(t),t.defaultPrevented||(function(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=document.activeElement;for(let r of e)if(h(r,{select:t}),document.activeElement!==n)return}(f(w).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&h(w))}return()=>{w.removeEventListener(u,b),setTimeout(()=>{let t=new CustomEvent(c,s);w.addEventListener(c,C),w.dispatchEvent(t),t.defaultPrevented||h(null!=e?e:document.body,{select:!0}),w.removeEventListener(c,C),v.remove(A)},0)}}},[w,b,C,A]);let k=r.useCallback(e=>{if(!n&&!d||A.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,r=document.activeElement;if(t&&r){let t=e.currentTarget,[o,i]=function(e){let t=f(e);return[p(t,e),p(t.reverse(),e)]}(t);o&&i?e.shiftKey||r!==i?e.shiftKey&&r===o&&(e.preventDefault(),n&&h(i,{select:!0})):(e.preventDefault(),n&&h(o,{select:!0})):r===t&&e.preventDefault()}},[n,d,A.paused]);return(0,l.jsx)(i.sG.div,{tabIndex:-1,...y,ref:R,onKeyDown:k})});function f(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function p(e,t){for(let n of e)if(!function(e,t){let{upTo:n}=t;if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===n||e!==n);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function h(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}d.displayName="FocusScope";var v=function(){let e=[];return{add(t){let n=e[0];t!==n&&(null==n||n.pause()),(e=m(e,t)).unshift(t)},remove(t){var n;null===(n=(e=m(e,t))[0])||void 0===n||n.resume()}}}();function m(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}},31114:(e,t,n)=>{n.d(t,{A:()=>z});var r,o=n(39249),i=n(12115),a="right-scroll-bar-position",l="width-before-scroll-bar";function u(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var c="undefined"!=typeof window?i.useLayoutEffect:i.useEffect,s=new WeakMap;function d(e){return e}var f=function(e){void 0===e&&(e={});var t,n,r,i,a=(t=null,void 0===n&&(n=d),r=[],i=!1,{read:function(){if(i)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return r.length?r[r.length-1]:null},useMedium:function(e){var t=n(e,i);return r.push(t),function(){r=r.filter(function(e){return e!==t})}},assignSyncMedium:function(e){for(i=!0;r.length;){var t=r;r=[],t.forEach(e)}r={push:function(t){return e(t)},filter:function(){return r}}},assignMedium:function(e){i=!0;var t=[];if(r.length){var n=r;r=[],n.forEach(e),t=r}var o=function(){var n=t;t=[],n.forEach(e)},a=function(){return Promise.resolve().then(o)};a(),r={push:function(e){t.push(e),a()},filter:function(e){return t=t.filter(e),r}}}});return a.options=(0,o.Cl)({async:!0,ssr:!1},e),a}(),p=function(){},h=i.forwardRef(function(e,t){var n,r,a,l,d=i.useRef(null),h=i.useState({onScrollCapture:p,onWheelCapture:p,onTouchMoveCapture:p}),v=h[0],m=h[1],g=e.forwardProps,y=e.children,w=e.className,x=e.removeScrollBar,b=e.enabled,C=e.shards,E=e.sideCar,R=e.noIsolation,A=e.inert,k=e.allowPinchZoom,M=e.as,S=e.gapMode,T=(0,o.Tt)(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noIsolation","inert","allowPinchZoom","as","gapMode"]),L=(n=[d,t],r=function(e){return n.forEach(function(t){return u(t,e)})},(a=(0,i.useState)(function(){return{value:null,callback:r,facade:{get current(){return a.value},set current(value){var e=a.value;e!==value&&(a.value=value,a.callback(value,e))}}}})[0]).callback=r,l=a.facade,c(function(){var e=s.get(l);if(e){var t=new Set(e),r=new Set(n),o=l.current;t.forEach(function(e){r.has(e)||u(e,null)}),r.forEach(function(e){t.has(e)||u(e,o)})}s.set(l,n)},[n]),l),j=(0,o.Cl)((0,o.Cl)({},T),v);return i.createElement(i.Fragment,null,b&&i.createElement(E,{sideCar:f,removeScrollBar:x,shards:C,noIsolation:R,inert:A,setCallbacks:m,allowPinchZoom:!!k,lockRef:d,gapMode:S}),g?i.cloneElement(i.Children.only(y),(0,o.Cl)((0,o.Cl)({},j),{ref:L})):i.createElement(void 0===M?"div":M,(0,o.Cl)({},j,{className:w,ref:L}),y))});h.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},h.classNames={fullWidth:l,zeroRight:a};var v=function(e){var t=e.sideCar,n=(0,o.Tt)(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw Error("Sidecar medium not found");return i.createElement(r,(0,o.Cl)({},n))};v.isSideCarExport=!0;var m=function(){var e=0,t=null;return{add:function(o){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=r||n.nc;return t&&e.setAttribute("nonce",t),e}())){var i,a;(i=t).styleSheet?i.styleSheet.cssText=o:i.appendChild(document.createTextNode(o)),a=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(a)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},g=function(){var e=m();return function(t,n){i.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},y=function(){var e=g();return function(t){return e(t.styles,t.dynamic),null}},w={left:0,top:0,right:0,gap:0},x=function(e){return parseInt(e||"",10)||0},b=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],r=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[x(n),x(r),x(o)]},C=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return w;var t=b(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},E=y(),R="data-scroll-locked",A=function(e,t,n,r){var o=e.left,i=e.top,u=e.right,c=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(c,"px ").concat(r,";\n  }\n  body[").concat(R,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(i,"px;\n    padding-right: ").concat(u,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(c,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(c,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(a," {\n    right: ").concat(c,"px ").concat(r,";\n  }\n  \n  .").concat(l," {\n    margin-right: ").concat(c,"px ").concat(r,";\n  }\n  \n  .").concat(a," .").concat(a," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(l," .").concat(l," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(R,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(c,"px;\n  }\n")},k=function(){var e=parseInt(document.body.getAttribute(R)||"0",10);return isFinite(e)?e:0},M=function(){i.useEffect(function(){return document.body.setAttribute(R,(k()+1).toString()),function(){var e=k()-1;e<=0?document.body.removeAttribute(R):document.body.setAttribute(R,e.toString())}},[])},S=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=void 0===r?"margin":r;M();var a=i.useMemo(function(){return C(o)},[o]);return i.createElement(E,{styles:A(a,!t,o,n?"":"!important")})},T=!1;if("undefined"!=typeof window)try{var L=Object.defineProperty({},"passive",{get:function(){return T=!0,!0}});window.addEventListener("test",L,L),window.removeEventListener("test",L,L)}catch(e){T=!1}var j=!!T&&{passive:!1},P=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&(n.overflowY!==n.overflowX||"TEXTAREA"===e.tagName||"visible"!==n[t])},D=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),N(e,r)){var o=I(e,r);if(o[1]>o[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},N=function(e,t){return"v"===e?P(t,"overflowY"):P(t,"overflowX")},I=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},O=function(e,t,n,r,o){var i,a=(i=window.getComputedStyle(t).direction,"h"===e&&"rtl"===i?-1:1),l=a*r,u=n.target,c=t.contains(u),s=!1,d=l>0,f=0,p=0;do{var h=I(e,u),v=h[0],m=h[1]-h[2]-a*v;(v||m)&&N(e,u)&&(f+=m,p+=v),u=u instanceof ShadowRoot?u.host:u.parentNode}while(!c&&u!==document.body||c&&(t.contains(u)||t===u));return d&&(o&&1>Math.abs(f)||!o&&l>f)?s=!0:!d&&(o&&1>Math.abs(p)||!o&&-l>p)&&(s=!0),s},F=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},_=function(e){return[e.deltaX,e.deltaY]},B=function(e){return e&&"current"in e?e.current:e},H=0,K=[];let W=(f.useMedium(function(e){var t=i.useRef([]),n=i.useRef([0,0]),r=i.useRef(),a=i.useState(H++)[0],l=i.useState(y)[0],u=i.useRef(e);i.useEffect(function(){u.current=e},[e]),i.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(a));var t=(0,o.fX)([e.lockRef.current],(e.shards||[]).map(B),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(a))}),function(){document.body.classList.remove("block-interactivity-".concat(a)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(a))})}}},[e.inert,e.lockRef.current,e.shards]);var c=i.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!u.current.allowPinchZoom;var o,i=F(e),a=n.current,l="deltaX"in e?e.deltaX:a[0]-i[0],c="deltaY"in e?e.deltaY:a[1]-i[1],s=e.target,d=Math.abs(l)>Math.abs(c)?"h":"v";if("touches"in e&&"h"===d&&"range"===s.type)return!1;var f=D(d,s);if(!f)return!0;if(f?o=d:(o="v"===d?"h":"v",f=D(d,s)),!f)return!1;if(!r.current&&"changedTouches"in e&&(l||c)&&(r.current=o),!o)return!0;var p=r.current||o;return O(p,t,e,"h"===p?l:c,!0)},[]),s=i.useCallback(function(e){if(K.length&&K[K.length-1]===l){var n="deltaY"in e?_(e):F(e),r=t.current.filter(function(t){var r;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(r=t.delta)[0]===n[0]&&r[1]===n[1]})[0];if(r&&r.should){e.cancelable&&e.preventDefault();return}if(!r){var o=(u.current.shards||[]).map(B).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?c(e,o[0]):!u.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),d=i.useCallback(function(e,n,r,o){var i={name:e,delta:n,target:r,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(r)};t.current.push(i),setTimeout(function(){t.current=t.current.filter(function(e){return e!==i})},1)},[]),f=i.useCallback(function(e){n.current=F(e),r.current=void 0},[]),p=i.useCallback(function(t){d(t.type,_(t),t.target,c(t,e.lockRef.current))},[]),h=i.useCallback(function(t){d(t.type,F(t),t.target,c(t,e.lockRef.current))},[]);i.useEffect(function(){return K.push(l),e.setCallbacks({onScrollCapture:p,onWheelCapture:p,onTouchMoveCapture:h}),document.addEventListener("wheel",s,j),document.addEventListener("touchmove",s,j),document.addEventListener("touchstart",f,j),function(){K=K.filter(function(e){return e!==l}),document.removeEventListener("wheel",s,j),document.removeEventListener("touchmove",s,j),document.removeEventListener("touchstart",f,j)}},[]);var v=e.removeScrollBar,m=e.inert;return i.createElement(i.Fragment,null,m?i.createElement(l,{styles:"\n  .block-interactivity-".concat(a," {pointer-events: none;}\n  .allow-interactivity-").concat(a," {pointer-events: all;}\n")}):null,v?i.createElement(S,{gapMode:e.gapMode}):null)}),v);var G=i.forwardRef(function(e,t){return i.createElement(h,(0,o.Cl)({},e,{ref:t,sideCar:W}))});G.classNames=h.classNames;let z=G},33349:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(40157).A)("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]])},38168:(e,t,n)=>{n.d(t,{Eq:()=>s});var r=function(e){return"undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body},o=new WeakMap,i=new WeakMap,a={},l=0,u=function(e){return e&&(e.host||u(e.parentNode))},c=function(e,t,n,r){var c=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var n=u(e);return n&&t.contains(n)?n:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});a[n]||(a[n]=new WeakMap);var s=a[n],d=[],f=new Set,p=new Set(c),h=function(e){!(!e||f.has(e))&&(f.add(e),h(e.parentNode))};c.forEach(h);var v=function(e){!(!e||p.has(e))&&Array.prototype.forEach.call(e.children,function(e){if(f.has(e))v(e);else try{var t=e.getAttribute(r),a=null!==t&&"false"!==t,l=(o.get(e)||0)+1,u=(s.get(e)||0)+1;o.set(e,l),s.set(e,u),d.push(e),1===l&&a&&i.set(e,!0),1===u&&e.setAttribute(n,"true"),a||e.setAttribute(r,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return v(t),f.clear(),l++,function(){d.forEach(function(e){var t=o.get(e)-1,a=s.get(e)-1;o.set(e,t),s.set(e,a),t||(i.has(e)||e.removeAttribute(r),i.delete(e)),a||e.removeAttribute(n)}),--l||(o=new WeakMap,o=new WeakMap,i=new WeakMap,a={})}},s=function(e,t,n){void 0===n&&(n="data-aria-hidden");var o=Array.from(Array.isArray(e)?e:[e]),i=t||r(e);return i?(o.push.apply(o,Array.from(i.querySelectorAll("[aria-live]"))),c(o,i,n,"aria-hidden")):function(){return null}}},38795:(e,t,n)=>{n.d(t,{Mz:()=>eq,i3:()=>e$,UC:()=>eZ,bL:()=>eY,Bk:()=>eP});var r=n(12115);let o=["top","right","bottom","left"],i=Math.min,a=Math.max,l=Math.round,u=Math.floor,c=e=>({x:e,y:e}),s={left:"right",right:"left",bottom:"top",top:"bottom"},d={start:"end",end:"start"};function f(e,t){return"function"==typeof e?e(t):e}function p(e){return e.split("-")[0]}function h(e){return e.split("-")[1]}function v(e){return"x"===e?"y":"x"}function m(e){return"y"===e?"height":"width"}function g(e){return["top","bottom"].includes(p(e))?"y":"x"}function y(e){return e.replace(/start|end/g,e=>d[e])}function w(e){return e.replace(/left|right|bottom|top/g,e=>s[e])}function x(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function b(e){let{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function C(e,t,n){let r,{reference:o,floating:i}=e,a=g(t),l=v(g(t)),u=m(l),c=p(t),s="y"===a,d=o.x+o.width/2-i.width/2,f=o.y+o.height/2-i.height/2,y=o[u]/2-i[u]/2;switch(c){case"top":r={x:d,y:o.y-i.height};break;case"bottom":r={x:d,y:o.y+o.height};break;case"right":r={x:o.x+o.width,y:f};break;case"left":r={x:o.x-i.width,y:f};break;default:r={x:o.x,y:o.y}}switch(h(t)){case"start":r[l]-=y*(n&&s?-1:1);break;case"end":r[l]+=y*(n&&s?-1:1)}return r}let E=async(e,t,n)=>{let{placement:r="bottom",strategy:o="absolute",middleware:i=[],platform:a}=n,l=i.filter(Boolean),u=await (null==a.isRTL?void 0:a.isRTL(t)),c=await a.getElementRects({reference:e,floating:t,strategy:o}),{x:s,y:d}=C(c,r,u),f=r,p={},h=0;for(let n=0;n<l.length;n++){let{name:i,fn:v}=l[n],{x:m,y:g,data:y,reset:w}=await v({x:s,y:d,initialPlacement:r,placement:f,strategy:o,middlewareData:p,rects:c,platform:a,elements:{reference:e,floating:t}});s=null!=m?m:s,d=null!=g?g:d,p={...p,[i]:{...p[i],...y}},w&&h<=50&&(h++,"object"==typeof w&&(w.placement&&(f=w.placement),w.rects&&(c=!0===w.rects?await a.getElementRects({reference:e,floating:t,strategy:o}):w.rects),{x:s,y:d}=C(c,f,u)),n=-1)}return{x:s,y:d,placement:f,strategy:o,middlewareData:p}};async function R(e,t){var n;void 0===t&&(t={});let{x:r,y:o,platform:i,rects:a,elements:l,strategy:u}=e,{boundary:c="clippingAncestors",rootBoundary:s="viewport",elementContext:d="floating",altBoundary:p=!1,padding:h=0}=f(t,e),v=x(h),m=l[p?"floating"===d?"reference":"floating":d],g=b(await i.getClippingRect({element:null==(n=await (null==i.isElement?void 0:i.isElement(m)))||n?m:m.contextElement||await (null==i.getDocumentElement?void 0:i.getDocumentElement(l.floating)),boundary:c,rootBoundary:s,strategy:u})),y="floating"===d?{x:r,y:o,width:a.floating.width,height:a.floating.height}:a.reference,w=await (null==i.getOffsetParent?void 0:i.getOffsetParent(l.floating)),C=await (null==i.isElement?void 0:i.isElement(w))&&await (null==i.getScale?void 0:i.getScale(w))||{x:1,y:1},E=b(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:l,rect:y,offsetParent:w,strategy:u}):y);return{top:(g.top-E.top+v.top)/C.y,bottom:(E.bottom-g.bottom+v.bottom)/C.y,left:(g.left-E.left+v.left)/C.x,right:(E.right-g.right+v.right)/C.x}}function A(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function k(e){return o.some(t=>e[t]>=0)}async function M(e,t){let{placement:n,platform:r,elements:o}=e,i=await (null==r.isRTL?void 0:r.isRTL(o.floating)),a=p(n),l=h(n),u="y"===g(n),c=["left","top"].includes(a)?-1:1,s=i&&u?-1:1,d=f(t,e),{mainAxis:v,crossAxis:m,alignmentAxis:y}="number"==typeof d?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:d.mainAxis||0,crossAxis:d.crossAxis||0,alignmentAxis:d.alignmentAxis};return l&&"number"==typeof y&&(m="end"===l?-1*y:y),u?{x:m*s,y:v*c}:{x:v*c,y:m*s}}function S(){return"undefined"!=typeof window}function T(e){return P(e)?(e.nodeName||"").toLowerCase():"#document"}function L(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function j(e){var t;return null==(t=(P(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function P(e){return!!S()&&(e instanceof Node||e instanceof L(e).Node)}function D(e){return!!S()&&(e instanceof Element||e instanceof L(e).Element)}function N(e){return!!S()&&(e instanceof HTMLElement||e instanceof L(e).HTMLElement)}function I(e){return!!S()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof L(e).ShadowRoot)}function O(e){let{overflow:t,overflowX:n,overflowY:r,display:o}=K(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(o)}function F(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch(e){return!1}})}function _(e){let t=B(),n=D(e)?K(e):e;return["transform","translate","scale","rotate","perspective"].some(e=>!!n[e]&&"none"!==n[e])||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||["transform","translate","scale","rotate","perspective","filter"].some(e=>(n.willChange||"").includes(e))||["paint","layout","strict","content"].some(e=>(n.contain||"").includes(e))}function B(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}function H(e){return["html","body","#document"].includes(T(e))}function K(e){return L(e).getComputedStyle(e)}function W(e){return D(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function G(e){if("html"===T(e))return e;let t=e.assignedSlot||e.parentNode||I(e)&&e.host||j(e);return I(t)?t.host:t}function z(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);let o=function e(t){let n=G(t);return H(n)?t.ownerDocument?t.ownerDocument.body:t.body:N(n)&&O(n)?n:e(n)}(e),i=o===(null==(r=e.ownerDocument)?void 0:r.body),a=L(o);if(i){let e=V(a);return t.concat(a,a.visualViewport||[],O(o)?o:[],e&&n?z(e):[])}return t.concat(o,z(o,[],n))}function V(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function U(e){let t=K(e),n=parseFloat(t.width)||0,r=parseFloat(t.height)||0,o=N(e),i=o?e.offsetWidth:n,a=o?e.offsetHeight:r,u=l(n)!==i||l(r)!==a;return u&&(n=i,r=a),{width:n,height:r,$:u}}function X(e){return D(e)?e:e.contextElement}function Y(e){let t=X(e);if(!N(t))return c(1);let n=t.getBoundingClientRect(),{width:r,height:o,$:i}=U(t),a=(i?l(n.width):n.width)/r,u=(i?l(n.height):n.height)/o;return a&&Number.isFinite(a)||(a=1),u&&Number.isFinite(u)||(u=1),{x:a,y:u}}let q=c(0);function Z(e){let t=L(e);return B()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:q}function $(e,t,n,r){var o;void 0===t&&(t=!1),void 0===n&&(n=!1);let i=e.getBoundingClientRect(),a=X(e),l=c(1);t&&(r?D(r)&&(l=Y(r)):l=Y(e));let u=(void 0===(o=n)&&(o=!1),r&&(!o||r===L(a))&&o)?Z(a):c(0),s=(i.left+u.x)/l.x,d=(i.top+u.y)/l.y,f=i.width/l.x,p=i.height/l.y;if(a){let e=L(a),t=r&&D(r)?L(r):r,n=e,o=V(n);for(;o&&r&&t!==n;){let e=Y(o),t=o.getBoundingClientRect(),r=K(o),i=t.left+(o.clientLeft+parseFloat(r.paddingLeft))*e.x,a=t.top+(o.clientTop+parseFloat(r.paddingTop))*e.y;s*=e.x,d*=e.y,f*=e.x,p*=e.y,s+=i,d+=a,o=V(n=L(o))}}return b({width:f,height:p,x:s,y:d})}function J(e,t){let n=W(e).scrollLeft;return t?t.left+n:$(j(e)).left+n}function Q(e,t,n){void 0===n&&(n=!1);let r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:J(e,r)),y:r.top+t.scrollTop}}function ee(e,t,n){let r;if("viewport"===t)r=function(e,t){let n=L(e),r=j(e),o=n.visualViewport,i=r.clientWidth,a=r.clientHeight,l=0,u=0;if(o){i=o.width,a=o.height;let e=B();(!e||e&&"fixed"===t)&&(l=o.offsetLeft,u=o.offsetTop)}return{width:i,height:a,x:l,y:u}}(e,n);else if("document"===t)r=function(e){let t=j(e),n=W(e),r=e.ownerDocument.body,o=a(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),i=a(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight),l=-n.scrollLeft+J(e),u=-n.scrollTop;return"rtl"===K(r).direction&&(l+=a(t.clientWidth,r.clientWidth)-o),{width:o,height:i,x:l,y:u}}(j(e));else if(D(t))r=function(e,t){let n=$(e,!0,"fixed"===t),r=n.top+e.clientTop,o=n.left+e.clientLeft,i=N(e)?Y(e):c(1),a=e.clientWidth*i.x,l=e.clientHeight*i.y;return{width:a,height:l,x:o*i.x,y:r*i.y}}(t,n);else{let n=Z(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return b(r)}function et(e){return"static"===K(e).position}function en(e,t){if(!N(e)||"fixed"===K(e).position)return null;if(t)return t(e);let n=e.offsetParent;return j(e)===n&&(n=n.ownerDocument.body),n}function er(e,t){let n=L(e);if(F(e))return n;if(!N(e)){let t=G(e);for(;t&&!H(t);){if(D(t)&&!et(t))return t;t=G(t)}return n}let r=en(e,t);for(;r&&["table","td","th"].includes(T(r))&&et(r);)r=en(r,t);return r&&H(r)&&et(r)&&!_(r)?n:r||function(e){let t=G(e);for(;N(t)&&!H(t);){if(_(t))return t;if(F(t))break;t=G(t)}return null}(e)||n}let eo=async function(e){let t=this.getOffsetParent||er,n=this.getDimensions,r=await n(e.floating);return{reference:function(e,t,n){let r=N(t),o=j(t),i="fixed"===n,a=$(e,!0,i,t),l={scrollLeft:0,scrollTop:0},u=c(0);if(r||!r&&!i){if(("body"!==T(t)||O(o))&&(l=W(t)),r){let e=$(t,!0,i,t);u.x=e.x+t.clientLeft,u.y=e.y+t.clientTop}else o&&(u.x=J(o))}let s=!o||r||i?c(0):Q(o,l);return{x:a.left+l.scrollLeft-u.x-s.x,y:a.top+l.scrollTop-u.y-s.y,width:a.width,height:a.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},ei={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e,i="fixed"===o,a=j(r),l=!!t&&F(t.floating);if(r===a||l&&i)return n;let u={scrollLeft:0,scrollTop:0},s=c(1),d=c(0),f=N(r);if((f||!f&&!i)&&(("body"!==T(r)||O(a))&&(u=W(r)),N(r))){let e=$(r);s=Y(r),d.x=e.x+r.clientLeft,d.y=e.y+r.clientTop}let p=!a||f||i?c(0):Q(a,u,!0);return{width:n.width*s.x,height:n.height*s.y,x:n.x*s.x-u.scrollLeft*s.x+d.x+p.x,y:n.y*s.y-u.scrollTop*s.y+d.y+p.y}},getDocumentElement:j,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e,l=[..."clippingAncestors"===n?F(t)?[]:function(e,t){let n=t.get(e);if(n)return n;let r=z(e,[],!1).filter(e=>D(e)&&"body"!==T(e)),o=null,i="fixed"===K(e).position,a=i?G(e):e;for(;D(a)&&!H(a);){let t=K(a),n=_(a);n||"fixed"!==t.position||(o=null),(i?!n&&!o:!n&&"static"===t.position&&!!o&&["absolute","fixed"].includes(o.position)||O(a)&&!n&&function e(t,n){let r=G(t);return!(r===n||!D(r)||H(r))&&("fixed"===K(r).position||e(r,n))}(e,a))?r=r.filter(e=>e!==a):o=t,a=G(a)}return t.set(e,r),r}(t,this._c):[].concat(n),r],u=l[0],c=l.reduce((e,n)=>{let r=ee(t,n,o);return e.top=a(r.top,e.top),e.right=i(r.right,e.right),e.bottom=i(r.bottom,e.bottom),e.left=a(r.left,e.left),e},ee(t,u,o));return{width:c.right-c.left,height:c.bottom-c.top,x:c.left,y:c.top}},getOffsetParent:er,getElementRects:eo,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:n}=U(e);return{width:t,height:n}},getScale:Y,isElement:D,isRTL:function(e){return"rtl"===K(e).direction}};function ea(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let el=e=>({name:"arrow",options:e,async fn(t){let{x:n,y:r,placement:o,rects:l,platform:u,elements:c,middlewareData:s}=t,{element:d,padding:p=0}=f(e,t)||{};if(null==d)return{};let y=x(p),w={x:n,y:r},b=v(g(o)),C=m(b),E=await u.getDimensions(d),R="y"===b,A=R?"clientHeight":"clientWidth",k=l.reference[C]+l.reference[b]-w[b]-l.floating[C],M=w[b]-l.reference[b],S=await (null==u.getOffsetParent?void 0:u.getOffsetParent(d)),T=S?S[A]:0;T&&await (null==u.isElement?void 0:u.isElement(S))||(T=c.floating[A]||l.floating[C]);let L=T/2-E[C]/2-1,j=i(y[R?"top":"left"],L),P=i(y[R?"bottom":"right"],L),D=T-E[C]-P,N=T/2-E[C]/2+(k/2-M/2),I=a(j,i(N,D)),O=!s.arrow&&null!=h(o)&&N!==I&&l.reference[C]/2-(N<j?j:P)-E[C]/2<0,F=O?N<j?N-j:N-D:0;return{[b]:w[b]+F,data:{[b]:I,centerOffset:N-I-F,...O&&{alignmentOffset:F}},reset:O}}}),eu=(e,t,n)=>{let r=new Map,o={platform:ei,...n},i={...o.platform,_c:r};return E(e,t,{...o,platform:i})};var ec=n(47650),es="undefined"!=typeof document?r.useLayoutEffect:r.useEffect;function ed(e,t){let n,r,o;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((n=e.length)!==t.length)return!1;for(r=n;0!=r--;)if(!ed(e[r],t[r]))return!1;return!0}if((n=(o=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(t,o[r]))return!1;for(r=n;0!=r--;){let n=o[r];if(("_owner"!==n||!e.$$typeof)&&!ed(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function ef(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function ep(e,t){let n=ef(e);return Math.round(t*n)/n}function eh(e){let t=r.useRef(e);return es(()=>{t.current=e}),t}let ev=e=>({name:"arrow",options:e,fn(t){let{element:n,padding:r}="function"==typeof e?e(t):e;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?el({element:n.current,padding:r}).fn(t):{}:n?el({element:n,padding:r}).fn(t):{}}}),em=(e,t)=>({...function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,r;let{x:o,y:i,placement:a,middlewareData:l}=t,u=await M(t,e);return a===(null==(n=l.offset)?void 0:n.placement)&&null!=(r=l.arrow)&&r.alignmentOffset?{}:{x:o+u.x,y:i+u.y,data:{...u,placement:a}}}}}(e),options:[e,t]}),eg=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:n,y:r,placement:o}=t,{mainAxis:l=!0,crossAxis:u=!1,limiter:c={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...s}=f(e,t),d={x:n,y:r},h=await R(t,s),m=g(p(o)),y=v(m),w=d[y],x=d[m];if(l){let e="y"===y?"top":"left",t="y"===y?"bottom":"right",n=w+h[e],r=w-h[t];w=a(n,i(w,r))}if(u){let e="y"===m?"top":"left",t="y"===m?"bottom":"right",n=x+h[e],r=x-h[t];x=a(n,i(x,r))}let b=c.fn({...t,[y]:w,[m]:x});return{...b,data:{x:b.x-n,y:b.y-r,enabled:{[y]:l,[m]:u}}}}}}(e),options:[e,t]}),ey=(e,t)=>({...function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:n,y:r,placement:o,rects:i,middlewareData:a}=t,{offset:l=0,mainAxis:u=!0,crossAxis:c=!0}=f(e,t),s={x:n,y:r},d=g(o),h=v(d),m=s[h],y=s[d],w=f(l,t),x="number"==typeof w?{mainAxis:w,crossAxis:0}:{mainAxis:0,crossAxis:0,...w};if(u){let e="y"===h?"height":"width",t=i.reference[h]-i.floating[e]+x.mainAxis,n=i.reference[h]+i.reference[e]-x.mainAxis;m<t?m=t:m>n&&(m=n)}if(c){var b,C;let e="y"===h?"width":"height",t=["top","left"].includes(p(o)),n=i.reference[d]-i.floating[e]+(t&&(null==(b=a.offset)?void 0:b[d])||0)+(t?0:x.crossAxis),r=i.reference[d]+i.reference[e]+(t?0:(null==(C=a.offset)?void 0:C[d])||0)-(t?x.crossAxis:0);y<n?y=n:y>r&&(y=r)}return{[h]:m,[d]:y}}}}(e),options:[e,t]}),ew=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,r,o,i,a;let{placement:l,middlewareData:u,rects:c,initialPlacement:s,platform:d,elements:x}=t,{mainAxis:b=!0,crossAxis:C=!0,fallbackPlacements:E,fallbackStrategy:A="bestFit",fallbackAxisSideDirection:k="none",flipAlignment:M=!0,...S}=f(e,t);if(null!=(n=u.arrow)&&n.alignmentOffset)return{};let T=p(l),L=g(s),j=p(s)===s,P=await (null==d.isRTL?void 0:d.isRTL(x.floating)),D=E||(j||!M?[w(s)]:function(e){let t=w(e);return[y(e),t,y(t)]}(s)),N="none"!==k;!E&&N&&D.push(...function(e,t,n,r){let o=h(e),i=function(e,t,n){let r=["left","right"],o=["right","left"];switch(e){case"top":case"bottom":if(n)return t?o:r;return t?r:o;case"left":case"right":return t?["top","bottom"]:["bottom","top"];default:return[]}}(p(e),"start"===n,r);return o&&(i=i.map(e=>e+"-"+o),t&&(i=i.concat(i.map(y)))),i}(s,M,k,P));let I=[s,...D],O=await R(t,S),F=[],_=(null==(r=u.flip)?void 0:r.overflows)||[];if(b&&F.push(O[T]),C){let e=function(e,t,n){void 0===n&&(n=!1);let r=h(e),o=v(g(e)),i=m(o),a="x"===o?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[i]>t.floating[i]&&(a=w(a)),[a,w(a)]}(l,c,P);F.push(O[e[0]],O[e[1]])}if(_=[..._,{placement:l,overflows:F}],!F.every(e=>e<=0)){let e=((null==(o=u.flip)?void 0:o.index)||0)+1,t=I[e];if(t)return{data:{index:e,overflows:_},reset:{placement:t}};let n=null==(i=_.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:i.placement;if(!n)switch(A){case"bestFit":{let e=null==(a=_.filter(e=>{if(N){let t=g(e.placement);return t===L||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:a[0];e&&(n=e);break}case"initialPlacement":n=s}if(l!==n)return{reset:{placement:n}}}return{}}}}(e),options:[e,t]}),ex=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var n,r;let o,l;let{placement:u,rects:c,platform:s,elements:d}=t,{apply:v=()=>{},...m}=f(e,t),y=await R(t,m),w=p(u),x=h(u),b="y"===g(u),{width:C,height:E}=c.floating;"top"===w||"bottom"===w?(o=w,l=x===(await (null==s.isRTL?void 0:s.isRTL(d.floating))?"start":"end")?"left":"right"):(l=w,o="end"===x?"top":"bottom");let A=E-y.top-y.bottom,k=C-y.left-y.right,M=i(E-y[o],A),S=i(C-y[l],k),T=!t.middlewareData.shift,L=M,j=S;if(null!=(n=t.middlewareData.shift)&&n.enabled.x&&(j=k),null!=(r=t.middlewareData.shift)&&r.enabled.y&&(L=A),T&&!x){let e=a(y.left,0),t=a(y.right,0),n=a(y.top,0),r=a(y.bottom,0);b?j=C-2*(0!==e||0!==t?e+t:a(y.left,y.right)):L=E-2*(0!==n||0!==r?n+r:a(y.top,y.bottom))}await v({...t,availableWidth:j,availableHeight:L});let P=await s.getDimensions(d.floating);return C!==P.width||E!==P.height?{reset:{rects:!0}}:{}}}}(e),options:[e,t]}),eb=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:n}=t,{strategy:r="referenceHidden",...o}=f(e,t);switch(r){case"referenceHidden":{let e=A(await R(t,{...o,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:k(e)}}}case"escaped":{let e=A(await R(t,{...o,altBoundary:!0}),n.floating);return{data:{escapedOffsets:e,escaped:k(e)}}}default:return{}}}}}(e),options:[e,t]}),eC=(e,t)=>({...ev(e),options:[e,t]});var eE=n(63655),eR=n(95155),eA=r.forwardRef((e,t)=>{let{children:n,width:r=10,height:o=5,...i}=e;return(0,eR.jsx)(eE.sG.svg,{...i,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,eR.jsx)("polygon",{points:"0,0 30,0 15,10"})})});eA.displayName="Arrow";var ek=n(6101),eM=n(46081),eS=n(39033),eT=n(52712),eL="Popper",[ej,eP]=(0,eM.A)(eL),[eD,eN]=ej(eL),eI=e=>{let{__scopePopper:t,children:n}=e,[o,i]=r.useState(null);return(0,eR.jsx)(eD,{scope:t,anchor:o,onAnchorChange:i,children:n})};eI.displayName=eL;var eO="PopperAnchor",eF=r.forwardRef((e,t)=>{let{__scopePopper:n,virtualRef:o,...i}=e,a=eN(eO,n),l=r.useRef(null),u=(0,ek.s)(t,l);return r.useEffect(()=>{a.onAnchorChange((null==o?void 0:o.current)||l.current)}),o?null:(0,eR.jsx)(eE.sG.div,{...i,ref:u})});eF.displayName=eO;var e_="PopperContent",[eB,eH]=ej(e_),eK=r.forwardRef((e,t)=>{var n,o,l,c,s,d,f,p;let{__scopePopper:h,side:v="bottom",sideOffset:m=0,align:g="center",alignOffset:y=0,arrowPadding:w=0,avoidCollisions:x=!0,collisionBoundary:b=[],collisionPadding:C=0,sticky:E="partial",hideWhenDetached:R=!1,updatePositionStrategy:A="optimized",onPlaced:k,...M}=e,S=eN(e_,h),[T,L]=r.useState(null),P=(0,ek.s)(t,e=>L(e)),[D,N]=r.useState(null),I=function(e){let[t,n]=r.useState(void 0);return(0,eT.N)(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let r,o;if(!Array.isArray(t)||!t.length)return;let i=t[0];if("borderBoxSize"in i){let e=i.borderBoxSize,t=Array.isArray(e)?e[0]:e;r=t.inlineSize,o=t.blockSize}else r=e.offsetWidth,o=e.offsetHeight;n({width:r,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t}(D),O=null!==(f=null==I?void 0:I.width)&&void 0!==f?f:0,F=null!==(p=null==I?void 0:I.height)&&void 0!==p?p:0,_="number"==typeof C?C:{top:0,right:0,bottom:0,left:0,...C},B=Array.isArray(b)?b:[b],H=B.length>0,K={padding:_,boundary:B.filter(eV),altBoundary:H},{refs:W,floatingStyles:G,placement:V,isPositioned:U,middlewareData:Y}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:n="absolute",middleware:o=[],platform:i,elements:{reference:a,floating:l}={},transform:u=!0,whileElementsMounted:c,open:s}=e,[d,f]=r.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[p,h]=r.useState(o);ed(p,o)||h(o);let[v,m]=r.useState(null),[g,y]=r.useState(null),w=r.useCallback(e=>{e!==E.current&&(E.current=e,m(e))},[]),x=r.useCallback(e=>{e!==R.current&&(R.current=e,y(e))},[]),b=a||v,C=l||g,E=r.useRef(null),R=r.useRef(null),A=r.useRef(d),k=null!=c,M=eh(c),S=eh(i),T=eh(s),L=r.useCallback(()=>{if(!E.current||!R.current)return;let e={placement:t,strategy:n,middleware:p};S.current&&(e.platform=S.current),eu(E.current,R.current,e).then(e=>{let t={...e,isPositioned:!1!==T.current};j.current&&!ed(A.current,t)&&(A.current=t,ec.flushSync(()=>{f(t)}))})},[p,t,n,S,T]);es(()=>{!1===s&&A.current.isPositioned&&(A.current.isPositioned=!1,f(e=>({...e,isPositioned:!1})))},[s]);let j=r.useRef(!1);es(()=>(j.current=!0,()=>{j.current=!1}),[]),es(()=>{if(b&&(E.current=b),C&&(R.current=C),b&&C){if(M.current)return M.current(b,C,L);L()}},[b,C,L,M,k]);let P=r.useMemo(()=>({reference:E,floating:R,setReference:w,setFloating:x}),[w,x]),D=r.useMemo(()=>({reference:b,floating:C}),[b,C]),N=r.useMemo(()=>{let e={position:n,left:0,top:0};if(!D.floating)return e;let t=ep(D.floating,d.x),r=ep(D.floating,d.y);return u?{...e,transform:"translate("+t+"px, "+r+"px)",...ef(D.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:r}},[n,u,D.floating,d.x,d.y]);return r.useMemo(()=>({...d,update:L,refs:P,elements:D,floatingStyles:N}),[d,L,P,D,N])}({strategy:"fixed",placement:v+("center"!==g?"-"+g:""),whileElementsMounted:function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(e,t,n,r){let o;void 0===r&&(r={});let{ancestorScroll:l=!0,ancestorResize:c=!0,elementResize:s="function"==typeof ResizeObserver,layoutShift:d="function"==typeof IntersectionObserver,animationFrame:f=!1}=r,p=X(e),h=l||c?[...p?z(p):[],...z(t)]:[];h.forEach(e=>{l&&e.addEventListener("scroll",n,{passive:!0}),c&&e.addEventListener("resize",n)});let v=p&&d?function(e,t){let n,r=null,o=j(e);function l(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return!function c(s,d){void 0===s&&(s=!1),void 0===d&&(d=1),l();let f=e.getBoundingClientRect(),{left:p,top:h,width:v,height:m}=f;if(s||t(),!v||!m)return;let g=u(h),y=u(o.clientWidth-(p+v)),w={rootMargin:-g+"px "+-y+"px "+-u(o.clientHeight-(h+m))+"px "+-u(p)+"px",threshold:a(0,i(1,d))||1},x=!0;function b(t){let r=t[0].intersectionRatio;if(r!==d){if(!x)return c();r?c(!1,r):n=setTimeout(()=>{c(!1,1e-7)},1e3)}1!==r||ea(f,e.getBoundingClientRect())||c(),x=!1}try{r=new IntersectionObserver(b,{...w,root:o.ownerDocument})}catch(e){r=new IntersectionObserver(b,w)}r.observe(e)}(!0),l}(p,n):null,m=-1,g=null;s&&(g=new ResizeObserver(e=>{let[r]=e;r&&r.target===p&&g&&(g.unobserve(t),cancelAnimationFrame(m),m=requestAnimationFrame(()=>{var e;null==(e=g)||e.observe(t)})),n()}),p&&!f&&g.observe(p),g.observe(t));let y=f?$(e):null;return f&&function t(){let r=$(e);y&&!ea(y,r)&&n(),y=r,o=requestAnimationFrame(t)}(),n(),()=>{var e;h.forEach(e=>{l&&e.removeEventListener("scroll",n),c&&e.removeEventListener("resize",n)}),null==v||v(),null==(e=g)||e.disconnect(),g=null,f&&cancelAnimationFrame(o)}}(...t,{animationFrame:"always"===A})},elements:{reference:S.anchor},middleware:[em({mainAxis:m+F,alignmentAxis:y}),x&&eg({mainAxis:!0,crossAxis:!1,limiter:"partial"===E?ey():void 0,...K}),x&&ew({...K}),ex({...K,apply:e=>{let{elements:t,rects:n,availableWidth:r,availableHeight:o}=e,{width:i,height:a}=n.reference,l=t.floating.style;l.setProperty("--radix-popper-available-width","".concat(r,"px")),l.setProperty("--radix-popper-available-height","".concat(o,"px")),l.setProperty("--radix-popper-anchor-width","".concat(i,"px")),l.setProperty("--radix-popper-anchor-height","".concat(a,"px"))}}),D&&eC({element:D,padding:w}),eU({arrowWidth:O,arrowHeight:F}),R&&eb({strategy:"referenceHidden",...K})]}),[q,Z]=eX(V),J=(0,eS.c)(k);(0,eT.N)(()=>{U&&(null==J||J())},[U,J]);let Q=null===(n=Y.arrow)||void 0===n?void 0:n.x,ee=null===(o=Y.arrow)||void 0===o?void 0:o.y,et=(null===(l=Y.arrow)||void 0===l?void 0:l.centerOffset)!==0,[en,er]=r.useState();return(0,eT.N)(()=>{T&&er(window.getComputedStyle(T).zIndex)},[T]),(0,eR.jsx)("div",{ref:W.setFloating,"data-radix-popper-content-wrapper":"",style:{...G,transform:U?G.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:en,"--radix-popper-transform-origin":[null===(c=Y.transformOrigin)||void 0===c?void 0:c.x,null===(s=Y.transformOrigin)||void 0===s?void 0:s.y].join(" "),...(null===(d=Y.hide)||void 0===d?void 0:d.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,eR.jsx)(eB,{scope:h,placedSide:q,onArrowChange:N,arrowX:Q,arrowY:ee,shouldHideArrow:et,children:(0,eR.jsx)(eE.sG.div,{"data-side":q,"data-align":Z,...M,ref:P,style:{...M.style,animation:U?void 0:"none"}})})})});eK.displayName=e_;var eW="PopperArrow",eG={top:"bottom",right:"left",bottom:"top",left:"right"},ez=r.forwardRef(function(e,t){let{__scopePopper:n,...r}=e,o=eH(eW,n),i=eG[o.placedSide];return(0,eR.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,eR.jsx)(eA,{...r,ref:t,style:{...r.style,display:"block"}})})});function eV(e){return null!==e}ez.displayName=eW;var eU=e=>({name:"transformOrigin",options:e,fn(t){var n,r,o,i,a;let{placement:l,rects:u,middlewareData:c}=t,s=(null===(n=c.arrow)||void 0===n?void 0:n.centerOffset)!==0,d=s?0:e.arrowWidth,f=s?0:e.arrowHeight,[p,h]=eX(l),v={start:"0%",center:"50%",end:"100%"}[h],m=(null!==(i=null===(r=c.arrow)||void 0===r?void 0:r.x)&&void 0!==i?i:0)+d/2,g=(null!==(a=null===(o=c.arrow)||void 0===o?void 0:o.y)&&void 0!==a?a:0)+f/2,y="",w="";return"bottom"===p?(y=s?v:"".concat(m,"px"),w="".concat(-f,"px")):"top"===p?(y=s?v:"".concat(m,"px"),w="".concat(u.floating.height+f,"px")):"right"===p?(y="".concat(-f,"px"),w=s?v:"".concat(g,"px")):"left"===p&&(y="".concat(u.floating.width+f,"px"),w=s?v:"".concat(g,"px")),{data:{x:y,y:w}}}});function eX(e){let[t,n="center"]=e.split("-");return[t,n]}var eY=eI,eq=eF,eZ=eK,e$=ez},50594:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(40157).A)("Info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]])},61285:(e,t,n)=>{n.d(t,{B:()=>u});var r,o=n(12115),i=n(52712),a=(r||(r=n.t(o,2)))["useId".toString()]||(()=>void 0),l=0;function u(e){let[t,n]=o.useState(a());return(0,i.N)(()=>{e||n(e=>e??String(l++))},[e]),e||(t?`radix-${t}`:"")}},70154:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(40157).A)("Circle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},73158:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(40157).A)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},85977:(e,t,n)=>{n.d(t,{H4:()=>b,_V:()=>x,bL:()=>w});var r=n(12115),o=n(46081),i=n(39033),a=n(52712),l=n(63655),u=n(95155),c="Avatar",[s,d]=(0,o.A)(c),[f,p]=s(c),h=r.forwardRef((e,t)=>{let{__scopeAvatar:n,...o}=e,[i,a]=r.useState("idle");return(0,u.jsx)(f,{scope:n,imageLoadingStatus:i,onImageLoadingStatusChange:a,children:(0,u.jsx)(l.sG.span,{...o,ref:t})})});h.displayName=c;var v="AvatarImage",m=r.forwardRef((e,t)=>{let{__scopeAvatar:n,src:o,onLoadingStatusChange:c=()=>{},...s}=e,d=p(v,n),f=function(e,t){let[n,o]=r.useState("idle");return(0,a.N)(()=>{if(!e){o("error");return}let n=!0,r=new window.Image,i=e=>()=>{n&&o(e)};return o("loading"),r.onload=i("loaded"),r.onerror=i("error"),r.src=e,t&&(r.referrerPolicy=t),()=>{n=!1}},[e,t]),n}(o,s.referrerPolicy),h=(0,i.c)(e=>{c(e),d.onImageLoadingStatusChange(e)});return(0,a.N)(()=>{"idle"!==f&&h(f)},[f,h]),"loaded"===f?(0,u.jsx)(l.sG.img,{...s,ref:t,src:o}):null});m.displayName=v;var g="AvatarFallback",y=r.forwardRef((e,t)=>{let{__scopeAvatar:n,delayMs:o,...i}=e,a=p(g,n),[c,s]=r.useState(void 0===o);return r.useEffect(()=>{if(void 0!==o){let e=window.setTimeout(()=>s(!0),o);return()=>window.clearTimeout(e)}},[o]),c&&"loaded"!==a.imageLoadingStatus?(0,u.jsx)(l.sG.span,{...i,ref:t}):null});y.displayName=g;var w=h,x=m,b=y},92293:(e,t,n)=>{n.d(t,{Oh:()=>i});var r=n(12115),o=0;function i(){r.useEffect(()=>{var e,t;let n=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",null!==(e=n[0])&&void 0!==e?e:a()),document.body.insertAdjacentElement("beforeend",null!==(t=n[1])&&void 0!==t?t:a()),o++,()=>{1===o&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),o--}},[])}function a(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}},94315:(e,t,n)=>{n.d(t,{jH:()=>i});var r=n(12115);n(95155);var o=r.createContext(void 0);function i(e){let t=r.useContext(o);return e||t||"ltr"}}}]);