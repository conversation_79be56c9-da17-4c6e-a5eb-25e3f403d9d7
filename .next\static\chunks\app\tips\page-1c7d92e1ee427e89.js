(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[273],{29100:(e,t,r)=>{"use strict";r.d(t,{Separator:()=>d});var o=r(95155),a=r(12115),n=r(63655),i="horizontal",s=["horizontal","vertical"],l=a.forwardRef((e,t)=>{var r;let{decorative:a,orientation:l=i,...c}=e,d=(r=l,s.includes(r))?l:i;return(0,o.jsx)(n.sG.div,{"data-orientation":d,...a?{role:"none"}:{"aria-orientation":"vertical"===d?d:void 0,role:"separator"},...c,ref:t})});l.displayName="Separator";var c=r(59434);let d=a.forwardRef((e,t)=>{let{className:r,orientation:a="horizontal",decorative:n=!0,...i}=e;return(0,o.jsx)(l,{ref:t,decorative:n,orientation:a,className:(0,c.cn)("shrink-0 bg-border","horizontal"===a?"h-[1px] w-full":"h-full w-[1px]",r),...i})});d.displayName=l.displayName},30285:(e,t,r)=>{"use strict";r.d(t,{$:()=>c,r:()=>l});var o=r(95155),a=r(12115),n=r(99708),i=r(74466),s=r(59434);let l=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-all duration-200 ease-in-out focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 active:scale-95",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90 hover:shadow-md hover:-translate-y-0.5",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90 hover:shadow-md hover:-translate-y-0.5",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground hover:shadow-sm hover:-translate-y-0.5",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80 hover:shadow-sm hover:-translate-y-0.5",ghost:"hover:bg-accent hover:text-accent-foreground hover:shadow-sm",link:"text-primary underline-offset-4 hover:underline",gradient:"btn-gradient-primary hover:shadow-lg","gradient-accent":"btn-gradient-accent hover:shadow-lg",success:"bg-green-600 text-white hover:bg-green-700 hover:shadow-md hover:-translate-y-0.5",warning:"bg-yellow-600 text-white hover:bg-yellow-700 hover:shadow-md hover:-translate-y-0.5",info:"bg-blue-600 text-white hover:bg-blue-700 hover:shadow-md hover:-translate-y-0.5"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3 text-xs",lg:"h-11 rounded-md px-8 text-base",xl:"h-12 rounded-lg px-10 text-lg",icon:"h-10 w-10","icon-sm":"h-8 w-8","icon-lg":"h-12 w-12"}},defaultVariants:{variant:"default",size:"default"}}),c=a.forwardRef((e,t)=>{let{className:r,variant:a,size:i,asChild:c=!1,...d}=e,u=c?n.DX:"button";return(0,o.jsx)(u,{className:(0,s.cn)(l({variant:a,size:i,className:r})),ref:t,...d})});c.displayName="Button"},41559:(e,t,r)=>{Promise.resolve().then(r.bind(r,44321)),Promise.resolve().then(r.bind(r,29100))},59434:(e,t,r)=>{"use strict";r.d(t,{cn:()=>n});var o=r(52596),a=r(39688);function n(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,a.QP)((0,o.$)(t))}},90925:(e,t,r)=>{"use strict";let o,a,n;r.d(t,{AuthProvider:()=>f,A:()=>g});var i=r(95155),s=r(12115),l=r(93004),c=r(23915),d=r(90858);if((0,c.Dk)().length)o=(0,c.Sx)(),console.log("Firebase app already initialized, getting existing app.");else try{o=(0,c.Wp)({apiKey:"AIzaSyBGok32mQZDRrbt9yQ0VTuZSqzLIF7xj7A",authDomain:"budgetwise-1nj2w.firebaseapp.com",projectId:"budgetwise-1nj2w",storageBucket:"budgetwise-1nj2w.appspot.com",messagingSenderId:"153665844551",appId:"1:153665844551:web:dc9f0ba3384c9b23c1c862"}),console.log("Firebase app initialized successfully with hardcoded config.")}catch(e){console.error("Firebase initialization error with hardcoded config:",e),o=void 0}if(o){try{a=(0,l.xI)(o),console.log("Firebase Auth instance obtained successfully.")}catch(e){console.error("Firebase getAuth error after app initialization:",e),a=void 0}try{n=(0,d.c7)(o),console.log("Firebase Storage instance obtained successfully.")}catch(e){console.error("Firebase getStorage error after app initialization:",e),n=void 0}}else console.error("Firebase app was not initialized successfully, auth/storage instances cannot be created."),a=void 0,n=void 0;var u=r(35695);let h=(0,s.createContext)(void 0);function g(){let e=(0,s.useContext)(h);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}function f(e){let{children:t}=e,[r,o]=(0,s.useState)(null),[c,g]=(0,s.useState)(!0),[f,p]=(0,s.useState)(null),v=(0,u.useRouter)();(0,s.useEffect)(()=>{let e=(0,l.hg)(a,e=>{o(e),g(!1)});return()=>e()},[]);let w=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"An unknown error occurred.",r=e.message||t;return"auth/user-not-found"===e.code&&(r="No user found with this email."),"auth/wrong-password"===e.code&&(r="Incorrect password."),"auth/email-already-in-use"===e.code&&(r="This email is already registered."),"auth/weak-password"===e.code&&(r="Password must be at least 6 characters."),"auth/popup-closed-by-user"===e.code&&(r="Google Sign-In cancelled."),"auth/requires-recent-login"===e.code&&(r="This operation is sensitive and requires recent authentication. Please log in again."),p(r),console.error("Auth Error:",e.code,e.message),null},b=async(e,t)=>{g(!0),p(null);try{let r=await (0,l.eJ)(a,e,t);return o(r.user),g(!1),r.user}catch(e){return g(!1),w(e,"Sign up failed.")}},y=async(e,t)=>{g(!0),p(null);try{let r=await (0,l.x9)(a,e,t);return o(r.user),g(!1),r.user}catch(e){return g(!1),w(e,"Sign in failed.")}},m=async()=>{g(!0),p(null);let e=new l.HF;try{let t=await (0,l.df)(a,e);return o(t.user),g(!1),t.user}catch(e){return g(!1),w(e,"Google sign-in failed.")}},x=async()=>{g(!0),p(null);try{await (0,l.CI)(a),o(null),v.push("/login")}catch(e){w(e,"Sign out failed.")}finally{g(!1)}},R=async(e,t)=>{if(!r)throw Error("User not authenticated.");g(!0),p(null);let i=r.photoURL;if(null===t){if(r.photoURL){let e=new RegExp("profilePictures%2F".concat(r.uid,"%2F([^?]+)")),t=r.photoURL.match(e);if(t&&t[1]){let e=decodeURIComponent(t[1]),o="profilePictures/".concat(r.uid,"/").concat(e);try{await (0,d.XR)((0,d.KR)(n,o))}catch(e){"storage/object-not-found"!==e.code&&console.warn("Failed to delete old photo by derived path ".concat(o,":"),e)}}else console.warn("Could not derive old photo path from URL for deletion.")}i=null}else if(t){let e=t.name.split(".").pop()||"jpg",o="profilePictures/".concat(r.uid,"/profileImage.").concat(e),a=(0,d.KR)(n,o);if(r.photoURL){let e=new RegExp("profilePictures%2F".concat(r.uid,"%2F([^?]+)")),t=r.photoURL.match(e);if(t&&t[1]){let e=decodeURIComponent(t[1]),a="profilePictures/".concat(r.uid,"/").concat(e);if(a!==o)try{await (0,d.XR)((0,d.KR)(n,a))}catch(e){"storage/object-not-found"!==e.code&&console.warn("Failed to delete old photo ".concat(a," before new upload:"),e)}}}let s=(0,d.bp)(a,t);await new Promise((e,t)=>{s.on("state_changed",()=>{},e=>{w(e,"Photo upload failed."),t(e)},async()=>{try{i=await (0,d.qk)(s.snapshot.ref),e()}catch(e){w(e,"Failed to get photo download URL."),t(e)}})})}try{await (0,l.r7)(r,{displayName:null===e?r.displayName:e,photoURL:i}),o(a.currentUser)}catch(e){throw w(e,"Profile update failed."),e}finally{g(!1)}},F=async(e,t)=>{if(!r||!r.email)throw Error("User not authenticated or email missing.");g(!0),p(null);let o=l.IX.credential(r.email,e);try{await (0,l.kZ)(r,o),await (0,l.f3)(r,t)}catch(e){throw w(e,"Password change failed."),e}finally{g(!1)}};return(0,i.jsx)(h.Provider,{value:{currentUser:r,loading:c,error:f,signUp:b,signIn:y,signInWithGoogle:m,signOut:x,clearError:()=>p(null),updateUserProfile:R,changeUserPassword:F},children:t})}}},e=>{var t=t=>e(e.s=t);e.O(0,[73,671,440,422,59,321,441,684,358],()=>t(41559)),_N_E=e.O()}]);