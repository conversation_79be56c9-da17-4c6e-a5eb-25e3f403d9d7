import 'genkit';
import 'genkit/plugin';
export { G as GoogleAIPlugin, P as PluginOptions, g as default, g as googleAI, b as googleAIPlugin, t as textEmbedding004, a as textEmbeddingGecko001 } from './embedder-C27oRyqL.mjs';
export { GeminiConfig, GeminiVersionString, gemini, gemini10Pro, gemini15Flash, gemini15Flash8b, gemini15Pro, gemini20Flash, gemini20FlashExp, gemini20FlashLite, gemini20ProExp0205, gemini25FlashPreview0417, gemini25ProExp0325, gemini25ProPreview0325 } from './gemini.mjs';
import '@google/generative-ai';
import 'genkit/model';
