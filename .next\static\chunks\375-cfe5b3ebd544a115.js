"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[375],{17759:(e,t,r)=>{r.d(t,{C5:()=>w,MJ:()=>g,Rr:()=>v,eI:()=>p,lR:()=>m,lV:()=>d,zB:()=>u});var a=r(95155),o=r(12115),n=r(99708),i=r(62177),s=r(59434),l=r(85057);let d=i.Op,c=o.createContext({}),u=e=>{let{...t}=e;return(0,a.jsx)(c.Provider,{value:{name:t.name},children:(0,a.jsx)(i.xI,{...t})})},f=()=>{let e=o.useContext(c),t=o.useContext(h),{getFieldState:r,formState:a}=(0,i.xW)(),n=r(e.name,a);if(!e)throw Error("useFormField should be used within <FormField>");let{id:s}=t;return{id:s,name:e.name,formItemId:"".concat(s,"-form-item"),formDescriptionId:"".concat(s,"-form-item-description"),formMessageId:"".concat(s,"-form-item-message"),...n}},h=o.createContext({}),p=o.forwardRef((e,t)=>{let{className:r,...n}=e,i=o.useId();return(0,a.jsx)(h.Provider,{value:{id:i},children:(0,a.jsx)("div",{ref:t,className:(0,s.cn)("space-y-2",r),...n})})});p.displayName="FormItem";let m=o.forwardRef((e,t)=>{let{className:r,...o}=e,{error:n,formItemId:i}=f();return(0,a.jsx)(l.J,{ref:t,className:(0,s.cn)(n&&"text-destructive",r),htmlFor:i,...o})});m.displayName="FormLabel";let g=o.forwardRef((e,t)=>{let{...r}=e,{error:o,formItemId:i,formDescriptionId:s,formMessageId:l}=f();return(0,a.jsx)(n.DX,{ref:t,id:i,"aria-describedby":o?"".concat(s," ").concat(l):"".concat(s),"aria-invalid":!!o,...r})});g.displayName="FormControl";let v=o.forwardRef((e,t)=>{let{className:r,...o}=e,{formDescriptionId:n}=f();return(0,a.jsx)("p",{ref:t,id:n,className:(0,s.cn)("text-sm text-muted-foreground",r),...o})});v.displayName="FormDescription";let w=o.forwardRef((e,t)=>{var r;let{className:o,children:n,...i}=e,{error:l,formMessageId:d}=f(),c=l?String(null!==(r=null==l?void 0:l.message)&&void 0!==r?r:""):n;return c?(0,a.jsx)("p",{ref:t,id:d,className:(0,s.cn)("text-sm font-medium text-destructive",o),...i,children:c}):null});w.displayName="FormMessage"},30285:(e,t,r)=>{r.d(t,{$:()=>d,r:()=>l});var a=r(95155),o=r(12115),n=r(99708),i=r(74466),s=r(59434);let l=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-all duration-200 ease-in-out focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 active:scale-95",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90 hover:shadow-md hover:-translate-y-0.5",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90 hover:shadow-md hover:-translate-y-0.5",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground hover:shadow-sm hover:-translate-y-0.5",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80 hover:shadow-sm hover:-translate-y-0.5",ghost:"hover:bg-accent hover:text-accent-foreground hover:shadow-sm",link:"text-primary underline-offset-4 hover:underline",gradient:"btn-gradient-primary hover:shadow-lg","gradient-accent":"btn-gradient-accent hover:shadow-lg",success:"bg-green-600 text-white hover:bg-green-700 hover:shadow-md hover:-translate-y-0.5",warning:"bg-yellow-600 text-white hover:bg-yellow-700 hover:shadow-md hover:-translate-y-0.5",info:"bg-blue-600 text-white hover:bg-blue-700 hover:shadow-md hover:-translate-y-0.5"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3 text-xs",lg:"h-11 rounded-md px-8 text-base",xl:"h-12 rounded-lg px-10 text-lg",icon:"h-10 w-10","icon-sm":"h-8 w-8","icon-lg":"h-12 w-12"}},defaultVariants:{variant:"default",size:"default"}}),d=o.forwardRef((e,t)=>{let{className:r,variant:o,size:i,asChild:d=!1,...c}=e,u=d?n.DX:"button";return(0,a.jsx)(u,{className:(0,s.cn)(l({variant:o,size:i,className:r})),ref:t,...c})});d.displayName="Button"},59434:(e,t,r)=>{r.d(t,{cn:()=>n});var a=r(52596),o=r(39688);function n(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,o.QP)((0,a.$)(t))}},62523:(e,t,r)=>{r.d(t,{p:()=>i});var a=r(95155),o=r(12115),n=r(59434);let i=o.forwardRef((e,t)=>{let{className:r,type:o,...i}=e;return(0,a.jsx)("input",{type:o,className:(0,n.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",r),ref:t,...i})});i.displayName="Input"},66695:(e,t,r)=>{r.d(t,{BT:()=>d,Wu:()=>c,ZB:()=>l,Zp:()=>i,aR:()=>s,wL:()=>u});var a=r(95155),o=r(12115),n=r(59434);let i=o.forwardRef((e,t)=>{let{className:r,...o}=e;return(0,a.jsx)("div",{ref:t,className:(0,n.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",r),...o})});i.displayName="Card";let s=o.forwardRef((e,t)=>{let{className:r,...o}=e;return(0,a.jsx)("div",{ref:t,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",r),...o})});s.displayName="CardHeader";let l=o.forwardRef((e,t)=>{let{className:r,...o}=e;return(0,a.jsx)("div",{ref:t,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",r),...o})});l.displayName="CardTitle";let d=o.forwardRef((e,t)=>{let{className:r,...o}=e;return(0,a.jsx)("div",{ref:t,className:(0,n.cn)("text-sm text-muted-foreground",r),...o})});d.displayName="CardDescription";let c=o.forwardRef((e,t)=>{let{className:r,...o}=e;return(0,a.jsx)("div",{ref:t,className:(0,n.cn)("p-6 pt-0",r),...o})});c.displayName="CardContent";let u=o.forwardRef((e,t)=>{let{className:r,...o}=e;return(0,a.jsx)("div",{ref:t,className:(0,n.cn)("flex items-center p-6 pt-0",r),...o})});u.displayName="CardFooter"},85057:(e,t,r)=>{r.d(t,{J:()=>d});var a=r(95155),o=r(12115),n=r(40968),i=r(74466),s=r(59434);let l=(0,i.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=o.forwardRef((e,t)=>{let{className:r,...o}=e;return(0,a.jsx)(n.b,{ref:t,className:(0,s.cn)(l(),r),...o})});d.displayName=n.b.displayName},90925:(e,t,r)=>{let a,o,n;r.d(t,{AuthProvider:()=>p,A:()=>h});var i=r(95155),s=r(12115),l=r(93004),d=r(23915),c=r(90858);if((0,d.Dk)().length)a=(0,d.Sx)(),console.log("Firebase app already initialized, getting existing app.");else try{a=(0,d.Wp)({apiKey:"AIzaSyBGok32mQZDRrbt9yQ0VTuZSqzLIF7xj7A",authDomain:"budgetwise-1nj2w.firebaseapp.com",projectId:"budgetwise-1nj2w",storageBucket:"budgetwise-1nj2w.appspot.com",messagingSenderId:"153665844551",appId:"1:153665844551:web:dc9f0ba3384c9b23c1c862"}),console.log("Firebase app initialized successfully with hardcoded config.")}catch(e){console.error("Firebase initialization error with hardcoded config:",e),a=void 0}if(a){try{o=(0,l.xI)(a),console.log("Firebase Auth instance obtained successfully.")}catch(e){console.error("Firebase getAuth error after app initialization:",e),o=void 0}try{n=(0,c.c7)(a),console.log("Firebase Storage instance obtained successfully.")}catch(e){console.error("Firebase getStorage error after app initialization:",e),n=void 0}}else console.error("Firebase app was not initialized successfully, auth/storage instances cannot be created."),o=void 0,n=void 0;var u=r(35695);let f=(0,s.createContext)(void 0);function h(){let e=(0,s.useContext)(f);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}function p(e){let{children:t}=e,[r,a]=(0,s.useState)(null),[d,h]=(0,s.useState)(!0),[p,m]=(0,s.useState)(null),g=(0,u.useRouter)();(0,s.useEffect)(()=>{let e=(0,l.hg)(o,e=>{a(e),h(!1)});return()=>e()},[]);let v=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"An unknown error occurred.",r=e.message||t;return"auth/user-not-found"===e.code&&(r="No user found with this email."),"auth/wrong-password"===e.code&&(r="Incorrect password."),"auth/email-already-in-use"===e.code&&(r="This email is already registered."),"auth/weak-password"===e.code&&(r="Password must be at least 6 characters."),"auth/popup-closed-by-user"===e.code&&(r="Google Sign-In cancelled."),"auth/requires-recent-login"===e.code&&(r="This operation is sensitive and requires recent authentication. Please log in again."),m(r),console.error("Auth Error:",e.code,e.message),null},w=async(e,t)=>{h(!0),m(null);try{let r=await (0,l.eJ)(o,e,t);return a(r.user),h(!1),r.user}catch(e){return h(!1),v(e,"Sign up failed.")}},b=async(e,t)=>{h(!0),m(null);try{let r=await (0,l.x9)(o,e,t);return a(r.user),h(!1),r.user}catch(e){return h(!1),v(e,"Sign in failed.")}},y=async()=>{h(!0),m(null);let e=new l.HF;try{let t=await (0,l.df)(o,e);return a(t.user),h(!1),t.user}catch(e){return h(!1),v(e,"Google sign-in failed.")}},x=async()=>{h(!0),m(null);try{await (0,l.CI)(o),a(null),g.push("/login")}catch(e){v(e,"Sign out failed.")}finally{h(!1)}},R=async(e,t)=>{if(!r)throw Error("User not authenticated.");h(!0),m(null);let i=r.photoURL;if(null===t){if(r.photoURL){let e=new RegExp("profilePictures%2F".concat(r.uid,"%2F([^?]+)")),t=r.photoURL.match(e);if(t&&t[1]){let e=decodeURIComponent(t[1]),a="profilePictures/".concat(r.uid,"/").concat(e);try{await (0,c.XR)((0,c.KR)(n,a))}catch(e){"storage/object-not-found"!==e.code&&console.warn("Failed to delete old photo by derived path ".concat(a,":"),e)}}else console.warn("Could not derive old photo path from URL for deletion.")}i=null}else if(t){let e=t.name.split(".").pop()||"jpg",a="profilePictures/".concat(r.uid,"/profileImage.").concat(e),o=(0,c.KR)(n,a);if(r.photoURL){let e=new RegExp("profilePictures%2F".concat(r.uid,"%2F([^?]+)")),t=r.photoURL.match(e);if(t&&t[1]){let e=decodeURIComponent(t[1]),o="profilePictures/".concat(r.uid,"/").concat(e);if(o!==a)try{await (0,c.XR)((0,c.KR)(n,o))}catch(e){"storage/object-not-found"!==e.code&&console.warn("Failed to delete old photo ".concat(o," before new upload:"),e)}}}let s=(0,c.bp)(o,t);await new Promise((e,t)=>{s.on("state_changed",()=>{},e=>{v(e,"Photo upload failed."),t(e)},async()=>{try{i=await (0,c.qk)(s.snapshot.ref),e()}catch(e){v(e,"Failed to get photo download URL."),t(e)}})})}try{await (0,l.r7)(r,{displayName:null===e?r.displayName:e,photoURL:i}),a(o.currentUser)}catch(e){throw v(e,"Profile update failed."),e}finally{h(!1)}},N=async(e,t)=>{if(!r||!r.email)throw Error("User not authenticated or email missing.");h(!0),m(null);let a=l.IX.credential(r.email,e);try{await (0,l.kZ)(r,a),await (0,l.f3)(r,t)}catch(e){throw v(e,"Password change failed."),e}finally{h(!1)}};return(0,i.jsx)(f.Provider,{value:{currentUser:r,loading:d,error:p,signUp:w,signIn:b,signInWithGoogle:y,signOut:x,clearError:()=>m(null),updateUserProfile:R,changeUserPassword:N},children:t})}}}]);