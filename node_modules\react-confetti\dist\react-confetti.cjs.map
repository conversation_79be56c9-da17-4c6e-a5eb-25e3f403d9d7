{"version": 3, "file": "react-confetti.cjs", "sources": ["../src/utils.ts", "../src/Particle.ts", "../src/ParticleGenerator.ts", "../src/Confetti.ts", "../src/ReactConfetti.tsx"], "sourcesContent": ["import { ICircle } from './Circle'\nimport { IPoint } from './Point'\nimport { IRect } from './Rect'\n\nexport function norm(value: number, min: number, max: number) {\n  return (value - min) / (max - min)\n}\n\nexport function lerp(lnorm: number, min: number, max: number) {\n  return (max - min) * lnorm + min\n}\n\nexport function map(\n  value: number,\n  sourceMin: number,\n  sourceMax: number,\n  destMin: number,\n  destMax: number,\n) {\n  return lerp(norm(value, sourceMin, sourceMax), destMin, destMax)\n}\n\nexport function clamp(value: number, min: number, max: number) {\n  return Math.min(Math.max(value, Math.min(min, max)), Math.max(min, max))\n}\n\nexport function distance(p0: IPoint, p1: IPoint) {\n  const dx = p1.x - p0.x\n  const dy = p1.y - p0.y\n  return Math.sqrt(dx * dx + dy * dy)\n}\n\nexport function distanceXY(x0: number, y0: number, x1: number, y1: number) {\n  const dx = x1 - x0\n  const dy = y1 - y0\n  return Math.sqrt(dx * dx + dy * dy)\n}\n\nexport function circleCollision(c0: ICircle, c1: ICircle) {\n  return distance(c0, c1) <= c0.radius + c1.radius\n}\n\nexport function circlePointCollision(x: number, y: number, circle: ICircle) {\n  return distanceXY(x, y, circle.x, circle.y) < circle.radius\n}\n\nexport function inRange(value: number, min: number, max: number) {\n  return value >= Math.min(min, max) && value <= Math.max(min, max)\n}\n\nexport function pointInRect(p: IPoint, rect: IRect) {\n  return (\n    inRange(p.x, rect.x, rect.x + rect.w) &&\n    inRange(p.y, rect.y, rect.y + rect.h)\n  )\n}\n\nexport function rangeIntersect(\n  min0: number,\n  max0: number,\n  min1: number,\n  max1: number,\n) {\n  return (\n    Math.max(min0, max0) >= Math.min(min1, max1) &&\n    Math.min(min0, max0) <= Math.max(min1, max1)\n  )\n}\n\nexport function rectIntersect(r0: IRect, r1: IRect) {\n  return (\n    rangeIntersect(r0.x, r0.x + r0.w, r1.x, r1.x + r1.w) &&\n    rangeIntersect(r0.y, r0.y + r0.h, r1.y, r1.y + r1.h)\n  )\n}\n\nexport function degreesToRads(degrees: number) {\n  return (degrees * Math.PI) / 180\n}\n\nexport function radsToDegrees(radians: number) {\n  return (radians * 180) / Math.PI\n}\n\nexport function randomRange(min: number, max: number) {\n  return min + Math.random() * (max - min)\n}\n\nexport function randomInt(min: number, max: number) {\n  return Math.floor(min + Math.random() * (max - min + 1))\n}\n", "import { IConfettiOptions } from './Confetti'\nimport { degreesToRads, randomInt, randomRange } from './utils'\n\nexport enum ParticleShape {\n  Circle = 0,\n  Square = 1,\n  Strip = 2,\n}\n\nenum RotationDirection {\n  Positive = 1,\n  Negative = -1,\n}\n\nconst DEFAULT_FRAME_TIME = 1000 / 60\n\nexport default class Particle {\n  constructor(\n    context: CanvasRenderingContext2D,\n    getOptions: () => IConfettiOptions,\n    x: number,\n    y: number,\n  ) {\n    this.getOptions = getOptions\n    const { colors, initialVelocityX, initialVelocityY } = this.getOptions()\n    this.context = context\n    this.x = x\n    this.y = y\n    this.w = randomRange(5, 20)\n    this.h = randomRange(5, 20)\n    this.radius = randomRange(5, 10)\n    this.vx =\n      typeof initialVelocityX === 'number'\n        ? randomRange(-initialVelocityX, initialVelocityX)\n        : randomRange(initialVelocityX.min, initialVelocityX.max)\n    this.vy =\n      typeof initialVelocityY === 'number'\n        ? randomRange(-initialVelocityY, 0)\n        : randomRange(initialVelocityY.min, initialVelocityY.max)\n    this.shape = randomInt(0, 2)\n    this.angle = degreesToRads(randomRange(0, 360))\n    this.angularSpin = randomRange(-0.2, 0.2)\n    this.color = colors[Math.floor(Math.random() * colors.length)]\n    this.rotateY = randomRange(0, 1)\n    this.rotationDirection = randomRange(0, 1)\n      ? RotationDirection.Positive\n      : RotationDirection.Negative\n  }\n\n  context: CanvasRenderingContext2D\n\n  radius: number\n\n  x: number\n\n  y: number\n\n  w: number\n\n  h: number\n\n  vx: number\n\n  vy: number\n\n  shape: ParticleShape\n\n  angle: number\n\n  angularSpin: number\n\n  color: string\n\n  // Actually used as scaleY to simulate rotation cheaply\n  rotateY: number\n\n  rotationDirection: RotationDirection\n\n  getOptions: () => IConfettiOptions\n\n  update(elapsed: number) {\n    const { gravity, wind, friction, opacity, drawShape } = this.getOptions()\n    const frameTimeMultiplier = elapsed / DEFAULT_FRAME_TIME\n    this.x += this.vx * frameTimeMultiplier\n    this.y += this.vy * frameTimeMultiplier\n    this.vy += gravity * frameTimeMultiplier\n    this.vx += wind * frameTimeMultiplier\n    this.vx *= friction ** frameTimeMultiplier\n    this.vy *= friction ** frameTimeMultiplier\n    if (\n      this.rotateY >= 1 &&\n      this.rotationDirection === RotationDirection.Positive\n    ) {\n      this.rotationDirection = RotationDirection.Negative\n    } else if (\n      this.rotateY <= -1 &&\n      this.rotationDirection === RotationDirection.Negative\n    ) {\n      this.rotationDirection = RotationDirection.Positive\n    }\n\n    const rotateDelta = 0.1 * this.rotationDirection * frameTimeMultiplier\n\n    this.rotateY += rotateDelta\n    this.angle += this.angularSpin\n    this.context.save()\n    this.context.translate(this.x, this.y)\n    this.context.rotate(this.angle)\n    this.context.scale(1, this.rotateY)\n    this.context.rotate(this.angle)\n    this.context.beginPath()\n    this.context.fillStyle = this.color\n    this.context.strokeStyle = this.color\n    this.context.globalAlpha = opacity\n    this.context.lineCap = 'round'\n    this.context.lineWidth = 2\n    if (drawShape && typeof drawShape === 'function') {\n      drawShape.call(this, this.context)\n    } else {\n      switch (this.shape) {\n        case ParticleShape.Circle: {\n          this.context.beginPath()\n          this.context.arc(0, 0, this.radius, 0, 2 * Math.PI)\n          this.context.fill()\n          break\n        }\n        case ParticleShape.Square: {\n          this.context.fillRect(-this.w / 2, -this.h / 2, this.w, this.h)\n          break\n        }\n        case ParticleShape.Strip: {\n          this.context.fillRect(-this.w / 6, -this.h / 2, this.w / 3, this.h)\n          break\n        }\n      }\n    }\n    this.context.closePath()\n    this.context.restore()\n  }\n}\n", "import { IConfettiOptions } from './Confetti'\nimport Particle from './Particle'\nimport { IRect } from './Rect'\nimport { randomRange } from './utils'\n\nexport interface IParticleGenerator extends IRect {\n  removeParticleAt: (index: number) => void\n  getParticle: () => void\n  animate: (elapsed: number) => boolean\n  particles: Particle[]\n  particlesGenerated: number\n}\n\nexport default class ParticleGenerator implements IParticleGenerator {\n  constructor(canvas: HTMLCanvasElement, getOptions: () => IConfettiOptions) {\n    this.canvas = canvas\n    const ctx = this.canvas.getContext('2d')\n    if (!ctx) {\n      throw new Error('Could not get canvas context')\n    }\n    this.context = ctx\n    this.getOptions = getOptions\n  }\n\n  canvas: HTMLCanvasElement\n\n  context: CanvasRenderingContext2D\n\n  getOptions: () => IConfettiOptions\n\n  x = 0\n\n  y = 0\n\n  w = 0\n\n  h = 0\n\n  lastNumberOfPieces = 0\n\n  tweenProgress = 0\n\n  tweenFrom = 0\n\n  particles: Particle[] = []\n\n  particlesGenerated = 0\n\n  removeParticleAt = (i: number) => {\n    this.particles.splice(i, 1)\n  }\n\n  getParticle = () => {\n    const newParticleX = randomRange(this.x, this.w + this.x)\n    const newParticleY = randomRange(this.y, this.h + this.y)\n    return new Particle(\n      this.context,\n      this.getOptions,\n      newParticleX,\n      newParticleY,\n    )\n  }\n\n  animate = (elapsed: number): boolean => {\n    const { canvas, context, particlesGenerated, lastNumberOfPieces } = this\n    const {\n      run,\n      recycle,\n      numberOfPieces,\n      debug,\n      tweenFunction,\n      tweenDuration,\n    } = this.getOptions()\n    if (!run) {\n      return false\n    }\n\n    const nP = this.particles.length\n    const activeCount = recycle ? nP : particlesGenerated\n\n    // Initial population\n    if (activeCount < numberOfPieces) {\n      // Use the numberOfPieces prop as a key to reset the easing timing\n      if (lastNumberOfPieces !== numberOfPieces) {\n        this.tweenProgress = 0\n        this.tweenFrom = activeCount\n        this.lastNumberOfPieces = numberOfPieces\n      }\n\n      // Clamp tweenProgress between 0 and tweenDuration\n      this.tweenProgress = Math.min(\n        tweenDuration,\n        Math.max(0, this.tweenProgress + elapsed),\n      )\n      const tweenedVal = tweenFunction(\n        this.tweenProgress,\n        this.tweenFrom,\n        numberOfPieces,\n        tweenDuration,\n      )\n      const numToAdd = Math.round(tweenedVal - activeCount)\n      for (let i = 0; i < numToAdd; i++) {\n        this.particles.push(this.getParticle())\n      }\n      this.particlesGenerated += numToAdd\n    }\n    if (debug) {\n      // Draw debug text\n      context.font = '12px sans-serif'\n      context.fillStyle = '#333'\n      context.textAlign = 'right'\n      context.fillText(\n        `Particles: ${nP}`,\n        canvas.width - 10,\n        canvas.height - 20,\n      )\n    }\n\n    // Maintain the population, iterating backwards to prevent issues when removing particles\n    for (let i = this.particles.length - 1; i >= 0; i--) {\n      const p = this.particles[i]\n      // Update each particle's position\n      p.update(elapsed)\n      // Prune the off-canvas particles\n      if (\n        p.y > canvas.height ||\n        p.y < -100 ||\n        p.x > canvas.width + 100 ||\n        p.x < -100\n      ) {\n        if (recycle && activeCount <= numberOfPieces) {\n          // Replace the particle with a brand new one\n          this.particles[i] = this.getParticle()\n        } else {\n          this.removeParticleAt(i)\n        }\n      }\n    }\n    return nP > 0 || activeCount < numberOfPieces\n  }\n}\n", "import * as tweens from 'tween-functions'\nimport ParticleGenerator from './ParticleGenerator'\nimport { IRect } from './Rect'\n\nexport interface IConfettiOptions {\n  /**\n   * Width of the component\n   * @default window.width\n   */\n  width: number\n  /**\n   * Height of the component\n   * @default window.height\n   */\n  height: number\n  /**\n   * Max number of confetti pieces to render.\n   * @default 200\n   */\n  numberOfPieces: number\n  /**\n   * Slows movement of pieces. (lower number = slower confetti)\n   * @default 0.99\n   */\n  friction: number\n  /**\n   * Blows confetti along the X axis.\n   * @default 0\n   */\n  wind: number\n  /**\n   * How fast it falls (pixels per frame)\n   * @default 0.1\n   */\n  gravity: number\n  /**\n   * How fast the confetti is emitted horizontally\n   * @default 4\n   */\n  initialVelocityX: { min: number; max: number } | number\n  /**\n   * How fast the confetti is emitted vertically\n   * @default 10\n   */\n  initialVelocityY: { min: number; max: number } | number\n  /**\n   * Array of colors to choose from.\n   */\n  colors: string[]\n  /**\n   * Opacity of the confetti.\n   * @default 1\n   */\n  opacity: number\n  /**\n   * If false, only numberOfPieces will be emitted and then stops. If true, when a confetto goes offscreen, a new one will be emitted.\n   * @default true\n   */\n  recycle: boolean\n  /**\n   * If false, stops the requestAnimationFrame loop.\n   * @default true\n   */\n  run: boolean\n  /**\n   * The frame rate of the animation. If set, the animation will be throttled to that frame rate.\n   * @default undefined\n   */\n  frameRate?: number\n  /**\n   * Renders some debug text on the canvas.\n   * @default false\n   */\n  debug: boolean\n  /**\n   * A Rect defining the area where the confetti will spawn.\n   * @default {\n   *   x: 0,\n   *   y: 0,\n   *   w: canvas.width,\n   *   h: 0\n   * }\n   */\n  confettiSource: IRect\n  /**\n   * Controls the rate at which confetti is spawned.\n   * @default easeInOutQuad\n   */\n  tweenFunction: (\n    currentTime: number,\n    currentValue: number,\n    targetValue: number,\n    duration: number,\n    s?: number,\n  ) => number\n  /**\n   * Number of milliseconds it should take to spawn numberOfPieces.\n   * @default 5000\n   */\n  tweenDuration: number\n  /**\n   * Function to draw your own confetti shapes.\n   */\n  drawShape?: (context: CanvasRenderingContext2D) => void\n  /**\n   * Function called when all confetti has fallen off-canvas.\n   */\n  onConfettiComplete?: (confettiInstance?: Confetti) => void\n}\n\nexport const confettiDefaults: Pick<\n  IConfettiOptions,\n  Exclude<keyof IConfettiOptions, 'confettiSource'>\n> = {\n  width: typeof window !== 'undefined' ? window.innerWidth : 300,\n  height: typeof window !== 'undefined' ? window.innerHeight : 200,\n  numberOfPieces: 200,\n  friction: 0.99,\n  wind: 0,\n  gravity: 0.1,\n  initialVelocityX: 4,\n  initialVelocityY: 10,\n  colors: [\n    '#f44336',\n    '#e91e63',\n    '#9c27b0',\n    '#673ab7',\n    '#3f51b5',\n    '#2196f3',\n    '#03a9f4',\n    '#00bcd4',\n    '#009688',\n    '#4CAF50',\n    '#8BC34A',\n    '#CDDC39',\n    '#FFEB3B',\n    '#FFC107',\n    '#FF9800',\n    '#FF5722',\n    '#795548',\n  ],\n  opacity: 1.0,\n  debug: false,\n  tweenFunction: tweens.easeInOutQuad,\n  tweenDuration: 5000,\n  recycle: true,\n  run: true,\n}\n\nexport class Confetti {\n  constructor(canvas: HTMLCanvasElement, opts: Partial<IConfettiOptions>) {\n    this.canvas = canvas\n    const ctx = this.canvas.getContext('2d')\n    if (!ctx) {\n      throw new Error('Could not get canvas context')\n    }\n    this.context = ctx\n\n    this.generator = new ParticleGenerator(\n      this.canvas,\n      () => this.options as IConfettiOptions,\n    )\n    this.options = opts\n    this.update()\n  }\n\n  canvas: HTMLCanvasElement\n\n  context: CanvasRenderingContext2D\n\n  _options!: IConfettiOptions\n\n  generator: ParticleGenerator\n\n  rafId?: number\n\n  lastFrameTime = 0\n\n  get options(): Partial<IConfettiOptions> {\n    return this._options\n  }\n\n  set options(opts: Partial<IConfettiOptions>) {\n    const lastRunState = this._options?.run\n    const lastRecycleState = this._options?.recycle\n    this.setOptionsWithDefaults(opts)\n    if (this.generator) {\n      Object.assign(this.generator, this.options.confettiSource)\n      if (\n        typeof opts.recycle === 'boolean' &&\n        opts.recycle &&\n        lastRecycleState === false\n      ) {\n        this.generator.lastNumberOfPieces = this.generator.particles.length\n      }\n    }\n    if (typeof opts.run === 'boolean' && opts.run && lastRunState === false) {\n      this.update()\n    }\n  }\n\n  setOptionsWithDefaults = (opts: Partial<IConfettiOptions>) => {\n    const computedConfettiDefaults = {\n      confettiSource: {\n        x: 0,\n        y: 0,\n        w: this.canvas.width,\n        h: 0,\n      },\n    }\n    this._options = {\n      ...computedConfettiDefaults,\n      ...confettiDefaults,\n      ...opts,\n    }\n    Object.assign(this, opts.confettiSource)\n  }\n\n  update = (timestamp = 0) => {\n    const {\n      options: { run, onConfettiComplete, frameRate },\n      canvas,\n      context,\n    } = this\n    // Cap elapsed time to 50ms to prevent large time steps\n    const elapsed = Math.min(timestamp - this.lastFrameTime, 50)\n    // Throttle the frame rate if set\n    if (frameRate && elapsed < 1000 / frameRate) {\n      this.rafId = requestAnimationFrame(this.update)\n      return\n    }\n\n    this.lastFrameTime = timestamp - (frameRate ? elapsed % frameRate : 0)\n\n    if (run) {\n      context.fillStyle = 'white'\n      context.clearRect(0, 0, canvas.width, canvas.height)\n    }\n    if (this.generator.animate(elapsed)) {\n      this.rafId = requestAnimationFrame(this.update)\n    } else {\n      if (\n        onConfettiComplete &&\n        typeof onConfettiComplete === 'function' &&\n        this.generator.particlesGenerated > 0\n      ) {\n        onConfettiComplete.call(this, this)\n      }\n      this._options.run = false\n    }\n  }\n\n  reset = () => {\n    if (this.generator && this.generator.particlesGenerated > 0) {\n      this.generator.particlesGenerated = 0\n      this.generator.particles = []\n      this.generator.lastNumberOfPieces = 0\n    }\n  }\n\n  stop = () => {\n    this.options = { run: false }\n    if (this.rafId) {\n      cancelAnimationFrame(this.rafId)\n      this.rafId = undefined\n    }\n  }\n}\n\nexport default Confetti\n", "import React from 'react'\nimport Confetti, { IConfettiOptions, confettiDefaults } from './Confetti'\n\nexport type { IConfettiOptions } from './Confetti'\n\nconst ref = React.createRef<HTMLCanvasElement>()\n\nexport type Props = Partial<IConfettiOptions> &\n  React.CanvasHTMLAttributes<HTMLCanvasElement> & {\n    canvasRef?: React.Ref<HTMLCanvasElement>\n  }\n\nclass ReactConfettiInternal extends React.Component<Props> {\n  static readonly defaultProps = {\n    ...confettiDefaults,\n  }\n\n  static readonly displayName = 'ReactConfetti'\n\n  constructor(props: Props) {\n    super(props)\n    this.canvas = (props.canvasRef as React.RefObject<HTMLCanvasElement>) || ref\n  }\n\n  canvas: React.RefObject<HTMLCanvasElement | null> = React.createRef()\n\n  confetti?: Confetti\n\n  componentDidMount() {\n    if (this.canvas.current) {\n      const opts = extractCanvasProps(this.props)[0]\n      this.confetti = new Confetti(this.canvas.current, opts)\n    }\n  }\n\n  componentDidUpdate() {\n    const confettiOptions = extractCanvasProps(this.props)[0]\n    if (this.confetti) {\n      this.confetti.options = confettiOptions as IConfettiOptions\n    }\n  }\n\n  componentWillUnmount() {\n    if (this.confetti) {\n      this.confetti.stop()\n    }\n    this.confetti = undefined\n  }\n\n  render() {\n    const [confettiOptions, passedProps] = extractCanvasProps(this.props)\n    const canvasStyles = {\n      zIndex: 2,\n      position: 'absolute' as const,\n      pointerEvents: 'none' as const,\n      top: 0,\n      left: 0,\n      bottom: 0,\n      right: 0,\n      ...passedProps.style,\n    }\n    return (\n      <canvas\n        width={confettiOptions.width}\n        height={confettiOptions.height}\n        ref={this.canvas}\n        {...passedProps}\n        style={canvasStyles}\n      />\n    )\n  }\n}\n\ninterface Refs {\n  [key: string]: React.Ref<HTMLElement>\n}\nfunction extractCanvasProps(\n  props: Partial<IConfettiOptions> | any,\n): [\n  Partial<IConfettiOptions>,\n  Partial<React.CanvasHTMLAttributes<HTMLCanvasElement>>,\n  Refs,\n] {\n  const confettiOptions: Partial<IConfettiOptions> = {}\n  const refs: Refs = {}\n  const rest: any = {}\n  const confettiOptionKeys = [\n    ...Object.keys(confettiDefaults),\n    'confettiSource',\n    'drawShape',\n    'onConfettiComplete',\n    'frameRate',\n  ]\n  const refProps = ['canvasRef']\n  for (const prop in props) {\n    const val = props[prop as string]\n    if (confettiOptionKeys.includes(prop)) {\n      confettiOptions[prop as keyof IConfettiOptions] = val\n    } else if (refProps.includes(prop)) {\n      refProps[prop as any] = val\n    } else {\n      rest[prop] = val\n    }\n  }\n  return [confettiOptions, rest, refs]\n}\n\nexport const ReactConfetti = React.forwardRef<HTMLCanvasElement, Props>(\n  (props, ref) => <ReactConfettiInternal canvasRef={ref} {...props} />,\n)\n\nexport default ReactConfetti\n"], "names": ["tweens", "_jsx"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AA4EM,SAAU,aAAa,CAAC,OAAe,EAAA;IAC3C,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC,EAAE,IAAI,GAAG;AAClC;AAMgB,SAAA,WAAW,CAAC,GAAW,EAAE,GAAW,EAAA;AAClD,IAAA,OAAO,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE,IAAI,GAAG,GAAG,GAAG,CAAC;AAC1C;AAEgB,SAAA,SAAS,CAAC,GAAW,EAAE,GAAW,EAAA;AAChD,IAAA,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE,IAAI,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC;AAC1D;;ACvFA,IAAY,aAIX;AAJD,CAAA,UAAY,aAAa,EAAA;AACvB,IAAA,aAAA,CAAA,aAAA,CAAA,QAAA,CAAA,GAAA,CAAA,CAAA,GAAA,QAAU;AACV,IAAA,aAAA,CAAA,aAAA,CAAA,QAAA,CAAA,GAAA,CAAA,CAAA,GAAA,QAAU;AACV,IAAA,aAAA,CAAA,aAAA,CAAA,OAAA,CAAA,GAAA,CAAA,CAAA,GAAA,OAAS;AACX,CAAC,EAJW,aAAa,KAAb,aAAa,GAIxB,EAAA,CAAA,CAAA;AAED,IAAK,iBAGJ;AAHD,CAAA,UAAK,iBAAiB,EAAA;AACpB,IAAA,iBAAA,CAAA,iBAAA,CAAA,UAAA,CAAA,GAAA,CAAA,CAAA,GAAA,UAAY;AACZ,IAAA,iBAAA,CAAA,iBAAA,CAAA,UAAA,CAAA,GAAA,EAAA,CAAA,GAAA,UAAa;AACf,CAAC,EAHI,iBAAiB,KAAjB,iBAAiB,GAGrB,EAAA,CAAA,CAAA;AAED,MAAM,kBAAkB,GAAG,IAAI,GAAG,EAAE;AAEtB,MAAO,QAAQ,CAAA;AAC3B,IAAA,WAAA,CACE,OAAiC,EACjC,UAAkC,EAClC,CAAS,EACT,CAAS,EAAA;AAET,QAAA,IAAI,CAAC,UAAU,GAAG,UAAU;AAC5B,QAAA,MAAM,EAAE,MAAM,EAAE,gBAAgB,EAAE,gBAAgB,EAAE,GAAG,IAAI,CAAC,UAAU,EAAE;AACxE,QAAA,IAAI,CAAC,OAAO,GAAG,OAAO;AACtB,QAAA,IAAI,CAAC,CAAC,GAAG,CAAC;AACV,QAAA,IAAI,CAAC,CAAC,GAAG,CAAC;QACV,IAAI,CAAC,CAAC,GAAG,WAAW,CAAC,CAAC,EAAE,EAAE,CAAC;QAC3B,IAAI,CAAC,CAAC,GAAG,WAAW,CAAC,CAAC,EAAE,EAAE,CAAC;QAC3B,IAAI,CAAC,MAAM,GAAG,WAAW,CAAC,CAAC,EAAE,EAAE,CAAC;AAChC,QAAA,IAAI,CAAC,EAAE;YACL,OAAO,gBAAgB,KAAK;AAC1B,kBAAE,WAAW,CAAC,CAAC,gBAAgB,EAAE,gBAAgB;kBAC/C,WAAW,CAAC,gBAAgB,CAAC,GAAG,EAAE,gBAAgB,CAAC,GAAG,CAAC;AAC7D,QAAA,IAAI,CAAC,EAAE;YACL,OAAO,gBAAgB,KAAK;AAC1B,kBAAE,WAAW,CAAC,CAAC,gBAAgB,EAAE,CAAC;kBAChC,WAAW,CAAC,gBAAgB,CAAC,GAAG,EAAE,gBAAgB,CAAC,GAAG,CAAC;QAC7D,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;AAC5B,QAAA,IAAI,CAAC,KAAK,GAAG,aAAa,CAAC,WAAW,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;QAC/C,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC,IAAI,EAAE,GAAG,CAAC;AACzC,QAAA,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC;QAC9D,IAAI,CAAC,OAAO,GAAG,WAAW,CAAC,CAAC,EAAE,CAAC,CAAC;QAChC,IAAI,CAAC,iBAAiB,GAAG,WAAW,CAAC,CAAC,EAAE,CAAC;cACrC,iBAAiB,CAAC;AACpB,cAAE,iBAAiB,CAAC,QAAQ;;AAkChC,IAAA,MAAM,CAAC,OAAe,EAAA;AACpB,QAAA,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,SAAS,EAAE,GAAG,IAAI,CAAC,UAAU,EAAE;AACzE,QAAA,MAAM,mBAAmB,GAAG,OAAO,GAAG,kBAAkB;QACxD,IAAI,CAAC,CAAC,IAAI,IAAI,CAAC,EAAE,GAAG,mBAAmB;QACvC,IAAI,CAAC,CAAC,IAAI,IAAI,CAAC,EAAE,GAAG,mBAAmB;AACvC,QAAA,IAAI,CAAC,EAAE,IAAI,OAAO,GAAG,mBAAmB;AACxC,QAAA,IAAI,CAAC,EAAE,IAAI,IAAI,GAAG,mBAAmB;AACrC,QAAA,IAAI,CAAC,EAAE,IAAI,QAAQ,IAAI,mBAAmB;AAC1C,QAAA,IAAI,CAAC,EAAE,IAAI,QAAQ,IAAI,mBAAmB;AAC1C,QAAA,IACE,IAAI,CAAC,OAAO,IAAI,CAAC;AACjB,YAAA,IAAI,CAAC,iBAAiB,KAAK,iBAAiB,CAAC,QAAQ,EACrD;AACA,YAAA,IAAI,CAAC,iBAAiB,GAAG,iBAAiB,CAAC,QAAQ;;AAC9C,aAAA,IACL,IAAI,CAAC,OAAO,IAAI,EAAE;AAClB,YAAA,IAAI,CAAC,iBAAiB,KAAK,iBAAiB,CAAC,QAAQ,EACrD;AACA,YAAA,IAAI,CAAC,iBAAiB,GAAG,iBAAiB,CAAC,QAAQ;;QAGrD,MAAM,WAAW,GAAG,GAAG,GAAG,IAAI,CAAC,iBAAiB,GAAG,mBAAmB;AAEtE,QAAA,IAAI,CAAC,OAAO,IAAI,WAAW;AAC3B,QAAA,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,WAAW;AAC9B,QAAA,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE;AACnB,QAAA,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;QACtC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC;QAC/B,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC;QACnC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC;AAC/B,QAAA,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE;QACxB,IAAI,CAAC,OAAO,CAAC,SAAS,GAAG,IAAI,CAAC,KAAK;QACnC,IAAI,CAAC,OAAO,CAAC,WAAW,GAAG,IAAI,CAAC,KAAK;AACrC,QAAA,IAAI,CAAC,OAAO,CAAC,WAAW,GAAG,OAAO;AAClC,QAAA,IAAI,CAAC,OAAO,CAAC,OAAO,GAAG,OAAO;AAC9B,QAAA,IAAI,CAAC,OAAO,CAAC,SAAS,GAAG,CAAC;AAC1B,QAAA,IAAI,SAAS,IAAI,OAAO,SAAS,KAAK,UAAU,EAAE;YAChD,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC;;aAC7B;AACL,YAAA,QAAQ,IAAI,CAAC,KAAK;AAChB,gBAAA,KAAK,aAAa,CAAC,MAAM,EAAE;AACzB,oBAAA,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE;oBACxB,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC;AACnD,oBAAA,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE;oBACnB;;AAEF,gBAAA,KAAK,aAAa,CAAC,MAAM,EAAE;oBACzB,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;oBAC/D;;AAEF,gBAAA,KAAK,aAAa,CAAC,KAAK,EAAE;AACxB,oBAAA,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;oBACnE;;;;AAIN,QAAA,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE;AACxB,QAAA,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE;;AAEzB;;AC9Ha,MAAO,iBAAiB,CAAA;IACpC,WAAY,CAAA,MAAyB,EAAE,UAAkC,EAAA;QAgBzE,IAAC,CAAA,CAAA,GAAG,CAAC;QAEL,IAAC,CAAA,CAAA,GAAG,CAAC;QAEL,IAAC,CAAA,CAAA,GAAG,CAAC;QAEL,IAAC,CAAA,CAAA,GAAG,CAAC;QAEL,IAAkB,CAAA,kBAAA,GAAG,CAAC;QAEtB,IAAa,CAAA,aAAA,GAAG,CAAC;QAEjB,IAAS,CAAA,SAAA,GAAG,CAAC;QAEb,IAAS,CAAA,SAAA,GAAe,EAAE;QAE1B,IAAkB,CAAA,kBAAA,GAAG,CAAC;AAEtB,QAAA,IAAA,CAAA,gBAAgB,GAAG,CAAC,CAAS,KAAI;YAC/B,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;AAC7B,SAAC;QAED,IAAW,CAAA,WAAA,GAAG,MAAK;AACjB,YAAA,MAAM,YAAY,GAAG,WAAW,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;AACzD,YAAA,MAAM,YAAY,GAAG,WAAW,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;AACzD,YAAA,OAAO,IAAI,QAAQ,CACjB,IAAI,CAAC,OAAO,EACZ,IAAI,CAAC,UAAU,EACf,YAAY,EACZ,YAAY,CACb;AACH,SAAC;AAED,QAAA,IAAA,CAAA,OAAO,GAAG,CAAC,OAAe,KAAa;YACrC,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,kBAAkB,EAAE,kBAAkB,EAAE,GAAG,IAAI;AACxE,YAAA,MAAM,EACJ,GAAG,EACH,OAAO,EACP,cAAc,EACd,KAAK,EACL,aAAa,EACb,aAAa,GACd,GAAG,IAAI,CAAC,UAAU,EAAE;YACrB,IAAI,CAAC,GAAG,EAAE;AACR,gBAAA,OAAO,KAAK;;AAGd,YAAA,MAAM,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM;YAChC,MAAM,WAAW,GAAG,OAAO,GAAG,EAAE,GAAG,kBAAkB;;AAGrD,YAAA,IAAI,WAAW,GAAG,cAAc,EAAE;;AAEhC,gBAAA,IAAI,kBAAkB,KAAK,cAAc,EAAE;AACzC,oBAAA,IAAI,CAAC,aAAa,GAAG,CAAC;AACtB,oBAAA,IAAI,CAAC,SAAS,GAAG,WAAW;AAC5B,oBAAA,IAAI,CAAC,kBAAkB,GAAG,cAAc;;;gBAI1C,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,GAAG,CAC3B,aAAa,EACb,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,aAAa,GAAG,OAAO,CAAC,CAC1C;AACD,gBAAA,MAAM,UAAU,GAAG,aAAa,CAC9B,IAAI,CAAC,aAAa,EAClB,IAAI,CAAC,SAAS,EACd,cAAc,EACd,aAAa,CACd;gBACD,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,WAAW,CAAC;AACrD,gBAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,EAAE,CAAC,EAAE,EAAE;oBACjC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;;AAEzC,gBAAA,IAAI,CAAC,kBAAkB,IAAI,QAAQ;;YAErC,IAAI,KAAK,EAAE;;AAET,gBAAA,OAAO,CAAC,IAAI,GAAG,iBAAiB;AAChC,gBAAA,OAAO,CAAC,SAAS,GAAG,MAAM;AAC1B,gBAAA,OAAO,CAAC,SAAS,GAAG,OAAO;AAC3B,gBAAA,OAAO,CAAC,QAAQ,CACd,cAAc,EAAE,CAAA,CAAE,EAClB,MAAM,CAAC,KAAK,GAAG,EAAE,EACjB,MAAM,CAAC,MAAM,GAAG,EAAE,CACnB;;;AAIH,YAAA,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;gBACnD,MAAM,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;;AAE3B,gBAAA,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC;;AAEjB,gBAAA,IACE,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM;AACnB,oBAAA,CAAC,CAAC,CAAC,GAAG,IAAI;AACV,oBAAA,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,KAAK,GAAG,GAAG;AACxB,oBAAA,CAAC,CAAC,CAAC,GAAG,IAAI,EACV;AACA,oBAAA,IAAI,OAAO,IAAI,WAAW,IAAI,cAAc,EAAE;;wBAE5C,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,WAAW,EAAE;;yBACjC;AACL,wBAAA,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC;;;;AAI9B,YAAA,OAAO,EAAE,GAAG,CAAC,IAAI,WAAW,GAAG,cAAc;AAC/C,SAAC;AA5HC,QAAA,IAAI,CAAC,MAAM,GAAG,MAAM;QACpB,MAAM,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC;QACxC,IAAI,CAAC,GAAG,EAAE;AACR,YAAA,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC;;AAEjD,QAAA,IAAI,CAAC,OAAO,GAAG,GAAG;AAClB,QAAA,IAAI,CAAC,UAAU,GAAG,UAAU;;AAuH/B;;AC9BM,MAAM,gBAAgB,GAGzB;AACF,IAAA,KAAK,EAAE,OAAO,MAAM,KAAK,WAAW,GAAG,MAAM,CAAC,UAAU,GAAG,GAAG;AAC9D,IAAA,MAAM,EAAE,OAAO,MAAM,KAAK,WAAW,GAAG,MAAM,CAAC,WAAW,GAAG,GAAG;AAChE,IAAA,cAAc,EAAE,GAAG;AACnB,IAAA,QAAQ,EAAE,IAAI;AACd,IAAA,IAAI,EAAE,CAAC;AACP,IAAA,OAAO,EAAE,GAAG;AACZ,IAAA,gBAAgB,EAAE,CAAC;AACnB,IAAA,gBAAgB,EAAE,EAAE;AACpB,IAAA,MAAM,EAAE;QACN,SAAS;QACT,SAAS;QACT,SAAS;QACT,SAAS;QACT,SAAS;QACT,SAAS;QACT,SAAS;QACT,SAAS;QACT,SAAS;QACT,SAAS;QACT,SAAS;QACT,SAAS;QACT,SAAS;QACT,SAAS;QACT,SAAS;QACT,SAAS;QACT,SAAS;AACV,KAAA;AACD,IAAA,OAAO,EAAE,GAAG;AACZ,IAAA,KAAK,EAAE,KAAK;IACZ,aAAa,EAAEA,iBAAM,CAAC,aAAa;AACnC,IAAA,aAAa,EAAE,IAAI;AACnB,IAAA,OAAO,EAAE,IAAI;AACb,IAAA,GAAG,EAAE,IAAI;CACV;MAEY,QAAQ,CAAA;IACnB,WAAY,CAAA,MAAyB,EAAE,IAA+B,EAAA;QA0BtE,IAAa,CAAA,aAAA,GAAG,CAAC;AAyBjB,QAAA,IAAA,CAAA,sBAAsB,GAAG,CAAC,IAA+B,KAAI;AAC3D,YAAA,MAAM,wBAAwB,GAAG;AAC/B,gBAAA,cAAc,EAAE;AACd,oBAAA,CAAC,EAAE,CAAC;AACJ,oBAAA,CAAC,EAAE,CAAC;AACJ,oBAAA,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK;AACpB,oBAAA,CAAC,EAAE,CAAC;AACL,iBAAA;aACF;YACD,IAAI,CAAC,QAAQ,GAAG;AACd,gBAAA,GAAG,wBAAwB;AAC3B,gBAAA,GAAG,gBAAgB;AACnB,gBAAA,GAAG,IAAI;aACR;YACD,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,cAAc,CAAC;AAC1C,SAAC;AAED,QAAA,IAAA,CAAA,MAAM,GAAG,CAAC,SAAS,GAAG,CAAC,KAAI;AACzB,YAAA,MAAM,EACJ,OAAO,EAAE,EAAE,GAAG,EAAE,kBAAkB,EAAE,SAAS,EAAE,EAC/C,MAAM,EACN,OAAO,GACR,GAAG,IAAI;;AAER,YAAA,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,SAAS,GAAG,IAAI,CAAC,aAAa,EAAE,EAAE,CAAC;;YAE5D,IAAI,SAAS,IAAI,OAAO,GAAG,IAAI,GAAG,SAAS,EAAE;gBAC3C,IAAI,CAAC,KAAK,GAAG,qBAAqB,CAAC,IAAI,CAAC,MAAM,CAAC;gBAC/C;;AAGF,YAAA,IAAI,CAAC,aAAa,GAAG,SAAS,IAAI,SAAS,GAAG,OAAO,GAAG,SAAS,GAAG,CAAC,CAAC;YAEtE,IAAI,GAAG,EAAE;AACP,gBAAA,OAAO,CAAC,SAAS,GAAG,OAAO;AAC3B,gBAAA,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,MAAM,CAAC;;YAEtD,IAAI,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;gBACnC,IAAI,CAAC,KAAK,GAAG,qBAAqB,CAAC,IAAI,CAAC,MAAM,CAAC;;iBAC1C;AACL,gBAAA,IACE,kBAAkB;oBAClB,OAAO,kBAAkB,KAAK,UAAU;AACxC,oBAAA,IAAI,CAAC,SAAS,CAAC,kBAAkB,GAAG,CAAC,EACrC;AACA,oBAAA,kBAAkB,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC;;AAErC,gBAAA,IAAI,CAAC,QAAQ,CAAC,GAAG,GAAG,KAAK;;AAE7B,SAAC;QAED,IAAK,CAAA,KAAA,GAAG,MAAK;AACX,YAAA,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,CAAC,kBAAkB,GAAG,CAAC,EAAE;AAC3D,gBAAA,IAAI,CAAC,SAAS,CAAC,kBAAkB,GAAG,CAAC;AACrC,gBAAA,IAAI,CAAC,SAAS,CAAC,SAAS,GAAG,EAAE;AAC7B,gBAAA,IAAI,CAAC,SAAS,CAAC,kBAAkB,GAAG,CAAC;;AAEzC,SAAC;QAED,IAAI,CAAA,IAAA,GAAG,MAAK;YACV,IAAI,CAAC,OAAO,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE;AAC7B,YAAA,IAAI,IAAI,CAAC,KAAK,EAAE;AACd,gBAAA,oBAAoB,CAAC,IAAI,CAAC,KAAK,CAAC;AAChC,gBAAA,IAAI,CAAC,KAAK,GAAG,SAAS;;AAE1B,SAAC;AAnHC,QAAA,IAAI,CAAC,MAAM,GAAG,MAAM;QACpB,MAAM,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC;QACxC,IAAI,CAAC,GAAG,EAAE;AACR,YAAA,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC;;AAEjD,QAAA,IAAI,CAAC,OAAO,GAAG,GAAG;AAElB,QAAA,IAAI,CAAC,SAAS,GAAG,IAAI,iBAAiB,CACpC,IAAI,CAAC,MAAM,EACX,MAAM,IAAI,CAAC,OAA2B,CACvC;AACD,QAAA,IAAI,CAAC,OAAO,GAAG,IAAI;QACnB,IAAI,CAAC,MAAM,EAAE;;AAef,IAAA,IAAI,OAAO,GAAA;QACT,OAAO,IAAI,CAAC,QAAQ;;IAGtB,IAAI,OAAO,CAAC,IAA+B,EAAA;AACzC,QAAA,MAAM,YAAY,GAAG,IAAI,CAAC,QAAQ,EAAE,GAAG;AACvC,QAAA,MAAM,gBAAgB,GAAG,IAAI,CAAC,QAAQ,EAAE,OAAO;AAC/C,QAAA,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC;AACjC,QAAA,IAAI,IAAI,CAAC,SAAS,EAAE;AAClB,YAAA,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC;AAC1D,YAAA,IACE,OAAO,IAAI,CAAC,OAAO,KAAK,SAAS;AACjC,gBAAA,IAAI,CAAC,OAAO;gBACZ,gBAAgB,KAAK,KAAK,EAC1B;AACA,gBAAA,IAAI,CAAC,SAAS,CAAC,kBAAkB,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,MAAM;;;AAGvE,QAAA,IAAI,OAAO,IAAI,CAAC,GAAG,KAAK,SAAS,IAAI,IAAI,CAAC,GAAG,IAAI,YAAY,KAAK,KAAK,EAAE;YACvE,IAAI,CAAC,MAAM,EAAE;;;AAsElB;;ACtQD,MAAM,GAAG,GAAG,KAAK,CAAC,SAAS,EAAqB;AAOhD,MAAM,qBAAsB,SAAQ,KAAK,CAAC,SAAgB,CAAA;AAOxD,IAAA,WAAA,CAAY,KAAY,EAAA;QACtB,KAAK,CAAC,KAAK,CAAC;AAId,QAAA,IAAA,CAAA,MAAM,GAA8C,KAAK,CAAC,SAAS,EAAE;QAHnE,IAAI,CAAC,MAAM,GAAI,KAAK,CAAC,SAAgD,IAAI,GAAG;;IAO9E,iBAAiB,GAAA;AACf,QAAA,IAAI,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE;YACvB,MAAM,IAAI,GAAG,kBAAkB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AAC9C,YAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,IAAI,CAAC;;;IAI3D,kBAAkB,GAAA;QAChB,MAAM,eAAe,GAAG,kBAAkB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AACzD,QAAA,IAAI,IAAI,CAAC,QAAQ,EAAE;AACjB,YAAA,IAAI,CAAC,QAAQ,CAAC,OAAO,GAAG,eAAmC;;;IAI/D,oBAAoB,GAAA;AAClB,QAAA,IAAI,IAAI,CAAC,QAAQ,EAAE;AACjB,YAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE;;AAEtB,QAAA,IAAI,CAAC,QAAQ,GAAG,SAAS;;IAG3B,MAAM,GAAA;AACJ,QAAA,MAAM,CAAC,eAAe,EAAE,WAAW,CAAC,GAAG,kBAAkB,CAAC,IAAI,CAAC,KAAK,CAAC;AACrE,QAAA,MAAM,YAAY,GAAG;AACnB,YAAA,MAAM,EAAE,CAAC;AACT,YAAA,QAAQ,EAAE,UAAmB;AAC7B,YAAA,aAAa,EAAE,MAAe;AAC9B,YAAA,GAAG,EAAE,CAAC;AACN,YAAA,IAAI,EAAE,CAAC;AACP,YAAA,MAAM,EAAE,CAAC;AACT,YAAA,KAAK,EAAE,CAAC;YACR,GAAG,WAAW,CAAC,KAAK;SACrB;QACD,QACEC,cACE,CAAA,QAAA,EAAA,EAAA,KAAK,EAAE,eAAe,CAAC,KAAK,EAC5B,MAAM,EAAE,eAAe,CAAC,MAAM,EAC9B,GAAG,EAAE,IAAI,CAAC,MAAM,EACZ,GAAA,WAAW,EACf,KAAK,EAAE,YAAY,EACnB,CAAA;;;AAvDU,qBAAA,CAAA,YAAY,GAAG;AAC7B,IAAA,GAAG,gBAAgB;AACpB,CAF2B;AAIZ,qBAAW,CAAA,WAAA,GAAG,eAAH;AA2D7B,SAAS,kBAAkB,CACzB,KAAsC,EAAA;IAMtC,MAAM,eAAe,GAA8B,EAAE;IACrD,MAAM,IAAI,GAAS,EAAE;IACrB,MAAM,IAAI,GAAQ,EAAE;AACpB,IAAA,MAAM,kBAAkB,GAAG;AACzB,QAAA,GAAG,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC;QAChC,gBAAgB;QAChB,WAAW;QACX,oBAAoB;QACpB,WAAW;KACZ;AACD,IAAA,MAAM,QAAQ,GAAG,CAAC,WAAW,CAAC;AAC9B,IAAA,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE;AACxB,QAAA,MAAM,GAAG,GAAG,KAAK,CAAC,IAAc,CAAC;AACjC,QAAA,IAAI,kBAAkB,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;AACrC,YAAA,eAAe,CAAC,IAA8B,CAAC,GAAG,GAAG;;AAChD,aAAA,IAAI,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;AAClC,YAAA,QAAQ,CAAC,IAAW,CAAC,GAAG,GAAG;;aACtB;AACL,YAAA,IAAI,CAAC,IAAI,CAAC,GAAG,GAAG;;;AAGpB,IAAA,OAAO,CAAC,eAAe,EAAE,IAAI,EAAE,IAAI,CAAC;AACtC;AAEa,MAAA,aAAa,GAAG,KAAK,CAAC,UAAU,CAC3C,CAAC,KAAK,EAAE,GAAG,KAAKA,cAAC,CAAA,qBAAqB,EAAC,EAAA,SAAS,EAAE,GAAG,EAAM,GAAA,KAAK,EAAI,CAAA;;;;"}