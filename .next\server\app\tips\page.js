(()=>{var e={};e.id=273,e.ids=[273],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12304:(e,r,t)=>{"use strict";t.d(r,{Separator:()=>o});let o=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call Separator() from the server but Separator is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Programming\\BudgetWise\\src\\components\\ui\\separator.tsx","Separator")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},39907:(e,r,t)=>{"use strict";t.d(r,{Separator:()=>c});var o=t(60687),n=t(43210),s=t(14163),a="horizontal",i=["horizontal","vertical"],l=n.forwardRef((e,r)=>{var t;let{decorative:n,orientation:l=a,...d}=e,c=(t=l,i.includes(t))?l:a;return(0,o.jsx)(s.sG.div,{"data-orientation":c,...n?{role:"none"}:{"aria-orientation":"vertical"===c?c:void 0,role:"separator"},...d,ref:r})});l.displayName="Separator";var d=t(4780);let c=n.forwardRef(({className:e,orientation:r="horizontal",decorative:t=!0,...n},s)=>(0,o.jsx)(l,{ref:s,decorative:t,orientation:r,className:(0,d.cn)("shrink-0 bg-border","horizontal"===r?"h-[1px] w-full":"h-full w-[1px]",e),...n}));c.displayName=l.displayName},44088:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>a.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>u,tree:()=>d});var o=t(65239),n=t(48088),s=t(88170),a=t.n(s),i=t(30893),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);t.d(r,l);let d={children:["",{children:["tips",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,84141)),"C:\\Users\\<USER>\\Desktop\\Programming\\BudgetWise\\src\\app\\tips\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\Users\\<USER>\\Desktop\\Programming\\BudgetWise\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\Programming\\BudgetWise\\src\\app\\tips\\page.tsx"],p={require:t,loadChunk:()=>Promise.resolve()},u=new o.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/tips/page",pathname:"/tips",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},45705:(e,r,t)=>{Promise.resolve().then(t.bind(t,68926)),Promise.resolve().then(t.bind(t,12304))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68926:(e,r,t)=>{"use strict";t.d(r,{default:()=>o});let o=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programming\\\\BudgetWise\\\\src\\\\components\\\\layout\\\\Header.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Programming\\BudgetWise\\src\\components\\layout\\Header.tsx","default")},79551:e=>{"use strict";e.exports=require("url")},84141:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>eA});var o=t(37413),n=t(68926),s=t(61120);let a=e=>{let r=c(e),{conflictingClassGroups:t,conflictingClassGroupModifiers:o}=e;return{getClassGroupId:e=>{let t=e.split("-");return""===t[0]&&1!==t.length&&t.shift(),i(t,r)||d(e)},getConflictingClassGroupIds:(e,r)=>{let n=t[e]||[];return r&&o[e]?[...n,...o[e]]:n}}},i=(e,r)=>{if(0===e.length)return r.classGroupId;let t=e[0],o=r.nextPart.get(t),n=o?i(e.slice(1),o):void 0;if(n)return n;if(0===r.validators.length)return;let s=e.join("-");return r.validators.find(({validator:e})=>e(s))?.classGroupId},l=/^\[(.+)\]$/,d=e=>{if(l.test(e)){let r=l.exec(e)[1],t=r?.substring(0,r.indexOf(":"));if(t)return"arbitrary.."+t}},c=e=>{let{theme:r,classGroups:t}=e,o={nextPart:new Map,validators:[]};for(let e in t)p(t[e],o,e,r);return o},p=(e,r,t,o)=>{e.forEach(e=>{if("string"==typeof e){(""===e?r:u(r,e)).classGroupId=t;return}if("function"==typeof e){if(m(e)){p(e(o),r,t,o);return}r.validators.push({validator:e,classGroupId:t});return}Object.entries(e).forEach(([e,n])=>{p(n,u(r,e),t,o)})})},u=(e,r)=>{let t=e;return r.split("-").forEach(e=>{t.nextPart.has(e)||t.nextPart.set(e,{nextPart:new Map,validators:[]}),t=t.nextPart.get(e)}),t},m=e=>e.isThemeGetter,f=e=>{if(e<1)return{get:()=>void 0,set:()=>{}};let r=0,t=new Map,o=new Map,n=(n,s)=>{t.set(n,s),++r>e&&(r=0,o=t,t=new Map)};return{get(e){let r=t.get(e);return void 0!==r?r:void 0!==(r=o.get(e))?(n(e,r),r):void 0},set(e,r){t.has(e)?t.set(e,r):n(e,r)}}},b=e=>{let{prefix:r,experimentalParseClassName:t}=e,o=e=>{let r;let t=[],o=0,n=0,s=0;for(let a=0;a<e.length;a++){let i=e[a];if(0===o&&0===n){if(":"===i){t.push(e.slice(s,a)),s=a+1;continue}if("/"===i){r=a;continue}}"["===i?o++:"]"===i?o--:"("===i?n++:")"===i&&n--}let a=0===t.length?e:e.substring(s),i=g(a);return{modifiers:t,hasImportantModifier:i!==a,baseClassName:i,maybePostfixModifierPosition:r&&r>s?r-s:void 0}};if(r){let e=r+":",t=o;o=r=>r.startsWith(e)?t(r.substring(e.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:r,maybePostfixModifierPosition:void 0}}if(t){let e=o;o=r=>t({className:r,parseClassName:e})}return o},g=e=>e.endsWith("!")?e.substring(0,e.length-1):e.startsWith("!")?e.substring(1):e,h=e=>{let r=Object.fromEntries(e.orderSensitiveModifiers.map(e=>[e,!0]));return e=>{if(e.length<=1)return e;let t=[],o=[];return e.forEach(e=>{"["===e[0]||r[e]?(t.push(...o.sort(),e),o=[]):o.push(e)}),t.push(...o.sort()),t}},x=e=>({cache:f(e.cacheSize),parseClassName:b(e),sortModifiers:h(e),...a(e)}),v=/\s+/,y=(e,r)=>{let{parseClassName:t,getClassGroupId:o,getConflictingClassGroupIds:n,sortModifiers:s}=r,a=[],i=e.trim().split(v),l="";for(let e=i.length-1;e>=0;e-=1){let r=i[e],{isExternal:d,modifiers:c,hasImportantModifier:p,baseClassName:u,maybePostfixModifierPosition:m}=t(r);if(d){l=r+(l.length>0?" "+l:l);continue}let f=!!m,b=o(f?u.substring(0,m):u);if(!b){if(!f||!(b=o(u))){l=r+(l.length>0?" "+l:l);continue}f=!1}let g=s(c).join(":"),h=p?g+"!":g,x=h+b;if(a.includes(x))continue;a.push(x);let v=n(b,f);for(let e=0;e<v.length;++e){let r=v[e];a.push(h+r)}l=r+(l.length>0?" "+l:l)}return l};function w(){let e,r,t=0,o="";for(;t<arguments.length;)(e=arguments[t++])&&(r=k(e))&&(o&&(o+=" "),o+=r);return o}let k=e=>{let r;if("string"==typeof e)return e;let t="";for(let o=0;o<e.length;o++)e[o]&&(r=k(e[o]))&&(t&&(t+=" "),t+=r);return t},z=e=>{let r=r=>r[e]||[];return r.isThemeGetter=!0,r},j=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,N=/^\((?:(\w[\w-]*):)?(.+)\)$/i,C=/^\d+\/\d+$/,P=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,A=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,R=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,M=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,S=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,B=e=>C.test(e),E=e=>!!e&&!Number.isNaN(Number(e)),G=e=>!!e&&Number.isInteger(Number(e)),_=e=>e.endsWith("%")&&E(e.slice(0,-1)),W=e=>P.test(e),q=()=>!0,I=e=>A.test(e)&&!R.test(e),T=()=>!1,$=e=>M.test(e),D=e=>S.test(e),U=e=>!L(e)&&!Z(e),H=e=>eo(e,ed,T),L=e=>j.test(e),O=e=>eo(e,ec,I),F=e=>eo(e,ep,E),Y=e=>eo(e,es,T),K=e=>eo(e,ei,D),X=e=>eo(e,T,$),Z=e=>N.test(e),J=e=>en(e,ec),Q=e=>en(e,eu),V=e=>en(e,es),ee=e=>en(e,ed),er=e=>en(e,ei),et=e=>en(e,em,!0),eo=(e,r,t)=>{let o=j.exec(e);return!!o&&(o[1]?r(o[1]):t(o[2]))},en=(e,r,t=!1)=>{let o=N.exec(e);return!!o&&(o[1]?r(o[1]):t)},es=e=>"position"===e,ea=new Set(["image","url"]),ei=e=>ea.has(e),el=new Set(["length","size","percentage"]),ed=e=>el.has(e),ec=e=>"length"===e,ep=e=>"number"===e,eu=e=>"family-name"===e,em=e=>"shadow"===e;Symbol.toStringTag;let ef=function(e,...r){let t,o,n;let s=function(i){return o=(t=x(r.reduce((e,r)=>r(e),e()))).cache.get,n=t.cache.set,s=a,a(i)};function a(e){let r=o(e);if(r)return r;let s=y(e,t);return n(e,s),s}return function(){return s(w.apply(null,arguments))}}(()=>{let e=z("color"),r=z("font"),t=z("text"),o=z("font-weight"),n=z("tracking"),s=z("leading"),a=z("breakpoint"),i=z("container"),l=z("spacing"),d=z("radius"),c=z("shadow"),p=z("inset-shadow"),u=z("drop-shadow"),m=z("blur"),f=z("perspective"),b=z("aspect"),g=z("ease"),h=z("animate"),x=()=>["auto","avoid","all","avoid-page","page","left","right","column"],v=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],y=()=>["auto","hidden","clip","visible","scroll"],w=()=>["auto","contain","none"],k=()=>[B,"px","full","auto",Z,L,l],j=()=>[G,"none","subgrid",Z,L],N=()=>["auto",{span:["full",G,Z,L]},Z,L],C=()=>[G,"auto",Z,L],P=()=>["auto","min","max","fr",Z,L],A=()=>[Z,L,l],R=()=>["start","end","center","between","around","evenly","stretch","baseline"],M=()=>["start","end","center","stretch"],S=()=>[Z,L,l],I=()=>["px",...S()],T=()=>["px","auto",...S()],$=()=>[B,"auto","px","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",Z,L,l],D=()=>[e,Z,L],eo=()=>[_,O],en=()=>["","none","full",d,Z,L],es=()=>["",E,J,O],ea=()=>["solid","dashed","dotted","double"],ei=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],el=()=>["","none",m,Z,L],ed=()=>["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",Z,L],ec=()=>["none",E,Z,L],ep=()=>["none",E,Z,L],eu=()=>[E,Z,L],em=()=>[B,"full","px",Z,L,l];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[W],breakpoint:[W],color:[q],container:[W],"drop-shadow":[W],ease:["in","out","in-out"],font:[U],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[W],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[W],shadow:[W],spacing:[E],text:[W],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",B,L,Z,b]}],container:["container"],columns:[{columns:[E,L,Z,i]}],"break-after":[{"break-after":x()}],"break-before":[{"break-before":x()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[...v(),L,Z]}],overflow:[{overflow:y()}],"overflow-x":[{"overflow-x":y()}],"overflow-y":[{"overflow-y":y()}],overscroll:[{overscroll:w()}],"overscroll-x":[{"overscroll-x":w()}],"overscroll-y":[{"overscroll-y":w()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:k()}],"inset-x":[{"inset-x":k()}],"inset-y":[{"inset-y":k()}],start:[{start:k()}],end:[{end:k()}],top:[{top:k()}],right:[{right:k()}],bottom:[{bottom:k()}],left:[{left:k()}],visibility:["visible","invisible","collapse"],z:[{z:[G,"auto",Z,L]}],basis:[{basis:[B,"full","auto",Z,L,i,l]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[E,B,"auto","initial","none",L]}],grow:[{grow:["",E,Z,L]}],shrink:[{shrink:["",E,Z,L]}],order:[{order:[G,"first","last","none",Z,L]}],"grid-cols":[{"grid-cols":j()}],"col-start-end":[{col:N()}],"col-start":[{"col-start":C()}],"col-end":[{"col-end":C()}],"grid-rows":[{"grid-rows":j()}],"row-start-end":[{row:N()}],"row-start":[{"row-start":C()}],"row-end":[{"row-end":C()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":P()}],"auto-rows":[{"auto-rows":P()}],gap:[{gap:A()}],"gap-x":[{"gap-x":A()}],"gap-y":[{"gap-y":A()}],"justify-content":[{justify:[...R(),"normal"]}],"justify-items":[{"justify-items":[...M(),"normal"]}],"justify-self":[{"justify-self":["auto",...M()]}],"align-content":[{content:["normal",...R()]}],"align-items":[{items:[...M(),"baseline"]}],"align-self":[{self:["auto",...M(),"baseline"]}],"place-content":[{"place-content":R()}],"place-items":[{"place-items":[...M(),"baseline"]}],"place-self":[{"place-self":["auto",...M()]}],p:[{p:I()}],px:[{px:I()}],py:[{py:I()}],ps:[{ps:I()}],pe:[{pe:I()}],pt:[{pt:I()}],pr:[{pr:I()}],pb:[{pb:I()}],pl:[{pl:I()}],m:[{m:T()}],mx:[{mx:T()}],my:[{my:T()}],ms:[{ms:T()}],me:[{me:T()}],mt:[{mt:T()}],mr:[{mr:T()}],mb:[{mb:T()}],ml:[{ml:T()}],"space-x":[{"space-x":S()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":S()}],"space-y-reverse":["space-y-reverse"],size:[{size:$()}],w:[{w:[i,"screen",...$()]}],"min-w":[{"min-w":[i,"screen","none",...$()]}],"max-w":[{"max-w":[i,"screen","none","prose",{screen:[a]},...$()]}],h:[{h:["screen",...$()]}],"min-h":[{"min-h":["screen","none",...$()]}],"max-h":[{"max-h":["screen",...$()]}],"font-size":[{text:["base",t,J,O]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[o,Z,F]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",_,L]}],"font-family":[{font:[Q,L,r]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[n,Z,L]}],"line-clamp":[{"line-clamp":[E,"none",Z,F]}],leading:[{leading:[Z,L,s,l]}],"list-image":[{"list-image":["none",Z,L]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",Z,L]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:D()}],"text-color":[{text:D()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...ea(),"wavy"]}],"text-decoration-thickness":[{decoration:[E,"from-font","auto",Z,O]}],"text-decoration-color":[{decoration:D()}],"underline-offset":[{"underline-offset":[E,"auto",Z,L]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:["px",...S()]}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",Z,L]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",Z,L]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[...v(),V,Y]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","space","round"]}]}],"bg-size":[{bg:["auto","cover","contain",ee,H]}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},G,Z,L],radial:["",Z,L],conic:[G,Z,L]},er,K]}],"bg-color":[{bg:D()}],"gradient-from-pos":[{from:eo()}],"gradient-via-pos":[{via:eo()}],"gradient-to-pos":[{to:eo()}],"gradient-from":[{from:D()}],"gradient-via":[{via:D()}],"gradient-to":[{to:D()}],rounded:[{rounded:en()}],"rounded-s":[{"rounded-s":en()}],"rounded-e":[{"rounded-e":en()}],"rounded-t":[{"rounded-t":en()}],"rounded-r":[{"rounded-r":en()}],"rounded-b":[{"rounded-b":en()}],"rounded-l":[{"rounded-l":en()}],"rounded-ss":[{"rounded-ss":en()}],"rounded-se":[{"rounded-se":en()}],"rounded-ee":[{"rounded-ee":en()}],"rounded-es":[{"rounded-es":en()}],"rounded-tl":[{"rounded-tl":en()}],"rounded-tr":[{"rounded-tr":en()}],"rounded-br":[{"rounded-br":en()}],"rounded-bl":[{"rounded-bl":en()}],"border-w":[{border:es()}],"border-w-x":[{"border-x":es()}],"border-w-y":[{"border-y":es()}],"border-w-s":[{"border-s":es()}],"border-w-e":[{"border-e":es()}],"border-w-t":[{"border-t":es()}],"border-w-r":[{"border-r":es()}],"border-w-b":[{"border-b":es()}],"border-w-l":[{"border-l":es()}],"divide-x":[{"divide-x":es()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":es()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...ea(),"hidden","none"]}],"divide-style":[{divide:[...ea(),"hidden","none"]}],"border-color":[{border:D()}],"border-color-x":[{"border-x":D()}],"border-color-y":[{"border-y":D()}],"border-color-s":[{"border-s":D()}],"border-color-e":[{"border-e":D()}],"border-color-t":[{"border-t":D()}],"border-color-r":[{"border-r":D()}],"border-color-b":[{"border-b":D()}],"border-color-l":[{"border-l":D()}],"divide-color":[{divide:D()}],"outline-style":[{outline:[...ea(),"none","hidden"]}],"outline-offset":[{"outline-offset":[E,Z,L]}],"outline-w":[{outline:["",E,J,O]}],"outline-color":[{outline:[e]}],shadow:[{shadow:["","none",c,et,X]}],"shadow-color":[{shadow:D()}],"inset-shadow":[{"inset-shadow":["none",Z,L,p]}],"inset-shadow-color":[{"inset-shadow":D()}],"ring-w":[{ring:es()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:D()}],"ring-offset-w":[{"ring-offset":[E,O]}],"ring-offset-color":[{"ring-offset":D()}],"inset-ring-w":[{"inset-ring":es()}],"inset-ring-color":[{"inset-ring":D()}],opacity:[{opacity:[E,Z,L]}],"mix-blend":[{"mix-blend":[...ei(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":ei()}],filter:[{filter:["","none",Z,L]}],blur:[{blur:el()}],brightness:[{brightness:[E,Z,L]}],contrast:[{contrast:[E,Z,L]}],"drop-shadow":[{"drop-shadow":["","none",u,Z,L]}],grayscale:[{grayscale:["",E,Z,L]}],"hue-rotate":[{"hue-rotate":[E,Z,L]}],invert:[{invert:["",E,Z,L]}],saturate:[{saturate:[E,Z,L]}],sepia:[{sepia:["",E,Z,L]}],"backdrop-filter":[{"backdrop-filter":["","none",Z,L]}],"backdrop-blur":[{"backdrop-blur":el()}],"backdrop-brightness":[{"backdrop-brightness":[E,Z,L]}],"backdrop-contrast":[{"backdrop-contrast":[E,Z,L]}],"backdrop-grayscale":[{"backdrop-grayscale":["",E,Z,L]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[E,Z,L]}],"backdrop-invert":[{"backdrop-invert":["",E,Z,L]}],"backdrop-opacity":[{"backdrop-opacity":[E,Z,L]}],"backdrop-saturate":[{"backdrop-saturate":[E,Z,L]}],"backdrop-sepia":[{"backdrop-sepia":["",E,Z,L]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":S()}],"border-spacing-x":[{"border-spacing-x":S()}],"border-spacing-y":[{"border-spacing-y":S()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",Z,L]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[E,"initial",Z,L]}],ease:[{ease:["linear","initial",g,Z,L]}],delay:[{delay:[E,Z,L]}],animate:[{animate:["none",h,Z,L]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[f,Z,L]}],"perspective-origin":[{"perspective-origin":ed()}],rotate:[{rotate:ec()}],"rotate-x":[{"rotate-x":ec()}],"rotate-y":[{"rotate-y":ec()}],"rotate-z":[{"rotate-z":ec()}],scale:[{scale:ep()}],"scale-x":[{"scale-x":ep()}],"scale-y":[{"scale-y":ep()}],"scale-z":[{"scale-z":ep()}],"scale-3d":["scale-3d"],skew:[{skew:eu()}],"skew-x":[{"skew-x":eu()}],"skew-y":[{"skew-y":eu()}],transform:[{transform:[Z,L,"","none","gpu","cpu"]}],"transform-origin":[{origin:ed()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:em()}],"translate-x":[{"translate-x":em()}],"translate-y":[{"translate-y":em()}],"translate-z":[{"translate-z":em()}],"translate-none":["translate-none"],accent:[{accent:D()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:D()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",Z,L]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":S()}],"scroll-mx":[{"scroll-mx":S()}],"scroll-my":[{"scroll-my":S()}],"scroll-ms":[{"scroll-ms":S()}],"scroll-me":[{"scroll-me":S()}],"scroll-mt":[{"scroll-mt":S()}],"scroll-mr":[{"scroll-mr":S()}],"scroll-mb":[{"scroll-mb":S()}],"scroll-ml":[{"scroll-ml":S()}],"scroll-p":[{"scroll-p":S()}],"scroll-px":[{"scroll-px":S()}],"scroll-py":[{"scroll-py":S()}],"scroll-ps":[{"scroll-ps":S()}],"scroll-pe":[{"scroll-pe":S()}],"scroll-pt":[{"scroll-pt":S()}],"scroll-pr":[{"scroll-pr":S()}],"scroll-pb":[{"scroll-pb":S()}],"scroll-pl":[{"scroll-pl":S()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",Z,L]}],fill:[{fill:["none",...D()]}],"stroke-w":[{stroke:[E,J,O,F]}],stroke:[{stroke:["none",...D()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["before","after","placeholder","file","marker","selection","first-line","first-letter","backdrop","*","**"]}});function eb(...e){return ef(function(){for(var e,r,t=0,o="",n=arguments.length;t<n;t++)(e=arguments[t])&&(r=function e(r){var t,o,n="";if("string"==typeof r||"number"==typeof r)n+=r;else if("object"==typeof r){if(Array.isArray(r)){var s=r.length;for(t=0;t<s;t++)r[t]&&(o=e(r[t]))&&(n&&(n+=" "),n+=o)}else for(o in r)r[o]&&(n&&(n+=" "),n+=o)}return n}(e))&&(o&&(o+=" "),o+=r);return o}(e))}let eg=s.forwardRef(({className:e,...r},t)=>(0,o.jsx)("div",{ref:t,className:eb("rounded-lg border bg-card text-card-foreground shadow-sm",e),...r}));eg.displayName="Card";let eh=s.forwardRef(({className:e,...r},t)=>(0,o.jsx)("div",{ref:t,className:eb("flex flex-col space-y-1.5 p-6",e),...r}));eh.displayName="CardHeader";let ex=s.forwardRef(({className:e,...r},t)=>(0,o.jsx)("div",{ref:t,className:eb("text-2xl font-semibold leading-none tracking-tight",e),...r}));ex.displayName="CardTitle",s.forwardRef(({className:e,...r},t)=>(0,o.jsx)("div",{ref:t,className:eb("text-sm text-muted-foreground",e),...r})).displayName="CardDescription";let ev=s.forwardRef(({className:e,...r},t)=>(0,o.jsx)("div",{ref:t,className:eb("p-6 pt-0",e),...r}));ev.displayName="CardContent",s.forwardRef(({className:e,...r},t)=>(0,o.jsx)("div",{ref:t,className:eb("flex items-center p-6 pt-0",e),...r})).displayName="CardFooter";let ey=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),ew=(...e)=>e.filter((e,r,t)=>!!e&&""!==e.trim()&&t.indexOf(e)===r).join(" ").trim();var ek={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let ez=(0,s.forwardRef)(({color:e="currentColor",size:r=24,strokeWidth:t=2,absoluteStrokeWidth:o,className:n="",children:a,iconNode:i,...l},d)=>(0,s.createElement)("svg",{ref:d,...ek,width:r,height:r,stroke:e,strokeWidth:o?24*Number(t)/Number(r):t,className:ew("lucide",n),...l},[...i.map(([e,r])=>(0,s.createElement)(e,r)),...Array.isArray(a)?a:[a]])),ej=((e,r)=>{let t=(0,s.forwardRef)(({className:t,...o},n)=>(0,s.createElement)(ez,{ref:n,iconNode:r,className:ew(`lucide-${ey(e)}`,t),...o}));return t.displayName=`${e}`,t})("Lightbulb",[["path",{d:"M15 14c.2-1 .7-1.7 1.5-2.5 1-.9 1.5-2.2 1.5-3.5A6 6 0 0 0 6 8c0 1 .2 2.2 1.5 ******* 1.3 1.5 1.5 2.5",key:"1gvzjb"}],["path",{d:"M9 18h6",key:"x1upvd"}],["path",{d:"M10 22h4",key:"ceow96"}]]);function eN({title:e,description:r}){return(0,o.jsxs)(eg,{className:"shadow-sm hover:shadow-md transition-shadow duration-200",children:[(0,o.jsx)(eh,{className:"pb-2",children:(0,o.jsxs)(ex,{className:"text-base flex items-center gap-2 font-headline",children:[(0,o.jsx)(ej,{className:"h-4 w-4 text-accent"}),e]})}),(0,o.jsx)(ev,{children:(0,o.jsx)("p",{className:"text-sm text-muted-foreground",children:r})})]})}var eC=t(12304);let eP=[{title:"Create a Realistic Budget",description:"Track your income and expenses for a month to understand your spending habits. Use this information to create a budget that reflects your actual lifestyle, making it easier to stick to."},{title:"The 50/30/20 Rule",description:"Allocate 50% of your income to needs (housing, food, transport), 30% to wants (entertainment, hobbies), and 20% to savings and debt repayment. Adjust percentages as needed."},{title:"Set Financial Goals",description:"Define short-term (e.g., emergency fund) and long-term (e.g., retirement, house down payment) goals. Having clear goals provides motivation to manage your money effectively."},{title:"Automate Your Savings",description:"Set up automatic transfers from your checking account to your savings account each payday. Treating savings like a bill ensures you consistently put money aside."},{title:"Review Your Budget Regularly",description:"Life changes, and so should your budget. Review it monthly or quarterly to make adjustments for new income, expenses, or financial goals."},{title:"Cut Unnecessary Expenses",description:"Identify non-essential spending like unused subscriptions or frequent dining out. Reducing these can free up significant cash for savings or debt."},{title:"Plan for Irregular Expenses",description:"Don't forget annual or semi-annual bills like insurance premiums, car registration, or holiday gifts. Divide these by 12 and save that amount monthly."},{title:"Use Cash for Certain Categories",description:"For categories where you tend to overspend (like entertainment or dining out), try the envelope system. Withdraw a set amount of cash for that category and stop spending when it's gone."},{title:"Build an Emergency Fund",description:"Aim to save 3-6 months' worth of living expenses in an easily accessible account. This fund can cover unexpected job loss, medical bills, or urgent repairs without derailing your budget."},{title:"Track Your Spending Diligently",description:"Use apps, spreadsheets, or a notebook to record every expense. This awareness helps you see where your money is going and identify areas for improvement."}];function eA(){return(0,o.jsxs)("div",{className:"flex flex-col min-h-screen bg-background",children:[(0,o.jsx)(n.default,{title:"BudgetWise"}),(0,o.jsxs)("main",{className:"flex-grow container mx-auto py-4 sm:py-6 px-2 sm:px-4",children:[(0,o.jsx)("h2",{className:"text-2xl font-headline font-semibold mb-1 text-foreground",children:"Budgeting Tips"}),(0,o.jsx)("p",{className:"text-sm text-muted-foreground mb-4",children:"Helpful advice to manage your finances better."}),(0,o.jsx)(eC.Separator,{className:"mb-6"}),(0,o.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6",children:eP.map((e,r)=>(0,o.jsx)(eN,{title:e.title,description:e.description},r))})]})]})}},87561:(e,r,t)=>{Promise.resolve().then(t.bind(t,85706)),Promise.resolve().then(t.bind(t,39907))}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),o=r.X(0,[447,242,99,330,253,529],()=>t(44088));module.exports=o})();