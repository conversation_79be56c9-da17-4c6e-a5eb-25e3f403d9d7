(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[454],{13896:(e,s,a)=>{"use strict";a.d(s,{A:()=>r});let r=(0,a(40157).A)("UserPlus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]])},19150:(e,s,a)=>{Promise.resolve().then(a.bind(a,96572))},31949:(e,s,a)=>{"use strict";a.d(s,{A:()=>r});let r=(0,a(40157).A)("TriangleAlert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},55365:(e,s,a)=>{"use strict";a.d(s,{Fc:()=>c,TN:()=>o,XL:()=>d});var r=a(95155),t=a(12115),i=a(74466),n=a(59434);let l=(0,i.F)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),c=t.forwardRef((e,s)=>{let{className:a,variant:t,...i}=e;return(0,r.jsx)("div",{ref:s,role:"alert",className:(0,n.cn)(l({variant:t}),a),...i})});c.displayName="Alert";let d=t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,r.jsx)("h5",{ref:s,className:(0,n.cn)("mb-1 font-medium leading-none tracking-tight",a),...t})});d.displayName="AlertTitle";let o=t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,r.jsx)("div",{ref:s,className:(0,n.cn)("text-sm [&_p]:leading-relaxed",a),...t})});o.displayName="AlertDescription"},96572:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>N});var r=a(95155),t=a(12115),i=a(6874),n=a.n(i),l=a(35695),c=a(1978),d=a(90925),o=a(62177),m=a(90221),u=a(55594),x=a(30285),h=a(62523),p=a(85057),f=a(17759),y=a(55365),j=a(31949);let v=u.Ik({email:u.Yj().email({message:"Invalid email address."}),password:u.Yj().min(6,{message:"Password must be at least 6 characters."}),confirmPassword:u.Yj()}).refine(e=>e.password===e.confirmPassword,{message:"Passwords don't match.",path:["confirmPassword"]});function g(){let{signUp:e,error:s,clearError:a}=(0,d.A)(),[i,n]=(0,t.useState)(!1),c=(0,l.useRouter)(),u=(0,o.mN)({resolver:(0,m.u)(v),defaultValues:{email:"",password:"",confirmPassword:""}}),g=async s=>{n(!0),a();let r=await e(s.email,s.password);n(!1),r&&c.push("/")};return(0,r.jsx)(f.lV,{...u,children:(0,r.jsxs)("form",{onSubmit:u.handleSubmit(g),className:"space-y-4",children:[s&&(0,r.jsxs)(y.Fc,{variant:"destructive",children:[(0,r.jsx)(j.A,{className:"h-4 w-4"}),(0,r.jsx)(y.XL,{children:"Registration Failed"}),(0,r.jsx)(y.TN,{children:s})]}),(0,r.jsx)(f.zB,{control:u.control,name:"email",render:e=>{let{field:s}=e;return(0,r.jsxs)(f.eI,{children:[(0,r.jsx)(p.J,{htmlFor:"email",children:"Email"}),(0,r.jsx)(f.MJ,{children:(0,r.jsx)(h.p,{id:"email",type:"email",placeholder:"<EMAIL>",...s})}),(0,r.jsx)(f.C5,{})]})}}),(0,r.jsx)(f.zB,{control:u.control,name:"password",render:e=>{let{field:s}=e;return(0,r.jsxs)(f.eI,{children:[(0,r.jsx)(p.J,{htmlFor:"password",children:"Password"}),(0,r.jsx)(f.MJ,{children:(0,r.jsx)(h.p,{id:"password",type:"password",placeholder:"••••••••",...s})}),(0,r.jsx)(f.C5,{})]})}}),(0,r.jsx)(f.zB,{control:u.control,name:"confirmPassword",render:e=>{let{field:s}=e;return(0,r.jsxs)(f.eI,{children:[(0,r.jsx)(p.J,{htmlFor:"confirmPassword",children:"Confirm Password"}),(0,r.jsx)(f.MJ,{children:(0,r.jsx)(h.p,{id:"confirmPassword",type:"password",placeholder:"••••••••",...s})}),(0,r.jsx)(f.C5,{})]})}}),(0,r.jsx)(x.$,{type:"submit",className:"w-full",disabled:i,children:i?"Creating Account...":"Create Account"})]})})}var w=a(66695),b=a(13896);function N(){let{currentUser:e,loading:s}=(0,d.A)(),a=(0,l.useRouter)();return((0,t.useEffect)(()=>{!s&&e&&a.push("/")},[e,s,a]),s)?(0,r.jsx)("div",{className:"flex justify-center items-center min-h-screen bg-background",children:(0,r.jsxs)(c.P.div,{initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},className:"text-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-muted-foreground",children:"Loading..."})]})}):!s&&e?null:(0,r.jsxs)("div",{className:"flex flex-col items-center justify-center min-h-screen bg-background p-4 relative overflow-hidden",children:[(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-accent/5 via-background to-primary/5"}),(0,r.jsx)(c.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6},className:"relative z-10",children:(0,r.jsxs)(w.Zp,{className:"w-full max-w-md card-hover glass",children:[(0,r.jsxs)(w.aR,{className:"text-center",children:[(0,r.jsx)(c.P.div,{initial:{scale:0},animate:{scale:1},transition:{delay:.2,type:"spring",stiffness:200},className:"flex justify-center items-center mb-4",children:(0,r.jsx)("div",{className:"p-3 rounded-full bg-gradient-to-r from-accent to-accent/80",children:(0,r.jsx)(b.A,{className:"h-12 w-12 text-accent-foreground"})})}),(0,r.jsxs)(c.P.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.3},children:[(0,r.jsx)(w.ZB,{className:"text-2xl font-headline bg-gradient-to-r from-foreground to-foreground/80 bg-clip-text text-transparent",children:"Create Your BudgetWise Account"}),(0,r.jsx)(w.BT,{className:"mt-2",children:"Start managing your finances effectively."})]})]}),(0,r.jsxs)(w.Wu,{className:"space-y-6",children:[(0,r.jsx)(c.P.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.4},children:(0,r.jsx)(g,{})}),(0,r.jsxs)(c.P.p,{initial:{opacity:0},animate:{opacity:1},transition:{delay:.5},className:"text-center text-sm text-muted-foreground",children:["Already have an account?"," ",(0,r.jsx)(n(),{href:"/login",className:"font-medium text-primary hover:underline transition-colors",children:"Sign in"})]})]})]})})]})}}},e=>{var s=s=>e(e.s=s);e.O(0,[73,671,440,521,375,441,684,358],()=>s(19150)),_N_E=e.O()}]);