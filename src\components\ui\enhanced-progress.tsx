'use client';

import * as React from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';

interface EnhancedProgressProps {
  value?: number;
  max?: number;
  className?: string;
  indicatorClassName?: string;
  showValue?: boolean;
  showPercentage?: boolean;
  animated?: boolean;
  variant?: 'default' | 'success' | 'warning' | 'destructive' | 'primary';
  size?: 'sm' | 'md' | 'lg';
  label?: string;
  formatValue?: (value: number, max: number) => string;
}

const variantStyles = {
  default: 'bg-primary',
  success: 'bg-green-500',
  warning: 'bg-yellow-500',
  destructive: 'bg-red-500',
  primary: 'bg-primary',
};

const sizeStyles = {
  sm: 'h-1',
  md: 'h-2',
  lg: 'h-3',
};

export function EnhancedProgress({
  value = 0,
  max = 100,
  className,
  indicatorClassName,
  showValue = false,
  showPercentage = false,
  animated = true,
  variant = 'default',
  size = 'md',
  label,
  formatValue,
  ...props
}: EnhancedProgressProps) {
  const percentage = Math.min(Math.max((value / max) * 100, 0), 100);
  
  const displayValue = formatValue 
    ? formatValue(value, max)
    : showPercentage 
      ? `${Math.round(percentage)}%`
      : `${value}/${max}`;

  return (
    <div className={cn('w-full', className)} {...props}>
      {(label || showValue || showPercentage) && (
        <div className="flex justify-between items-center mb-2">
          {label && (
            <span className="text-sm font-medium text-foreground">{label}</span>
          )}
          {(showValue || showPercentage) && (
            <span className="text-sm text-muted-foreground">{displayValue}</span>
          )}
        </div>
      )}
      
      <div
        className={cn(
          'relative w-full overflow-hidden rounded-full bg-muted',
          sizeStyles[size]
        )}
      >
        {animated ? (
          <motion.div
            className={cn(
              'h-full rounded-full transition-colors',
              variantStyles[variant],
              indicatorClassName
            )}
            initial={{ width: 0 }}
            animate={{ width: `${percentage}%` }}
            transition={{
              duration: 1,
              ease: "easeOut"
            }}
          />
        ) : (
          <div
            className={cn(
              'h-full rounded-full transition-all duration-500 ease-out',
              variantStyles[variant],
              indicatorClassName
            )}
            style={{ width: `${percentage}%` }}
          />
        )}
        
        {/* Shimmer effect for loading states */}
        {animated && percentage > 0 && percentage < 100 && (
          <div
            className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-shimmer"
            style={{
              backgroundSize: '200% 100%',
            }}
          />
        )}
      </div>
    </div>
  );
}

interface CircularProgressProps {
  value?: number;
  max?: number;
  size?: number;
  strokeWidth?: number;
  className?: string;
  variant?: 'default' | 'success' | 'warning' | 'destructive' | 'primary';
  showValue?: boolean;
  showPercentage?: boolean;
  animated?: boolean;
  children?: React.ReactNode;
}

export function CircularProgress({
  value = 0,
  max = 100,
  size = 120,
  strokeWidth = 8,
  className,
  variant = 'default',
  showValue = false,
  showPercentage = true,
  animated = true,
  children,
}: CircularProgressProps) {
  const percentage = Math.min(Math.max((value / max) * 100, 0), 100);
  const radius = (size - strokeWidth) / 2;
  const circumference = radius * 2 * Math.PI;
  const strokeDasharray = circumference;
  const strokeDashoffset = circumference - (percentage / 100) * circumference;

  const strokeColors = {
    default: 'stroke-primary',
    success: 'stroke-green-500',
    warning: 'stroke-yellow-500',
    destructive: 'stroke-red-500',
    primary: 'stroke-primary',
  };

  return (
    <div className={cn('relative inline-flex items-center justify-center', className)}>
      <svg
        width={size}
        height={size}
        className="transform -rotate-90"
      >
        {/* Background circle */}
        <circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          stroke="currentColor"
          strokeWidth={strokeWidth}
          fill="none"
          className="text-muted opacity-20"
        />
        
        {/* Progress circle */}
        {animated ? (
          <motion.circle
            cx={size / 2}
            cy={size / 2}
            r={radius}
            strokeWidth={strokeWidth}
            fill="none"
            strokeDasharray={strokeDasharray}
            strokeLinecap="round"
            className={cn('transition-colors', strokeColors[variant])}
            initial={{ strokeDashoffset: circumference }}
            animate={{ strokeDashoffset }}
            transition={{
              duration: 1.5,
              ease: "easeOut"
            }}
          />
        ) : (
          <circle
            cx={size / 2}
            cy={size / 2}
            r={radius}
            strokeWidth={strokeWidth}
            fill="none"
            strokeDasharray={strokeDasharray}
            strokeDashoffset={strokeDashoffset}
            strokeLinecap="round"
            className={cn('transition-all duration-500 ease-out', strokeColors[variant])}
          />
        )}
      </svg>
      
      {/* Center content */}
      <div className="absolute inset-0 flex items-center justify-center">
        {children || (
          <div className="text-center">
            {showPercentage && (
              <div className="text-2xl font-bold text-foreground">
                {Math.round(percentage)}%
              </div>
            )}
            {showValue && (
              <div className="text-sm text-muted-foreground">
                {value}/{max}
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}

interface ProgressStepsProps {
  steps: Array<{
    label: string;
    completed: boolean;
    current?: boolean;
  }>;
  className?: string;
  variant?: 'default' | 'success' | 'warning' | 'destructive' | 'primary';
}

export function ProgressSteps({ steps, className, variant = 'default' }: ProgressStepsProps) {
  const completedColor = {
    default: 'bg-primary border-primary text-primary-foreground',
    success: 'bg-green-500 border-green-500 text-white',
    warning: 'bg-yellow-500 border-yellow-500 text-white',
    destructive: 'bg-red-500 border-red-500 text-white',
    primary: 'bg-primary border-primary text-primary-foreground',
  };

  const currentColor = {
    default: 'bg-primary/20 border-primary text-primary',
    success: 'bg-green-500/20 border-green-500 text-green-600',
    warning: 'bg-yellow-500/20 border-yellow-500 text-yellow-600',
    destructive: 'bg-red-500/20 border-red-500 text-red-600',
    primary: 'bg-primary/20 border-primary text-primary',
  };

  return (
    <div className={cn('flex items-center', className)}>
      {steps.map((step, index) => (
        <React.Fragment key={index}>
          <motion.div
            initial={{ scale: 0.8, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ delay: index * 0.1 }}
            className="flex flex-col items-center"
          >
            <div
              className={cn(
                'w-8 h-8 rounded-full border-2 flex items-center justify-center text-sm font-medium transition-all duration-300',
                step.completed
                  ? completedColor[variant]
                  : step.current
                  ? currentColor[variant]
                  : 'bg-muted border-muted-foreground/20 text-muted-foreground'
              )}
            >
              {step.completed ? '✓' : index + 1}
            </div>
            <span className={cn(
              'mt-2 text-xs text-center max-w-20',
              step.completed || step.current ? 'text-foreground' : 'text-muted-foreground'
            )}>
              {step.label}
            </span>
          </motion.div>
          
          {index < steps.length - 1 && (
            <div
              className={cn(
                'flex-1 h-0.5 mx-2 transition-colors duration-300',
                steps[index + 1]?.completed || step.completed
                  ? variantStyles[variant].replace('bg-', 'bg-')
                  : 'bg-muted'
              )}
            />
          )}
        </React.Fragment>
      ))}
    </div>
  );
}
