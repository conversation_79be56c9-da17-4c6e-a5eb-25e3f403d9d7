(()=>{var e={};e.id=974,e.ids=[974],e.modules={22:(e,t,r)=>{var n=r(75254),o=r(20623),i=r(48169),a=r(40542),l=r(45058);e.exports=function(e){return"function"==typeof e?e:null==e?i:"object"==typeof e?a(e)?o(e[0],e[1]):n(e):l(e)}},658:(e,t,r)=>{e.exports=r(41547)(r(85718),"Map")},1566:(e,t,r)=>{var n=r(89167),o=r(658),i=r(30401),a=r(34772),l=r(17830),s=r(29395),c=r(12290),u="[object Map]",f="[object Promise]",d="[object Set]",p="[object WeakMap]",h="[object DataView]",y=c(n),m=c(o),v=c(i),g=c(a),b=c(l),x=s;(n&&x(new n(new ArrayBuffer(1)))!=h||o&&x(new o)!=u||i&&x(i.resolve())!=f||a&&x(new a)!=d||l&&x(new l)!=p)&&(x=function(e){var t=s(e),r="[object Object]"==t?e.constructor:void 0,n=r?c(r):"";if(n)switch(n){case y:return h;case m:return u;case v:return f;case g:return d;case b:return p}return t}),e.exports=x},1707:(e,t,r)=>{var n=r(35142),o=r(46436);e.exports=function(e,t){t=n(t,e);for(var r=0,i=t.length;null!=e&&r<i;)e=e[o(t[r++])];return r&&r==i?e:void 0}},1944:e=>{e.exports=function(){return!1}},2408:e=>{e.exports=function(e){var t=-1,r=Array(e.size);return e.forEach(function(e){r[++t]=e}),r}},2896:(e,t,r)=>{var n=r(81488),o=r(59467);e.exports=function(e,t){return null!=e&&o(e,t,n)}},2984:(e,t,r)=>{var n=r(49227);e.exports=function(e,t,r){for(var o=-1,i=e.length;++o<i;){var a=e[o],l=t(a);if(null!=l&&(void 0===s?l==l&&!n(l):r(l,s)))var s=l,c=a}return c}},3105:e=>{e.exports=function(e){return e.split("")}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4999:(e,t,r)=>{e.exports=r(85718).Uint8Array},5231:(e,t,r)=>{var n=r(29395),o=r(55048);e.exports=function(e){if(!o(e))return!1;var t=n(e);return"[object Function]"==t||"[object GeneratorFunction]"==t||"[object AsyncFunction]"==t||"[object Proxy]"==t}},5359:e=>{e.exports=function(e){var t=null==e?0:e.length;return t?e[t-1]:void 0}},5566:(e,t,r)=>{var n=r(41011),o=r(34117),i=r(66713),a=r(42403);e.exports=function(e){return function(t){var r=o(t=a(t))?i(t):void 0,l=r?r[0]:t.charAt(0),s=r?n(r,1).join(""):t.slice(1);return l[e]()+s}}},6053:e=>{var t=/\s/;e.exports=function(e){for(var r=e.length;r--&&t.test(e.charAt(r)););return r}},6330:e=>{e.exports=function(){return[]}},7383:(e,t,r)=>{var n=r(67009),o=r(32269),i=r(38428),a=r(55048);e.exports=function(e,t,r){if(!a(r))return!1;var l=typeof t;return("number"==l?!!(o(r)&&i(t,r.length)):"string"==l&&t in r)&&n(r[t],e)}},7651:(e,t,r)=>{var n=r(82038),o=r(52931),i=r(32269);e.exports=function(e){return i(e)?n(e):o(e)}},8336:(e,t,r)=>{var n=r(45803);e.exports=function(e,t){var r=e.__data__;return n(t)?r["string"==typeof t?"string":"hash"]:r.map}},8852:(e,t,r)=>{var n=r(1707);e.exports=function(e){return function(t){return n(t,e)}}},10034:(e,t,r)=>{var n=r(2984),o=r(22),i=r(46063);e.exports=function(e,t){return e&&e.length?n(e,o(t,2),i):void 0}},10090:(e,t,r)=>{var n=r(80458),o=r(89624),i=r(47282),a=i&&i.isTypedArray;e.exports=a?o(a):n},10653:(e,t,r)=>{var n=r(21456),o=r(63979),i=r(7651);e.exports=function(e){return n(e,i,o)}},10663:e=>{e.exports="object"==typeof global&&global&&global.Object===Object&&global},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11117:e=>{"use strict";var t=Object.prototype.hasOwnProperty,r="~";function n(){}function o(e,t,r){this.fn=e,this.context=t,this.once=r||!1}function i(e,t,n,i,a){if("function"!=typeof n)throw TypeError("The listener must be a function");var l=new o(n,i||e,a),s=r?r+t:t;return e._events[s]?e._events[s].fn?e._events[s]=[e._events[s],l]:e._events[s].push(l):(e._events[s]=l,e._eventsCount++),e}function a(e,t){0==--e._eventsCount?e._events=new n:delete e._events[t]}function l(){this._events=new n,this._eventsCount=0}Object.create&&(n.prototype=Object.create(null),new n().__proto__||(r=!1)),l.prototype.eventNames=function(){var e,n,o=[];if(0===this._eventsCount)return o;for(n in e=this._events)t.call(e,n)&&o.push(r?n.slice(1):n);return Object.getOwnPropertySymbols?o.concat(Object.getOwnPropertySymbols(e)):o},l.prototype.listeners=function(e){var t=r?r+e:e,n=this._events[t];if(!n)return[];if(n.fn)return[n.fn];for(var o=0,i=n.length,a=Array(i);o<i;o++)a[o]=n[o].fn;return a},l.prototype.listenerCount=function(e){var t=r?r+e:e,n=this._events[t];return n?n.fn?1:n.length:0},l.prototype.emit=function(e,t,n,o,i,a){var l=r?r+e:e;if(!this._events[l])return!1;var s,c,u=this._events[l],f=arguments.length;if(u.fn){switch(u.once&&this.removeListener(e,u.fn,void 0,!0),f){case 1:return u.fn.call(u.context),!0;case 2:return u.fn.call(u.context,t),!0;case 3:return u.fn.call(u.context,t,n),!0;case 4:return u.fn.call(u.context,t,n,o),!0;case 5:return u.fn.call(u.context,t,n,o,i),!0;case 6:return u.fn.call(u.context,t,n,o,i,a),!0}for(c=1,s=Array(f-1);c<f;c++)s[c-1]=arguments[c];u.fn.apply(u.context,s)}else{var d,p=u.length;for(c=0;c<p;c++)switch(u[c].once&&this.removeListener(e,u[c].fn,void 0,!0),f){case 1:u[c].fn.call(u[c].context);break;case 2:u[c].fn.call(u[c].context,t);break;case 3:u[c].fn.call(u[c].context,t,n);break;case 4:u[c].fn.call(u[c].context,t,n,o);break;default:if(!s)for(d=1,s=Array(f-1);d<f;d++)s[d-1]=arguments[d];u[c].fn.apply(u[c].context,s)}}return!0},l.prototype.on=function(e,t,r){return i(this,e,t,r,!1)},l.prototype.once=function(e,t,r){return i(this,e,t,r,!0)},l.prototype.removeListener=function(e,t,n,o){var i=r?r+e:e;if(!this._events[i])return this;if(!t)return a(this,i),this;var l=this._events[i];if(l.fn)l.fn!==t||o&&!l.once||n&&l.context!==n||a(this,i);else{for(var s=0,c=[],u=l.length;s<u;s++)(l[s].fn!==t||o&&!l[s].once||n&&l[s].context!==n)&&c.push(l[s]);c.length?this._events[i]=1===c.length?c[0]:c:a(this,i)}return this},l.prototype.removeAllListeners=function(e){var t;return e?(t=r?r+e:e,this._events[t]&&a(this,t)):(this._events=new n,this._eventsCount=0),this},l.prototype.off=l.prototype.removeListener,l.prototype.addListener=l.prototype.on,l.prefixed=r,l.EventEmitter=l,e.exports=l},11424:(e,t,r)=>{var n=r(47603);e.exports=r(66400)(n)},11539:(e,t,r)=>{var n=r(37643),o=r(55048),i=r(49227),a=0/0,l=/^[-+]0x[0-9a-f]+$/i,s=/^0b[01]+$/i,c=/^0o[0-7]+$/i,u=parseInt;e.exports=function(e){if("number"==typeof e)return e;if(i(e))return a;if(o(e)){var t="function"==typeof e.valueOf?e.valueOf():e;e=o(t)?t+"":t}if("string"!=typeof e)return 0===e?e:+e;e=n(e);var r=s.test(e);return r||c.test(e)?u(e.slice(2),r?2:8):l.test(e)?a:+e}},12290:e=>{var t=Function.prototype.toString;e.exports=function(e){if(null!=e){try{return t.call(e)}catch(e){}try{return e+""}catch(e){}}return""}},12344:(e,t,r)=>{e.exports=r(65984)()},14675:e=>{e.exports=function(e){return function(){return e}}},14975:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(82614).A)("TriangleAlert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},15451:(e,t,r)=>{var n=r(29395),o=r(27467);e.exports=function(e){return o(e)&&"[object Arguments]"==n(e)}},15871:(e,t,r)=>{var n=r(36341),o=r(27467);e.exports=function e(t,r,i,a,l){return t===r||(null!=t&&null!=r&&(o(t)||o(r))?n(t,r,i,a,e,l):t!=t&&r!=r)}},15883:(e,t,r)=>{var n=r(2984),o=r(46063),i=r(48169);e.exports=function(e){return e&&e.length?n(e,i,o):void 0}},15909:(e,t,r)=>{var n=r(87506),o=r(66930),i=r(658);e.exports=function(){this.size=0,this.__data__={hash:new n,map:new(i||o),string:new n}}},16854:e=>{e.exports=function(e){return this.__data__.has(e)}},17518:(e,t,r)=>{var n=r(21367),o=r(1707),i=r(22),a=r(54765),l=r(43378),s=r(89624),c=r(65727),u=r(48169),f=r(40542);e.exports=function(e,t,r){t=t.length?n(t,function(e){return f(e)?function(t){return o(t,1===e.length?e[0]:e)}:e}):[u];var d=-1;return t=n(t,s(i)),l(a(e,function(e,r,o){return{criteria:n(t,function(t){return t(e)}),index:++d,value:e}}),function(e,t){return c(e,t,r)})}},17830:(e,t,r)=>{e.exports=r(41547)(r(85718),"WeakMap")},18234:(e,t,r)=>{var n=r(91290),o=r(22),i=r(84482),a=Math.max;e.exports=function(e,t,r){var l=null==e?0:e.length;if(!l)return -1;var s=null==r?0:i(r);return s<0&&(s=a(l+s,0)),n(e,o(t,3),s)}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19976:(e,t,r)=>{var n=r(8336);e.exports=function(e,t){var r=n(this,e),o=r.size;return r.set(e,t),this.size+=+(r.size!=o),this}},20540:(e,t,r)=>{var n=r(55048),o=r(70151),i=r(11539),a=Math.max,l=Math.min;e.exports=function(e,t,r){var s,c,u,f,d,p,h=0,y=!1,m=!1,v=!0;if("function"!=typeof e)throw TypeError("Expected a function");function g(t){var r=s,n=c;return s=c=void 0,h=t,f=e.apply(n,r)}function b(e){var r=e-p,n=e-h;return void 0===p||r>=t||r<0||m&&n>=u}function x(){var e,r,n,i=o();if(b(i))return w(i);d=setTimeout(x,(e=i-p,r=i-h,n=t-e,m?l(n,u-r):n))}function w(e){return(d=void 0,v&&s)?g(e):(s=c=void 0,f)}function j(){var e,r=o(),n=b(r);if(s=arguments,c=this,p=r,n){if(void 0===d)return h=e=p,d=setTimeout(x,t),y?g(e):f;if(m)return clearTimeout(d),d=setTimeout(x,t),g(p)}return void 0===d&&(d=setTimeout(x,t)),f}return t=i(t)||0,n(r)&&(y=!!r.leading,u=(m="maxWait"in r)?a(i(r.maxWait)||0,t):u,v="trailing"in r?!!r.trailing:v),j.cancel=function(){void 0!==d&&clearTimeout(d),h=0,s=p=c=d=void 0},j.flush=function(){return void 0===d?f:w(o())},j}},20623:(e,t,r)=>{var n=r(15871),o=r(40491),i=r(2896),a=r(67619),l=r(34883),s=r(41132),c=r(46436);e.exports=function(e,t){return a(e)&&l(t)?s(c(e),t):function(r){var a=o(r,e);return void 0===a&&a===t?i(r,e):n(t,a,3)}}},21204:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});let n=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programming\\\\BudgetWise\\\\src\\\\app\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Programming\\BudgetWise\\src\\app\\page.tsx","default")},21367:e=>{e.exports=function(e,t){for(var r=-1,n=null==e?0:e.length,o=Array(n);++r<n;)o[r]=t(e[r],r,e);return o}},21456:(e,t,r)=>{var n=r(41693),o=r(40542);e.exports=function(e,t,r){var i=t(e);return o(e)?i:n(i,r(e))}},21592:(e,t,r)=>{var n=r(42205),o=r(61837);e.exports=function(e,t){return n(o(e,t),1)}},21630:(e,t,r)=>{var n=r(10653),o=Object.prototype.hasOwnProperty;e.exports=function(e,t,r,i,a,l){var s=1&r,c=n(e),u=c.length;if(u!=n(t).length&&!s)return!1;for(var f=u;f--;){var d=c[f];if(!(s?d in t:o.call(t,d)))return!1}var p=l.get(e),h=l.get(t);if(p&&h)return p==t&&h==e;var y=!0;l.set(e,t),l.set(t,e);for(var m=s;++f<u;){var v=e[d=c[f]],g=t[d];if(i)var b=s?i(g,v,d,t,e,l):i(v,g,d,e,t,l);if(!(void 0===b?v===g||a(v,g,r,i,l):b)){y=!1;break}m||(m="constructor"==d)}if(y&&!m){var x=e.constructor,w=t.constructor;x!=w&&"constructor"in e&&"constructor"in t&&!("function"==typeof x&&x instanceof x&&"function"==typeof w&&w instanceof w)&&(y=!1)}return l.delete(e),l.delete(t),y}},22964:(e,t,r)=>{e.exports=r(23729)(r(18234))},23729:(e,t,r)=>{var n=r(22),o=r(32269),i=r(7651);e.exports=function(e){return function(t,r,a){var l=Object(t);if(!o(t)){var s=n(r,3);t=i(t),r=function(e){return s(l[e],e,l)}}var c=e(t,r,a);return c>-1?l[s?t[c]:c]:void 0}}},25118:e=>{e.exports=function(e){return this.__data__.has(e)}},27006:(e,t,r)=>{var n=r(46328),o=r(99525),i=r(58276);e.exports=function(e,t,r,a,l,s){var c=1&r,u=e.length,f=t.length;if(u!=f&&!(c&&f>u))return!1;var d=s.get(e),p=s.get(t);if(d&&p)return d==t&&p==e;var h=-1,y=!0,m=2&r?new n:void 0;for(s.set(e,t),s.set(t,e);++h<u;){var v=e[h],g=t[h];if(a)var b=c?a(g,v,h,t,e,s):a(v,g,h,e,t,s);if(void 0!==b){if(b)continue;y=!1;break}if(m){if(!o(t,function(e,t){if(!i(m,t)&&(v===e||l(v,e,r,a,s)))return m.push(t)})){y=!1;break}}else if(!(v===g||l(v,g,r,a,s))){y=!1;break}}return s.delete(e),s.delete(t),y}},27467:e=>{e.exports=function(e){return null!=e&&"object"==typeof e}},27669:e=>{e.exports=function(){this.__data__=[],this.size=0}},28837:(e,t,r)=>{var n=r(57797),o=Array.prototype.splice;e.exports=function(e){var t=this.__data__,r=n(t,e);return!(r<0)&&(r==t.length-1?t.pop():o.call(t,r,1),--this.size,!0)}},28977:(e,t,r)=>{var n=r(11539),o=1/0;e.exports=function(e){return e?(e=n(e))===o||e===-o?(e<0?-1:1)*17976931348623157e292:e==e?e:0:0===e?e:0}},29205:(e,t,r)=>{var n=r(8336);e.exports=function(e){var t=n(this,e).delete(e);return this.size-=+!!t,t}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29395:(e,t,r)=>{var n=r(79474),o=r(70222),i=r(84713),a=n?n.toStringTag:void 0;e.exports=function(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":a&&a in Object(e)?o(e):i(e)}},29508:(e,t,r)=>{var n=r(8336);e.exports=function(e){return n(this,e).get(e)}},29632:(e,t,r)=>{"use strict";e.exports=r(97668)},30316:(e,t,r)=>{var n=r(67554);e.exports=function(e,t){var r=!0;return n(e,function(e,n,o){return r=!!t(e,n,o)}),r}},30401:(e,t,r)=>{e.exports=r(41547)(r(85718),"Promise")},30854:(e,t,r)=>{var n=r(66930),o=r(658),i=r(95746);e.exports=function(e,t){var r=this.__data__;if(r instanceof n){var a=r.__data__;if(!o||a.length<199)return a.push([e,t]),this.size=++r.size,this;r=this.__data__=new i(a)}return r.set(e,t),this.size=r.size,this}},32269:(e,t,r)=>{var n=r(5231),o=r(69619);e.exports=function(e){return null!=e&&o(e.length)&&!n(e)}},33873:e=>{"use strict";e.exports=require("path")},34117:e=>{var t=RegExp("[\\u200d\ud800-\udfff\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff\\ufe0e\\ufe0f]");e.exports=function(e){return t.test(e)}},34452:e=>{"use strict";e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},34746:e=>{e.exports=function(e){return this.__data__.get(e)}},34772:(e,t,r)=>{e.exports=r(41547)(r(85718),"Set")},34821:e=>{e.exports=function(e,t){for(var r=-1,n=Array(e);++r<e;)n[r]=t(r);return n}},34883:(e,t,r)=>{var n=r(55048);e.exports=function(e){return e==e&&!n(e)}},34990:(e,t,r)=>{e.exports=r(87321)()},35142:(e,t,r)=>{var n=r(40542),o=r(67619),i=r(51449),a=r(42403);e.exports=function(e,t){return n(e)?e:o(e,t)?[e]:i(a(e))}},35163:(e,t,r)=>{var n=r(15451),o=r(27467),i=Object.prototype,a=i.hasOwnProperty,l=i.propertyIsEnumerable;e.exports=n(function(){return arguments}())?n:function(e){return o(e)&&a.call(e,"callee")&&!l.call(e,"callee")}},35697:(e,t,r)=>{var n=r(79474),o=r(4999),i=r(67009),a=r(27006),l=r(59774),s=r(2408),c=n?n.prototype:void 0,u=c?c.valueOf:void 0;e.exports=function(e,t,r,n,c,f,d){switch(r){case"[object DataView]":if(e.byteLength!=t.byteLength||e.byteOffset!=t.byteOffset)break;e=e.buffer,t=t.buffer;case"[object ArrayBuffer]":if(e.byteLength!=t.byteLength||!f(new o(e),new o(t)))break;return!0;case"[object Boolean]":case"[object Date]":case"[object Number]":return i(+e,+t);case"[object Error]":return e.name==t.name&&e.message==t.message;case"[object RegExp]":case"[object String]":return e==t+"";case"[object Map]":var p=l;case"[object Set]":var h=1&n;if(p||(p=s),e.size!=t.size&&!h)break;var y=d.get(e);if(y)return y==t;n|=2,d.set(e,t);var m=a(p(e),p(t),n,c,f,d);return d.delete(e),m;case"[object Symbol]":if(u)return u.call(e)==u.call(t)}return!1}},35800:(e,t,r)=>{var n=r(57797);e.exports=function(e){return n(this.__data__,e)>-1}},36315:(e,t,r)=>{var n=r(22),o=r(92662);e.exports=function(e,t){return e&&e.length?o(e,n(t,2)):[]}},36341:(e,t,r)=>{var n=r(67200),o=r(27006),i=r(35697),a=r(21630),l=r(1566),s=r(40542),c=r(80329),u=r(10090),f="[object Arguments]",d="[object Array]",p="[object Object]",h=Object.prototype.hasOwnProperty;e.exports=function(e,t,r,y,m,v){var g=s(e),b=s(t),x=g?d:l(e),w=b?d:l(t);x=x==f?p:x,w=w==f?p:w;var j=x==p,O=w==p,S=x==w;if(S&&c(e)){if(!c(t))return!1;g=!0,j=!1}if(S&&!j)return v||(v=new n),g||u(e)?o(e,t,r,y,m,v):i(e,t,x,r,y,m,v);if(!(1&r)){var A=j&&h.call(e,"__wrapped__"),P=O&&h.call(t,"__wrapped__");if(A||P){var k=A?e.value():e,E=P?t.value():t;return v||(v=new n),m(k,E,r,y,v)}}return!!S&&(v||(v=new n),a(e,t,r,y,m,v))}},36959:e=>{e.exports=function(){}},37456:e=>{e.exports=function(e){return null==e}},37575:(e,t,r)=>{var n=r(66930);e.exports=function(){this.__data__=new n,this.size=0}},37643:(e,t,r)=>{var n=r(6053),o=/^\s+/;e.exports=function(e){return e?e.slice(0,n(e)+1).replace(o,""):e}},37733:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>gb});var n={};r.r(n),r.d(n,{scaleBand:()=>aw,scaleDiverging:()=>function e(){var t=sa(uj()(lJ));return t.copy=function(){return ub(t,e())},ay.apply(t,arguments)},scaleDivergingLog:()=>function e(){var t=sy(uj()).domain([.1,1,10]);return t.copy=function(){return ub(t,e()).base(t.base())},ay.apply(t,arguments)},scaleDivergingPow:()=>uO,scaleDivergingSqrt:()=>uS,scaleDivergingSymlog:()=>function e(){var t=sg(uj());return t.copy=function(){return ub(t,e()).constant(t.constant())},ay.apply(t,arguments)},scaleIdentity:()=>function e(t){var r;function n(e){return null==e||isNaN(e*=1)?r:e}return n.invert=n,n.domain=n.range=function(e){return arguments.length?(t=Array.from(e,lK),n):t.slice()},n.unknown=function(e){return arguments.length?(r=e,n):r},n.copy=function(){return e(t).unknown(r)},t=arguments.length?Array.from(t,lK):[0,1],sa(n)},scaleImplicit:()=>ab,scaleLinear:()=>sl,scaleLog:()=>function e(){let t=sy(l5()).domain([1,10]);return t.copy=()=>l2(t,e()).base(t.base()),ah.apply(t,arguments),t},scaleOrdinal:()=>ax,scalePoint:()=>aj,scalePow:()=>sO,scaleQuantile:()=>function e(){var t,r=[],n=[],o=[];function i(){var e=0,t=Math.max(1,n.length);for(o=Array(t-1);++e<t;)o[e-1]=function(e,t,r=li){if(!(!(n=e.length)||isNaN(t*=1))){if(t<=0||n<2)return+r(e[0],0,e);if(t>=1)return+r(e[n-1],n-1,e);var n,o=(n-1)*t,i=Math.floor(o),a=+r(e[i],i,e);return a+(+r(e[i+1],i+1,e)-a)*(o-i)}}(r,e/t);return a}function a(e){return null==e||isNaN(e*=1)?t:n[ll(o,e)]}return a.invertExtent=function(e){var t=n.indexOf(e);return t<0?[NaN,NaN]:[t>0?o[t-1]:r[0],t<o.length?o[t]:r[r.length-1]]},a.domain=function(e){if(!arguments.length)return r.slice();for(let t of(r=[],e))null==t||isNaN(t*=1)||r.push(t);return r.sort(lt),i()},a.range=function(e){return arguments.length?(n=Array.from(e),i()):n.slice()},a.unknown=function(e){return arguments.length?(t=e,a):t},a.quantiles=function(){return o.slice()},a.copy=function(){return e().domain(r).range(n).unknown(t)},ah.apply(a,arguments)},scaleQuantize:()=>function e(){var t,r=0,n=1,o=1,i=[.5],a=[0,1];function l(e){return null!=e&&e<=e?a[ll(i,e,0,o)]:t}function s(){var e=-1;for(i=Array(o);++e<o;)i[e]=((e+1)*n-(e-o)*r)/(o+1);return l}return l.domain=function(e){return arguments.length?([r,n]=e,r*=1,n*=1,s()):[r,n]},l.range=function(e){return arguments.length?(o=(a=Array.from(e)).length-1,s()):a.slice()},l.invertExtent=function(e){var t=a.indexOf(e);return t<0?[NaN,NaN]:t<1?[r,i[0]]:t>=o?[i[o-1],n]:[i[t-1],i[t]]},l.unknown=function(e){return arguments.length&&(t=e),l},l.thresholds=function(){return i.slice()},l.copy=function(){return e().domain([r,n]).range(a).unknown(t)},ah.apply(sa(l),arguments)},scaleRadial:()=>function e(){var t,r=l4(),n=[0,1],o=!1;function i(e){var n,i=Math.sign(n=r(e))*Math.sqrt(Math.abs(n));return isNaN(i)?t:o?Math.round(i):i}return i.invert=function(e){return r.invert(sA(e))},i.domain=function(e){return arguments.length?(r.domain(e),i):r.domain()},i.range=function(e){return arguments.length?(r.range((n=Array.from(e,lK)).map(sA)),i):n.slice()},i.rangeRound=function(e){return i.range(e).round(!0)},i.round=function(e){return arguments.length?(o=!!e,i):o},i.clamp=function(e){return arguments.length?(r.clamp(e),i):r.clamp()},i.unknown=function(e){return arguments.length?(t=e,i):t},i.copy=function(){return e(r.domain(),n).round(o).clamp(r.clamp()).unknown(t)},ah.apply(i,arguments),sa(i)},scaleSequential:()=>function e(){var t=sa(ug()(lJ));return t.copy=function(){return ub(t,e())},ay.apply(t,arguments)},scaleSequentialLog:()=>function e(){var t=sy(ug()).domain([1,10]);return t.copy=function(){return ub(t,e()).base(t.base())},ay.apply(t,arguments)},scaleSequentialPow:()=>ux,scaleSequentialQuantile:()=>function e(){var t=[],r=lJ;function n(e){if(null!=e&&!isNaN(e*=1))return r((ll(t,e,1)-1)/(t.length-1))}return n.domain=function(e){if(!arguments.length)return t.slice();for(let r of(t=[],e))null==r||isNaN(r*=1)||t.push(r);return t.sort(lt),n},n.interpolator=function(e){return arguments.length?(r=e,n):r},n.range=function(){return t.map((e,n)=>r(n/(t.length-1)))},n.quantiles=function(e){return Array.from({length:e+1},(r,n)=>(function(e,t,r){if(!(!(n=(e=Float64Array.from(function*(e,t){if(void 0===t)for(let t of e)null!=t&&(t*=1)>=t&&(yield t);else{let r=-1;for(let n of e)null!=(n=t(n,++r,e))&&(n*=1)>=n&&(yield n)}}(e,void 0))).length)||isNaN(t*=1))){if(t<=0||n<2)return sk(e);if(t>=1)return sP(e);var n,o=(n-1)*t,i=Math.floor(o),a=sP((function e(t,r,n=0,o=1/0,i){if(r=Math.floor(r),n=Math.floor(Math.max(0,n)),o=Math.floor(Math.min(t.length-1,o)),!(n<=r&&r<=o))return t;for(i=void 0===i?sE:function(e=lt){if(e===lt)return sE;if("function"!=typeof e)throw TypeError("compare is not a function");return(t,r)=>{let n=e(t,r);return n||0===n?n:(0===e(r,r))-(0===e(t,t))}}(i);o>n;){if(o-n>600){let a=o-n+1,l=r-n+1,s=Math.log(a),c=.5*Math.exp(2*s/3),u=.5*Math.sqrt(s*c*(a-c)/a)*(l-a/2<0?-1:1),f=Math.max(n,Math.floor(r-l*c/a+u)),d=Math.min(o,Math.floor(r+(a-l)*c/a+u));e(t,r,f,d,i)}let a=t[r],l=n,s=o;for(sN(t,n,r),i(t[o],a)>0&&sN(t,n,o);l<s;){for(sN(t,l,s),++l,--s;0>i(t[l],a);)++l;for(;i(t[s],a)>0;)--s}0===i(t[n],a)?sN(t,n,s):sN(t,++s,o),s<=r&&(n=s+1),r<=s&&(o=s-1)}return t})(e,i).subarray(0,i+1));return a+(sk(e.subarray(i+1))-a)*(o-i)}})(t,n/e))},n.copy=function(){return e(r).domain(t)},ay.apply(n,arguments)},scaleSequentialSqrt:()=>uw,scaleSequentialSymlog:()=>function e(){var t=sg(ug());return t.copy=function(){return ub(t,e()).constant(t.constant())},ay.apply(t,arguments)},scaleSqrt:()=>sS,scaleSymlog:()=>function e(){var t=sg(l5());return t.copy=function(){return l2(t,e()).constant(t.constant())},ah.apply(t,arguments)},scaleThreshold:()=>function e(){var t,r=[.5],n=[0,1],o=1;function i(e){return null!=e&&e<=e?n[ll(r,e,0,o)]:t}return i.domain=function(e){return arguments.length?(o=Math.min((r=Array.from(e)).length,n.length-1),i):r.slice()},i.range=function(e){return arguments.length?(n=Array.from(e),o=Math.min(r.length,n.length-1),i):n.slice()},i.invertExtent=function(e){var t=n.indexOf(e);return[r[t-1],r[t]]},i.unknown=function(e){return arguments.length?(t=e,i):t},i.copy=function(){return e().domain(r).range(n).unknown(t)},ah.apply(i,arguments)},scaleTime:()=>um,scaleUtc:()=>uv,tickFormat:()=>si});var o=r(60687),i=r(43210),a=r.n(i),l=r(16189),s=r(12157),c=r(72789),u=r(21279),f=r(32582);class d extends i.Component{getSnapshotBeforeUpdate(e){let t=this.props.childRef.current;if(t&&e.isPresent&&!this.props.isPresent){let e=this.props.sizeRef.current;e.height=t.offsetHeight||0,e.width=t.offsetWidth||0,e.top=t.offsetTop,e.left=t.offsetLeft}return null}componentDidUpdate(){}render(){return this.props.children}}function p({children:e,isPresent:t}){let r=(0,i.useId)(),n=(0,i.useRef)(null),a=(0,i.useRef)({width:0,height:0,top:0,left:0}),{nonce:l}=(0,i.useContext)(f.Q);return(0,i.useInsertionEffect)(()=>{let{width:e,height:o,top:i,left:s}=a.current;if(t||!n.current||!e||!o)return;n.current.dataset.motionPopId=r;let c=document.createElement("style");return l&&(c.nonce=l),document.head.appendChild(c),c.sheet&&c.sheet.insertRule(`
          [data-motion-pop-id="${r}"] {
            position: absolute !important;
            width: ${e}px !important;
            height: ${o}px !important;
            top: ${i}px !important;
            left: ${s}px !important;
          }
        `),()=>{document.head.removeChild(c)}},[t]),(0,o.jsx)(d,{isPresent:t,childRef:n,sizeRef:a,children:i.cloneElement(e,{ref:n})})}let h=({children:e,initial:t,isPresent:r,onExitComplete:n,custom:a,presenceAffectsLayout:l,mode:s})=>{let f=(0,c.M)(y),d=(0,i.useId)(),h=(0,i.useCallback)(e=>{for(let t of(f.set(e,!0),f.values()))if(!t)return;n&&n()},[f,n]),m=(0,i.useMemo)(()=>({id:d,initial:t,isPresent:r,custom:a,onExitComplete:h,register:e=>(f.set(e,!1),()=>f.delete(e))}),l?[Math.random(),h]:[r,h]);return(0,i.useMemo)(()=>{f.forEach((e,t)=>f.set(t,!1))},[r]),i.useEffect(()=>{r||f.size||!n||n()},[r]),"popLayout"===s&&(e=(0,o.jsx)(p,{isPresent:r,children:e})),(0,o.jsx)(u.t.Provider,{value:m,children:e})};function y(){return new Map}var m=r(86044);let v=e=>e.key||"";function g(e){let t=[];return i.Children.forEach(e,e=>{(0,i.isValidElement)(e)&&t.push(e)}),t}var b=r(15124);let x=({children:e,custom:t,initial:r=!0,onExitComplete:n,presenceAffectsLayout:a=!0,mode:l="sync",propagate:u=!1})=>{let[f,d]=(0,m.xQ)(u),p=(0,i.useMemo)(()=>g(e),[e]),y=u&&!f?[]:p.map(v),x=(0,i.useRef)(!0),w=(0,i.useRef)(p),j=(0,c.M)(()=>new Map),[O,S]=(0,i.useState)(p),[A,P]=(0,i.useState)(p);(0,b.E)(()=>{x.current=!1,w.current=p;for(let e=0;e<A.length;e++){let t=v(A[e]);y.includes(t)?j.delete(t):!0!==j.get(t)&&j.set(t,!1)}},[A,y.length,y.join("-")]);let k=[];if(p!==O){let e=[...p];for(let t=0;t<A.length;t++){let r=A[t],n=v(r);y.includes(n)||(e.splice(t,0,r),k.push(r))}"wait"===l&&k.length&&(e=k),P(g(e)),S(p);return}let{forceRender:E}=(0,i.useContext)(s.L);return(0,o.jsx)(o.Fragment,{children:A.map(e=>{let i=v(e),s=(!u||!!f)&&(p===A||y.includes(i));return(0,o.jsx)(h,{isPresent:s,initial:(!x.current||!!r)&&void 0,custom:s?void 0:t,presenceAffectsLayout:a,mode:l,onExitComplete:s?void 0:()=>{if(!j.has(i))return;j.set(i,!0);let e=!0;j.forEach(t=>{t||(e=!1)}),e&&(null==E||E(),P(w.current),u&&(null==d||d()),n&&n())},children:e},i)})})};var w=r(97905),j=r(85706),O=r(89667),S=r(80013),A=r(44493),P=r(82614);let k=(0,P.A)("DollarSign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]]);function E({totalIncome:e,onIncomeChange:t,balancesVisible:r}){let[n,a]=(0,i.useState)("");return(0,o.jsxs)(A.Zp,{children:[(0,o.jsx)(A.aR,{className:"pb-2",children:(0,o.jsxs)(A.ZB,{className:"text-lg flex items-center gap-2 font-headline",children:[(0,o.jsx)(k,{className:"h-5 w-5 text-primary"}),"Total Income"]})}),(0,o.jsx)(A.Wu,{children:(0,o.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,o.jsx)(S.J,{htmlFor:"totalIncome",className:"sr-only",children:"Total Income"}),(0,o.jsx)("span",{className:"text-lg font-semibold text-muted-foreground",children:"R"}),(0,o.jsx)(O.p,{id:"totalIncome",type:r?"number":"text",placeholder:"e.g., 6000",value:n,onChange:e=>{let n=e.target.value;r&&a(n);let o=""===n?0:parseFloat(n);!isNaN(o)&&o>=0?t(o):""===n&&t(0)},className:"text-lg h-10 flex-grow",min:"0",step:"any",readOnly:!r&&"••••"===n})]})})]})}var N=r(11273),M=r(14163),C="Progress",[T,_]=(0,N.A)(C),[I,D]=T(C),R=i.forwardRef((e,t)=>{var r,n;let{__scopeProgress:i,value:a=null,max:l,getValueLabel:s=$,...c}=e;(l||0===l)&&!F(l)&&console.error((r=`${l}`,`Invalid prop \`max\` of value \`${r}\` supplied to \`Progress\`. Only numbers greater than 0 are valid max values. Defaulting to \`100\`.`));let u=F(l)?l:100;null===a||q(a,u)||console.error((n=`${a}`,`Invalid prop \`value\` of value \`${n}\` supplied to \`Progress\`. The \`value\` prop must be:
  - a positive number
  - less than the value passed to \`max\` (or 100 if no \`max\` prop is set)
  - \`null\` or \`undefined\` if the progress is indeterminate.

Defaulting to \`null\`.`));let f=q(a,u)?a:null,d=U(f)?s(f,u):void 0;return(0,o.jsx)(I,{scope:i,value:f,max:u,children:(0,o.jsx)(M.sG.div,{"aria-valuemax":u,"aria-valuemin":0,"aria-valuenow":U(f)?f:void 0,"aria-valuetext":d,role:"progressbar","data-state":z(f,u),"data-value":f??void 0,"data-max":u,...c,ref:t})})});R.displayName=C;var B="ProgressIndicator",L=i.forwardRef((e,t)=>{let{__scopeProgress:r,...n}=e,i=D(B,r);return(0,o.jsx)(M.sG.div,{"data-state":z(i.value,i.max),"data-value":i.value??void 0,"data-max":i.max,...n,ref:t})});function $(e,t){return`${Math.round(e/t*100)}%`}function z(e,t){return null==e?"indeterminate":e===t?"complete":"loading"}function U(e){return"number"==typeof e}function F(e){return U(e)&&!isNaN(e)&&e>0}function q(e,t){return U(e)&&!isNaN(e)&&e<=t&&e>=0}L.displayName=B;var W=r(4780);let V=i.forwardRef(({className:e,value:t,...r},n)=>(0,o.jsx)(R,{ref:n,className:(0,W.cn)("relative h-4 w-full overflow-hidden rounded-full bg-secondary",e),...r,children:(0,o.jsx)(L,{className:"h-full w-full flex-1 bg-primary transition-all",style:{transform:`translateX(-${100-(t||0)}%)`}})}));V.displayName=R.displayName;let H=(0,P.A)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]]);var G=r(14975);let X=(0,P.A)("BadgeCheck",[["path",{d:"M3.85 8.62a4 4 0 0 1 4.78-4.77 4 4 0 0 1 6.74 0 4 4 0 0 1 4.78 4.78 4 4 0 0 1 0 6.74 4 4 0 0 1-4.77 4.78 4 4 0 0 1-6.75 0 4 4 0 0 1-4.78-4.77 4 4 0 0 1 0-6.76Z",key:"3c2336"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]]);function Y({totalIncome:e,overallTotalAllocated:t,balancesVisible:r}){let n=e-t,i=e>0?t/e*100:0,a=n<0,l=e=>r?`R ${e.toFixed(2)}`:"R ••••";return(0,o.jsxs)(A.Zp,{children:[(0,o.jsx)(A.aR,{className:"pb-2",children:(0,o.jsxs)(A.ZB,{className:"text-lg flex items-center gap-2 font-headline",children:[(0,o.jsx)(H,{className:"h-5 w-5 text-primary"}),"Budget Summary"]})}),(0,o.jsxs)(A.Wu,{className:"space-y-3",children:[(0,o.jsxs)("div",{className:"flex justify-between items-center text-sm",children:[(0,o.jsx)("span",{className:"text-muted-foreground",children:"Total Income:"}),(0,o.jsx)("span",{className:"font-semibold",children:l(e)})]}),(0,o.jsxs)("div",{className:"flex justify-between items-center text-sm",children:[(0,o.jsx)("span",{className:"text-muted-foreground",children:"Total Allocated:"}),(0,o.jsx)("span",{className:"font-semibold",children:l(t)})]}),(0,o.jsx)(V,{value:Math.min(i,100),className:a?"bg-destructive":""}),(0,o.jsxs)("div",{className:`flex justify-between items-center text-sm font-semibold ${a?"text-destructive":"text-green-600"}`,children:[(0,o.jsx)("span",{children:a?"Over Allocated:":"Remaining:"}),(0,o.jsx)("span",{children:l(Math.abs(n))})]}),a&&(0,o.jsxs)("div",{className:"flex items-center gap-2 text-destructive text-xs p-2 bg-destructive/10 rounded-md",children:[(0,o.jsx)(G.A,{className:"h-4 w-4"}),(0,o.jsx)("span",{children:"Warning: Your allocations exceed your income!"})]}),!a&&e>0&&t<=e&&(0,o.jsxs)("div",{className:"flex items-center gap-2 text-green-600 text-xs p-2 bg-green-600/10 rounded-md",children:[(0,o.jsx)(X,{className:"h-4 w-4"}),(0,o.jsx)("span",{children:"Your budget is balanced or has funds remaining."})]})]})]})}var K=r(29523),Z=r(9510),J=r(98599),Q=r(70569),ee=r(65551),et=r(66156),er=r(46059),en=r(96963),eo="Collapsible",[ei,ea]=(0,N.A)(eo),[el,es]=ei(eo),ec=i.forwardRef((e,t)=>{let{__scopeCollapsible:r,open:n,defaultOpen:a,disabled:l,onOpenChange:s,...c}=e,[u=!1,f]=(0,ee.i)({prop:n,defaultProp:a,onChange:s});return(0,o.jsx)(el,{scope:r,disabled:l,contentId:(0,en.B)(),open:u,onOpenToggle:i.useCallback(()=>f(e=>!e),[f]),children:(0,o.jsx)(M.sG.div,{"data-state":ey(u),"data-disabled":l?"":void 0,...c,ref:t})})});ec.displayName=eo;var eu="CollapsibleTrigger",ef=i.forwardRef((e,t)=>{let{__scopeCollapsible:r,...n}=e,i=es(eu,r);return(0,o.jsx)(M.sG.button,{type:"button","aria-controls":i.contentId,"aria-expanded":i.open||!1,"data-state":ey(i.open),"data-disabled":i.disabled?"":void 0,disabled:i.disabled,...n,ref:t,onClick:(0,Q.m)(e.onClick,i.onOpenToggle)})});ef.displayName=eu;var ed="CollapsibleContent",ep=i.forwardRef((e,t)=>{let{forceMount:r,...n}=e,i=es(ed,e.__scopeCollapsible);return(0,o.jsx)(er.C,{present:r||i.open,children:({present:e})=>(0,o.jsx)(eh,{...n,ref:t,present:e})})});ep.displayName=ed;var eh=i.forwardRef((e,t)=>{let{__scopeCollapsible:r,present:n,children:a,...l}=e,s=es(ed,r),[c,u]=i.useState(n),f=i.useRef(null),d=(0,J.s)(t,f),p=i.useRef(0),h=p.current,y=i.useRef(0),m=y.current,v=s.open||c,g=i.useRef(v),b=i.useRef(void 0);return i.useEffect(()=>{let e=requestAnimationFrame(()=>g.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,et.N)(()=>{let e=f.current;if(e){b.current=b.current||{transitionDuration:e.style.transitionDuration,animationName:e.style.animationName},e.style.transitionDuration="0s",e.style.animationName="none";let t=e.getBoundingClientRect();p.current=t.height,y.current=t.width,g.current||(e.style.transitionDuration=b.current.transitionDuration,e.style.animationName=b.current.animationName),u(n)}},[s.open,n]),(0,o.jsx)(M.sG.div,{"data-state":ey(s.open),"data-disabled":s.disabled?"":void 0,id:s.contentId,hidden:!v,...l,ref:d,style:{"--radix-collapsible-content-height":h?`${h}px`:void 0,"--radix-collapsible-content-width":m?`${m}px`:void 0,...e.style},children:v&&a})});function ey(e){return e?"open":"closed"}var em=r(43),ev="Accordion",eg=["Home","End","ArrowDown","ArrowUp","ArrowLeft","ArrowRight"],[eb,ex,ew]=(0,Z.N)(ev),[ej,eO]=(0,N.A)(ev,[ew,ea]),eS=ea(),eA=i.forwardRef((e,t)=>{let{type:r,...n}=e;return(0,o.jsx)(eb.Provider,{scope:e.__scopeAccordion,children:"multiple"===r?(0,o.jsx)(eC,{...n,ref:t}):(0,o.jsx)(eM,{...n,ref:t})})});eA.displayName=ev;var[eP,ek]=ej(ev),[eE,eN]=ej(ev,{collapsible:!1}),eM=i.forwardRef((e,t)=>{let{value:r,defaultValue:n,onValueChange:a=()=>{},collapsible:l=!1,...s}=e,[c,u]=(0,ee.i)({prop:r,defaultProp:n,onChange:a});return(0,o.jsx)(eP,{scope:e.__scopeAccordion,value:c?[c]:[],onItemOpen:u,onItemClose:i.useCallback(()=>l&&u(""),[l,u]),children:(0,o.jsx)(eE,{scope:e.__scopeAccordion,collapsible:l,children:(0,o.jsx)(eI,{...s,ref:t})})})}),eC=i.forwardRef((e,t)=>{let{value:r,defaultValue:n,onValueChange:a=()=>{},...l}=e,[s=[],c]=(0,ee.i)({prop:r,defaultProp:n,onChange:a}),u=i.useCallback(e=>c((t=[])=>[...t,e]),[c]),f=i.useCallback(e=>c((t=[])=>t.filter(t=>t!==e)),[c]);return(0,o.jsx)(eP,{scope:e.__scopeAccordion,value:s,onItemOpen:u,onItemClose:f,children:(0,o.jsx)(eE,{scope:e.__scopeAccordion,collapsible:!0,children:(0,o.jsx)(eI,{...l,ref:t})})})}),[eT,e_]=ej(ev),eI=i.forwardRef((e,t)=>{let{__scopeAccordion:r,disabled:n,dir:a,orientation:l="vertical",...s}=e,c=i.useRef(null),u=(0,J.s)(c,t),f=ex(r),d="ltr"===(0,em.jH)(a),p=(0,Q.m)(e.onKeyDown,e=>{if(!eg.includes(e.key))return;let t=e.target,r=f().filter(e=>!e.ref.current?.disabled),n=r.findIndex(e=>e.ref.current===t),o=r.length;if(-1===n)return;e.preventDefault();let i=n,a=o-1,s=()=>{(i=n+1)>a&&(i=0)},c=()=>{(i=n-1)<0&&(i=a)};switch(e.key){case"Home":i=0;break;case"End":i=a;break;case"ArrowRight":"horizontal"===l&&(d?s():c());break;case"ArrowDown":"vertical"===l&&s();break;case"ArrowLeft":"horizontal"===l&&(d?c():s());break;case"ArrowUp":"vertical"===l&&c()}let u=i%o;r[u].ref.current?.focus()});return(0,o.jsx)(eT,{scope:r,disabled:n,direction:a,orientation:l,children:(0,o.jsx)(eb.Slot,{scope:r,children:(0,o.jsx)(M.sG.div,{...s,"data-orientation":l,ref:u,onKeyDown:n?void 0:p})})})}),eD="AccordionItem",[eR,eB]=ej(eD),eL=i.forwardRef((e,t)=>{let{__scopeAccordion:r,value:n,...i}=e,a=e_(eD,r),l=ek(eD,r),s=eS(r),c=(0,en.B)(),u=n&&l.value.includes(n)||!1,f=a.disabled||e.disabled;return(0,o.jsx)(eR,{scope:r,open:u,disabled:f,triggerId:c,children:(0,o.jsx)(ec,{"data-orientation":a.orientation,"data-state":eV(u),...s,...i,ref:t,disabled:f,open:u,onOpenChange:e=>{e?l.onItemOpen(n):l.onItemClose(n)}})})});eL.displayName=eD;var e$="AccordionHeader",ez=i.forwardRef((e,t)=>{let{__scopeAccordion:r,...n}=e,i=e_(ev,r),a=eB(e$,r);return(0,o.jsx)(M.sG.h3,{"data-orientation":i.orientation,"data-state":eV(a.open),"data-disabled":a.disabled?"":void 0,...n,ref:t})});ez.displayName=e$;var eU="AccordionTrigger",eF=i.forwardRef((e,t)=>{let{__scopeAccordion:r,...n}=e,i=e_(ev,r),a=eB(eU,r),l=eN(eU,r),s=eS(r);return(0,o.jsx)(eb.ItemSlot,{scope:r,children:(0,o.jsx)(ef,{"aria-disabled":a.open&&!l.collapsible||void 0,"data-orientation":i.orientation,id:a.triggerId,...s,...n,ref:t})})});eF.displayName=eU;var eq="AccordionContent",eW=i.forwardRef((e,t)=>{let{__scopeAccordion:r,...n}=e,i=e_(ev,r),a=eB(eq,r),l=eS(r);return(0,o.jsx)(ep,{role:"region","aria-labelledby":a.triggerId,"data-orientation":i.orientation,...l,...n,ref:t,style:{"--radix-accordion-content-height":"var(--radix-collapsible-content-height)","--radix-accordion-content-width":"var(--radix-collapsible-content-width)",...e.style}})});function eV(e){return e?"open":"closed"}eW.displayName=eq;let eH=(0,P.A)("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]]),eG=i.forwardRef(({className:e,...t},r)=>(0,o.jsx)(eL,{ref:r,className:(0,W.cn)("border-b",e),...t}));eG.displayName="AccordionItem";let eX=i.forwardRef(({className:e,children:t,...r},n)=>(0,o.jsx)(ez,{className:"flex",children:(0,o.jsxs)(eF,{ref:n,className:(0,W.cn)("flex flex-1 items-center justify-between py-4 font-medium transition-all hover:underline [&[data-state=open]>.accordion-trigger-chevron]:rotate-180",e),...r,children:[t,(0,o.jsx)(eH,{className:"h-4 w-4 shrink-0 transition-transform duration-200 accordion-trigger-chevron"})]})}));eX.displayName=eF.displayName;let eY=i.forwardRef(({className:e,children:t,...r},n)=>(0,o.jsx)(eW,{ref:n,className:"overflow-hidden text-sm transition-all data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down",...r,children:(0,o.jsx)("div",{className:(0,W.cn)("pb-4 pt-0",e),children:t})}));eY.displayName=eW.displayName;var eK=r(31355),eZ=r(32547),eJ=r(25028),eQ=r(1359),e0=r(11490),e1=r(63376),e2=r(8730),e5="Dialog",[e4,e3]=(0,N.A)(e5),[e6,e8]=e4(e5),e7=e=>{let{__scopeDialog:t,children:r,open:n,defaultOpen:a,onOpenChange:l,modal:s=!0}=e,c=i.useRef(null),u=i.useRef(null),[f=!1,d]=(0,ee.i)({prop:n,defaultProp:a,onChange:l});return(0,o.jsx)(e6,{scope:t,triggerRef:c,contentRef:u,contentId:(0,en.B)(),titleId:(0,en.B)(),descriptionId:(0,en.B)(),open:f,onOpenChange:d,onOpenToggle:i.useCallback(()=>d(e=>!e),[d]),modal:s,children:r})};e7.displayName=e5;var e9="DialogTrigger",te=i.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,i=e8(e9,r),a=(0,J.s)(t,i.triggerRef);return(0,o.jsx)(M.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":i.open,"aria-controls":i.contentId,"data-state":tb(i.open),...n,ref:a,onClick:(0,Q.m)(e.onClick,i.onOpenToggle)})});te.displayName=e9;var tt="DialogPortal",[tr,tn]=e4(tt,{forceMount:void 0}),to=e=>{let{__scopeDialog:t,forceMount:r,children:n,container:a}=e,l=e8(tt,t);return(0,o.jsx)(tr,{scope:t,forceMount:r,children:i.Children.map(n,e=>(0,o.jsx)(er.C,{present:r||l.open,children:(0,o.jsx)(eJ.Z,{asChild:!0,container:a,children:e})}))})};to.displayName=tt;var ti="DialogOverlay",ta=i.forwardRef((e,t)=>{let r=tn(ti,e.__scopeDialog),{forceMount:n=r.forceMount,...i}=e,a=e8(ti,e.__scopeDialog);return a.modal?(0,o.jsx)(er.C,{present:n||a.open,children:(0,o.jsx)(tl,{...i,ref:t})}):null});ta.displayName=ti;var tl=i.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,i=e8(ti,r);return(0,o.jsx)(e0.A,{as:e2.DX,allowPinchZoom:!0,shards:[i.contentRef],children:(0,o.jsx)(M.sG.div,{"data-state":tb(i.open),...n,ref:t,style:{pointerEvents:"auto",...n.style}})})}),ts="DialogContent",tc=i.forwardRef((e,t)=>{let r=tn(ts,e.__scopeDialog),{forceMount:n=r.forceMount,...i}=e,a=e8(ts,e.__scopeDialog);return(0,o.jsx)(er.C,{present:n||a.open,children:a.modal?(0,o.jsx)(tu,{...i,ref:t}):(0,o.jsx)(tf,{...i,ref:t})})});tc.displayName=ts;var tu=i.forwardRef((e,t)=>{let r=e8(ts,e.__scopeDialog),n=i.useRef(null),a=(0,J.s)(t,r.contentRef,n);return i.useEffect(()=>{let e=n.current;if(e)return(0,e1.Eq)(e)},[]),(0,o.jsx)(td,{...e,ref:a,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,Q.m)(e.onCloseAutoFocus,e=>{e.preventDefault(),r.triggerRef.current?.focus()}),onPointerDownOutside:(0,Q.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey;(2===t.button||r)&&e.preventDefault()}),onFocusOutside:(0,Q.m)(e.onFocusOutside,e=>e.preventDefault())})}),tf=i.forwardRef((e,t)=>{let r=e8(ts,e.__scopeDialog),n=i.useRef(!1),a=i.useRef(!1);return(0,o.jsx)(td,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{e.onCloseAutoFocus?.(t),t.defaultPrevented||(n.current||r.triggerRef.current?.focus(),t.preventDefault()),n.current=!1,a.current=!1},onInteractOutside:t=>{e.onInteractOutside?.(t),t.defaultPrevented||(n.current=!0,"pointerdown"!==t.detail.originalEvent.type||(a.current=!0));let o=t.target;r.triggerRef.current?.contains(o)&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&a.current&&t.preventDefault()}})}),td=i.forwardRef((e,t)=>{let{__scopeDialog:r,trapFocus:n,onOpenAutoFocus:a,onCloseAutoFocus:l,...s}=e,c=e8(ts,r),u=i.useRef(null),f=(0,J.s)(t,u);return(0,eQ.Oh)(),(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(eZ.n,{asChild:!0,loop:!0,trapped:n,onMountAutoFocus:a,onUnmountAutoFocus:l,children:(0,o.jsx)(eK.qW,{role:"dialog",id:c.contentId,"aria-describedby":c.descriptionId,"aria-labelledby":c.titleId,"data-state":tb(c.open),...s,ref:f,onDismiss:()=>c.onOpenChange(!1)})}),(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(tO,{titleId:c.titleId}),(0,o.jsx)(tS,{contentRef:u,descriptionId:c.descriptionId})]})]})}),tp="DialogTitle",th=i.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,i=e8(tp,r);return(0,o.jsx)(M.sG.h2,{id:i.titleId,...n,ref:t})});th.displayName=tp;var ty="DialogDescription",tm=i.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,i=e8(ty,r);return(0,o.jsx)(M.sG.p,{id:i.descriptionId,...n,ref:t})});tm.displayName=ty;var tv="DialogClose",tg=i.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,i=e8(tv,r);return(0,o.jsx)(M.sG.button,{type:"button",...n,ref:t,onClick:(0,Q.m)(e.onClick,()=>i.onOpenChange(!1))})});function tb(e){return e?"open":"closed"}tg.displayName=tv;var tx="DialogTitleWarning",[tw,tj]=(0,N.q)(tx,{contentName:ts,titleName:tp,docsSlug:"dialog"}),tO=({titleId:e})=>{let t=tj(tx),r=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return i.useEffect(()=>{e&&!document.getElementById(e)&&console.error(r)},[r,e]),null},tS=({contentRef:e,descriptionId:t})=>{let r=tj("DialogDescriptionWarning"),n=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${r.contentName}}.`;return i.useEffect(()=>{let r=e.current?.getAttribute("aria-describedby");t&&r&&!document.getElementById(t)&&console.warn(n)},[n,e,t]),null},tA=r(78726);let tP=i.forwardRef(({className:e,...t},r)=>(0,o.jsx)(ta,{ref:r,className:(0,W.cn)("fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...t}));tP.displayName=ta.displayName;let tk=i.forwardRef(({className:e,children:t,...r},n)=>(0,o.jsxs)(to,{children:[(0,o.jsx)(tP,{}),(0,o.jsxs)(tc,{ref:n,className:(0,W.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",e),...r,children:[t,(0,o.jsxs)(tg,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,o.jsx)(tA.A,{className:"h-4 w-4"}),(0,o.jsx)("span",{className:"sr-only",children:"Close"})]})]})]}));tk.displayName=tc.displayName;let tE=({className:e,...t})=>(0,o.jsx)("div",{className:(0,W.cn)("flex flex-col space-y-1.5 text-center sm:text-left",e),...t});tE.displayName="DialogHeader";let tN=i.forwardRef(({className:e,...t},r)=>(0,o.jsx)(th,{ref:r,className:(0,W.cn)("text-lg font-semibold leading-none tracking-tight",e),...t}));tN.displayName=th.displayName,i.forwardRef(({className:e,...t},r)=>(0,o.jsx)(tm,{ref:r,className:(0,W.cn)("text-sm text-muted-foreground",e),...t})).displayName=tm.displayName;let tM=(0,P.A)("PenLine",[["path",{d:"M12 20h9",key:"t2du7b"}],["path",{d:"M16.376 3.622a1 1 0 0 1 3.002 3.002L7.368 18.635a2 2 0 0 1-.855.506l-2.872.838a.5.5 0 0 1-.62-.62l.838-2.872a2 2 0 0 1 .506-.854z",key:"1ykcvy"}]]);var tC=r(57207);function tT({subCategory:e,onEdit:t,onDelete:r,balancesVisible:n}){return(0,o.jsxs)("div",{className:"flex items-center justify-between p-2 border-b border-border/50 last:border-b-0",children:[(0,o.jsxs)("div",{className:"flex-1",children:[(0,o.jsx)("p",{className:"text-sm font-medium",children:e.name}),(0,o.jsxs)("p",{className:"text-xs text-muted-foreground",children:["Allocated: ",n?`R ${e.allocatedAmount.toFixed(2)}`:"R ••••"]})]}),(0,o.jsxs)("div",{className:"flex gap-1",children:[(0,o.jsxs)(K.$,{variant:"ghost",size:"icon",onClick:t,className:"h-7 w-7",children:[(0,o.jsx)(tM,{className:"h-4 w-4"}),(0,o.jsx)("span",{className:"sr-only",children:"Edit Subcategory"})]}),(0,o.jsxs)(K.$,{variant:"ghost",size:"icon",onClick:r,className:"h-7 w-7 text-destructive hover:text-destructive",children:[(0,o.jsx)(tC.A,{className:"h-4 w-4"}),(0,o.jsx)("span",{className:"sr-only",children:"Delete Subcategory"})]})]})]})}var t_=r(63442),tI=r(27605),tD=r(45880),tR=r(71669);let tB=tD.Ik({name:tD.Yj().min(1,{message:"Subcategory name is required."}).max(50,{message:"Name must be 50 characters or less."}),allocatedAmount:tD.vk(e=>"string"==typeof e?parseFloat(e):e,tD.ai().min(0,{message:"Allocated amount must be a positive number."}))});function tL({onSubmit:e,initialData:t,onClose:r,parentCategoryName:n,balancesVisible:i}){let a=(0,tI.mN)({resolver:(0,t_.u)(tB),defaultValues:{name:t?.name||"",allocatedAmount:t?.allocatedAmount||0}});return(0,o.jsx)(tR.lV,{...a,children:(0,o.jsxs)("form",{onSubmit:a.handleSubmit(t=>{e(t)&&(a.reset(),r())}),className:"space-y-4",children:[(0,o.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Adding to: ",(0,o.jsx)("span",{className:"font-semibold",children:n})]}),(0,o.jsx)(tR.zB,{control:a.control,name:"name",render:({field:e})=>(0,o.jsxs)(tR.eI,{children:[(0,o.jsx)(tR.lR,{children:"Subcategory Name"}),(0,o.jsx)(tR.MJ,{children:(0,o.jsx)(O.p,{placeholder:"e.g., Fruits & Vegetables",...e})}),(0,o.jsx)(tR.C5,{})]})}),(0,o.jsx)(tR.zB,{control:a.control,name:"allocatedAmount",render:({field:e})=>(0,o.jsxs)(tR.eI,{children:[(0,o.jsx)(tR.lR,{children:"Allocated Amount (R)"}),(0,o.jsx)(tR.MJ,{children:(0,o.jsx)(O.p,{type:i?"number":"password",placeholder:i?"e.g., 100":"••••",...e,value:i?e.value:e.value>0?"••••":"0",onChange:t=>{if(i)e.onChange(""===t.target.value?0:parseFloat(t.target.value));else{let r=parseFloat(t.target.value);isNaN(r)?""===t.target.value&&e.onChange(0):e.onChange(r)}},step:"any"})}),(0,o.jsx)(tR.C5,{})]})}),(0,o.jsxs)("div",{className:"flex justify-end gap-2 pt-2",children:[(0,o.jsx)(K.$,{type:"button",variant:"outline",onClick:r,children:"Cancel"}),(0,o.jsx)(K.$,{type:"submit",children:t?.id?"Save Changes":"Add Subcategory"})]})]})})}let t$=(0,P.A)("ListCollapse",[["path",{d:"m3 10 2.5-2.5L3 5",key:"i6eama"}],["path",{d:"m3 19 2.5-2.5L3 14",key:"w2gmor"}],["path",{d:"M10 6h11",key:"c7qv1k"}],["path",{d:"M10 12h11",key:"6m4ad9"}],["path",{d:"M10 18h11",key:"11hvi2"}]]),tz=(0,P.A)("ListTree",[["path",{d:"M21 12h-8",key:"1bmf0i"}],["path",{d:"M21 6H8",key:"1pqkrb"}],["path",{d:"M21 18h-8",key:"1tm79t"}],["path",{d:"M3 6v4c0 1.1.9 2 2 2h3",key:"1ywdgy"}],["path",{d:"M3 10v6c0 1.1.9 2 2 2h3",key:"2wc746"}]]),tU=(0,P.A)("CirclePlus",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M8 12h8",key:"1wcyev"}],["path",{d:"M12 8v8",key:"napkw2"}]]);var tF=r(29867);function tq({category:e,onAddSubCategory:t,onUpdateSubCategory:r,onDeleteSubCategory:n,balancesVisible:a,categoryIsVisible:l}){let[s,c]=(0,i.useState)(!1),[u,f]=(0,i.useState)(null),{toast:d}=(0,tF.dj)(),p=a&&l,h=e.subCategories.reduce((e,t)=>e+t.allocatedAmount,0),y=e.budget-h,m=y<0,v=e.budget>0?h/e.budget*100:0,g=e=>p?`R ${e.toFixed(2)}`:"R ••••";return(0,o.jsxs)(A.Zp,{className:"mb-3",children:[(0,o.jsx)(A.aR,{className:"flex flex-row items-start justify-between pb-2 pt-3 px-4",children:(0,o.jsxs)("div",{children:[(0,o.jsx)(A.ZB,{className:"text-base font-headline",children:e.name}),(0,o.jsxs)(A.BT,{className:"text-xs",children:["Budget: ",g(e.budget)]})]})}),(0,o.jsxs)(A.Wu,{className:"px-4 pb-3 space-y-2",children:[(0,o.jsxs)("div",{className:"text-xs space-y-1",children:[(0,o.jsxs)("div",{className:"flex justify-between",children:[(0,o.jsx)("span",{children:"Subcategories Total:"}),(0,o.jsx)("span",{children:g(h)})]}),(0,o.jsx)(V,{value:Math.min(v,100),className:m?"bg-destructive/70 [&>*]:bg-destructive":"[&>*]:bg-primary"}),(0,o.jsxs)("div",{className:`flex justify-between font-medium ${m?"text-destructive":"text-green-600"}`,children:[(0,o.jsx)("span",{children:"Remaining in Budget:"}),(0,o.jsx)("span",{children:g(y)})]})]}),m&&p&&(0,o.jsxs)("div",{className:"flex items-center gap-1 text-destructive text-xs p-1.5 bg-destructive/10 rounded-md",children:[(0,o.jsx)(G.A,{className:"h-3 w-3"}),(0,o.jsx)("span",{children:"Subcategory allocations exceed this category's budget!"})]}),e.subCategories.length>0&&(0,o.jsx)(eA,{type:"single",collapsible:!0,className:"w-full text-sm",children:(0,o.jsxs)(eG,{value:`cat-${e.id}-subcategories`,className:"border-t pt-2",children:[(0,o.jsxs)(eX,{className:"py-1 text-xs hover:no-underline justify-start gap-1 group",children:[(0,o.jsx)(t$,{className:"h-3 w-3 hidden group-data-[state=open]:block"}),(0,o.jsx)(tz,{className:"h-3 w-3 block group-data-[state=open]:hidden"}),(0,o.jsxs)("span",{children:["Subcategories (",e.subCategories.length,")"]})]}),(0,o.jsx)(eY,{className:"pt-1 pb-0 pl-2 border-l ml-1.5",children:e.subCategories.map(t=>(0,o.jsx)(tT,{subCategory:t,onEdit:()=>{f(t),c(!0)},onDelete:()=>n(e.id,t.id),balancesVisible:p},t.id))})]})})]}),(0,o.jsx)(A.wL,{className:"px-4 pb-3 pt-0",children:(0,o.jsxs)(e7,{open:s,onOpenChange:e=>{c(e),e||f(null)},children:[(0,o.jsx)(te,{asChild:!0,children:(0,o.jsxs)(K.$,{variant:"outline",size:"sm",className:"w-full text-xs",onClick:()=>{f(null),c(!0)},children:[(0,o.jsx)(tU,{className:"mr-1 h-3 w-3"})," Add Subcategory"]})}),(0,o.jsxs)(tk,{className:"sm:max-w-[425px]",children:[(0,o.jsx)(tE,{children:(0,o.jsxs)(tN,{className:"font-headline",children:[u?"Edit":"Add"," Subcategory"]})}),(0,o.jsx)(tL,{onSubmit:u?t=>{if(!u)return!1;let n=r(e.id,u.id,t.name,t.allocatedAmount);return n?(d({title:"Subcategory Updated",description:`${t.name} updated.`}),f(null),c(!1)):d({title:"Error",description:`Cannot update allocation. Exceeds remaining budget in ${e.name}.`,variant:"destructive"}),n}:r=>{let n=t(e.id,r.name,r.allocatedAmount);return n?(d({title:"Subcategory Added",description:`${r.name} added to ${e.name}.`}),c(!1)):d({title:"Error",description:`Cannot allocate ${p?"R"+r.allocatedAmount.toFixed(2):"amount"}. Exceeds remaining budget in ${e.name}.`,variant:"destructive"}),n},initialData:u||{},onClose:()=>{c(!1),f(null)},parentCategoryName:e.name,balancesVisible:p})]})]})})]})}let tW=tD.Ik({name:tD.Yj().min(1,{message:"Category name is required."}).max(50,{message:"Name must be 50 characters or less."}),budget:tD.vk(e=>"string"==typeof e?parseFloat(e):e,tD.ai().min(0,{message:"Budget must be a positive number."}))});function tV({onSubmit:e,initialData:t,onClose:r}){let n=(0,tI.mN)({resolver:(0,t_.u)(tW),defaultValues:{name:t?.name||"",budget:t?.budget||0}});return(0,o.jsx)(tR.lV,{...n,children:(0,o.jsxs)("form",{onSubmit:n.handleSubmit(t=>{e(t),n.reset(),r()}),className:"space-y-4",children:[(0,o.jsx)(tR.zB,{control:n.control,name:"name",render:({field:e})=>(0,o.jsxs)(tR.eI,{children:[(0,o.jsx)(tR.lR,{children:"Category Name"}),(0,o.jsx)(tR.MJ,{children:(0,o.jsx)(O.p,{placeholder:"e.g., Groceries",...e})}),(0,o.jsx)(tR.C5,{})]})}),(0,o.jsx)(tR.zB,{control:n.control,name:"budget",render:({field:e})=>(0,o.jsxs)(tR.eI,{children:[(0,o.jsx)(tR.lR,{children:"Budget Amount (R)"}),(0,o.jsx)(tR.MJ,{children:(0,o.jsx)(O.p,{type:"number",placeholder:"e.g., 500",...e,step:"any"})}),(0,o.jsx)(tR.C5,{})]})}),(0,o.jsxs)("div",{className:"flex justify-end gap-2 pt-2",children:[(0,o.jsx)(K.$,{type:"button",variant:"outline",onClick:r,children:"Cancel"}),(0,o.jsx)(K.$,{type:"submit",children:t?.id?"Save Changes":"Add Category"})]})]})})}var tH="AlertDialog",[tG,tX]=(0,N.A)(tH,[e3]),tY=e3(),tK=e=>{let{__scopeAlertDialog:t,...r}=e,n=tY(t);return(0,o.jsx)(e7,{...n,...r,modal:!0})};tK.displayName=tH,i.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...n}=e,i=tY(r);return(0,o.jsx)(te,{...i,...n,ref:t})}).displayName="AlertDialogTrigger";var tZ=e=>{let{__scopeAlertDialog:t,...r}=e,n=tY(t);return(0,o.jsx)(to,{...n,...r})};tZ.displayName="AlertDialogPortal";var tJ=i.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...n}=e,i=tY(r);return(0,o.jsx)(ta,{...i,...n,ref:t})});tJ.displayName="AlertDialogOverlay";var tQ="AlertDialogContent",[t0,t1]=tG(tQ),t2=i.forwardRef((e,t)=>{let{__scopeAlertDialog:r,children:n,...a}=e,l=tY(r),s=i.useRef(null),c=(0,J.s)(t,s),u=i.useRef(null);return(0,o.jsx)(tw,{contentName:tQ,titleName:t5,docsSlug:"alert-dialog",children:(0,o.jsx)(t0,{scope:r,cancelRef:u,children:(0,o.jsxs)(tc,{role:"alertdialog",...l,...a,ref:c,onOpenAutoFocus:(0,Q.m)(a.onOpenAutoFocus,e=>{e.preventDefault(),u.current?.focus({preventScroll:!0})}),onPointerDownOutside:e=>e.preventDefault(),onInteractOutside:e=>e.preventDefault(),children:[(0,o.jsx)(e2.xV,{children:n}),(0,o.jsx)(re,{contentRef:s})]})})})});t2.displayName=tQ;var t5="AlertDialogTitle",t4=i.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...n}=e,i=tY(r);return(0,o.jsx)(th,{...i,...n,ref:t})});t4.displayName=t5;var t3="AlertDialogDescription",t6=i.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...n}=e,i=tY(r);return(0,o.jsx)(tm,{...i,...n,ref:t})});t6.displayName=t3;var t8=i.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...n}=e,i=tY(r);return(0,o.jsx)(tg,{...i,...n,ref:t})});t8.displayName="AlertDialogAction";var t7="AlertDialogCancel",t9=i.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...n}=e,{cancelRef:i}=t1(t7,r),a=tY(r),l=(0,J.s)(t,i);return(0,o.jsx)(tg,{...a,...n,ref:l})});t9.displayName=t7;var re=({contentRef:e})=>{let t=`\`${tQ}\` requires a description for the component to be accessible for screen reader users.

You can add a description to the \`${tQ}\` by passing a \`${t3}\` component as a child, which also benefits sighted users by adding visible context to the dialog.

Alternatively, you can use your own component as a description by assigning it an \`id\` and passing the same value to the \`aria-describedby\` prop in \`${tQ}\`. If the description is confusing or duplicative for sighted users, you can use the \`@radix-ui/react-visually-hidden\` primitive as a wrapper around your description component.

For more information, see https://radix-ui.com/primitives/docs/components/alert-dialog`;return i.useEffect(()=>{document.getElementById(e.current?.getAttribute("aria-describedby"))||console.warn(t)},[t,e]),null};let rt=i.forwardRef(({className:e,...t},r)=>(0,o.jsx)(tJ,{className:(0,W.cn)("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...t,ref:r}));rt.displayName=tJ.displayName;let rr=i.forwardRef(({className:e,...t},r)=>(0,o.jsxs)(tZ,{children:[(0,o.jsx)(rt,{}),(0,o.jsx)(t2,{ref:r,className:(0,W.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",e),...t})]}));rr.displayName=t2.displayName;let rn=({className:e,...t})=>(0,o.jsx)("div",{className:(0,W.cn)("flex flex-col space-y-2 text-center sm:text-left",e),...t});rn.displayName="AlertDialogHeader";let ro=({className:e,...t})=>(0,o.jsx)("div",{className:(0,W.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",e),...t});ro.displayName="AlertDialogFooter";let ri=i.forwardRef(({className:e,...t},r)=>(0,o.jsx)(t4,{ref:r,className:(0,W.cn)("text-lg font-semibold",e),...t}));ri.displayName=t4.displayName;let ra=i.forwardRef(({className:e,...t},r)=>(0,o.jsx)(t6,{ref:r,className:(0,W.cn)("text-sm text-muted-foreground",e),...t}));ra.displayName=t6.displayName;let rl=i.forwardRef(({className:e,...t},r)=>(0,o.jsx)(t8,{ref:r,className:(0,W.cn)((0,K.r)(),e),...t}));rl.displayName=t8.displayName;let rs=i.forwardRef(({className:e,...t},r)=>(0,o.jsx)(t9,{ref:r,className:(0,W.cn)((0,K.r)({variant:"outline"}),"mt-2 sm:mt-0",e),...t}));rs.displayName=t9.displayName;let rc=(0,P.A)("LayoutList",[["rect",{width:"7",height:"7",x:"3",y:"3",rx:"1",key:"1g98yp"}],["rect",{width:"7",height:"7",x:"3",y:"14",rx:"1",key:"1bb6yr"}],["path",{d:"M14 4h7",key:"3xa0d5"}],["path",{d:"M14 9h7",key:"1icrd9"}],["path",{d:"M14 15h7",key:"1mj8o2"}],["path",{d:"M14 20h7",key:"11slyb"}]]);var ru=r(76311),rf=r(11003);function rd({categories:e,onAddCategory:t,onUpdateCategory:r,onDeleteCategory:n,onAddSubCategory:a,onUpdateSubCategory:l,onDeleteSubCategory:s,onToggleCategoryVisibility:c,balancesVisible:u}){let[f,d]=(0,i.useState)(!1),[p,h]=(0,i.useState)(null),[y,m]=(0,i.useState)(null),{toast:v}=(0,tF.dj)();return(0,o.jsxs)(A.Zp,{children:[(0,o.jsxs)(A.aR,{className:"flex flex-row items-center justify-between pb-2",children:[(0,o.jsxs)(A.ZB,{className:"text-lg flex items-center gap-2 font-headline",children:[(0,o.jsx)(rc,{className:"h-5 w-5 text-primary"}),"Budget Categories"]}),(0,o.jsxs)(e7,{open:f,onOpenChange:e=>{d(e),e||h(null)},children:[(0,o.jsx)(te,{asChild:!0,children:(0,o.jsxs)(K.$,{size:"sm",onClick:()=>{h(null),d(!0)},children:[(0,o.jsx)(tU,{className:"mr-2 h-4 w-4"})," Add Category"]})}),(0,o.jsxs)(tk,{className:"sm:max-w-[425px]",children:[(0,o.jsx)(tE,{children:(0,o.jsxs)(tN,{className:"font-headline",children:[p?"Edit":"Add"," Category"]})}),(0,o.jsx)(tV,{onSubmit:p?e=>{p&&(r(p.id,e.name,e.budget),v({title:"Category Updated",description:`${e.name} has been updated.`}),h(null),d(!1))}:e=>{t(e.name,e.budget),v({title:"Category Added",description:`${e.name} has been added.`}),d(!1)},initialData:p||{},onClose:()=>{d(!1),h(null)}})]})]})]}),(0,o.jsx)(A.Wu,{className:"pt-2",children:0===e.length?(0,o.jsx)("p",{className:"text-sm text-muted-foreground text-center py-4",children:'No categories added yet. Click "Add Category" to start.'}):(0,o.jsx)("div",{className:"space-y-2",children:e.map(e=>(0,o.jsxs)("div",{className:"relative group",children:[(0,o.jsx)(tq,{category:e,onUpdateCategory:r,onDeleteCategory:n,onAddSubCategory:a,onUpdateSubCategory:l,onDeleteSubCategory:s,balancesVisible:u,categoryIsVisible:e.isVisible??!0,onToggleVisibility:()=>c(e.id)}),(0,o.jsxs)("div",{className:"absolute top-2 right-2 flex gap-1 opacity-0 group-hover:opacity-100 transition-opacity",children:[(0,o.jsxs)(K.$,{variant:"ghost",size:"icon",className:"h-7 w-7 bg-card hover:bg-muted",onClick:()=>c(e.id),children:[e.isVisible??!0?(0,o.jsx)(ru.A,{className:"h-4 w-4"}):(0,o.jsx)(rf.A,{className:"h-4 w-4"}),(0,o.jsx)("span",{className:"sr-only",children:e.isVisible??!0?"Hide Category Balances":"Show Category Balances"})]}),(0,o.jsxs)(K.$,{variant:"ghost",size:"icon",className:"h-7 w-7 bg-card hover:bg-muted",onClick:()=>{h(e),d(!0)},children:[(0,o.jsx)(tM,{className:"h-4 w-4"}),(0,o.jsx)("span",{className:"sr-only",children:"Edit Category"})]}),(0,o.jsxs)(K.$,{variant:"ghost",size:"icon",className:"h-7 w-7 text-destructive hover:text-destructive bg-card hover:bg-muted",onClick:()=>m(e.id),children:[(0,o.jsx)(tC.A,{className:"h-4 w-4"}),(0,o.jsx)("span",{className:"sr-only",children:"Delete Category"})]})]})]},e.id))})}),(0,o.jsx)(tK,{open:!!y,onOpenChange:()=>m(null),children:(0,o.jsxs)(rr,{children:[(0,o.jsxs)(rn,{children:[(0,o.jsx)(ri,{children:"Are you sure?"}),(0,o.jsxs)(ra,{children:["This action will delete the category",e.find(e=>e.id===y)?.subCategories.length?" and all its subcategories":"",". This cannot be undone."]})]}),(0,o.jsxs)(ro,{children:[(0,o.jsx)(rs,{children:"Cancel"}),(0,o.jsx)(rl,{onClick:()=>{if(y){let t=e.find(e=>e.id===y);n(y),v({title:"Category Deleted",description:`${t?.name||"Category"} has been deleted.`}),m(null)}},className:"bg-destructive hover:bg-destructive/90",children:"Delete"})]})]})})]})}let rp=(0,P.A)("ChartPie",[["path",{d:"M21 12c.552 0 1.005-.449.95-.998a10 10 0 0 0-8.953-8.951c-.55-.055-.998.398-.998.95v8a1 1 0 0 0 1 1z",key:"pzmjnu"}],["path",{d:"M21.21 15.89A10 10 0 1 1 8 2.83",key:"k2fpak"}]]);var rh=r(49384),ry=r(45603),rm=r.n(ry),rv=r(63866),rg=r.n(rv),rb=r(77822),rx=r.n(rb),rw=r(40491),rj=r.n(rw),rO=r(93490),rS=r.n(rO),rA=function(e){return 0===e?0:e>0?1:-1},rP=function(e){return rg()(e)&&e.indexOf("%")===e.length-1},rk=function(e){return rS()(e)&&!rx()(e)},rE=function(e){return rk(e)||rg()(e)},rN=0,rM=function(e){var t=++rN;return"".concat(e||"").concat(t)},rC=function(e,t){var r,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,o=arguments.length>3&&void 0!==arguments[3]&&arguments[3];if(!rk(e)&&!rg()(e))return n;if(rP(e)){var i=e.indexOf("%");r=t*parseFloat(e.slice(0,i))/100}else r=+e;return rx()(r)&&(r=n),o&&r>t&&(r=t),r},rT=function(e){if(!e)return null;var t=Object.keys(e);return t&&t.length?e[t[0]]:null},r_=function(e){if(!Array.isArray(e))return!1;for(var t=e.length,r={},n=0;n<t;n++){if(r[e[n]])return!0;r[e[n]]=!0}return!1},rI=function(e,t){return rk(e)&&rk(t)?function(r){return e+r*(t-e)}:function(){return t}};function rD(e,t,r){return e&&e.length?e.find(function(e){return e&&("function"==typeof t?t(e):rj()(e,t))===r}):null}var rR=function(e,t){for(var r=arguments.length,n=Array(r>2?r-2:0),o=2;o<r;o++)n[o-2]=arguments[o]},rB=r(37456),rL=r.n(rB),r$=r(5231),rz=r.n(r$),rU=r(55048),rF=r.n(rU),rq=r(29632);function rW(e,t){for(var r in e)if(({}).hasOwnProperty.call(e,r)&&(!({}).hasOwnProperty.call(t,r)||e[r]!==t[r]))return!1;for(var n in t)if(({}).hasOwnProperty.call(t,n)&&!({}).hasOwnProperty.call(e,n))return!1;return!0}function rV(e){return(rV="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var rH=["aria-activedescendant","aria-atomic","aria-autocomplete","aria-busy","aria-checked","aria-colcount","aria-colindex","aria-colspan","aria-controls","aria-current","aria-describedby","aria-details","aria-disabled","aria-errormessage","aria-expanded","aria-flowto","aria-haspopup","aria-hidden","aria-invalid","aria-keyshortcuts","aria-label","aria-labelledby","aria-level","aria-live","aria-modal","aria-multiline","aria-multiselectable","aria-orientation","aria-owns","aria-placeholder","aria-posinset","aria-pressed","aria-readonly","aria-relevant","aria-required","aria-roledescription","aria-rowcount","aria-rowindex","aria-rowspan","aria-selected","aria-setsize","aria-sort","aria-valuemax","aria-valuemin","aria-valuenow","aria-valuetext","className","color","height","id","lang","max","media","method","min","name","style","target","width","role","tabIndex","accentHeight","accumulate","additive","alignmentBaseline","allowReorder","alphabetic","amplitude","arabicForm","ascent","attributeName","attributeType","autoReverse","azimuth","baseFrequency","baselineShift","baseProfile","bbox","begin","bias","by","calcMode","capHeight","clip","clipPath","clipPathUnits","clipRule","colorInterpolation","colorInterpolationFilters","colorProfile","colorRendering","contentScriptType","contentStyleType","cursor","cx","cy","d","decelerate","descent","diffuseConstant","direction","display","divisor","dominantBaseline","dur","dx","dy","edgeMode","elevation","enableBackground","end","exponent","externalResourcesRequired","fill","fillOpacity","fillRule","filter","filterRes","filterUnits","floodColor","floodOpacity","focusable","fontFamily","fontSize","fontSizeAdjust","fontStretch","fontStyle","fontVariant","fontWeight","format","from","fx","fy","g1","g2","glyphName","glyphOrientationHorizontal","glyphOrientationVertical","glyphRef","gradientTransform","gradientUnits","hanging","horizAdvX","horizOriginX","href","ideographic","imageRendering","in2","in","intercept","k1","k2","k3","k4","k","kernelMatrix","kernelUnitLength","kerning","keyPoints","keySplines","keyTimes","lengthAdjust","letterSpacing","lightingColor","limitingConeAngle","local","markerEnd","markerHeight","markerMid","markerStart","markerUnits","markerWidth","mask","maskContentUnits","maskUnits","mathematical","mode","numOctaves","offset","opacity","operator","order","orient","orientation","origin","overflow","overlinePosition","overlineThickness","paintOrder","panose1","pathLength","patternContentUnits","patternTransform","patternUnits","pointerEvents","pointsAtX","pointsAtY","pointsAtZ","preserveAlpha","preserveAspectRatio","primitiveUnits","r","radius","refX","refY","renderingIntent","repeatCount","repeatDur","requiredExtensions","requiredFeatures","restart","result","rotate","rx","ry","seed","shapeRendering","slope","spacing","specularConstant","specularExponent","speed","spreadMethod","startOffset","stdDeviation","stemh","stemv","stitchTiles","stopColor","stopOpacity","strikethroughPosition","strikethroughThickness","string","stroke","strokeDasharray","strokeDashoffset","strokeLinecap","strokeLinejoin","strokeMiterlimit","strokeOpacity","strokeWidth","surfaceScale","systemLanguage","tableValues","targetX","targetY","textAnchor","textDecoration","textLength","textRendering","to","transform","u1","u2","underlinePosition","underlineThickness","unicode","unicodeBidi","unicodeRange","unitsPerEm","vAlphabetic","values","vectorEffect","version","vertAdvY","vertOriginX","vertOriginY","vHanging","vIdeographic","viewTarget","visibility","vMathematical","widths","wordSpacing","writingMode","x1","x2","x","xChannelSelector","xHeight","xlinkActuate","xlinkArcrole","xlinkHref","xlinkRole","xlinkShow","xlinkTitle","xlinkType","xmlBase","xmlLang","xmlns","xmlnsXlink","xmlSpace","y1","y2","y","yChannelSelector","z","zoomAndPan","ref","key","angle"],rG=["points","pathLength"],rX={svg:["viewBox","children"],polygon:rG,polyline:rG},rY=["dangerouslySetInnerHTML","onCopy","onCopyCapture","onCut","onCutCapture","onPaste","onPasteCapture","onCompositionEnd","onCompositionEndCapture","onCompositionStart","onCompositionStartCapture","onCompositionUpdate","onCompositionUpdateCapture","onFocus","onFocusCapture","onBlur","onBlurCapture","onChange","onChangeCapture","onBeforeInput","onBeforeInputCapture","onInput","onInputCapture","onReset","onResetCapture","onSubmit","onSubmitCapture","onInvalid","onInvalidCapture","onLoad","onLoadCapture","onError","onErrorCapture","onKeyDown","onKeyDownCapture","onKeyPress","onKeyPressCapture","onKeyUp","onKeyUpCapture","onAbort","onAbortCapture","onCanPlay","onCanPlayCapture","onCanPlayThrough","onCanPlayThroughCapture","onDurationChange","onDurationChangeCapture","onEmptied","onEmptiedCapture","onEncrypted","onEncryptedCapture","onEnded","onEndedCapture","onLoadedData","onLoadedDataCapture","onLoadedMetadata","onLoadedMetadataCapture","onLoadStart","onLoadStartCapture","onPause","onPauseCapture","onPlay","onPlayCapture","onPlaying","onPlayingCapture","onProgress","onProgressCapture","onRateChange","onRateChangeCapture","onSeeked","onSeekedCapture","onSeeking","onSeekingCapture","onStalled","onStalledCapture","onSuspend","onSuspendCapture","onTimeUpdate","onTimeUpdateCapture","onVolumeChange","onVolumeChangeCapture","onWaiting","onWaitingCapture","onAuxClick","onAuxClickCapture","onClick","onClickCapture","onContextMenu","onContextMenuCapture","onDoubleClick","onDoubleClickCapture","onDrag","onDragCapture","onDragEnd","onDragEndCapture","onDragEnter","onDragEnterCapture","onDragExit","onDragExitCapture","onDragLeave","onDragLeaveCapture","onDragOver","onDragOverCapture","onDragStart","onDragStartCapture","onDrop","onDropCapture","onMouseDown","onMouseDownCapture","onMouseEnter","onMouseLeave","onMouseMove","onMouseMoveCapture","onMouseOut","onMouseOutCapture","onMouseOver","onMouseOverCapture","onMouseUp","onMouseUpCapture","onSelect","onSelectCapture","onTouchCancel","onTouchCancelCapture","onTouchEnd","onTouchEndCapture","onTouchMove","onTouchMoveCapture","onTouchStart","onTouchStartCapture","onPointerDown","onPointerDownCapture","onPointerMove","onPointerMoveCapture","onPointerUp","onPointerUpCapture","onPointerCancel","onPointerCancelCapture","onPointerEnter","onPointerEnterCapture","onPointerLeave","onPointerLeaveCapture","onPointerOver","onPointerOverCapture","onPointerOut","onPointerOutCapture","onGotPointerCapture","onGotPointerCaptureCapture","onLostPointerCapture","onLostPointerCaptureCapture","onScroll","onScrollCapture","onWheel","onWheelCapture","onAnimationStart","onAnimationStartCapture","onAnimationEnd","onAnimationEndCapture","onAnimationIteration","onAnimationIterationCapture","onTransitionEnd","onTransitionEndCapture"],rK=function(e,t){if(!e||"function"==typeof e||"boolean"==typeof e)return null;var r=e;if((0,i.isValidElement)(e)&&(r=e.props),!rF()(r))return null;var n={};return Object.keys(r).forEach(function(e){rY.includes(e)&&(n[e]=t||function(t){return r[e](r,t)})}),n},rZ=function(e,t,r){if(!rF()(e)||"object"!==rV(e))return null;var n=null;return Object.keys(e).forEach(function(o){var i=e[o];rY.includes(o)&&"function"==typeof i&&(n||(n={}),n[o]=function(e){return i(t,r,e),null})}),n},rJ=["children"],rQ=["children"];function r0(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}var r1={click:"onClick",mousedown:"onMouseDown",mouseup:"onMouseUp",mouseover:"onMouseOver",mousemove:"onMouseMove",mouseout:"onMouseOut",mouseenter:"onMouseEnter",mouseleave:"onMouseLeave",touchcancel:"onTouchCancel",touchend:"onTouchEnd",touchmove:"onTouchMove",touchstart:"onTouchStart",contextmenu:"onContextMenu",dblclick:"onDoubleClick"},r2=function(e){return"string"==typeof e?e:e?e.displayName||e.name||"Component":""},r5=null,r4=null,r3=function e(t){if(t===r5&&Array.isArray(r4))return r4;var r=[];return i.Children.forEach(t,function(t){rL()(t)||((0,rq.isFragment)(t)?r=r.concat(e(t.props.children)):r.push(t))}),r4=r,r5=t,r};function r6(e,t){var r=[],n=[];return n=Array.isArray(t)?t.map(function(e){return r2(e)}):[r2(t)],r3(e).forEach(function(e){var t=rj()(e,"type.displayName")||rj()(e,"type.name");-1!==n.indexOf(t)&&r.push(e)}),r}function r8(e,t){var r=r6(e,t);return r&&r[0]}var r7=function(e){if(!e||!e.props)return!1;var t=e.props,r=t.width,n=t.height;return!!rk(r)&&!(r<=0)&&!!rk(n)&&!(n<=0)},r9=["a","altGlyph","altGlyphDef","altGlyphItem","animate","animateColor","animateMotion","animateTransform","circle","clipPath","color-profile","cursor","defs","desc","ellipse","feBlend","feColormatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence","filter","font","font-face","font-face-format","font-face-name","font-face-url","foreignObject","g","glyph","glyphRef","hkern","image","line","lineGradient","marker","mask","metadata","missing-glyph","mpath","path","pattern","polygon","polyline","radialGradient","rect","script","set","stop","style","svg","switch","symbol","text","textPath","title","tref","tspan","use","view","vkern"],ne=function(e,t,r,n){var o,i=null!==(o=null==rX?void 0:rX[n])&&void 0!==o?o:[];return!rz()(e)&&(n&&i.includes(t)||rH.includes(t))||r&&rY.includes(t)},nt=function(e,t,r){if(!e||"function"==typeof e||"boolean"==typeof e)return null;var n=e;if((0,i.isValidElement)(e)&&(n=e.props),!rF()(n))return null;var o={};return Object.keys(n).forEach(function(e){var i;ne(null===(i=n)||void 0===i?void 0:i[e],e,t,r)&&(o[e]=n[e])}),o},nr=function e(t,r){if(t===r)return!0;var n=i.Children.count(t);if(n!==i.Children.count(r))return!1;if(0===n)return!0;if(1===n)return nn(Array.isArray(t)?t[0]:t,Array.isArray(r)?r[0]:r);for(var o=0;o<n;o++){var a=t[o],l=r[o];if(Array.isArray(a)||Array.isArray(l)){if(!e(a,l))return!1}else if(!nn(a,l))return!1}return!0},nn=function(e,t){if(rL()(e)&&rL()(t))return!0;if(!rL()(e)&&!rL()(t)){var r=e.props||{},n=r.children,o=r0(r,rJ),i=t.props||{},a=i.children,l=r0(i,rQ);if(n&&a)return rW(o,l)&&nr(n,a);if(!n&&!a)return rW(o,l)}return!1},no=function(e,t){var r=[],n={};return r3(e).forEach(function(e,o){var i;if((i=e)&&i.type&&rg()(i.type)&&r9.indexOf(i.type)>=0)r.push(e);else if(e){var a=r2(e.type),l=t[a]||{},s=l.handler,c=l.once;if(s&&(!c||!n[a])){var u=s(e,a,o);r.push(u),n[a]=!0}}}),r},ni=function(e){var t=e&&e.type;return t&&r1[t]?r1[t]:null};function na(e){return(na="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function nl(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function ns(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?nl(Object(r),!0).forEach(function(t){var n,o,i;n=e,o=t,i=r[t],(o=function(e){var t=function(e,t){if("object"!=na(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=na(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==na(t)?t:t+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):nl(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function nc(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}var nu=(0,i.forwardRef)(function(e,t){var r,n=e.aspect,o=e.initialDimension,l=void 0===o?{width:-1,height:-1}:o,s=e.width,c=void 0===s?"100%":s,u=e.height,f=void 0===u?"100%":u,d=e.minWidth,p=void 0===d?0:d,h=e.minHeight,y=e.maxHeight,m=e.children,v=e.debounce,g=void 0===v?0:v,b=e.id,x=e.className,w=e.onResize,j=e.style,O=(0,i.useRef)(null),S=(0,i.useRef)();S.current=w,(0,i.useImperativeHandle)(t,function(){return Object.defineProperty(O.current,"current",{get:function(){return console.warn("The usage of ref.current.current is deprecated and will no longer be supported."),O.current},configurable:!0})});var A=function(e){if(Array.isArray(e))return e}(r=(0,i.useState)({containerWidth:l.width,containerHeight:l.height}))||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,a,l=[],s=!0,c=!1;try{i=(r=r.call(e)).next;for(;!(s=(n=i.call(r)).done)&&(l.push(n.value),l.length!==t);s=!0);}catch(e){c=!0,o=e}finally{try{if(!s&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(c)throw o}}return l}}(r,2)||function(e,t){if(e){if("string"==typeof e)return nc(e,2);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return nc(e,t)}}(r,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),P=A[0],k=A[1],E=(0,i.useCallback)(function(e,t){k(function(r){var n=Math.round(e),o=Math.round(t);return r.containerWidth===n&&r.containerHeight===o?r:{containerWidth:n,containerHeight:o}})},[]);(0,i.useEffect)(function(){var e=function(e){var t,r=e[0].contentRect,n=r.width,o=r.height;E(n,o),null===(t=S.current)||void 0===t||t.call(S,n,o)};g>0&&(e=rm()(e,g,{trailing:!0,leading:!1}));var t=new ResizeObserver(e),r=O.current.getBoundingClientRect();return E(r.width,r.height),t.observe(O.current),function(){t.disconnect()}},[E,g]);var N=(0,i.useMemo)(function(){var e=P.containerWidth,t=P.containerHeight;if(e<0||t<0)return null;rR(rP(c)||rP(f),"The width(%s) and height(%s) are both fixed numbers,\n       maybe you don't need to use a ResponsiveContainer.",c,f),rR(!n||n>0,"The aspect(%s) must be greater than zero.",n);var r=rP(c)?e:c,o=rP(f)?t:f;n&&n>0&&(r?o=r/n:o&&(r=o*n),y&&o>y&&(o=y)),rR(r>0||o>0,"The width(%s) and height(%s) of chart should be greater than 0,\n       please check the style of container, or the props width(%s) and height(%s),\n       or add a minWidth(%s) or minHeight(%s) or use aspect(%s) to control the\n       height and width.",r,o,c,f,p,h,n);var l=!Array.isArray(m)&&r2(m.type).endsWith("Chart");return a().Children.map(m,function(e){return a().isValidElement(e)?(0,i.cloneElement)(e,ns({width:r,height:o},l?{style:ns({height:"100%",width:"100%",maxHeight:o,maxWidth:r},e.props.style)}:{})):e})},[n,m,f,y,h,p,P,c]);return a().createElement("div",{id:b?"".concat(b):void 0,className:(0,rh.A)("recharts-responsive-container",x),style:ns(ns({},void 0===j?{}:j),{},{width:c,height:f,minWidth:p,minHeight:h,maxHeight:y}),ref:O},N)}),nf=r(85938),nd=r.n(nf);function np(e){return(np="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function nh(){return(nh=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function ny(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function nm(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function nv(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?nm(Object(r),!0).forEach(function(t){var n,o,i;n=e,o=t,i=r[t],(o=function(e){var t=function(e,t){if("object"!=np(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=np(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==np(t)?t:t+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):nm(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function ng(e){return Array.isArray(e)&&rE(e[0])&&rE(e[1])?e.join(" ~ "):e}var nb=function(e){var t=e.separator,r=void 0===t?" : ":t,n=e.contentStyle,o=e.itemStyle,i=void 0===o?{}:o,l=e.labelStyle,s=e.payload,c=e.formatter,u=e.itemSorter,f=e.wrapperClassName,d=e.labelClassName,p=e.label,h=e.labelFormatter,y=e.accessibilityLayer,m=nv({margin:0,padding:10,backgroundColor:"#fff",border:"1px solid #ccc",whiteSpace:"nowrap"},void 0===n?{}:n),v=nv({margin:0},void 0===l?{}:l),g=!rL()(p),b=g?p:"",x=(0,rh.A)("recharts-default-tooltip",f),w=(0,rh.A)("recharts-tooltip-label",d);return g&&h&&null!=s&&(b=h(p,s)),a().createElement("div",nh({className:x,style:m},void 0!==y&&y?{role:"status","aria-live":"assertive"}:{}),a().createElement("p",{className:w,style:v},a().isValidElement(b)?b:"".concat(b)),function(){if(s&&s.length){var e=(u?nd()(s,u):s).map(function(e,t){if("none"===e.type)return null;var n=nv({display:"block",paddingTop:4,paddingBottom:4,color:e.color||"#000"},i),o=e.formatter||c||ng,l=e.value,u=e.name,f=l,d=u;if(o&&null!=f&&null!=d){var p=o(l,u,e,t,s);if(Array.isArray(p)){var h=function(e){if(Array.isArray(e))return e}(p)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,a,l=[],s=!0,c=!1;try{i=(r=r.call(e)).next;for(;!(s=(n=i.call(r)).done)&&(l.push(n.value),l.length!==t);s=!0);}catch(e){c=!0,o=e}finally{try{if(!s&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(c)throw o}}return l}}(p,2)||function(e,t){if(e){if("string"==typeof e)return ny(e,2);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return ny(e,t)}}(p,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}();f=h[0],d=h[1]}else f=p}return a().createElement("li",{className:"recharts-tooltip-item",key:"tooltip-item-".concat(t),style:n},rE(d)?a().createElement("span",{className:"recharts-tooltip-item-name"},d):null,rE(d)?a().createElement("span",{className:"recharts-tooltip-item-separator"},r):null,a().createElement("span",{className:"recharts-tooltip-item-value"},f),a().createElement("span",{className:"recharts-tooltip-item-unit"},e.unit||""))});return a().createElement("ul",{className:"recharts-tooltip-item-list",style:{padding:0,margin:0}},e)}return null}())};function nx(e){return(nx="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function nw(e,t,r){var n;return(n=function(e,t){if("object"!=nx(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=nx(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"),(t="symbol"==nx(n)?n:n+"")in e)?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var nj="recharts-tooltip-wrapper",nO={visibility:"hidden"};function nS(e){var t=e.allowEscapeViewBox,r=e.coordinate,n=e.key,o=e.offsetTopLeft,i=e.position,a=e.reverseDirection,l=e.tooltipDimension,s=e.viewBox,c=e.viewBoxDimension;if(i&&rk(i[n]))return i[n];var u=r[n]-l-o,f=r[n]+o;return t[n]?a[n]?u:f:a[n]?u<s[n]?Math.max(f,s[n]):Math.max(u,s[n]):f+l>s[n]+c?Math.max(u,s[n]):Math.max(f,s[n])}function nA(e){return(nA="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function nP(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function nk(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?nP(Object(r),!0).forEach(function(t){nC(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):nP(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function nE(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(nE=function(){return!!e})()}function nN(e){return(nN=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function nM(e,t){return(nM=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function nC(e,t,r){return(t=nT(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function nT(e){var t=function(e,t){if("object"!=nA(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=nA(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==nA(t)?t:t+""}var n_=function(e){var t;function r(){!function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}(this,r);for(var e,t,n,o=arguments.length,i=Array(o),a=0;a<o;a++)i[a]=arguments[a];return t=r,n=[].concat(i),t=nN(t),nC(e=function(e,t){if(t&&("object"===nA(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,nE()?Reflect.construct(t,n||[],nN(this).constructor):t.apply(this,n)),"state",{dismissed:!1,dismissedAtCoordinate:{x:0,y:0},lastBoundingBox:{width:-1,height:-1}}),nC(e,"handleKeyDown",function(t){if("Escape"===t.key){var r,n,o,i;e.setState({dismissed:!0,dismissedAtCoordinate:{x:null!==(r=null===(n=e.props.coordinate)||void 0===n?void 0:n.x)&&void 0!==r?r:0,y:null!==(o=null===(i=e.props.coordinate)||void 0===i?void 0:i.y)&&void 0!==o?o:0}})}}),e}return function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&nM(e,t)}(r,e),t=[{key:"updateBBox",value:function(){if(this.wrapperNode&&this.wrapperNode.getBoundingClientRect){var e=this.wrapperNode.getBoundingClientRect();(Math.abs(e.width-this.state.lastBoundingBox.width)>1||Math.abs(e.height-this.state.lastBoundingBox.height)>1)&&this.setState({lastBoundingBox:{width:e.width,height:e.height}})}else(-1!==this.state.lastBoundingBox.width||-1!==this.state.lastBoundingBox.height)&&this.setState({lastBoundingBox:{width:-1,height:-1}})}},{key:"componentDidMount",value:function(){document.addEventListener("keydown",this.handleKeyDown),this.updateBBox()}},{key:"componentWillUnmount",value:function(){document.removeEventListener("keydown",this.handleKeyDown)}},{key:"componentDidUpdate",value:function(){var e,t;this.props.active&&this.updateBBox(),this.state.dismissed&&((null===(e=this.props.coordinate)||void 0===e?void 0:e.x)!==this.state.dismissedAtCoordinate.x||(null===(t=this.props.coordinate)||void 0===t?void 0:t.y)!==this.state.dismissedAtCoordinate.y)&&(this.state.dismissed=!1)}},{key:"render",value:function(){var e,t,r,n,o,i,l,s,c,u,f,d,p,h,y,m,v,g,b,x=this,w=this.props,j=w.active,O=w.allowEscapeViewBox,S=w.animationDuration,A=w.animationEasing,P=w.children,k=w.coordinate,E=w.hasPayload,N=w.isAnimationActive,M=w.offset,C=w.position,T=w.reverseDirection,_=w.useTranslate3d,I=w.viewBox,D=w.wrapperStyle,R=(d=(e={allowEscapeViewBox:O,coordinate:k,offsetTopLeft:M,position:C,reverseDirection:T,tooltipBox:this.state.lastBoundingBox,useTranslate3d:_,viewBox:I}).allowEscapeViewBox,p=e.coordinate,h=e.offsetTopLeft,y=e.position,m=e.reverseDirection,v=e.tooltipBox,g=e.useTranslate3d,b=e.viewBox,v.height>0&&v.width>0&&p?(r=(t={translateX:u=nS({allowEscapeViewBox:d,coordinate:p,key:"x",offsetTopLeft:h,position:y,reverseDirection:m,tooltipDimension:v.width,viewBox:b,viewBoxDimension:b.width}),translateY:f=nS({allowEscapeViewBox:d,coordinate:p,key:"y",offsetTopLeft:h,position:y,reverseDirection:m,tooltipDimension:v.height,viewBox:b,viewBoxDimension:b.height}),useTranslate3d:g}).translateX,n=t.translateY,c={transform:t.useTranslate3d?"translate3d(".concat(r,"px, ").concat(n,"px, 0)"):"translate(".concat(r,"px, ").concat(n,"px)")}):c=nO,{cssProperties:c,cssClasses:(i=(o={translateX:u,translateY:f,coordinate:p}).coordinate,l=o.translateX,s=o.translateY,(0,rh.A)(nj,nw(nw(nw(nw({},"".concat(nj,"-right"),rk(l)&&i&&rk(i.x)&&l>=i.x),"".concat(nj,"-left"),rk(l)&&i&&rk(i.x)&&l<i.x),"".concat(nj,"-bottom"),rk(s)&&i&&rk(i.y)&&s>=i.y),"".concat(nj,"-top"),rk(s)&&i&&rk(i.y)&&s<i.y)))}),B=R.cssClasses,L=R.cssProperties,$=nk(nk({transition:N&&j?"transform ".concat(S,"ms ").concat(A):void 0},L),{},{pointerEvents:"none",visibility:!this.state.dismissed&&j&&E?"visible":"hidden",position:"absolute",top:0,left:0},D);return a().createElement("div",{tabIndex:-1,className:B,style:$,ref:function(e){x.wrapperNode=e}},P)}}],function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,nT(n.key),n)}}(r.prototype,t),Object.defineProperty(r,"prototype",{writable:!1}),r}(i.PureComponent),nI={isSsr:!0,get:function(e){return nI[e]},set:function(e,t){if("string"==typeof e)nI[e]=t;else{var r=Object.keys(e);r&&r.length&&r.forEach(function(t){nI[t]=e[t]})}}},nD=r(36315),nR=r.n(nD);function nB(e,t,r){return!0===t?nR()(e,r):rz()(t)?nR()(e,t):e}function nL(e){return(nL="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function n$(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function nz(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?n$(Object(r),!0).forEach(function(t){nW(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):n$(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function nU(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(nU=function(){return!!e})()}function nF(e){return(nF=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function nq(e,t){return(nq=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function nW(e,t,r){return(t=nV(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function nV(e){var t=function(e,t){if("object"!=nL(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=nL(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==nL(t)?t:t+""}function nH(e){return e.dataKey}var nG=function(e){var t;function r(){var e,t;return function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}(this,r),e=r,t=arguments,e=nF(e),function(e,t){if(t&&("object"===nL(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,nU()?Reflect.construct(e,t||[],nF(this).constructor):e.apply(this,t))}return function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&nq(e,t)}(r,e),t=[{key:"render",value:function(){var e,t=this,r=this.props,n=r.active,o=r.allowEscapeViewBox,i=r.animationDuration,l=r.animationEasing,s=r.content,c=r.coordinate,u=r.filterNull,f=r.isAnimationActive,d=r.offset,p=r.payload,h=r.payloadUniqBy,y=r.position,m=r.reverseDirection,v=r.useTranslate3d,g=r.viewBox,b=r.wrapperStyle,x=null!=p?p:[];u&&x.length&&(x=nB(p.filter(function(e){return null!=e.value&&(!0!==e.hide||t.props.includeHidden)}),h,nH));var w=x.length>0;return a().createElement(n_,{allowEscapeViewBox:o,animationDuration:i,animationEasing:l,isAnimationActive:f,active:n,coordinate:c,hasPayload:w,offset:d,position:y,reverseDirection:m,useTranslate3d:v,viewBox:g,wrapperStyle:b},(e=nz(nz({},this.props),{},{payload:x}),a().isValidElement(s)?a().cloneElement(s,e):"function"==typeof s?a().createElement(s,e):a().createElement(nb,e)))}}],function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,nV(n.key),n)}}(r.prototype,t),Object.defineProperty(r,"prototype",{writable:!1}),r}(i.PureComponent);nW(nG,"displayName","Tooltip"),nW(nG,"defaultProps",{accessibilityLayer:!1,allowEscapeViewBox:{x:!1,y:!1},animationDuration:400,animationEasing:"ease",contentStyle:{},coordinate:{x:0,y:0},cursor:!0,cursorStyle:{},filterNull:!0,isAnimationActive:!nI.isSsr,itemStyle:{},labelStyle:{},offset:10,reverseDirection:{x:!1,y:!1},separator:" : ",trigger:"hover",useTranslate3d:!1,viewBox:{x:0,y:0,height:0,width:0},wrapperStyle:{}});var nX=["children","width","height","viewBox","className","style","title","desc"];function nY(){return(nY=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function nK(e){var t=e.children,r=e.width,n=e.height,o=e.viewBox,i=e.className,l=e.style,s=e.title,c=e.desc,u=function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(e,nX),f=o||{width:r,height:n,x:0,y:0},d=(0,rh.A)("recharts-surface",i);return a().createElement("svg",nY({},nt(u,!0,"svg"),{className:d,width:r,height:n,style:l,viewBox:"".concat(f.x," ").concat(f.y," ").concat(f.width," ").concat(f.height)}),a().createElement("title",null,s),a().createElement("desc",null,c),t)}var nZ=r(69433),nJ=r.n(nZ);let nQ=Math.cos,n0=Math.sin,n1=Math.sqrt,n2=Math.PI,n5=2*n2,n4={draw(e,t){let r=n1(t/n2);e.moveTo(r,0),e.arc(0,0,r,0,n5)}},n3=n1(1/3),n6=2*n3,n8=n0(n2/10)/n0(7*n2/10),n7=n0(n5/10)*n8,n9=-nQ(n5/10)*n8,oe=n1(3),ot=n1(3)/2,or=1/n1(12),on=(or/2+1)*3;function oo(e){return function(){return e}}let oi=Math.PI,oa=2*oi,ol=oa-1e-6;function os(e){this._+=e[0];for(let t=1,r=e.length;t<r;++t)this._+=arguments[t]+e[t]}class oc{constructor(e){this._x0=this._y0=this._x1=this._y1=null,this._="",this._append=null==e?os:function(e){let t=Math.floor(e);if(!(t>=0))throw Error(`invalid digits: ${e}`);if(t>15)return os;let r=10**t;return function(e){this._+=e[0];for(let t=1,n=e.length;t<n;++t)this._+=Math.round(arguments[t]*r)/r+e[t]}}(e)}moveTo(e,t){this._append`M${this._x0=this._x1=+e},${this._y0=this._y1=+t}`}closePath(){null!==this._x1&&(this._x1=this._x0,this._y1=this._y0,this._append`Z`)}lineTo(e,t){this._append`L${this._x1=+e},${this._y1=+t}`}quadraticCurveTo(e,t,r,n){this._append`Q${+e},${+t},${this._x1=+r},${this._y1=+n}`}bezierCurveTo(e,t,r,n,o,i){this._append`C${+e},${+t},${+r},${+n},${this._x1=+o},${this._y1=+i}`}arcTo(e,t,r,n,o){if(e*=1,t*=1,r*=1,n*=1,(o*=1)<0)throw Error(`negative radius: ${o}`);let i=this._x1,a=this._y1,l=r-e,s=n-t,c=i-e,u=a-t,f=c*c+u*u;if(null===this._x1)this._append`M${this._x1=e},${this._y1=t}`;else if(f>1e-6){if(Math.abs(u*l-s*c)>1e-6&&o){let d=r-i,p=n-a,h=l*l+s*s,y=Math.sqrt(h),m=Math.sqrt(f),v=o*Math.tan((oi-Math.acos((h+f-(d*d+p*p))/(2*y*m)))/2),g=v/m,b=v/y;Math.abs(g-1)>1e-6&&this._append`L${e+g*c},${t+g*u}`,this._append`A${o},${o},0,0,${+(u*d>c*p)},${this._x1=e+b*l},${this._y1=t+b*s}`}else this._append`L${this._x1=e},${this._y1=t}`}}arc(e,t,r,n,o,i){if(e*=1,t*=1,r*=1,i=!!i,r<0)throw Error(`negative radius: ${r}`);let a=r*Math.cos(n),l=r*Math.sin(n),s=e+a,c=t+l,u=1^i,f=i?n-o:o-n;null===this._x1?this._append`M${s},${c}`:(Math.abs(this._x1-s)>1e-6||Math.abs(this._y1-c)>1e-6)&&this._append`L${s},${c}`,r&&(f<0&&(f=f%oa+oa),f>ol?this._append`A${r},${r},0,1,${u},${e-a},${t-l}A${r},${r},0,1,${u},${this._x1=s},${this._y1=c}`:f>1e-6&&this._append`A${r},${r},0,${+(f>=oi)},${u},${this._x1=e+r*Math.cos(o)},${this._y1=t+r*Math.sin(o)}`)}rect(e,t,r,n){this._append`M${this._x0=this._x1=+e},${this._y0=this._y1=+t}h${r*=1}v${+n}h${-r}Z`}toString(){return this._}}function ou(e){let t=3;return e.digits=function(r){if(!arguments.length)return t;if(null==r)t=null;else{let e=Math.floor(r);if(!(e>=0))throw RangeError(`invalid digits: ${r}`);t=e}return e},()=>new oc(t)}function of(e){return(of="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}oc.prototype,n1(3),n1(3);var od=["type","size","sizeType"];function op(){return(op=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function oh(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function oy(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?oh(Object(r),!0).forEach(function(t){var n,o,i;n=e,o=t,i=r[t],(o=function(e){var t=function(e,t){if("object"!=of(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=of(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==of(t)?t:t+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):oh(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var om={symbolCircle:n4,symbolCross:{draw(e,t){let r=n1(t/5)/2;e.moveTo(-3*r,-r),e.lineTo(-r,-r),e.lineTo(-r,-3*r),e.lineTo(r,-3*r),e.lineTo(r,-r),e.lineTo(3*r,-r),e.lineTo(3*r,r),e.lineTo(r,r),e.lineTo(r,3*r),e.lineTo(-r,3*r),e.lineTo(-r,r),e.lineTo(-3*r,r),e.closePath()}},symbolDiamond:{draw(e,t){let r=n1(t/n6),n=r*n3;e.moveTo(0,-r),e.lineTo(n,0),e.lineTo(0,r),e.lineTo(-n,0),e.closePath()}},symbolSquare:{draw(e,t){let r=n1(t),n=-r/2;e.rect(n,n,r,r)}},symbolStar:{draw(e,t){let r=n1(.8908130915292852*t),n=n7*r,o=n9*r;e.moveTo(0,-r),e.lineTo(n,o);for(let t=1;t<5;++t){let i=n5*t/5,a=nQ(i),l=n0(i);e.lineTo(l*r,-a*r),e.lineTo(a*n-l*o,l*n+a*o)}e.closePath()}},symbolTriangle:{draw(e,t){let r=-n1(t/(3*oe));e.moveTo(0,2*r),e.lineTo(-oe*r,-r),e.lineTo(oe*r,-r),e.closePath()}},symbolWye:{draw(e,t){let r=n1(t/on),n=r/2,o=r*or,i=r*or+r,a=-n;e.moveTo(n,o),e.lineTo(n,i),e.lineTo(a,i),e.lineTo(-.5*n-ot*o,ot*n+-.5*o),e.lineTo(-.5*n-ot*i,ot*n+-.5*i),e.lineTo(-.5*a-ot*i,ot*a+-.5*i),e.lineTo(-.5*n+ot*o,-.5*o-ot*n),e.lineTo(-.5*n+ot*i,-.5*i-ot*n),e.lineTo(-.5*a+ot*i,-.5*i-ot*a),e.closePath()}}},ov=Math.PI/180,og=function(e,t,r){if("area"===t)return e;switch(r){case"cross":return 5*e*e/9;case"diamond":return .5*e*e/Math.sqrt(3);case"square":return e*e;case"star":var n=18*ov;return 1.25*e*e*(Math.tan(n)-Math.tan(2*n)*Math.pow(Math.tan(n),2));case"triangle":return Math.sqrt(3)*e*e/4;case"wye":return(21-10*Math.sqrt(3))*e*e/8;default:return Math.PI*e*e/4}},ob=function(e){var t,r=e.type,n=void 0===r?"circle":r,o=e.size,i=void 0===o?64:o,l=e.sizeType,s=void 0===l?"area":l,c=oy(oy({},function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(e,od)),{},{type:n,size:i,sizeType:s}),u=c.className,f=c.cx,d=c.cy,p=nt(c,!0);return f===+f&&d===+d&&i===+i?a().createElement("path",op({},p,{className:(0,rh.A)("recharts-symbols",u),transform:"translate(".concat(f,", ").concat(d,")"),d:(t=om["symbol".concat(nJ()(n))]||n4,(function(e,t){let r=null,n=ou(o);function o(){let o;if(r||(r=o=n()),e.apply(this,arguments).draw(r,+t.apply(this,arguments)),o)return r=null,o+""||null}return e="function"==typeof e?e:oo(e||n4),t="function"==typeof t?t:oo(void 0===t?64:+t),o.type=function(t){return arguments.length?(e="function"==typeof t?t:oo(t),o):e},o.size=function(e){return arguments.length?(t="function"==typeof e?e:oo(+e),o):t},o.context=function(e){return arguments.length?(r=null==e?null:e,o):r},o})().type(t).size(og(i,s,n))())})):null};function ox(e){return(ox="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function ow(){return(ow=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function oj(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}ob.registerSymbol=function(e,t){om["symbol".concat(nJ()(e))]=t};function oO(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(oO=function(){return!!e})()}function oS(e){return(oS=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function oA(e,t){return(oA=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function oP(e,t,r){return(t=ok(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function ok(e){var t=function(e,t){if("object"!=ox(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=ox(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==ox(t)?t:t+""}var oE=function(e){var t;function r(){var e,t;return function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}(this,r),e=r,t=arguments,e=oS(e),function(e,t){if(t&&("object"===ox(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,oO()?Reflect.construct(e,t||[],oS(this).constructor):e.apply(this,t))}return function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&oA(e,t)}(r,e),t=[{key:"renderIcon",value:function(e){var t=this.props.inactiveColor,r=32/6,n=32/3,o=e.inactive?t:e.color;if("plainline"===e.type)return a().createElement("line",{strokeWidth:4,fill:"none",stroke:o,strokeDasharray:e.payload.strokeDasharray,x1:0,y1:16,x2:32,y2:16,className:"recharts-legend-icon"});if("line"===e.type)return a().createElement("path",{strokeWidth:4,fill:"none",stroke:o,d:"M0,".concat(16,"h").concat(n,"\n            A").concat(r,",").concat(r,",0,1,1,").concat(2*n,",").concat(16,"\n            H").concat(32,"M").concat(2*n,",").concat(16,"\n            A").concat(r,",").concat(r,",0,1,1,").concat(n,",").concat(16),className:"recharts-legend-icon"});if("rect"===e.type)return a().createElement("path",{stroke:"none",fill:o,d:"M0,".concat(4,"h").concat(32,"v").concat(24,"h").concat(-32,"z"),className:"recharts-legend-icon"});if(a().isValidElement(e.legendIcon)){var i=function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?oj(Object(r),!0).forEach(function(t){oP(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):oj(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}({},e);return delete i.legendIcon,a().cloneElement(e.legendIcon,i)}return a().createElement(ob,{fill:o,cx:16,cy:16,size:32,sizeType:"diameter",type:e.type})}},{key:"renderItems",value:function(){var e=this,t=this.props,r=t.payload,n=t.iconSize,o=t.layout,i=t.formatter,l=t.inactiveColor,s={x:0,y:0,width:32,height:32},c={display:"horizontal"===o?"inline-block":"block",marginRight:10},u={display:"inline-block",verticalAlign:"middle",marginRight:4};return r.map(function(t,r){var o=t.formatter||i,f=(0,rh.A)(oP(oP({"recharts-legend-item":!0},"legend-item-".concat(r),!0),"inactive",t.inactive));if("none"===t.type)return null;var d=rz()(t.value)?null:t.value;rR(!rz()(t.value),'The name property is also required when using a function for the dataKey of a chart\'s cartesian components. Ex: <Bar name="Name of my Data"/>');var p=t.inactive?l:t.color;return a().createElement("li",ow({className:f,style:c,key:"legend-item-".concat(r)},rZ(e.props,t,r)),a().createElement(nK,{width:n,height:n,viewBox:s,style:u},e.renderIcon(t)),a().createElement("span",{className:"recharts-legend-item-text",style:{color:p}},o?o(d,t,r):d))})}},{key:"render",value:function(){var e=this.props,t=e.payload,r=e.layout,n=e.align;return t&&t.length?a().createElement("ul",{className:"recharts-default-legend",style:{padding:0,margin:0,textAlign:"horizontal"===r?n:"left"}},this.renderItems()):null}}],function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,ok(n.key),n)}}(r.prototype,t),Object.defineProperty(r,"prototype",{writable:!1}),r}(i.PureComponent);function oN(e){return(oN="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}oP(oE,"displayName","Legend"),oP(oE,"defaultProps",{iconSize:14,layout:"horizontal",align:"center",verticalAlign:"middle",inactiveColor:"#ccc"});var oM=["ref"];function oC(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function oT(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?oC(Object(r),!0).forEach(function(t){oB(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):oC(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function o_(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,oL(n.key),n)}}function oI(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(oI=function(){return!!e})()}function oD(e){return(oD=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function oR(e,t){return(oR=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function oB(e,t,r){return(t=oL(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function oL(e){var t=function(e,t){if("object"!=oN(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=oN(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==oN(t)?t:t+""}function o$(e){return e.value}var oz=function(e){var t,r;function n(){!function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}(this,n);for(var e,t,r,o=arguments.length,i=Array(o),a=0;a<o;a++)i[a]=arguments[a];return t=n,r=[].concat(i),t=oD(t),oB(e=function(e,t){if(t&&("object"===oN(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,oI()?Reflect.construct(t,r||[],oD(this).constructor):t.apply(this,r)),"lastBoundingBox",{width:-1,height:-1}),e}return function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&oR(e,t)}(n,e),t=[{key:"componentDidMount",value:function(){this.updateBBox()}},{key:"componentDidUpdate",value:function(){this.updateBBox()}},{key:"getBBox",value:function(){if(this.wrapperNode&&this.wrapperNode.getBoundingClientRect){var e=this.wrapperNode.getBoundingClientRect();return e.height=this.wrapperNode.offsetHeight,e.width=this.wrapperNode.offsetWidth,e}return null}},{key:"updateBBox",value:function(){var e=this.props.onBBoxUpdate,t=this.getBBox();t?(Math.abs(t.width-this.lastBoundingBox.width)>1||Math.abs(t.height-this.lastBoundingBox.height)>1)&&(this.lastBoundingBox.width=t.width,this.lastBoundingBox.height=t.height,e&&e(t)):(-1!==this.lastBoundingBox.width||-1!==this.lastBoundingBox.height)&&(this.lastBoundingBox.width=-1,this.lastBoundingBox.height=-1,e&&e(null))}},{key:"getBBoxSnapshot",value:function(){return this.lastBoundingBox.width>=0&&this.lastBoundingBox.height>=0?oT({},this.lastBoundingBox):{width:0,height:0}}},{key:"getDefaultPosition",value:function(e){var t,r,n=this.props,o=n.layout,i=n.align,a=n.verticalAlign,l=n.margin,s=n.chartWidth,c=n.chartHeight;return e&&(void 0!==e.left&&null!==e.left||void 0!==e.right&&null!==e.right)||(t="center"===i&&"vertical"===o?{left:((s||0)-this.getBBoxSnapshot().width)/2}:"right"===i?{right:l&&l.right||0}:{left:l&&l.left||0}),e&&(void 0!==e.top&&null!==e.top||void 0!==e.bottom&&null!==e.bottom)||(r="middle"===a?{top:((c||0)-this.getBBoxSnapshot().height)/2}:"bottom"===a?{bottom:l&&l.bottom||0}:{top:l&&l.top||0}),oT(oT({},t),r)}},{key:"render",value:function(){var e=this,t=this.props,r=t.content,n=t.width,o=t.height,i=t.wrapperStyle,l=t.payloadUniqBy,s=t.payload,c=oT(oT({position:"absolute",width:n||"auto",height:o||"auto"},this.getDefaultPosition(i)),i);return a().createElement("div",{className:"recharts-legend-wrapper",style:c,ref:function(t){e.wrapperNode=t}},function(e,t){if(a().isValidElement(e))return a().cloneElement(e,t);if("function"==typeof e)return a().createElement(e,t);t.ref;var r=function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(t,oM);return a().createElement(oE,r)}(r,oT(oT({},this.props),{},{payload:nB(s,l,o$)})))}}],r=[{key:"getWithHeight",value:function(e,t){var r=oT(oT({},this.defaultProps),e.props).layout;return"vertical"===r&&rk(e.props.height)?{height:e.props.height}:"horizontal"===r?{width:e.props.width||t}:null}}],t&&o_(n.prototype,t),r&&o_(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(i.PureComponent);oB(oz,"displayName","Legend"),oB(oz,"defaultProps",{iconSize:14,layout:"horizontal",align:"center",verticalAlign:"bottom"});let oU={light:"",dark:".dark"},oF=i.createContext(null);function oq(){let e=i.useContext(oF);if(!e)throw Error("useChart must be used within a <ChartContainer />");return e}let oW=i.forwardRef(({id:e,className:t,children:r,config:n,...a},l)=>{let s=i.useId(),c=`chart-${e||s.replace(/:/g,"")}`;return(0,o.jsx)(oF.Provider,{value:{config:n},children:(0,o.jsxs)("div",{"data-chart":c,ref:l,className:(0,W.cn)("flex aspect-video justify-center text-xs [&_.recharts-cartesian-axis-tick_text]:fill-muted-foreground [&_.recharts-cartesian-grid_line[stroke='#ccc']]:stroke-border/50 [&_.recharts-curve.recharts-tooltip-cursor]:stroke-border [&_.recharts-dot[stroke='#fff']]:stroke-transparent [&_.recharts-layer]:outline-none [&_.recharts-polar-grid_[stroke='#ccc']]:stroke-border [&_.recharts-radial-bar-background-sector]:fill-muted [&_.recharts-rectangle.recharts-tooltip-cursor]:fill-muted [&_.recharts-reference-line_[stroke='#ccc']]:stroke-border [&_.recharts-sector[stroke='#fff']]:stroke-transparent [&_.recharts-sector]:outline-none [&_.recharts-surface]:outline-none",t),...a,children:[(0,o.jsx)(oV,{id:c,config:n}),(0,o.jsx)(nu,{children:r})]})})});oW.displayName="Chart";let oV=({id:e,config:t})=>{let r=Object.entries(t).filter(([,e])=>e.theme||e.color);return r.length?(0,o.jsx)("style",{dangerouslySetInnerHTML:{__html:Object.entries(oU).map(([t,n])=>`
${n} [data-chart=${e}] {
${r.map(([e,r])=>{let n=r.theme?.[t]||r.color;return n?`  --color-${e}: ${n};`:null}).join("\n")}
}
`).join("\n")}}):null},oH=i.forwardRef(({active:e,payload:t,className:r,indicator:n="dot",hideLabel:a=!1,hideIndicator:l=!1,label:s,labelFormatter:c,labelClassName:u,formatter:f,color:d,nameKey:p,labelKey:h},y)=>{let{config:m}=oq(),v=i.useMemo(()=>{if(a||!t?.length)return null;let[e]=t,r=`${h||e.dataKey||e.name||"value"}`,n=oX(m,e,r),i=h||"string"!=typeof s?n?.label:m[s]?.label||s;return c?(0,o.jsx)("div",{className:(0,W.cn)("font-medium",u),children:c(i,t)}):i?(0,o.jsx)("div",{className:(0,W.cn)("font-medium",u),children:i}):null},[s,c,t,a,u,m,h]);if(!e||!t?.length)return null;let g=1===t.length&&"dot"!==n;return(0,o.jsxs)("div",{ref:y,className:(0,W.cn)("grid min-w-[8rem] items-start gap-1.5 rounded-lg border border-border/50 bg-background px-2.5 py-1.5 text-xs shadow-xl",r),children:[g?null:v,(0,o.jsx)("div",{className:"grid gap-1.5",children:t.map((e,t)=>{let r=`${p||e.name||e.dataKey||"value"}`,i=oX(m,e,r),a=d||e.payload.fill||e.color;return(0,o.jsx)("div",{className:(0,W.cn)("flex w-full flex-wrap items-stretch gap-2 [&>svg]:h-2.5 [&>svg]:w-2.5 [&>svg]:text-muted-foreground","dot"===n&&"items-center"),children:f&&e?.value!==void 0&&e.name?f(e.value,e.name,e,t,e.payload):(0,o.jsxs)(o.Fragment,{children:[i?.icon?(0,o.jsx)(i.icon,{}):!l&&(0,o.jsx)("div",{className:(0,W.cn)("shrink-0 rounded-[2px] border-[--color-border] bg-[--color-bg]",{"h-2.5 w-2.5":"dot"===n,"w-1":"line"===n,"w-0 border-[1.5px] border-dashed bg-transparent":"dashed"===n,"my-0.5":g&&"dashed"===n}),style:{"--color-bg":a,"--color-border":a}}),(0,o.jsxs)("div",{className:(0,W.cn)("flex flex-1 justify-between leading-none",g?"items-end":"items-center"),children:[(0,o.jsxs)("div",{className:"grid gap-1.5",children:[g?v:null,(0,o.jsx)("span",{className:"text-muted-foreground",children:i?.label||e.name})]}),e.value&&(0,o.jsx)("span",{className:"font-mono font-medium tabular-nums text-foreground",children:e.value.toLocaleString()})]})]})},e.dataKey)})})]})});oH.displayName="ChartTooltip";let oG=i.forwardRef(({className:e,hideIcon:t=!1,payload:r,verticalAlign:n="bottom",nameKey:i},a)=>{let{config:l}=oq();return r?.length?(0,o.jsx)("div",{ref:a,className:(0,W.cn)("flex items-center justify-center gap-4","top"===n?"pb-3":"pt-3",e),children:r.map(e=>{let r=`${i||e.dataKey||"value"}`,n=oX(l,e,r);return(0,o.jsxs)("div",{className:(0,W.cn)("flex items-center gap-1.5 [&>svg]:h-3 [&>svg]:w-3 [&>svg]:text-muted-foreground"),children:[n?.icon&&!t?(0,o.jsx)(n.icon,{}):(0,o.jsx)("div",{className:"h-2 w-2 shrink-0 rounded-[2px]",style:{backgroundColor:e.color}}),n?.label]},e.value)})}):null});function oX(e,t,r){if("object"!=typeof t||null===t)return;let n="payload"in t&&"object"==typeof t.payload&&null!==t.payload?t.payload:void 0,o=r;return r in t&&"string"==typeof t[r]?o=t[r]:n&&r in n&&"string"==typeof n[r]&&(o=n[r]),o in e?e[o]:e[r]}oG.displayName="ChartLegend";var oY=r(34990),oK=r.n(oY);function oZ(e,t){if(!e)throw Error("Invariant failed")}var oJ=["children","className"];function oQ(){return(oQ=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}var o0=a().forwardRef(function(e,t){var r=e.children,n=e.className,o=function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(e,oJ),i=(0,rh.A)("recharts-layer",n);return a().createElement("g",oQ({className:i},nt(o,!0),{ref:t}),r)});function o1(){return(o1=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}var o2=function(e){var t=e.cx,r=e.cy,n=e.r,o=e.className,i=(0,rh.A)("recharts-dot",o);return t===+t&&r===+r&&n===+n?a().createElement("circle",o1({},nt(e,!1),rK(e),{className:i,cx:t,cy:r,r:n})):null},o5=r(87955),o4=r.n(o5),o3=Object.getOwnPropertyNames,o6=Object.getOwnPropertySymbols,o8=Object.prototype.hasOwnProperty;function o7(e,t){return function(r,n,o){return e(r,n,o)&&t(r,n,o)}}function o9(e){return function(t,r,n){if(!t||!r||"object"!=typeof t||"object"!=typeof r)return e(t,r,n);var o=n.cache,i=o.get(t),a=o.get(r);if(i&&a)return i===r&&a===t;o.set(t,r),o.set(r,t);var l=e(t,r,n);return o.delete(t),o.delete(r),l}}function ie(e){return o3(e).concat(o6(e))}var it=Object.hasOwn||function(e,t){return o8.call(e,t)};function ir(e,t){return e===t||!e&&!t&&e!=e&&t!=t}var io=Object.getOwnPropertyDescriptor,ii=Object.keys;function ia(e,t,r){var n=e.length;if(t.length!==n)return!1;for(;n-- >0;)if(!r.equals(e[n],t[n],n,n,e,t,r))return!1;return!0}function il(e,t){return ir(e.getTime(),t.getTime())}function is(e,t){return e.name===t.name&&e.message===t.message&&e.cause===t.cause&&e.stack===t.stack}function ic(e,t){return e===t}function iu(e,t,r){var n,o,i=e.size;if(i!==t.size)return!1;if(!i)return!0;for(var a=Array(i),l=e.entries(),s=0;(n=l.next())&&!n.done;){for(var c=t.entries(),u=!1,f=0;(o=c.next())&&!o.done;){if(a[f]){f++;continue}var d=n.value,p=o.value;if(r.equals(d[0],p[0],s,f,e,t,r)&&r.equals(d[1],p[1],d[0],p[0],e,t,r)){u=a[f]=!0;break}f++}if(!u)return!1;s++}return!0}function id(e,t,r){var n=ii(e),o=n.length;if(ii(t).length!==o)return!1;for(;o-- >0;)if(!ib(e,t,r,n[o]))return!1;return!0}function ip(e,t,r){var n,o,i,a=ie(e),l=a.length;if(ie(t).length!==l)return!1;for(;l-- >0;)if(!ib(e,t,r,n=a[l])||(o=io(e,n),i=io(t,n),(o||i)&&(!o||!i||o.configurable!==i.configurable||o.enumerable!==i.enumerable||o.writable!==i.writable)))return!1;return!0}function ih(e,t){return ir(e.valueOf(),t.valueOf())}function iy(e,t){return e.source===t.source&&e.flags===t.flags}function im(e,t,r){var n,o,i=e.size;if(i!==t.size)return!1;if(!i)return!0;for(var a=Array(i),l=e.values();(n=l.next())&&!n.done;){for(var s=t.values(),c=!1,u=0;(o=s.next())&&!o.done;){if(!a[u]&&r.equals(n.value,o.value,n.value,o.value,e,t,r)){c=a[u]=!0;break}u++}if(!c)return!1}return!0}function iv(e,t){var r=e.length;if(t.length!==r)return!1;for(;r-- >0;)if(e[r]!==t[r])return!1;return!0}function ig(e,t){return e.hostname===t.hostname&&e.pathname===t.pathname&&e.protocol===t.protocol&&e.port===t.port&&e.hash===t.hash&&e.username===t.username&&e.password===t.password}function ib(e,t,r,n){return("_owner"===n||"__o"===n||"__v"===n)&&(!!e.$$typeof||!!t.$$typeof)||it(t,n)&&r.equals(e[n],t[n],n,n,e,t,r)}var ix=Array.isArray,iw="function"==typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView:null,ij=Object.assign,iO=Object.prototype.toString.call.bind(Object.prototype.toString),iS=iA();function iA(e){void 0===e&&(e={});var t,r,n,o,i,a,l,s,c,u,f,d,p,h=e.circular,y=e.createInternalComparator,m=e.createState,v=e.strict,g=(r=(t=function(e){var t=e.circular,r=e.createCustomConfig,n=e.strict,o={areArraysEqual:n?ip:ia,areDatesEqual:il,areErrorsEqual:is,areFunctionsEqual:ic,areMapsEqual:n?o7(iu,ip):iu,areNumbersEqual:ir,areObjectsEqual:n?ip:id,arePrimitiveWrappersEqual:ih,areRegExpsEqual:iy,areSetsEqual:n?o7(im,ip):im,areTypedArraysEqual:n?ip:iv,areUrlsEqual:ig};if(r&&(o=ij({},o,r(o))),t){var i=o9(o.areArraysEqual),a=o9(o.areMapsEqual),l=o9(o.areObjectsEqual),s=o9(o.areSetsEqual);o=ij({},o,{areArraysEqual:i,areMapsEqual:a,areObjectsEqual:l,areSetsEqual:s})}return o}(e)).areArraysEqual,n=t.areDatesEqual,o=t.areErrorsEqual,i=t.areFunctionsEqual,a=t.areMapsEqual,l=t.areNumbersEqual,s=t.areObjectsEqual,c=t.arePrimitiveWrappersEqual,u=t.areRegExpsEqual,f=t.areSetsEqual,d=t.areTypedArraysEqual,p=t.areUrlsEqual,function(e,t,h){if(e===t)return!0;if(null==e||null==t)return!1;var y=typeof e;if(y!==typeof t)return!1;if("object"!==y)return"number"===y?l(e,t,h):"function"===y&&i(e,t,h);var m=e.constructor;if(m!==t.constructor)return!1;if(m===Object)return s(e,t,h);if(ix(e))return r(e,t,h);if(null!=iw&&iw(e))return d(e,t,h);if(m===Date)return n(e,t,h);if(m===RegExp)return u(e,t,h);if(m===Map)return a(e,t,h);if(m===Set)return f(e,t,h);var v=iO(e);return"[object Date]"===v?n(e,t,h):"[object RegExp]"===v?u(e,t,h):"[object Map]"===v?a(e,t,h):"[object Set]"===v?f(e,t,h):"[object Object]"===v?"function"!=typeof e.then&&"function"!=typeof t.then&&s(e,t,h):"[object URL]"===v?p(e,t,h):"[object Error]"===v?o(e,t,h):"[object Arguments]"===v?s(e,t,h):("[object Boolean]"===v||"[object Number]"===v||"[object String]"===v)&&c(e,t,h)}),b=y?y(g):function(e,t,r,n,o,i,a){return g(e,t,a)};return function(e){var t=e.circular,r=e.comparator,n=e.createState,o=e.equals,i=e.strict;if(n)return function(e,a){var l=n(),s=l.cache;return r(e,a,{cache:void 0===s?t?new WeakMap:void 0:s,equals:o,meta:l.meta,strict:i})};if(t)return function(e,t){return r(e,t,{cache:new WeakMap,equals:o,meta:void 0,strict:i})};var a={cache:void 0,equals:o,meta:void 0,strict:i};return function(e,t){return r(e,t,a)}}({circular:void 0!==h&&h,comparator:g,createState:m,equals:b,strict:void 0!==v&&v})}function iP(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=-1;requestAnimationFrame(function n(o){if(r<0&&(r=o),o-r>t)e(o),r=-1;else{var i;i=n,"undefined"!=typeof requestAnimationFrame&&requestAnimationFrame(i)}})}function ik(e){return(ik="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function iE(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function iN(e){return(iN="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function iM(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function iC(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?iM(Object(r),!0).forEach(function(t){iT(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):iM(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function iT(e,t,r){var n;return(n=function(e,t){if("object"!==iN(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!==iN(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"),(t="symbol"===iN(n)?n:String(n))in e)?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}iA({strict:!0}),iA({circular:!0}),iA({circular:!0,strict:!0}),iA({createInternalComparator:function(){return ir}}),iA({strict:!0,createInternalComparator:function(){return ir}}),iA({circular:!0,createInternalComparator:function(){return ir}}),iA({circular:!0,createInternalComparator:function(){return ir},strict:!0});var i_=function(e){return e},iI=function(e,t){return Object.keys(t).reduce(function(r,n){return iC(iC({},r),{},iT({},n,e(n,t[n])))},{})},iD=function(e,t,r){return e.map(function(e){return"".concat(e.replace(/([A-Z])/g,function(e){return"-".concat(e.toLowerCase())})," ").concat(t,"ms ").concat(r)}).join(",")},iR=function(e,t,r,n,o,i,a,l){};function iB(e,t){if(e){if("string"==typeof e)return iL(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return iL(e,t)}}function iL(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}var i$=function(e,t){return[0,3*e,3*t-6*e,3*e-3*t+1]},iz=function(e,t){return e.map(function(e,r){return e*Math.pow(t,r)}).reduce(function(e,t){return e+t})},iU=function(e,t){return function(r){return iz(i$(e,t),r)}},iF=function(){for(var e,t,r=arguments.length,n=Array(r),o=0;o<r;o++)n[o]=arguments[o];var i=n[0],a=n[1],l=n[2],s=n[3];if(1===n.length)switch(n[0]){case"linear":i=0,a=0,l=1,s=1;break;case"ease":i=.25,a=.1,l=.25,s=1;break;case"ease-in":i=.42,a=0,l=1,s=1;break;case"ease-out":i=.42,a=0,l=.58,s=1;break;case"ease-in-out":i=0,a=0,l=.58,s=1;break;default:var c=n[0].split("(");if("cubic-bezier"===c[0]&&4===c[1].split(")")[0].split(",").length){var u,f=function(e){if(Array.isArray(e))return e}(u=c[1].split(")")[0].split(",").map(function(e){return parseFloat(e)}))||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,a,l=[],s=!0,c=!1;try{i=(r=r.call(e)).next;for(;!(s=(n=i.call(r)).done)&&(l.push(n.value),l.length!==t);s=!0);}catch(e){c=!0,o=e}finally{try{if(!s&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(c)throw o}}return l}}(u,4)||iB(u,4)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}();i=f[0],a=f[1],l=f[2],s=f[3]}else iR(!1,"[configBezier]: arguments should be one of oneOf 'linear', 'ease', 'ease-in', 'ease-out', 'ease-in-out','cubic-bezier(x1,y1,x2,y2)', instead received %s",n)}iR([i,l,a,s].every(function(e){return"number"==typeof e&&e>=0&&e<=1}),"[configBezier]: arguments should be x1, y1, x2, y2 of [0, 1] instead received %s",n);var d=iU(i,l),p=iU(a,s),h=(e=i,t=l,function(r){var n;return iz([].concat(function(e){if(Array.isArray(e))return iL(e)}(n=i$(e,t).map(function(e,t){return e*t}).slice(1))||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(n)||iB(n)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),[0]),r)}),y=function(e){for(var t=e>1?1:e,r=t,n=0;n<8;++n){var o,i=d(r)-t,a=h(r);if(1e-4>Math.abs(i-t)||a<1e-4)break;r=(o=r-i/a)>1?1:o<0?0:o}return p(r)};return y.isStepper=!1,y},iq=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.stiff,r=void 0===t?100:t,n=e.damping,o=void 0===n?8:n,i=e.dt,a=void 0===i?17:i,l=function(e,t,n){var i=n+(-(e-t)*r-n*o)*a/1e3,l=n*a/1e3+e;return 1e-4>Math.abs(l-t)&&1e-4>Math.abs(i)?[t,0]:[l,i]};return l.isStepper=!0,l.dt=a,l},iW=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];var n=t[0];if("string"==typeof n)switch(n){case"ease":case"ease-in-out":case"ease-out":case"ease-in":case"linear":return iF(n);case"spring":return iq();default:if("cubic-bezier"===n.split("(")[0])return iF(n);iR(!1,"[configEasing]: first argument should be one of 'ease', 'ease-in', 'ease-out', 'ease-in-out','cubic-bezier(x1,y1,x2,y2)', 'linear' and 'spring', instead  received %s",t)}return"function"==typeof n?n:(iR(!1,"[configEasing]: first argument type should be function or string, instead received %s",t),null)};function iV(e){return(iV="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function iH(e){return function(e){if(Array.isArray(e))return iZ(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||iK(e)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function iG(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function iX(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?iG(Object(r),!0).forEach(function(t){iY(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):iG(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function iY(e,t,r){var n;return(n=function(e,t){if("object"!==iV(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!==iV(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"),(t="symbol"===iV(n)?n:String(n))in e)?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function iK(e,t){if(e){if("string"==typeof e)return iZ(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return iZ(e,t)}}function iZ(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}var iJ=function(e,t,r){return e+(t-e)*r},iQ=function(e){return e.from!==e.to},i0=function e(t,r,n){var o=iI(function(e,r){if(iQ(r)){var n,o=function(e){if(Array.isArray(e))return e}(n=t(r.from,r.to,r.velocity))||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,a,l=[],s=!0,c=!1;try{i=(r=r.call(e)).next;for(;!(s=(n=i.call(r)).done)&&(l.push(n.value),l.length!==t);s=!0);}catch(e){c=!0,o=e}finally{try{if(!s&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(c)throw o}}return l}}(n,2)||iK(n,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),i=o[0],a=o[1];return iX(iX({},r),{},{from:i,velocity:a})}return r},r);return n<1?iI(function(e,t){return iQ(t)?iX(iX({},t),{},{velocity:iJ(t.velocity,o[e].velocity,n),from:iJ(t.from,o[e].from,n)}):t},r):e(t,o,n-1)};let i1=function(e,t,r,n,o){var i,a,l=[Object.keys(e),Object.keys(t)].reduce(function(e,t){return e.filter(function(e){return t.includes(e)})}),s=l.reduce(function(r,n){return iX(iX({},r),{},iY({},n,[e[n],t[n]]))},{}),c=l.reduce(function(r,n){return iX(iX({},r),{},iY({},n,{from:e[n],velocity:0,to:t[n]}))},{}),u=-1,f=function(){return null};return f=r.isStepper?function(n){i||(i=n);var a=(n-i)/r.dt;c=i0(r,c,a),o(iX(iX(iX({},e),t),iI(function(e,t){return t.from},c))),i=n,Object.values(c).filter(iQ).length&&(u=requestAnimationFrame(f))}:function(i){a||(a=i);var l=(i-a)/n,c=iI(function(e,t){return iJ.apply(void 0,iH(t).concat([r(l)]))},s);if(o(iX(iX(iX({},e),t),c)),l<1)u=requestAnimationFrame(f);else{var d=iI(function(e,t){return iJ.apply(void 0,iH(t).concat([r(1)]))},s);o(iX(iX(iX({},e),t),d))}},function(){return requestAnimationFrame(f),function(){cancelAnimationFrame(u)}}};function i2(e){return(i2="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var i5=["children","begin","duration","attributeName","easing","isActive","steps","from","to","canBegin","onAnimationEnd","shouldReAnimate","onAnimationReStart"];function i4(e){return function(e){if(Array.isArray(e))return i3(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return i3(e,void 0);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return i3(e,t)}}(e)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function i3(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function i6(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function i8(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?i6(Object(r),!0).forEach(function(t){i7(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):i6(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function i7(e,t,r){return(t=i9(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function i9(e){var t=function(e,t){if("object"!==i2(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!==i2(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===i2(t)?t:String(t)}function ae(e,t){return(ae=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function at(e,t){if(t&&("object"===i2(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return ar(e)}function ar(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function an(e){return(an=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}var ao=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&ae(e,t)}(o,e);var t,r,n=(t=function(){if("undefined"==typeof Reflect||!Reflect.construct||Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(e){return!1}}(),function(){var e,r=an(o);return e=t?Reflect.construct(r,arguments,an(this).constructor):r.apply(this,arguments),at(this,e)});function o(e,t){!function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}(this,o);var r=n.call(this,e,t),i=r.props,a=i.isActive,l=i.attributeName,s=i.from,c=i.to,u=i.steps,f=i.children,d=i.duration;if(r.handleStyleChange=r.handleStyleChange.bind(ar(r)),r.changeStyle=r.changeStyle.bind(ar(r)),!a||d<=0)return r.state={style:{}},"function"==typeof f&&(r.state={style:c}),at(r);if(u&&u.length)r.state={style:u[0].style};else if(s){if("function"==typeof f)return r.state={style:s},at(r);r.state={style:l?i7({},l,s):s}}else r.state={style:{}};return r}return r=[{key:"componentDidMount",value:function(){var e=this.props,t=e.isActive,r=e.canBegin;this.mounted=!0,t&&r&&this.runAnimation(this.props)}},{key:"componentDidUpdate",value:function(e){var t=this.props,r=t.isActive,n=t.canBegin,o=t.attributeName,i=t.shouldReAnimate,a=t.to,l=t.from,s=this.state.style;if(n){if(!r){var c={style:o?i7({},o,a):a};this.state&&s&&(o&&s[o]!==a||!o&&s!==a)&&this.setState(c);return}if(!iS(e.to,a)||!e.canBegin||!e.isActive){var u=!e.canBegin||!e.isActive;this.manager&&this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation();var f=u||i?l:e.to;if(this.state&&s){var d={style:o?i7({},o,f):f};(o&&s[o]!==f||!o&&s!==f)&&this.setState(d)}this.runAnimation(i8(i8({},this.props),{},{from:f,begin:0}))}}}},{key:"componentWillUnmount",value:function(){this.mounted=!1;var e=this.props.onAnimationEnd;this.unSubscribe&&this.unSubscribe(),this.manager&&(this.manager.stop(),this.manager=null),this.stopJSAnimation&&this.stopJSAnimation(),e&&e()}},{key:"handleStyleChange",value:function(e){this.changeStyle(e)}},{key:"changeStyle",value:function(e){this.mounted&&this.setState({style:e})}},{key:"runJSAnimation",value:function(e){var t=this,r=e.from,n=e.to,o=e.duration,i=e.easing,a=e.begin,l=e.onAnimationEnd,s=e.onAnimationStart,c=i1(r,n,iW(i),o,this.changeStyle);this.manager.start([s,a,function(){t.stopJSAnimation=c()},o,l])}},{key:"runStepAnimation",value:function(e){var t=this,r=e.steps,n=e.begin,o=e.onAnimationStart,i=r[0],a=i.style,l=i.duration;return this.manager.start([o].concat(i4(r.reduce(function(e,n,o){if(0===o)return e;var i=n.duration,a=n.easing,l=void 0===a?"ease":a,s=n.style,c=n.properties,u=n.onAnimationEnd,f=o>0?r[o-1]:n,d=c||Object.keys(s);if("function"==typeof l||"spring"===l)return[].concat(i4(e),[t.runJSAnimation.bind(t,{from:f.style,to:s,duration:i,easing:l}),i]);var p=iD(d,i,l),h=i8(i8(i8({},f.style),s),{},{transition:p});return[].concat(i4(e),[h,i,u]).filter(i_)},[a,Math.max(void 0===l?0:l,n)])),[e.onAnimationEnd]))}},{key:"runAnimation",value:function(e){if(!this.manager){var t,r,n,o;this.manager=(r=function(){return null},n=!1,o=function e(t){if(!n){if(Array.isArray(t)){if(!t.length)return;var o=function(e){if(Array.isArray(e))return e}(t)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(t)||function(e,t){if(e){if("string"==typeof e)return iE(e,void 0);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return iE(e,t)}}(t)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),i=o[0],a=o.slice(1);if("number"==typeof i){iP(e.bind(null,a),i);return}e(i),iP(e.bind(null,a));return}"object"===ik(t)&&r(t),"function"==typeof t&&t()}},{stop:function(){n=!0},start:function(e){n=!1,o(e)},subscribe:function(e){return r=e,function(){r=function(){return null}}}})}var i=e.begin,a=e.duration,l=e.attributeName,s=e.to,c=e.easing,u=e.onAnimationStart,f=e.onAnimationEnd,d=e.steps,p=e.children,h=this.manager;if(this.unSubscribe=h.subscribe(this.handleStyleChange),"function"==typeof c||"function"==typeof p||"spring"===c){this.runJSAnimation(e);return}if(d.length>1){this.runStepAnimation(e);return}var y=l?i7({},l,s):s,m=iD(Object.keys(y),a,c);h.start([u,i,i8(i8({},y),{},{transition:m}),a,f])}},{key:"render",value:function(){var e=this.props,t=e.children,r=(e.begin,e.duration),n=(e.attributeName,e.easing,e.isActive),o=(e.steps,e.from,e.to,e.canBegin,e.onAnimationEnd,e.shouldReAnimate,e.onAnimationReStart,function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r,n,o={},i=Object.keys(e);for(n=0;n<i.length;n++)r=i[n],t.indexOf(r)>=0||(o[r]=e[r]);return o}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(e,i5)),l=i.Children.count(t),s=this.state.style;if("function"==typeof t)return t(s);if(!n||0===l||r<=0)return t;var c=function(e){var t=e.props,r=t.style,n=t.className;return(0,i.cloneElement)(e,i8(i8({},o),{},{style:i8(i8({},void 0===r?{}:r),s),className:n}))};return 1===l?c(i.Children.only(t)):a().createElement("div",null,i.Children.map(t,function(e){return c(e)}))}}],function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,i9(n.key),n)}}(o.prototype,r),Object.defineProperty(o,"prototype",{writable:!1}),o}(i.PureComponent);function ai(e){return(ai="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function aa(){return(aa=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function al(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function as(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function ac(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?as(Object(r),!0).forEach(function(t){var n,o,i;n=e,o=t,i=r[t],(o=function(e){var t=function(e,t){if("object"!=ai(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=ai(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==ai(t)?t:t+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):as(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}ao.displayName="Animate",ao.defaultProps={begin:0,duration:1e3,from:"",to:"",attributeName:"",easing:"ease",isActive:!0,canBegin:!0,steps:[],onAnimationEnd:function(){},onAnimationStart:function(){}},ao.propTypes={from:o4().oneOfType([o4().object,o4().string]),to:o4().oneOfType([o4().object,o4().string]),attributeName:o4().string,duration:o4().number,begin:o4().number,easing:o4().oneOfType([o4().string,o4().func]),steps:o4().arrayOf(o4().shape({duration:o4().number.isRequired,style:o4().object.isRequired,easing:o4().oneOfType([o4().oneOf(["ease","ease-in","ease-out","ease-in-out","linear"]),o4().func]),properties:o4().arrayOf("string"),onAnimationEnd:o4().func})),children:o4().oneOfType([o4().node,o4().func]),isActive:o4().bool,canBegin:o4().bool,onAnimationEnd:o4().func,shouldReAnimate:o4().bool,onAnimationStart:o4().func,onAnimationReStart:o4().func};var au=function(e,t,r,n,o){var i,a=Math.min(Math.abs(r)/2,Math.abs(n)/2),l=n>=0?1:-1,s=r>=0?1:-1,c=+(n>=0&&r>=0||n<0&&r<0);if(a>0&&o instanceof Array){for(var u=[0,0,0,0],f=0;f<4;f++)u[f]=o[f]>a?a:o[f];i="M".concat(e,",").concat(t+l*u[0]),u[0]>0&&(i+="A ".concat(u[0],",").concat(u[0],",0,0,").concat(c,",").concat(e+s*u[0],",").concat(t)),i+="L ".concat(e+r-s*u[1],",").concat(t),u[1]>0&&(i+="A ".concat(u[1],",").concat(u[1],",0,0,").concat(c,",\n        ").concat(e+r,",").concat(t+l*u[1])),i+="L ".concat(e+r,",").concat(t+n-l*u[2]),u[2]>0&&(i+="A ".concat(u[2],",").concat(u[2],",0,0,").concat(c,",\n        ").concat(e+r-s*u[2],",").concat(t+n)),i+="L ".concat(e+s*u[3],",").concat(t+n),u[3]>0&&(i+="A ".concat(u[3],",").concat(u[3],",0,0,").concat(c,",\n        ").concat(e,",").concat(t+n-l*u[3])),i+="Z"}else if(a>0&&o===+o&&o>0){var d=Math.min(a,o);i="M ".concat(e,",").concat(t+l*d,"\n            A ").concat(d,",").concat(d,",0,0,").concat(c,",").concat(e+s*d,",").concat(t,"\n            L ").concat(e+r-s*d,",").concat(t,"\n            A ").concat(d,",").concat(d,",0,0,").concat(c,",").concat(e+r,",").concat(t+l*d,"\n            L ").concat(e+r,",").concat(t+n-l*d,"\n            A ").concat(d,",").concat(d,",0,0,").concat(c,",").concat(e+r-s*d,",").concat(t+n,"\n            L ").concat(e+s*d,",").concat(t+n,"\n            A ").concat(d,",").concat(d,",0,0,").concat(c,",").concat(e,",").concat(t+n-l*d," Z")}else i="M ".concat(e,",").concat(t," h ").concat(r," v ").concat(n," h ").concat(-r," Z");return i},af=function(e,t){if(!e||!t)return!1;var r=e.x,n=e.y,o=t.x,i=t.y,a=t.width,l=t.height;if(Math.abs(a)>0&&Math.abs(l)>0){var s=Math.min(o,o+a),c=Math.max(o,o+a),u=Math.min(i,i+l),f=Math.max(i,i+l);return r>=s&&r<=c&&n>=u&&n<=f}return!1},ad={x:0,y:0,width:0,height:0,radius:0,isAnimationActive:!1,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},ap=function(e){var t,r=ac(ac({},ad),e),n=(0,i.useRef)(),o=function(e){if(Array.isArray(e))return e}(t=(0,i.useState)(-1))||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,a,l=[],s=!0,c=!1;try{i=(r=r.call(e)).next;for(;!(s=(n=i.call(r)).done)&&(l.push(n.value),l.length!==t);s=!0);}catch(e){c=!0,o=e}finally{try{if(!s&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(c)throw o}}return l}}(t,2)||function(e,t){if(e){if("string"==typeof e)return al(e,2);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return al(e,t)}}(t,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),l=o[0],s=o[1];(0,i.useEffect)(function(){if(n.current&&n.current.getTotalLength)try{var e=n.current.getTotalLength();e&&s(e)}catch(e){}},[]);var c=r.x,u=r.y,f=r.width,d=r.height,p=r.radius,h=r.className,y=r.animationEasing,m=r.animationDuration,v=r.animationBegin,g=r.isAnimationActive,b=r.isUpdateAnimationActive;if(c!==+c||u!==+u||f!==+f||d!==+d||0===f||0===d)return null;var x=(0,rh.A)("recharts-rectangle",h);return b?a().createElement(ao,{canBegin:l>0,from:{width:f,height:d,x:c,y:u},to:{width:f,height:d,x:c,y:u},duration:m,animationEasing:y,isActive:b},function(e){var t=e.width,o=e.height,i=e.x,s=e.y;return a().createElement(ao,{canBegin:l>0,from:"0px ".concat(-1===l?1:l,"px"),to:"".concat(l,"px 0px"),attributeName:"strokeDasharray",begin:v,duration:m,isActive:g,easing:y},a().createElement("path",aa({},nt(r,!0),{className:x,d:au(i,s,t,o,p),ref:n})))}):a().createElement("path",aa({},nt(r,!0),{className:x,d:au(c,u,f,d,p)}))};function ah(e,t){switch(arguments.length){case 0:break;case 1:this.range(e);break;default:this.range(t).domain(e)}return this}function ay(e,t){switch(arguments.length){case 0:break;case 1:"function"==typeof e?this.interpolator(e):this.range(e);break;default:this.domain(e),"function"==typeof t?this.interpolator(t):this.range(t)}return this}class am extends Map{constructor(e,t=ag){if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:t}}),null!=e)for(let[t,r]of e)this.set(t,r)}get(e){return super.get(av(this,e))}has(e){return super.has(av(this,e))}set(e,t){return super.set(function({_intern:e,_key:t},r){let n=t(r);return e.has(n)?e.get(n):(e.set(n,r),r)}(this,e),t)}delete(e){return super.delete(function({_intern:e,_key:t},r){let n=t(r);return e.has(n)&&(r=e.get(n),e.delete(n)),r}(this,e))}}function av({_intern:e,_key:t},r){let n=t(r);return e.has(n)?e.get(n):r}function ag(e){return null!==e&&"object"==typeof e?e.valueOf():e}let ab=Symbol("implicit");function ax(){var e=new am,t=[],r=[],n=ab;function o(o){let i=e.get(o);if(void 0===i){if(n!==ab)return n;e.set(o,i=t.push(o)-1)}return r[i%r.length]}return o.domain=function(r){if(!arguments.length)return t.slice();for(let n of(t=[],e=new am,r))e.has(n)||e.set(n,t.push(n)-1);return o},o.range=function(e){return arguments.length?(r=Array.from(e),o):r.slice()},o.unknown=function(e){return arguments.length?(n=e,o):n},o.copy=function(){return ax(t,r).unknown(n)},ah.apply(o,arguments),o}function aw(){var e,t,r=ax().unknown(void 0),n=r.domain,o=r.range,i=0,a=1,l=!1,s=0,c=0,u=.5;function f(){var r=n().length,f=a<i,d=f?a:i,p=f?i:a;e=(p-d)/Math.max(1,r-s+2*c),l&&(e=Math.floor(e)),d+=(p-d-e*(r-s))*u,t=e*(1-s),l&&(d=Math.round(d),t=Math.round(t));var h=(function(e,t,r){e*=1,t*=1,r=(o=arguments.length)<2?(t=e,e=0,1):o<3?1:+r;for(var n=-1,o=0|Math.max(0,Math.ceil((t-e)/r)),i=Array(o);++n<o;)i[n]=e+n*r;return i})(r).map(function(t){return d+e*t});return o(f?h.reverse():h)}return delete r.unknown,r.domain=function(e){return arguments.length?(n(e),f()):n()},r.range=function(e){return arguments.length?([i,a]=e,i*=1,a*=1,f()):[i,a]},r.rangeRound=function(e){return[i,a]=e,i*=1,a*=1,l=!0,f()},r.bandwidth=function(){return t},r.step=function(){return e},r.round=function(e){return arguments.length?(l=!!e,f()):l},r.padding=function(e){return arguments.length?(s=Math.min(1,c=+e),f()):s},r.paddingInner=function(e){return arguments.length?(s=Math.min(1,e),f()):s},r.paddingOuter=function(e){return arguments.length?(c=+e,f()):c},r.align=function(e){return arguments.length?(u=Math.max(0,Math.min(1,e)),f()):u},r.copy=function(){return aw(n(),[i,a]).round(l).paddingInner(s).paddingOuter(c).align(u)},ah.apply(f(),arguments)}function aj(){return function e(t){var r=t.copy;return t.padding=t.paddingOuter,delete t.paddingInner,delete t.paddingOuter,t.copy=function(){return e(r())},t}(aw.apply(null,arguments).paddingInner(1))}function aO(e){return(aO="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function aS(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function aA(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?aS(Object(r),!0).forEach(function(t){var n,o,i;n=e,o=t,i=r[t],(o=function(e){var t=function(e,t){if("object"!=aO(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=aO(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==aO(t)?t:t+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):aS(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function aP(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}var ak={widthCache:{},cacheCount:0},aE={position:"absolute",top:"-20000px",left:0,padding:0,margin:0,border:"none",whiteSpace:"pre"},aN="recharts_measurement_span",aM=function(e){var t,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(null==e||nI.isSsr)return{width:0,height:0};var n=(Object.keys(t=aA({},r)).forEach(function(e){t[e]||delete t[e]}),t),o=JSON.stringify({text:e,copyStyle:n});if(ak.widthCache[o])return ak.widthCache[o];try{var i=document.getElementById(aN);i||((i=document.createElement("span")).setAttribute("id",aN),i.setAttribute("aria-hidden","true"),document.body.appendChild(i));var a=aA(aA({},aE),n);Object.assign(i.style,a),i.textContent="".concat(e);var l=i.getBoundingClientRect(),s={width:l.width,height:l.height};return ak.widthCache[o]=s,++ak.cacheCount>2e3&&(ak.cacheCount=0,ak.widthCache={}),s}catch(e){return{width:0,height:0}}};function aC(e){return(aC="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function aT(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,a,l=[],s=!0,c=!1;try{if(i=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;s=!1}else for(;!(s=(n=i.call(r)).done)&&(l.push(n.value),l.length!==t);s=!0);}catch(e){c=!0,o=e}finally{try{if(!s&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(c)throw o}}return l}}(e,t)||function(e,t){if(e){if("string"==typeof e)return a_(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return a_(e,t)}}(e,t)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function a_(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function aI(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,function(e){var t=function(e,t){if("object"!=aC(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=aC(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==aC(t)?t:t+""}(n.key),n)}}var aD=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([*/])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,aR=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([+-])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,aB=/^px|cm|vh|vw|em|rem|%|mm|in|pt|pc|ex|ch|vmin|vmax|Q$/,aL=/(-?\d+(?:\.\d+)?)([a-zA-Z%]+)?/,a$={cm:96/2.54,mm:96/25.4,pt:96/72,pc:16,in:96,Q:96/101.6,px:1},az=Object.keys(a$),aU=function(){var e,t;function r(e,t){(function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")})(this,r),this.num=e,this.unit=t,this.num=e,this.unit=t,Number.isNaN(e)&&(this.unit=""),""===t||aB.test(t)||(this.num=NaN,this.unit=""),az.includes(t)&&(this.num=e*a$[t],this.unit="px")}return e=[{key:"add",value:function(e){return this.unit!==e.unit?new r(NaN,""):new r(this.num+e.num,this.unit)}},{key:"subtract",value:function(e){return this.unit!==e.unit?new r(NaN,""):new r(this.num-e.num,this.unit)}},{key:"multiply",value:function(e){return""!==this.unit&&""!==e.unit&&this.unit!==e.unit?new r(NaN,""):new r(this.num*e.num,this.unit||e.unit)}},{key:"divide",value:function(e){return""!==this.unit&&""!==e.unit&&this.unit!==e.unit?new r(NaN,""):new r(this.num/e.num,this.unit||e.unit)}},{key:"toString",value:function(){return"".concat(this.num).concat(this.unit)}},{key:"isNaN",value:function(){return Number.isNaN(this.num)}}],t=[{key:"parse",value:function(e){var t,n=aT(null!==(t=aL.exec(e))&&void 0!==t?t:[],3),o=n[1],i=n[2];return new r(parseFloat(o),null!=i?i:"")}}],e&&aI(r.prototype,e),t&&aI(r,t),Object.defineProperty(r,"prototype",{writable:!1}),r}();function aF(e){if(e.includes("NaN"))return"NaN";for(var t=e;t.includes("*")||t.includes("/");){var r,n=aT(null!==(r=aD.exec(t))&&void 0!==r?r:[],4),o=n[1],i=n[2],a=n[3],l=aU.parse(null!=o?o:""),s=aU.parse(null!=a?a:""),c="*"===i?l.multiply(s):l.divide(s);if(c.isNaN())return"NaN";t=t.replace(aD,c.toString())}for(;t.includes("+")||/.-\d+(?:\.\d+)?/.test(t);){var u,f=aT(null!==(u=aR.exec(t))&&void 0!==u?u:[],4),d=f[1],p=f[2],h=f[3],y=aU.parse(null!=d?d:""),m=aU.parse(null!=h?h:""),v="+"===p?y.add(m):y.subtract(m);if(v.isNaN())return"NaN";t=t.replace(aR,v.toString())}return t}var aq=/\(([^()]*)\)/;function aW(e){var t=function(e){try{var t;return t=e.replace(/\s+/g,""),t=function(e){for(var t=e;t.includes("(");){var r=aT(aq.exec(t),2)[1];t=t.replace(aq,aF(r))}return t}(t),t=aF(t)}catch(e){return"NaN"}}(e.slice(5,-1));return"NaN"===t?"":t}var aV=["x","y","lineHeight","capHeight","scaleToFit","textAnchor","verticalAnchor","fill"],aH=["dx","dy","angle","className","breakAll"];function aG(){return(aG=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function aX(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}function aY(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,a,l=[],s=!0,c=!1;try{if(i=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;s=!1}else for(;!(s=(n=i.call(r)).done)&&(l.push(n.value),l.length!==t);s=!0);}catch(e){c=!0,o=e}finally{try{if(!s&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(c)throw o}}return l}}(e,t)||function(e,t){if(e){if("string"==typeof e)return aK(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return aK(e,t)}}(e,t)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function aK(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}var aZ=/[ \f\n\r\t\v\u2028\u2029]+/,aJ=function(e){var t=e.children,r=e.breakAll,n=e.style;try{var o=[];rL()(t)||(o=r?t.toString().split(""):t.toString().split(aZ));var i=o.map(function(e){return{word:e,width:aM(e,n).width}}),a=r?0:aM("\xa0",n).width;return{wordsWithComputedWidth:i,spaceWidth:a}}catch(e){return null}},aQ=function(e,t,r,n,o){var i,a=e.maxLines,l=e.children,s=e.style,c=e.breakAll,u=rk(a),f=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return e.reduce(function(e,t){var i=t.word,a=t.width,l=e[e.length-1];return l&&(null==n||o||l.width+a+r<Number(n))?(l.words.push(i),l.width+=a+r):e.push({words:[i],width:a}),e},[])},d=f(t);if(!u)return d;for(var p=function(e){var t=f(aJ({breakAll:c,style:s,children:l.slice(0,e)+"…"}).wordsWithComputedWidth);return[t.length>a||t.reduce(function(e,t){return e.width>t.width?e:t}).width>Number(n),t]},h=0,y=l.length-1,m=0;h<=y&&m<=l.length-1;){var v=Math.floor((h+y)/2),g=aY(p(v-1),2),b=g[0],x=g[1],w=aY(p(v),1)[0];if(b||w||(h=v+1),b&&w&&(y=v-1),!b&&w){i=x;break}m++}return i||d},a0=function(e){return[{words:rL()(e)?[]:e.toString().split(aZ)}]},a1=function(e){var t=e.width,r=e.scaleToFit,n=e.children,o=e.style,i=e.breakAll,a=e.maxLines;if((t||r)&&!nI.isSsr){var l=aJ({breakAll:i,children:n,style:o});if(!l)return a0(n);var s=l.wordsWithComputedWidth,c=l.spaceWidth;return aQ({breakAll:i,children:n,maxLines:a,style:o},s,c,t,r)}return a0(n)},a2="#808080",a5=function(e){var t,r=e.x,n=void 0===r?0:r,o=e.y,l=void 0===o?0:o,s=e.lineHeight,c=void 0===s?"1em":s,u=e.capHeight,f=void 0===u?"0.71em":u,d=e.scaleToFit,p=void 0!==d&&d,h=e.textAnchor,y=e.verticalAnchor,m=e.fill,v=void 0===m?a2:m,g=aX(e,aV),b=(0,i.useMemo)(function(){return a1({breakAll:g.breakAll,children:g.children,maxLines:g.maxLines,scaleToFit:p,style:g.style,width:g.width})},[g.breakAll,g.children,g.maxLines,p,g.style,g.width]),x=g.dx,w=g.dy,j=g.angle,O=g.className,S=g.breakAll,A=aX(g,aH);if(!rE(n)||!rE(l))return null;var P=n+(rk(x)?x:0),k=l+(rk(w)?w:0);switch(void 0===y?"end":y){case"start":t=aW("calc(".concat(f,")"));break;case"middle":t=aW("calc(".concat((b.length-1)/2," * -").concat(c," + (").concat(f," / 2))"));break;default:t=aW("calc(".concat(b.length-1," * -").concat(c,")"))}var E=[];if(p){var N=b[0].width,M=g.width;E.push("scale(".concat((rk(M)?M/N:1)/N,")"))}return j&&E.push("rotate(".concat(j,", ").concat(P,", ").concat(k,")")),E.length&&(A.transform=E.join(" ")),a().createElement("text",aG({},nt(A,!0),{x:P,y:k,className:(0,rh.A)("recharts-text",O),textAnchor:void 0===h?"start":h,fill:v.includes("url")?a2:v}),b.map(function(e,r){var n=e.words.join(S?"":" ");return a().createElement("tspan",{x:P,dy:0===r?t:c,key:"".concat(n,"-").concat(r)},n)}))};let a4=Math.sqrt(50),a3=Math.sqrt(10),a6=Math.sqrt(2);function a8(e,t,r){let n,o,i;let a=(t-e)/Math.max(0,r),l=Math.floor(Math.log10(a)),s=a/Math.pow(10,l),c=s>=a4?10:s>=a3?5:s>=a6?2:1;return(l<0?(n=Math.round(e*(i=Math.pow(10,-l)/c)),o=Math.round(t*i),n/i<e&&++n,o/i>t&&--o,i=-i):(n=Math.round(e/(i=Math.pow(10,l)*c)),o=Math.round(t/i),n*i<e&&++n,o*i>t&&--o),o<n&&.5<=r&&r<2)?a8(e,t,2*r):[n,o,i]}function a7(e,t,r){if(t*=1,e*=1,!((r*=1)>0))return[];if(e===t)return[e];let n=t<e,[o,i,a]=n?a8(t,e,r):a8(e,t,r);if(!(i>=o))return[];let l=i-o+1,s=Array(l);if(n){if(a<0)for(let e=0;e<l;++e)s[e]=-((i-e)/a);else for(let e=0;e<l;++e)s[e]=(i-e)*a}else if(a<0)for(let e=0;e<l;++e)s[e]=-((o+e)/a);else for(let e=0;e<l;++e)s[e]=(o+e)*a;return s}function a9(e,t,r){return a8(e*=1,t*=1,r*=1)[2]}function le(e,t,r){t*=1,e*=1,r*=1;let n=t<e,o=n?a9(t,e,r):a9(e,t,r);return(n?-1:1)*(o<0?-(1/o):o)}function lt(e,t){return null==e||null==t?NaN:e<t?-1:e>t?1:e>=t?0:NaN}function lr(e,t){return null==e||null==t?NaN:t<e?-1:t>e?1:t>=e?0:NaN}function ln(e){let t,r,n;function o(e,n,i=0,a=e.length){if(i<a){if(0!==t(n,n))return a;do{let t=i+a>>>1;0>r(e[t],n)?i=t+1:a=t}while(i<a)}return i}return 2!==e.length?(t=lt,r=(t,r)=>lt(e(t),r),n=(t,r)=>e(t)-r):(t=e===lt||e===lr?e:lo,r=e,n=e),{left:o,center:function(e,t,r=0,i=e.length){let a=o(e,t,r,i-1);return a>r&&n(e[a-1],t)>-n(e[a],t)?a-1:a},right:function(e,n,o=0,i=e.length){if(o<i){if(0!==t(n,n))return i;do{let t=o+i>>>1;0>=r(e[t],n)?o=t+1:i=t}while(o<i)}return o}}}function lo(){return 0}function li(e){return null===e?NaN:+e}let la=ln(lt),ll=la.right;function ls(e,t,r){e.prototype=t.prototype=r,r.constructor=e}function lc(e,t){var r=Object.create(e.prototype);for(var n in t)r[n]=t[n];return r}function lu(){}la.left,ln(li).center;var lf="\\s*([+-]?\\d+)\\s*",ld="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",lp="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",lh=/^#([0-9a-f]{3,8})$/,ly=RegExp(`^rgb\\(${lf},${lf},${lf}\\)$`),lm=RegExp(`^rgb\\(${lp},${lp},${lp}\\)$`),lv=RegExp(`^rgba\\(${lf},${lf},${lf},${ld}\\)$`),lg=RegExp(`^rgba\\(${lp},${lp},${lp},${ld}\\)$`),lb=RegExp(`^hsl\\(${ld},${lp},${lp}\\)$`),lx=RegExp(`^hsla\\(${ld},${lp},${lp},${ld}\\)$`),lw={aliceblue:0xf0f8ff,antiquewhite:0xfaebd7,aqua:65535,aquamarine:8388564,azure:0xf0ffff,beige:0xf5f5dc,bisque:0xffe4c4,black:0,blanchedalmond:0xffebcd,blue:255,blueviolet:9055202,brown:0xa52a2a,burlywood:0xdeb887,cadetblue:6266528,chartreuse:8388352,chocolate:0xd2691e,coral:0xff7f50,cornflowerblue:6591981,cornsilk:0xfff8dc,crimson:0xdc143c,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:0xb8860b,darkgray:0xa9a9a9,darkgreen:25600,darkgrey:0xa9a9a9,darkkhaki:0xbdb76b,darkmagenta:9109643,darkolivegreen:5597999,darkorange:0xff8c00,darkorchid:0x9932cc,darkred:9109504,darksalmon:0xe9967a,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:0xff1493,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:0xb22222,floralwhite:0xfffaf0,forestgreen:2263842,fuchsia:0xff00ff,gainsboro:0xdcdcdc,ghostwhite:0xf8f8ff,gold:0xffd700,goldenrod:0xdaa520,gray:8421504,green:32768,greenyellow:0xadff2f,grey:8421504,honeydew:0xf0fff0,hotpink:0xff69b4,indianred:0xcd5c5c,indigo:4915330,ivory:0xfffff0,khaki:0xf0e68c,lavender:0xe6e6fa,lavenderblush:0xfff0f5,lawngreen:8190976,lemonchiffon:0xfffacd,lightblue:0xadd8e6,lightcoral:0xf08080,lightcyan:0xe0ffff,lightgoldenrodyellow:0xfafad2,lightgray:0xd3d3d3,lightgreen:9498256,lightgrey:0xd3d3d3,lightpink:0xffb6c1,lightsalmon:0xffa07a,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:0xb0c4de,lightyellow:0xffffe0,lime:65280,limegreen:3329330,linen:0xfaf0e6,magenta:0xff00ff,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:0xba55d3,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:0xc71585,midnightblue:1644912,mintcream:0xf5fffa,mistyrose:0xffe4e1,moccasin:0xffe4b5,navajowhite:0xffdead,navy:128,oldlace:0xfdf5e6,olive:8421376,olivedrab:7048739,orange:0xffa500,orangered:0xff4500,orchid:0xda70d6,palegoldenrod:0xeee8aa,palegreen:0x98fb98,paleturquoise:0xafeeee,palevioletred:0xdb7093,papayawhip:0xffefd5,peachpuff:0xffdab9,peru:0xcd853f,pink:0xffc0cb,plum:0xdda0dd,powderblue:0xb0e0e6,purple:8388736,rebeccapurple:6697881,red:0xff0000,rosybrown:0xbc8f8f,royalblue:4286945,saddlebrown:9127187,salmon:0xfa8072,sandybrown:0xf4a460,seagreen:3050327,seashell:0xfff5ee,sienna:0xa0522d,silver:0xc0c0c0,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:0xfffafa,springgreen:65407,steelblue:4620980,tan:0xd2b48c,teal:32896,thistle:0xd8bfd8,tomato:0xff6347,turquoise:4251856,violet:0xee82ee,wheat:0xf5deb3,white:0xffffff,whitesmoke:0xf5f5f5,yellow:0xffff00,yellowgreen:0x9acd32};function lj(){return this.rgb().formatHex()}function lO(){return this.rgb().formatRgb()}function lS(e){var t,r;return e=(e+"").trim().toLowerCase(),(t=lh.exec(e))?(r=t[1].length,t=parseInt(t[1],16),6===r?lA(t):3===r?new lE(t>>8&15|t>>4&240,t>>4&15|240&t,(15&t)<<4|15&t,1):8===r?lP(t>>24&255,t>>16&255,t>>8&255,(255&t)/255):4===r?lP(t>>12&15|t>>8&240,t>>8&15|t>>4&240,t>>4&15|240&t,((15&t)<<4|15&t)/255):null):(t=ly.exec(e))?new lE(t[1],t[2],t[3],1):(t=lm.exec(e))?new lE(255*t[1]/100,255*t[2]/100,255*t[3]/100,1):(t=lv.exec(e))?lP(t[1],t[2],t[3],t[4]):(t=lg.exec(e))?lP(255*t[1]/100,255*t[2]/100,255*t[3]/100,t[4]):(t=lb.exec(e))?lI(t[1],t[2]/100,t[3]/100,1):(t=lx.exec(e))?lI(t[1],t[2]/100,t[3]/100,t[4]):lw.hasOwnProperty(e)?lA(lw[e]):"transparent"===e?new lE(NaN,NaN,NaN,0):null}function lA(e){return new lE(e>>16&255,e>>8&255,255&e,1)}function lP(e,t,r,n){return n<=0&&(e=t=r=NaN),new lE(e,t,r,n)}function lk(e,t,r,n){var o;return 1==arguments.length?((o=e)instanceof lu||(o=lS(o)),o)?new lE((o=o.rgb()).r,o.g,o.b,o.opacity):new lE:new lE(e,t,r,null==n?1:n)}function lE(e,t,r,n){this.r=+e,this.g=+t,this.b=+r,this.opacity=+n}function lN(){return`#${l_(this.r)}${l_(this.g)}${l_(this.b)}`}function lM(){let e=lC(this.opacity);return`${1===e?"rgb(":"rgba("}${lT(this.r)}, ${lT(this.g)}, ${lT(this.b)}${1===e?")":`, ${e})`}`}function lC(e){return isNaN(e)?1:Math.max(0,Math.min(1,e))}function lT(e){return Math.max(0,Math.min(255,Math.round(e)||0))}function l_(e){return((e=lT(e))<16?"0":"")+e.toString(16)}function lI(e,t,r,n){return n<=0?e=t=r=NaN:r<=0||r>=1?e=t=NaN:t<=0&&(e=NaN),new lR(e,t,r,n)}function lD(e){if(e instanceof lR)return new lR(e.h,e.s,e.l,e.opacity);if(e instanceof lu||(e=lS(e)),!e)return new lR;if(e instanceof lR)return e;var t=(e=e.rgb()).r/255,r=e.g/255,n=e.b/255,o=Math.min(t,r,n),i=Math.max(t,r,n),a=NaN,l=i-o,s=(i+o)/2;return l?(a=t===i?(r-n)/l+(r<n)*6:r===i?(n-t)/l+2:(t-r)/l+4,l/=s<.5?i+o:2-i-o,a*=60):l=s>0&&s<1?0:a,new lR(a,l,s,e.opacity)}function lR(e,t,r,n){this.h=+e,this.s=+t,this.l=+r,this.opacity=+n}function lB(e){return(e=(e||0)%360)<0?e+360:e}function lL(e){return Math.max(0,Math.min(1,e||0))}function l$(e,t,r){return(e<60?t+(r-t)*e/60:e<180?r:e<240?t+(r-t)*(240-e)/60:t)*255}function lz(e,t,r,n,o){var i=e*e,a=i*e;return((1-3*e+3*i-a)*t+(4-6*i+3*a)*r+(1+3*e+3*i-3*a)*n+a*o)/6}ls(lu,lS,{copy(e){return Object.assign(new this.constructor,this,e)},displayable(){return this.rgb().displayable()},hex:lj,formatHex:lj,formatHex8:function(){return this.rgb().formatHex8()},formatHsl:function(){return lD(this).formatHsl()},formatRgb:lO,toString:lO}),ls(lE,lk,lc(lu,{brighter(e){return e=null==e?1.4285714285714286:Math.pow(1.4285714285714286,e),new lE(this.r*e,this.g*e,this.b*e,this.opacity)},darker(e){return e=null==e?.7:Math.pow(.7,e),new lE(this.r*e,this.g*e,this.b*e,this.opacity)},rgb(){return this},clamp(){return new lE(lT(this.r),lT(this.g),lT(this.b),lC(this.opacity))},displayable(){return -.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:lN,formatHex:lN,formatHex8:function(){return`#${l_(this.r)}${l_(this.g)}${l_(this.b)}${l_((isNaN(this.opacity)?1:this.opacity)*255)}`},formatRgb:lM,toString:lM})),ls(lR,function(e,t,r,n){return 1==arguments.length?lD(e):new lR(e,t,r,null==n?1:n)},lc(lu,{brighter(e){return e=null==e?1.4285714285714286:Math.pow(1.4285714285714286,e),new lR(this.h,this.s,this.l*e,this.opacity)},darker(e){return e=null==e?.7:Math.pow(.7,e),new lR(this.h,this.s,this.l*e,this.opacity)},rgb(){var e=this.h%360+(this.h<0)*360,t=isNaN(e)||isNaN(this.s)?0:this.s,r=this.l,n=r+(r<.5?r:1-r)*t,o=2*r-n;return new lE(l$(e>=240?e-240:e+120,o,n),l$(e,o,n),l$(e<120?e+240:e-120,o,n),this.opacity)},clamp(){return new lR(lB(this.h),lL(this.s),lL(this.l),lC(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){let e=lC(this.opacity);return`${1===e?"hsl(":"hsla("}${lB(this.h)}, ${100*lL(this.s)}%, ${100*lL(this.l)}%${1===e?")":`, ${e})`}`}}));let lU=e=>()=>e;function lF(e,t){var r,n,o=t-e;return o?(r=e,n=o,function(e){return r+e*n}):lU(isNaN(e)?t:e)}let lq=function e(t){var r,n=1==(r=+t)?lF:function(e,t){var n,o,i;return t-e?(n=e,o=t,n=Math.pow(n,i=r),o=Math.pow(o,i)-n,i=1/i,function(e){return Math.pow(n+e*o,i)}):lU(isNaN(e)?t:e)};function o(e,t){var r=n((e=lk(e)).r,(t=lk(t)).r),o=n(e.g,t.g),i=n(e.b,t.b),a=lF(e.opacity,t.opacity);return function(t){return e.r=r(t),e.g=o(t),e.b=i(t),e.opacity=a(t),e+""}}return o.gamma=e,o}(1);function lW(e){return function(t){var r,n,o=t.length,i=Array(o),a=Array(o),l=Array(o);for(r=0;r<o;++r)n=lk(t[r]),i[r]=n.r||0,a[r]=n.g||0,l[r]=n.b||0;return i=e(i),a=e(a),l=e(l),n.opacity=1,function(e){return n.r=i(e),n.g=a(e),n.b=l(e),n+""}}}lW(function(e){var t=e.length-1;return function(r){var n=r<=0?r=0:r>=1?(r=1,t-1):Math.floor(r*t),o=e[n],i=e[n+1],a=n>0?e[n-1]:2*o-i,l=n<t-1?e[n+2]:2*i-o;return lz((r-n/t)*t,a,o,i,l)}}),lW(function(e){var t=e.length;return function(r){var n=Math.floor(((r%=1)<0?++r:r)*t),o=e[(n+t-1)%t],i=e[n%t],a=e[(n+1)%t],l=e[(n+2)%t];return lz((r-n/t)*t,o,i,a,l)}});function lV(e,t){return e*=1,t*=1,function(r){return e*(1-r)+t*r}}var lH=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,lG=RegExp(lH.source,"g");function lX(e,t){var r,n,o=typeof t;return null==t||"boolean"===o?lU(t):("number"===o?lV:"string"===o?(n=lS(t))?(t=n,lq):function(e,t){var r,n,o,i,a,l=lH.lastIndex=lG.lastIndex=0,s=-1,c=[],u=[];for(e+="",t+="";(o=lH.exec(e))&&(i=lG.exec(t));)(a=i.index)>l&&(a=t.slice(l,a),c[s]?c[s]+=a:c[++s]=a),(o=o[0])===(i=i[0])?c[s]?c[s]+=i:c[++s]=i:(c[++s]=null,u.push({i:s,x:lV(o,i)})),l=lG.lastIndex;return l<t.length&&(a=t.slice(l),c[s]?c[s]+=a:c[++s]=a),c.length<2?u[0]?(r=u[0].x,function(e){return r(e)+""}):(n=t,function(){return n}):(t=u.length,function(e){for(var r,n=0;n<t;++n)c[(r=u[n]).i]=r.x(e);return c.join("")})}:t instanceof lS?lq:t instanceof Date?function(e,t){var r=new Date;return e*=1,t*=1,function(n){return r.setTime(e*(1-n)+t*n),r}}:!ArrayBuffer.isView(r=t)||r instanceof DataView?Array.isArray(t)?function(e,t){var r,n=t?t.length:0,o=e?Math.min(n,e.length):0,i=Array(o),a=Array(n);for(r=0;r<o;++r)i[r]=lX(e[r],t[r]);for(;r<n;++r)a[r]=t[r];return function(e){for(r=0;r<o;++r)a[r]=i[r](e);return a}}:"function"!=typeof t.valueOf&&"function"!=typeof t.toString||isNaN(t)?function(e,t){var r,n={},o={};for(r in(null===e||"object"!=typeof e)&&(e={}),(null===t||"object"!=typeof t)&&(t={}),t)r in e?n[r]=lX(e[r],t[r]):o[r]=t[r];return function(e){for(r in n)o[r]=n[r](e);return o}}:lV:function(e,t){t||(t=[]);var r,n=e?Math.min(t.length,e.length):0,o=t.slice();return function(i){for(r=0;r<n;++r)o[r]=e[r]*(1-i)+t[r]*i;return o}})(e,t)}function lY(e,t){return e*=1,t*=1,function(r){return Math.round(e*(1-r)+t*r)}}function lK(e){return+e}var lZ=[0,1];function lJ(e){return e}function lQ(e,t){var r;return(t-=e*=1)?function(r){return(r-e)/t}:(r=isNaN(t)?NaN:.5,function(){return r})}function l0(e,t,r){var n=e[0],o=e[1],i=t[0],a=t[1];return o<n?(n=lQ(o,n),i=r(a,i)):(n=lQ(n,o),i=r(i,a)),function(e){return i(n(e))}}function l1(e,t,r){var n=Math.min(e.length,t.length)-1,o=Array(n),i=Array(n),a=-1;for(e[n]<e[0]&&(e=e.slice().reverse(),t=t.slice().reverse());++a<n;)o[a]=lQ(e[a],e[a+1]),i[a]=r(t[a],t[a+1]);return function(t){var r=ll(e,t,1,n)-1;return i[r](o[r](t))}}function l2(e,t){return t.domain(e.domain()).range(e.range()).interpolate(e.interpolate()).clamp(e.clamp()).unknown(e.unknown())}function l5(){var e,t,r,n,o,i,a=lZ,l=lZ,s=lX,c=lJ;function u(){var e,t,r,s=Math.min(a.length,l.length);return c!==lJ&&(e=a[0],t=a[s-1],e>t&&(r=e,e=t,t=r),c=function(r){return Math.max(e,Math.min(t,r))}),n=s>2?l1:l0,o=i=null,f}function f(t){return null==t||isNaN(t*=1)?r:(o||(o=n(a.map(e),l,s)))(e(c(t)))}return f.invert=function(r){return c(t((i||(i=n(l,a.map(e),lV)))(r)))},f.domain=function(e){return arguments.length?(a=Array.from(e,lK),u()):a.slice()},f.range=function(e){return arguments.length?(l=Array.from(e),u()):l.slice()},f.rangeRound=function(e){return l=Array.from(e),s=lY,u()},f.clamp=function(e){return arguments.length?(c=!!e||lJ,u()):c!==lJ},f.interpolate=function(e){return arguments.length?(s=e,u()):s},f.unknown=function(e){return arguments.length?(r=e,f):r},function(r,n){return e=r,t=n,u()}}function l4(){return l5()(lJ,lJ)}var l3=/^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;function l6(e){var t;if(!(t=l3.exec(e)))throw Error("invalid format: "+e);return new l8({fill:t[1],align:t[2],sign:t[3],symbol:t[4],zero:t[5],width:t[6],comma:t[7],precision:t[8]&&t[8].slice(1),trim:t[9],type:t[10]})}function l8(e){this.fill=void 0===e.fill?" ":e.fill+"",this.align=void 0===e.align?">":e.align+"",this.sign=void 0===e.sign?"-":e.sign+"",this.symbol=void 0===e.symbol?"":e.symbol+"",this.zero=!!e.zero,this.width=void 0===e.width?void 0:+e.width,this.comma=!!e.comma,this.precision=void 0===e.precision?void 0:+e.precision,this.trim=!!e.trim,this.type=void 0===e.type?"":e.type+""}function l7(e,t){if((r=(e=t?e.toExponential(t-1):e.toExponential()).indexOf("e"))<0)return null;var r,n=e.slice(0,r);return[n.length>1?n[0]+n.slice(2):n,+e.slice(r+1)]}function l9(e){return(e=l7(Math.abs(e)))?e[1]:NaN}function se(e,t){var r=l7(e,t);if(!r)return e+"";var n=r[0],o=r[1];return o<0?"0."+Array(-o).join("0")+n:n.length>o+1?n.slice(0,o+1)+"."+n.slice(o+1):n+Array(o-n.length+2).join("0")}l6.prototype=l8.prototype,l8.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(void 0===this.width?"":Math.max(1,0|this.width))+(this.comma?",":"")+(void 0===this.precision?"":"."+Math.max(0,0|this.precision))+(this.trim?"~":"")+this.type};let st={"%":(e,t)=>(100*e).toFixed(t),b:e=>Math.round(e).toString(2),c:e=>e+"",d:function(e){return Math.abs(e=Math.round(e))>=1e21?e.toLocaleString("en").replace(/,/g,""):e.toString(10)},e:(e,t)=>e.toExponential(t),f:(e,t)=>e.toFixed(t),g:(e,t)=>e.toPrecision(t),o:e=>Math.round(e).toString(8),p:(e,t)=>se(100*e,t),r:se,s:function(e,t){var r=l7(e,t);if(!r)return e+"";var n=r[0],o=r[1],i=o-(uM=3*Math.max(-8,Math.min(8,Math.floor(o/3))))+1,a=n.length;return i===a?n:i>a?n+Array(i-a+1).join("0"):i>0?n.slice(0,i)+"."+n.slice(i):"0."+Array(1-i).join("0")+l7(e,Math.max(0,t+i-1))[0]},X:e=>Math.round(e).toString(16).toUpperCase(),x:e=>Math.round(e).toString(16)};function sr(e){return e}var sn=Array.prototype.map,so=["y","z","a","f","p","n","\xb5","m","","k","M","G","T","P","E","Z","Y"];function si(e,t,r,n){var o,i,a,l=le(e,t,r);switch((n=l6(null==n?",f":n)).type){case"s":var s=Math.max(Math.abs(e),Math.abs(t));return null==n.precision&&!isNaN(a=Math.max(0,3*Math.max(-8,Math.min(8,Math.floor(l9(s)/3)))-l9(Math.abs(l))))&&(n.precision=a),u_(n,s);case"":case"e":case"g":case"p":case"r":null==n.precision&&!isNaN(a=Math.max(0,l9(Math.abs(Math.max(Math.abs(e),Math.abs(t)))-(o=Math.abs(o=l)))-l9(o))+1)&&(n.precision=a-("e"===n.type));break;case"f":case"%":null==n.precision&&!isNaN(a=Math.max(0,-l9(Math.abs(l))))&&(n.precision=a-("%"===n.type)*2)}return uT(n)}function sa(e){var t=e.domain;return e.ticks=function(e){var r=t();return a7(r[0],r[r.length-1],null==e?10:e)},e.tickFormat=function(e,r){var n=t();return si(n[0],n[n.length-1],null==e?10:e,r)},e.nice=function(r){null==r&&(r=10);var n,o,i=t(),a=0,l=i.length-1,s=i[a],c=i[l],u=10;for(c<s&&(o=s,s=c,c=o,o=a,a=l,l=o);u-- >0;){if((o=a9(s,c,r))===n)return i[a]=s,i[l]=c,t(i);if(o>0)s=Math.floor(s/o)*o,c=Math.ceil(c/o)*o;else if(o<0)s=Math.ceil(s*o)/o,c=Math.floor(c*o)/o;else break;n=o}return e},e}function sl(){var e=l4();return e.copy=function(){return l2(e,sl())},ah.apply(e,arguments),sa(e)}function ss(e,t){e=e.slice();var r,n=0,o=e.length-1,i=e[n],a=e[o];return a<i&&(r=n,n=o,o=r,r=i,i=a,a=r),e[n]=t.floor(i),e[o]=t.ceil(a),e}function sc(e){return Math.log(e)}function su(e){return Math.exp(e)}function sf(e){return-Math.log(-e)}function sd(e){return-Math.exp(-e)}function sp(e){return isFinite(e)?+("1e"+e):e<0?0:e}function sh(e){return(t,r)=>-e(-t,r)}function sy(e){let t,r;let n=e(sc,su),o=n.domain,i=10;function a(){var a,l;return t=(a=i)===Math.E?Math.log:10===a&&Math.log10||2===a&&Math.log2||(a=Math.log(a),e=>Math.log(e)/a),r=10===(l=i)?sp:l===Math.E?Math.exp:e=>Math.pow(l,e),o()[0]<0?(t=sh(t),r=sh(r),e(sf,sd)):e(sc,su),n}return n.base=function(e){return arguments.length?(i=+e,a()):i},n.domain=function(e){return arguments.length?(o(e),a()):o()},n.ticks=e=>{let n,a;let l=o(),s=l[0],c=l[l.length-1],u=c<s;u&&([s,c]=[c,s]);let f=t(s),d=t(c),p=null==e?10:+e,h=[];if(!(i%1)&&d-f<p){if(f=Math.floor(f),d=Math.ceil(d),s>0){for(;f<=d;++f)for(n=1;n<i;++n)if(!((a=f<0?n/r(-f):n*r(f))<s)){if(a>c)break;h.push(a)}}else for(;f<=d;++f)for(n=i-1;n>=1;--n)if(!((a=f>0?n/r(-f):n*r(f))<s)){if(a>c)break;h.push(a)}2*h.length<p&&(h=a7(s,c,p))}else h=a7(f,d,Math.min(d-f,p)).map(r);return u?h.reverse():h},n.tickFormat=(e,o)=>{if(null==e&&(e=10),null==o&&(o=10===i?"s":","),"function"!=typeof o&&(i%1||null!=(o=l6(o)).precision||(o.trim=!0),o=uT(o)),e===1/0)return o;let a=Math.max(1,i*e/n.ticks().length);return e=>{let n=e/r(Math.round(t(e)));return n*i<i-.5&&(n*=i),n<=a?o(e):""}},n.nice=()=>o(ss(o(),{floor:e=>r(Math.floor(t(e))),ceil:e=>r(Math.ceil(t(e)))})),n}function sm(e){return function(t){return Math.sign(t)*Math.log1p(Math.abs(t/e))}}function sv(e){return function(t){return Math.sign(t)*Math.expm1(Math.abs(t))*e}}function sg(e){var t=1,r=e(sm(1),sv(t));return r.constant=function(r){return arguments.length?e(sm(t=+r),sv(t)):t},sa(r)}function sb(e){return function(t){return t<0?-Math.pow(-t,e):Math.pow(t,e)}}function sx(e){return e<0?-Math.sqrt(-e):Math.sqrt(e)}function sw(e){return e<0?-e*e:e*e}function sj(e){var t=e(lJ,lJ),r=1;return t.exponent=function(t){return arguments.length?1==(r=+t)?e(lJ,lJ):.5===r?e(sx,sw):e(sb(r),sb(1/r)):r},sa(t)}function sO(){var e=sj(l5());return e.copy=function(){return l2(e,sO()).exponent(e.exponent())},ah.apply(e,arguments),e}function sS(){return sO.apply(null,arguments).exponent(.5)}function sA(e){return Math.sign(e)*e*e}function sP(e,t){let r;if(void 0===t)for(let t of e)null!=t&&(r<t||void 0===r&&t>=t)&&(r=t);else{let n=-1;for(let o of e)null!=(o=t(o,++n,e))&&(r<o||void 0===r&&o>=o)&&(r=o)}return r}function sk(e,t){let r;if(void 0===t)for(let t of e)null!=t&&(r>t||void 0===r&&t>=t)&&(r=t);else{let n=-1;for(let o of e)null!=(o=t(o,++n,e))&&(r>o||void 0===r&&o>=o)&&(r=o)}return r}uT=(uC=function(e){var t,r,n,o=void 0===e.grouping||void 0===e.thousands?sr:(t=sn.call(e.grouping,Number),r=e.thousands+"",function(e,n){for(var o=e.length,i=[],a=0,l=t[0],s=0;o>0&&l>0&&(s+l+1>n&&(l=Math.max(1,n-s)),i.push(e.substring(o-=l,o+l)),!((s+=l+1)>n));)l=t[a=(a+1)%t.length];return i.reverse().join(r)}),i=void 0===e.currency?"":e.currency[0]+"",a=void 0===e.currency?"":e.currency[1]+"",l=void 0===e.decimal?".":e.decimal+"",s=void 0===e.numerals?sr:(n=sn.call(e.numerals,String),function(e){return e.replace(/[0-9]/g,function(e){return n[+e]})}),c=void 0===e.percent?"%":e.percent+"",u=void 0===e.minus?"−":e.minus+"",f=void 0===e.nan?"NaN":e.nan+"";function d(e){var t=(e=l6(e)).fill,r=e.align,n=e.sign,d=e.symbol,p=e.zero,h=e.width,y=e.comma,m=e.precision,v=e.trim,g=e.type;"n"===g?(y=!0,g="g"):st[g]||(void 0===m&&(m=12),v=!0,g="g"),(p||"0"===t&&"="===r)&&(p=!0,t="0",r="=");var b="$"===d?i:"#"===d&&/[boxX]/.test(g)?"0"+g.toLowerCase():"",x="$"===d?a:/[%p]/.test(g)?c:"",w=st[g],j=/[defgprs%]/.test(g);function O(e){var i,a,c,d=b,O=x;if("c"===g)O=w(e)+O,e="";else{var S=(e*=1)<0||1/e<0;if(e=isNaN(e)?f:w(Math.abs(e),m),v&&(e=function(e){e:for(var t,r=e.length,n=1,o=-1;n<r;++n)switch(e[n]){case".":o=t=n;break;case"0":0===o&&(o=n),t=n;break;default:if(!+e[n])break e;o>0&&(o=0)}return o>0?e.slice(0,o)+e.slice(t+1):e}(e)),S&&0==+e&&"+"!==n&&(S=!1),d=(S?"("===n?n:u:"-"===n||"("===n?"":n)+d,O=("s"===g?so[8+uM/3]:"")+O+(S&&"("===n?")":""),j){for(i=-1,a=e.length;++i<a;)if(48>(c=e.charCodeAt(i))||c>57){O=(46===c?l+e.slice(i+1):e.slice(i))+O,e=e.slice(0,i);break}}}y&&!p&&(e=o(e,1/0));var A=d.length+e.length+O.length,P=A<h?Array(h-A+1).join(t):"";switch(y&&p&&(e=o(P+e,P.length?h-O.length:1/0),P=""),r){case"<":e=d+e+O+P;break;case"=":e=d+P+e+O;break;case"^":e=P.slice(0,A=P.length>>1)+d+e+O+P.slice(A);break;default:e=P+d+e+O}return s(e)}return m=void 0===m?6:/[gprs]/.test(g)?Math.max(1,Math.min(21,m)):Math.max(0,Math.min(20,m)),O.toString=function(){return e+""},O}return{format:d,formatPrefix:function(e,t){var r=d(((e=l6(e)).type="f",e)),n=3*Math.max(-8,Math.min(8,Math.floor(l9(t)/3))),o=Math.pow(10,-n),i=so[8+n/3];return function(e){return r(o*e)+i}}}}({thousands:",",grouping:[3],currency:["$",""]})).format,u_=uC.formatPrefix;function sE(e,t){return(null==e||!(e>=e))-(null==t||!(t>=t))||(e<t?-1:+(e>t))}function sN(e,t,r){let n=e[t];e[t]=e[r],e[r]=n}let sM=new Date,sC=new Date;function sT(e,t,r,n){function o(t){return e(t=0==arguments.length?new Date:new Date(+t)),t}return o.floor=t=>(e(t=new Date(+t)),t),o.ceil=r=>(e(r=new Date(r-1)),t(r,1),e(r),r),o.round=e=>{let t=o(e),r=o.ceil(e);return e-t<r-e?t:r},o.offset=(e,r)=>(t(e=new Date(+e),null==r?1:Math.floor(r)),e),o.range=(r,n,i)=>{let a;let l=[];if(r=o.ceil(r),i=null==i?1:Math.floor(i),!(r<n)||!(i>0))return l;do l.push(a=new Date(+r)),t(r,i),e(r);while(a<r&&r<n);return l},o.filter=r=>sT(t=>{if(t>=t)for(;e(t),!r(t);)t.setTime(t-1)},(e,n)=>{if(e>=e){if(n<0)for(;++n<=0;)for(;t(e,-1),!r(e););else for(;--n>=0;)for(;t(e,1),!r(e););}}),r&&(o.count=(t,n)=>(sM.setTime(+t),sC.setTime(+n),e(sM),e(sC),Math.floor(r(sM,sC))),o.every=e=>isFinite(e=Math.floor(e))&&e>0?e>1?o.filter(n?t=>n(t)%e==0:t=>o.count(0,t)%e==0):o:null),o}let s_=sT(()=>{},(e,t)=>{e.setTime(+e+t)},(e,t)=>t-e);s_.every=e=>isFinite(e=Math.floor(e))&&e>0?e>1?sT(t=>{t.setTime(Math.floor(t/e)*e)},(t,r)=>{t.setTime(+t+r*e)},(t,r)=>(r-t)/e):s_:null,s_.range;let sI=sT(e=>{e.setTime(e-e.getMilliseconds())},(e,t)=>{e.setTime(+e+1e3*t)},(e,t)=>(t-e)/1e3,e=>e.getUTCSeconds());sI.range;let sD=sT(e=>{e.setTime(e-e.getMilliseconds()-1e3*e.getSeconds())},(e,t)=>{e.setTime(+e+6e4*t)},(e,t)=>(t-e)/6e4,e=>e.getMinutes());sD.range;let sR=sT(e=>{e.setUTCSeconds(0,0)},(e,t)=>{e.setTime(+e+6e4*t)},(e,t)=>(t-e)/6e4,e=>e.getUTCMinutes());sR.range;let sB=sT(e=>{e.setTime(e-e.getMilliseconds()-1e3*e.getSeconds()-6e4*e.getMinutes())},(e,t)=>{e.setTime(+e+36e5*t)},(e,t)=>(t-e)/36e5,e=>e.getHours());sB.range;let sL=sT(e=>{e.setUTCMinutes(0,0,0)},(e,t)=>{e.setTime(+e+36e5*t)},(e,t)=>(t-e)/36e5,e=>e.getUTCHours());sL.range;let s$=sT(e=>e.setHours(0,0,0,0),(e,t)=>e.setDate(e.getDate()+t),(e,t)=>(t-e-(t.getTimezoneOffset()-e.getTimezoneOffset())*6e4)/864e5,e=>e.getDate()-1);s$.range;let sz=sT(e=>{e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+t)},(e,t)=>(t-e)/864e5,e=>e.getUTCDate()-1);sz.range;let sU=sT(e=>{e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+t)},(e,t)=>(t-e)/864e5,e=>Math.floor(e/864e5));function sF(e){return sT(t=>{t.setDate(t.getDate()-(t.getDay()+7-e)%7),t.setHours(0,0,0,0)},(e,t)=>{e.setDate(e.getDate()+7*t)},(e,t)=>(t-e-(t.getTimezoneOffset()-e.getTimezoneOffset())*6e4)/6048e5)}sU.range;let sq=sF(0),sW=sF(1),sV=sF(2),sH=sF(3),sG=sF(4),sX=sF(5),sY=sF(6);function sK(e){return sT(t=>{t.setUTCDate(t.getUTCDate()-(t.getUTCDay()+7-e)%7),t.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+7*t)},(e,t)=>(t-e)/6048e5)}sq.range,sW.range,sV.range,sH.range,sG.range,sX.range,sY.range;let sZ=sK(0),sJ=sK(1),sQ=sK(2),s0=sK(3),s1=sK(4),s2=sK(5),s5=sK(6);sZ.range,sJ.range,sQ.range,s0.range,s1.range,s2.range,s5.range;let s4=sT(e=>{e.setDate(1),e.setHours(0,0,0,0)},(e,t)=>{e.setMonth(e.getMonth()+t)},(e,t)=>t.getMonth()-e.getMonth()+(t.getFullYear()-e.getFullYear())*12,e=>e.getMonth());s4.range;let s3=sT(e=>{e.setUTCDate(1),e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCMonth(e.getUTCMonth()+t)},(e,t)=>t.getUTCMonth()-e.getUTCMonth()+(t.getUTCFullYear()-e.getUTCFullYear())*12,e=>e.getUTCMonth());s3.range;let s6=sT(e=>{e.setMonth(0,1),e.setHours(0,0,0,0)},(e,t)=>{e.setFullYear(e.getFullYear()+t)},(e,t)=>t.getFullYear()-e.getFullYear(),e=>e.getFullYear());s6.every=e=>isFinite(e=Math.floor(e))&&e>0?sT(t=>{t.setFullYear(Math.floor(t.getFullYear()/e)*e),t.setMonth(0,1),t.setHours(0,0,0,0)},(t,r)=>{t.setFullYear(t.getFullYear()+r*e)}):null,s6.range;let s8=sT(e=>{e.setUTCMonth(0,1),e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCFullYear(e.getUTCFullYear()+t)},(e,t)=>t.getUTCFullYear()-e.getUTCFullYear(),e=>e.getUTCFullYear());function s7(e,t,r,n,o,i){let a=[[sI,1,1e3],[sI,5,5e3],[sI,15,15e3],[sI,30,3e4],[i,1,6e4],[i,5,3e5],[i,15,9e5],[i,30,18e5],[o,1,36e5],[o,3,108e5],[o,6,216e5],[o,12,432e5],[n,1,864e5],[n,2,1728e5],[r,1,6048e5],[t,1,2592e6],[t,3,7776e6],[e,1,31536e6]];function l(t,r,n){let o=Math.abs(r-t)/n,i=ln(([,,e])=>e).right(a,o);if(i===a.length)return e.every(le(t/31536e6,r/31536e6,n));if(0===i)return s_.every(Math.max(le(t,r,n),1));let[l,s]=a[o/a[i-1][2]<a[i][2]/o?i-1:i];return l.every(s)}return[function(e,t,r){let n=t<e;n&&([e,t]=[t,e]);let o=r&&"function"==typeof r.range?r:l(e,t,r),i=o?o.range(e,+t+1):[];return n?i.reverse():i},l]}s8.every=e=>isFinite(e=Math.floor(e))&&e>0?sT(t=>{t.setUTCFullYear(Math.floor(t.getUTCFullYear()/e)*e),t.setUTCMonth(0,1),t.setUTCHours(0,0,0,0)},(t,r)=>{t.setUTCFullYear(t.getUTCFullYear()+r*e)}):null,s8.range;let[s9,ce]=s7(s8,s3,sZ,sU,sL,sR),[ct,cr]=s7(s6,s4,sq,s$,sB,sD);function cn(e){if(0<=e.y&&e.y<100){var t=new Date(-1,e.m,e.d,e.H,e.M,e.S,e.L);return t.setFullYear(e.y),t}return new Date(e.y,e.m,e.d,e.H,e.M,e.S,e.L)}function co(e){if(0<=e.y&&e.y<100){var t=new Date(Date.UTC(-1,e.m,e.d,e.H,e.M,e.S,e.L));return t.setUTCFullYear(e.y),t}return new Date(Date.UTC(e.y,e.m,e.d,e.H,e.M,e.S,e.L))}function ci(e,t,r){return{y:e,m:t,d:r,H:0,M:0,S:0,L:0}}var ca={"-":"",_:" ",0:"0"},cl=/^\s*\d+/,cs=/^%/,cc=/[\\^$*+?|[\]().{}]/g;function cu(e,t,r){var n=e<0?"-":"",o=(n?-e:e)+"",i=o.length;return n+(i<r?Array(r-i+1).join(t)+o:o)}function cf(e){return e.replace(cc,"\\$&")}function cd(e){return RegExp("^(?:"+e.map(cf).join("|")+")","i")}function cp(e){return new Map(e.map((e,t)=>[e.toLowerCase(),t]))}function ch(e,t,r){var n=cl.exec(t.slice(r,r+1));return n?(e.w=+n[0],r+n[0].length):-1}function cy(e,t,r){var n=cl.exec(t.slice(r,r+1));return n?(e.u=+n[0],r+n[0].length):-1}function cm(e,t,r){var n=cl.exec(t.slice(r,r+2));return n?(e.U=+n[0],r+n[0].length):-1}function cv(e,t,r){var n=cl.exec(t.slice(r,r+2));return n?(e.V=+n[0],r+n[0].length):-1}function cg(e,t,r){var n=cl.exec(t.slice(r,r+2));return n?(e.W=+n[0],r+n[0].length):-1}function cb(e,t,r){var n=cl.exec(t.slice(r,r+4));return n?(e.y=+n[0],r+n[0].length):-1}function cx(e,t,r){var n=cl.exec(t.slice(r,r+2));return n?(e.y=+n[0]+(+n[0]>68?1900:2e3),r+n[0].length):-1}function cw(e,t,r){var n=/^(Z)|([+-]\d\d)(?::?(\d\d))?/.exec(t.slice(r,r+6));return n?(e.Z=n[1]?0:-(n[2]+(n[3]||"00")),r+n[0].length):-1}function cj(e,t,r){var n=cl.exec(t.slice(r,r+1));return n?(e.q=3*n[0]-3,r+n[0].length):-1}function cO(e,t,r){var n=cl.exec(t.slice(r,r+2));return n?(e.m=n[0]-1,r+n[0].length):-1}function cS(e,t,r){var n=cl.exec(t.slice(r,r+2));return n?(e.d=+n[0],r+n[0].length):-1}function cA(e,t,r){var n=cl.exec(t.slice(r,r+3));return n?(e.m=0,e.d=+n[0],r+n[0].length):-1}function cP(e,t,r){var n=cl.exec(t.slice(r,r+2));return n?(e.H=+n[0],r+n[0].length):-1}function ck(e,t,r){var n=cl.exec(t.slice(r,r+2));return n?(e.M=+n[0],r+n[0].length):-1}function cE(e,t,r){var n=cl.exec(t.slice(r,r+2));return n?(e.S=+n[0],r+n[0].length):-1}function cN(e,t,r){var n=cl.exec(t.slice(r,r+3));return n?(e.L=+n[0],r+n[0].length):-1}function cM(e,t,r){var n=cl.exec(t.slice(r,r+6));return n?(e.L=Math.floor(n[0]/1e3),r+n[0].length):-1}function cC(e,t,r){var n=cs.exec(t.slice(r,r+1));return n?r+n[0].length:-1}function cT(e,t,r){var n=cl.exec(t.slice(r));return n?(e.Q=+n[0],r+n[0].length):-1}function c_(e,t,r){var n=cl.exec(t.slice(r));return n?(e.s=+n[0],r+n[0].length):-1}function cI(e,t){return cu(e.getDate(),t,2)}function cD(e,t){return cu(e.getHours(),t,2)}function cR(e,t){return cu(e.getHours()%12||12,t,2)}function cB(e,t){return cu(1+s$.count(s6(e),e),t,3)}function cL(e,t){return cu(e.getMilliseconds(),t,3)}function c$(e,t){return cL(e,t)+"000"}function cz(e,t){return cu(e.getMonth()+1,t,2)}function cU(e,t){return cu(e.getMinutes(),t,2)}function cF(e,t){return cu(e.getSeconds(),t,2)}function cq(e){var t=e.getDay();return 0===t?7:t}function cW(e,t){return cu(sq.count(s6(e)-1,e),t,2)}function cV(e){var t=e.getDay();return t>=4||0===t?sG(e):sG.ceil(e)}function cH(e,t){return e=cV(e),cu(sG.count(s6(e),e)+(4===s6(e).getDay()),t,2)}function cG(e){return e.getDay()}function cX(e,t){return cu(sW.count(s6(e)-1,e),t,2)}function cY(e,t){return cu(e.getFullYear()%100,t,2)}function cK(e,t){return cu((e=cV(e)).getFullYear()%100,t,2)}function cZ(e,t){return cu(e.getFullYear()%1e4,t,4)}function cJ(e,t){var r=e.getDay();return cu((e=r>=4||0===r?sG(e):sG.ceil(e)).getFullYear()%1e4,t,4)}function cQ(e){var t=e.getTimezoneOffset();return(t>0?"-":(t*=-1,"+"))+cu(t/60|0,"0",2)+cu(t%60,"0",2)}function c0(e,t){return cu(e.getUTCDate(),t,2)}function c1(e,t){return cu(e.getUTCHours(),t,2)}function c2(e,t){return cu(e.getUTCHours()%12||12,t,2)}function c5(e,t){return cu(1+sz.count(s8(e),e),t,3)}function c4(e,t){return cu(e.getUTCMilliseconds(),t,3)}function c3(e,t){return c4(e,t)+"000"}function c6(e,t){return cu(e.getUTCMonth()+1,t,2)}function c8(e,t){return cu(e.getUTCMinutes(),t,2)}function c7(e,t){return cu(e.getUTCSeconds(),t,2)}function c9(e){var t=e.getUTCDay();return 0===t?7:t}function ue(e,t){return cu(sZ.count(s8(e)-1,e),t,2)}function ut(e){var t=e.getUTCDay();return t>=4||0===t?s1(e):s1.ceil(e)}function ur(e,t){return e=ut(e),cu(s1.count(s8(e),e)+(4===s8(e).getUTCDay()),t,2)}function un(e){return e.getUTCDay()}function uo(e,t){return cu(sJ.count(s8(e)-1,e),t,2)}function ui(e,t){return cu(e.getUTCFullYear()%100,t,2)}function ua(e,t){return cu((e=ut(e)).getUTCFullYear()%100,t,2)}function ul(e,t){return cu(e.getUTCFullYear()%1e4,t,4)}function us(e,t){var r=e.getUTCDay();return cu((e=r>=4||0===r?s1(e):s1.ceil(e)).getUTCFullYear()%1e4,t,4)}function uc(){return"+0000"}function uu(){return"%"}function uf(e){return+e}function ud(e){return Math.floor(+e/1e3)}function up(e){return new Date(e)}function uh(e){return e instanceof Date?+e:+new Date(+e)}function uy(e,t,r,n,o,i,a,l,s,c){var u=l4(),f=u.invert,d=u.domain,p=c(".%L"),h=c(":%S"),y=c("%I:%M"),m=c("%I %p"),v=c("%a %d"),g=c("%b %d"),b=c("%B"),x=c("%Y");function w(e){return(s(e)<e?p:l(e)<e?h:a(e)<e?y:i(e)<e?m:n(e)<e?o(e)<e?v:g:r(e)<e?b:x)(e)}return u.invert=function(e){return new Date(f(e))},u.domain=function(e){return arguments.length?d(Array.from(e,uh)):d().map(up)},u.ticks=function(t){var r=d();return e(r[0],r[r.length-1],null==t?10:t)},u.tickFormat=function(e,t){return null==t?w:c(t)},u.nice=function(e){var r=d();return e&&"function"==typeof e.range||(e=t(r[0],r[r.length-1],null==e?10:e)),e?d(ss(r,e)):u},u.copy=function(){return l2(u,uy(e,t,r,n,o,i,a,l,s,c))},u}function um(){return ah.apply(uy(ct,cr,s6,s4,sq,s$,sB,sD,sI,uD).domain([new Date(2e3,0,1),new Date(2e3,0,2)]),arguments)}function uv(){return ah.apply(uy(s9,ce,s8,s3,sZ,sz,sL,sR,sI,uR).domain([Date.UTC(2e3,0,1),Date.UTC(2e3,0,2)]),arguments)}function ug(){var e,t,r,n,o,i=0,a=1,l=lJ,s=!1;function c(t){return null==t||isNaN(t*=1)?o:l(0===r?.5:(t=(n(t)-e)*r,s?Math.max(0,Math.min(1,t)):t))}function u(e){return function(t){var r,n;return arguments.length?([r,n]=t,l=e(r,n),c):[l(0),l(1)]}}return c.domain=function(o){return arguments.length?([i,a]=o,e=n(i*=1),t=n(a*=1),r=e===t?0:1/(t-e),c):[i,a]},c.clamp=function(e){return arguments.length?(s=!!e,c):s},c.interpolator=function(e){return arguments.length?(l=e,c):l},c.range=u(lX),c.rangeRound=u(lY),c.unknown=function(e){return arguments.length?(o=e,c):o},function(o){return n=o,e=o(i),t=o(a),r=e===t?0:1/(t-e),c}}function ub(e,t){return t.domain(e.domain()).interpolator(e.interpolator()).clamp(e.clamp()).unknown(e.unknown())}function ux(){var e=sj(ug());return e.copy=function(){return ub(e,ux()).exponent(e.exponent())},ay.apply(e,arguments)}function uw(){return ux.apply(null,arguments).exponent(.5)}function uj(){var e,t,r,n,o,i,a,l=0,s=.5,c=1,u=1,f=lJ,d=!1;function p(e){return isNaN(e*=1)?a:(e=.5+((e=+i(e))-t)*(u*e<u*t?n:o),f(d?Math.max(0,Math.min(1,e)):e))}function h(e){return function(t){var r,n,o;return arguments.length?([r,n,o]=t,f=function(e,t){void 0===t&&(t=e,e=lX);for(var r=0,n=t.length-1,o=t[0],i=Array(n<0?0:n);r<n;)i[r]=e(o,o=t[++r]);return function(e){var t=Math.max(0,Math.min(n-1,Math.floor(e*=n)));return i[t](e-t)}}(e,[r,n,o]),p):[f(0),f(.5),f(1)]}}return p.domain=function(a){return arguments.length?([l,s,c]=a,e=i(l*=1),t=i(s*=1),r=i(c*=1),n=e===t?0:.5/(t-e),o=t===r?0:.5/(r-t),u=t<e?-1:1,p):[l,s,c]},p.clamp=function(e){return arguments.length?(d=!!e,p):d},p.interpolator=function(e){return arguments.length?(f=e,p):f},p.range=h(lX),p.rangeRound=h(lY),p.unknown=function(e){return arguments.length?(a=e,p):a},function(a){return i=a,e=a(l),t=a(s),r=a(c),n=e===t?0:.5/(t-e),o=t===r?0:.5/(r-t),u=t<e?-1:1,p}}function uO(){var e=sj(uj());return e.copy=function(){return ub(e,uO()).exponent(e.exponent())},ay.apply(e,arguments)}function uS(){return uO.apply(null,arguments).exponent(.5)}function uA(e,t){if((o=e.length)>1)for(var r,n,o,i=1,a=e[t[0]],l=a.length;i<o;++i)for(n=a,a=e[t[i]],r=0;r<l;++r)a[r][1]+=a[r][0]=isNaN(n[r][1])?n[r][0]:n[r][1]}function uP(e){return"object"==typeof e&&"length"in e?e:Array.from(e)}function uk(e){for(var t=e.length,r=Array(t);--t>=0;)r[t]=t;return r}function uE(e,t){return e[t]}function uN(e){let t=[];return t.key=e,t}uD=(uI=function(e){var t=e.dateTime,r=e.date,n=e.time,o=e.periods,i=e.days,a=e.shortDays,l=e.months,s=e.shortMonths,c=cd(o),u=cp(o),f=cd(i),d=cp(i),p=cd(a),h=cp(a),y=cd(l),m=cp(l),v=cd(s),g=cp(s),b={a:function(e){return a[e.getDay()]},A:function(e){return i[e.getDay()]},b:function(e){return s[e.getMonth()]},B:function(e){return l[e.getMonth()]},c:null,d:cI,e:cI,f:c$,g:cK,G:cJ,H:cD,I:cR,j:cB,L:cL,m:cz,M:cU,p:function(e){return o[+(e.getHours()>=12)]},q:function(e){return 1+~~(e.getMonth()/3)},Q:uf,s:ud,S:cF,u:cq,U:cW,V:cH,w:cG,W:cX,x:null,X:null,y:cY,Y:cZ,Z:cQ,"%":uu},x={a:function(e){return a[e.getUTCDay()]},A:function(e){return i[e.getUTCDay()]},b:function(e){return s[e.getUTCMonth()]},B:function(e){return l[e.getUTCMonth()]},c:null,d:c0,e:c0,f:c3,g:ua,G:us,H:c1,I:c2,j:c5,L:c4,m:c6,M:c8,p:function(e){return o[+(e.getUTCHours()>=12)]},q:function(e){return 1+~~(e.getUTCMonth()/3)},Q:uf,s:ud,S:c7,u:c9,U:ue,V:ur,w:un,W:uo,x:null,X:null,y:ui,Y:ul,Z:uc,"%":uu},w={a:function(e,t,r){var n=p.exec(t.slice(r));return n?(e.w=h.get(n[0].toLowerCase()),r+n[0].length):-1},A:function(e,t,r){var n=f.exec(t.slice(r));return n?(e.w=d.get(n[0].toLowerCase()),r+n[0].length):-1},b:function(e,t,r){var n=v.exec(t.slice(r));return n?(e.m=g.get(n[0].toLowerCase()),r+n[0].length):-1},B:function(e,t,r){var n=y.exec(t.slice(r));return n?(e.m=m.get(n[0].toLowerCase()),r+n[0].length):-1},c:function(e,r,n){return S(e,t,r,n)},d:cS,e:cS,f:cM,g:cx,G:cb,H:cP,I:cP,j:cA,L:cN,m:cO,M:ck,p:function(e,t,r){var n=c.exec(t.slice(r));return n?(e.p=u.get(n[0].toLowerCase()),r+n[0].length):-1},q:cj,Q:cT,s:c_,S:cE,u:cy,U:cm,V:cv,w:ch,W:cg,x:function(e,t,n){return S(e,r,t,n)},X:function(e,t,r){return S(e,n,t,r)},y:cx,Y:cb,Z:cw,"%":cC};function j(e,t){return function(r){var n,o,i,a=[],l=-1,s=0,c=e.length;for(r instanceof Date||(r=new Date(+r));++l<c;)37===e.charCodeAt(l)&&(a.push(e.slice(s,l)),null!=(o=ca[n=e.charAt(++l)])?n=e.charAt(++l):o="e"===n?" ":"0",(i=t[n])&&(n=i(r,o)),a.push(n),s=l+1);return a.push(e.slice(s,l)),a.join("")}}function O(e,t){return function(r){var n,o,i=ci(1900,void 0,1);if(S(i,e,r+="",0)!=r.length)return null;if("Q"in i)return new Date(i.Q);if("s"in i)return new Date(1e3*i.s+("L"in i?i.L:0));if(!t||"Z"in i||(i.Z=0),"p"in i&&(i.H=i.H%12+12*i.p),void 0===i.m&&(i.m="q"in i?i.q:0),"V"in i){if(i.V<1||i.V>53)return null;"w"in i||(i.w=1),"Z"in i?(n=(o=(n=co(ci(i.y,0,1))).getUTCDay())>4||0===o?sJ.ceil(n):sJ(n),n=sz.offset(n,(i.V-1)*7),i.y=n.getUTCFullYear(),i.m=n.getUTCMonth(),i.d=n.getUTCDate()+(i.w+6)%7):(n=(o=(n=cn(ci(i.y,0,1))).getDay())>4||0===o?sW.ceil(n):sW(n),n=s$.offset(n,(i.V-1)*7),i.y=n.getFullYear(),i.m=n.getMonth(),i.d=n.getDate()+(i.w+6)%7)}else("W"in i||"U"in i)&&("w"in i||(i.w="u"in i?i.u%7:+("W"in i)),o="Z"in i?co(ci(i.y,0,1)).getUTCDay():cn(ci(i.y,0,1)).getDay(),i.m=0,i.d="W"in i?(i.w+6)%7+7*i.W-(o+5)%7:i.w+7*i.U-(o+6)%7);return"Z"in i?(i.H+=i.Z/100|0,i.M+=i.Z%100,co(i)):cn(i)}}function S(e,t,r,n){for(var o,i,a=0,l=t.length,s=r.length;a<l;){if(n>=s)return -1;if(37===(o=t.charCodeAt(a++))){if(!(i=w[(o=t.charAt(a++))in ca?t.charAt(a++):o])||(n=i(e,r,n))<0)return -1}else if(o!=r.charCodeAt(n++))return -1}return n}return b.x=j(r,b),b.X=j(n,b),b.c=j(t,b),x.x=j(r,x),x.X=j(n,x),x.c=j(t,x),{format:function(e){var t=j(e+="",b);return t.toString=function(){return e},t},parse:function(e){var t=O(e+="",!1);return t.toString=function(){return e},t},utcFormat:function(e){var t=j(e+="",x);return t.toString=function(){return e},t},utcParse:function(e){var t=O(e+="",!0);return t.toString=function(){return e},t}}}({dateTime:"%x, %X",date:"%-m/%-d/%Y",time:"%-I:%M:%S %p",periods:["AM","PM"],days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],shortDays:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],shortMonths:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]})).format,uI.parse,uR=uI.utcFormat,uI.utcParse,Array.prototype.slice;var uM,uC,uT,u_,uI,uD,uR,uB,uL,u$=r(90453),uz=r.n(u$),uU=r(15883),uF=r.n(uU),uq=r(21592),uW=r.n(uq),uV=r(71967),uH=r.n(uV),uG=!0,uX="[DecimalError] ",uY=uX+"Invalid argument: ",uK=uX+"Exponent out of range: ",uZ=Math.floor,uJ=Math.pow,uQ=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,u0=uZ(1286742750677284.5),u1={};function u2(e,t){var r,n,o,i,a,l,s,c,u=e.constructor,f=u.precision;if(!e.s||!t.s)return t.s||(t=new u(e)),uG?fr(t,f):t;if(s=e.d,c=t.d,a=e.e,o=t.e,s=s.slice(),i=a-o){for(i<0?(n=s,i=-i,l=c.length):(n=c,o=a,l=s.length),i>(l=(a=Math.ceil(f/7))>l?a+1:l+1)&&(i=l,n.length=1),n.reverse();i--;)n.push(0);n.reverse()}for((l=s.length)-(i=c.length)<0&&(i=l,n=c,c=s,s=n),r=0;i;)r=(s[--i]=s[i]+c[i]+r)/1e7|0,s[i]%=1e7;for(r&&(s.unshift(r),++o),l=s.length;0==s[--l];)s.pop();return t.d=s,t.e=o,uG?fr(t,f):t}function u5(e,t,r){if(e!==~~e||e<t||e>r)throw Error(uY+e)}function u4(e){var t,r,n,o=e.length-1,i="",a=e[0];if(o>0){for(i+=a,t=1;t<o;t++)(r=7-(n=e[t]+"").length)&&(i+=u9(r)),i+=n;(r=7-(n=(a=e[t])+"").length)&&(i+=u9(r))}else if(0===a)return"0";for(;a%10==0;)a/=10;return i+a}u1.absoluteValue=u1.abs=function(){var e=new this.constructor(this);return e.s&&(e.s=1),e},u1.comparedTo=u1.cmp=function(e){var t,r,n,o;if(e=new this.constructor(e),this.s!==e.s)return this.s||-e.s;if(this.e!==e.e)return this.e>e.e^this.s<0?1:-1;for(t=0,r=(n=this.d.length)<(o=e.d.length)?n:o;t<r;++t)if(this.d[t]!==e.d[t])return this.d[t]>e.d[t]^this.s<0?1:-1;return n===o?0:n>o^this.s<0?1:-1},u1.decimalPlaces=u1.dp=function(){var e=this.d.length-1,t=(e-this.e)*7;if(e=this.d[e])for(;e%10==0;e/=10)t--;return t<0?0:t},u1.dividedBy=u1.div=function(e){return u3(this,new this.constructor(e))},u1.dividedToIntegerBy=u1.idiv=function(e){var t=this.constructor;return fr(u3(this,new t(e),0,1),t.precision)},u1.equals=u1.eq=function(e){return!this.cmp(e)},u1.exponent=function(){return u8(this)},u1.greaterThan=u1.gt=function(e){return this.cmp(e)>0},u1.greaterThanOrEqualTo=u1.gte=function(e){return this.cmp(e)>=0},u1.isInteger=u1.isint=function(){return this.e>this.d.length-2},u1.isNegative=u1.isneg=function(){return this.s<0},u1.isPositive=u1.ispos=function(){return this.s>0},u1.isZero=function(){return 0===this.s},u1.lessThan=u1.lt=function(e){return 0>this.cmp(e)},u1.lessThanOrEqualTo=u1.lte=function(e){return 1>this.cmp(e)},u1.logarithm=u1.log=function(e){var t,r=this.constructor,n=r.precision,o=n+5;if(void 0===e)e=new r(10);else if((e=new r(e)).s<1||e.eq(uL))throw Error(uX+"NaN");if(this.s<1)throw Error(uX+(this.s?"NaN":"-Infinity"));return this.eq(uL)?new r(0):(uG=!1,t=u3(fe(this,o),fe(e,o),o),uG=!0,fr(t,n))},u1.minus=u1.sub=function(e){return e=new this.constructor(e),this.s==e.s?fn(this,e):u2(this,(e.s=-e.s,e))},u1.modulo=u1.mod=function(e){var t,r=this.constructor,n=r.precision;if(!(e=new r(e)).s)throw Error(uX+"NaN");return this.s?(uG=!1,t=u3(this,e,0,1).times(e),uG=!0,this.minus(t)):fr(new r(this),n)},u1.naturalExponential=u1.exp=function(){return u6(this)},u1.naturalLogarithm=u1.ln=function(){return fe(this)},u1.negated=u1.neg=function(){var e=new this.constructor(this);return e.s=-e.s||0,e},u1.plus=u1.add=function(e){return e=new this.constructor(e),this.s==e.s?u2(this,e):fn(this,(e.s=-e.s,e))},u1.precision=u1.sd=function(e){var t,r,n;if(void 0!==e&&!!e!==e&&1!==e&&0!==e)throw Error(uY+e);if(t=u8(this)+1,r=7*(n=this.d.length-1)+1,n=this.d[n]){for(;n%10==0;n/=10)r--;for(n=this.d[0];n>=10;n/=10)r++}return e&&t>r?t:r},u1.squareRoot=u1.sqrt=function(){var e,t,r,n,o,i,a,l=this.constructor;if(this.s<1){if(!this.s)return new l(0);throw Error(uX+"NaN")}for(e=u8(this),uG=!1,0==(o=Math.sqrt(+this))||o==1/0?(((t=u4(this.d)).length+e)%2==0&&(t+="0"),o=Math.sqrt(t),e=uZ((e+1)/2)-(e<0||e%2),n=new l(t=o==1/0?"5e"+e:(t=o.toExponential()).slice(0,t.indexOf("e")+1)+e)):n=new l(o.toString()),o=a=(r=l.precision)+3;;)if(n=(i=n).plus(u3(this,i,a+2)).times(.5),u4(i.d).slice(0,a)===(t=u4(n.d)).slice(0,a)){if(t=t.slice(a-3,a+1),o==a&&"4999"==t){if(fr(i,r+1,0),i.times(i).eq(this)){n=i;break}}else if("9999"!=t)break;a+=4}return uG=!0,fr(n,r)},u1.times=u1.mul=function(e){var t,r,n,o,i,a,l,s,c,u=this.constructor,f=this.d,d=(e=new u(e)).d;if(!this.s||!e.s)return new u(0);for(e.s*=this.s,r=this.e+e.e,(s=f.length)<(c=d.length)&&(i=f,f=d,d=i,a=s,s=c,c=a),i=[],n=a=s+c;n--;)i.push(0);for(n=c;--n>=0;){for(t=0,o=s+n;o>n;)l=i[o]+d[n]*f[o-n-1]+t,i[o--]=l%1e7|0,t=l/1e7|0;i[o]=(i[o]+t)%1e7|0}for(;!i[--a];)i.pop();return t?++r:i.shift(),e.d=i,e.e=r,uG?fr(e,u.precision):e},u1.toDecimalPlaces=u1.todp=function(e,t){var r=this,n=r.constructor;return(r=new n(r),void 0===e)?r:(u5(e,0,1e9),void 0===t?t=n.rounding:u5(t,0,8),fr(r,e+u8(r)+1,t))},u1.toExponential=function(e,t){var r,n=this,o=n.constructor;return void 0===e?r=fo(n,!0):(u5(e,0,1e9),void 0===t?t=o.rounding:u5(t,0,8),r=fo(n=fr(new o(n),e+1,t),!0,e+1)),r},u1.toFixed=function(e,t){var r,n,o=this.constructor;return void 0===e?fo(this):(u5(e,0,1e9),void 0===t?t=o.rounding:u5(t,0,8),r=fo((n=fr(new o(this),e+u8(this)+1,t)).abs(),!1,e+u8(n)+1),this.isneg()&&!this.isZero()?"-"+r:r)},u1.toInteger=u1.toint=function(){var e=this.constructor;return fr(new e(this),u8(this)+1,e.rounding)},u1.toNumber=function(){return+this},u1.toPower=u1.pow=function(e){var t,r,n,o,i,a,l=this,s=l.constructor,c=+(e=new s(e));if(!e.s)return new s(uL);if(!(l=new s(l)).s){if(e.s<1)throw Error(uX+"Infinity");return l}if(l.eq(uL))return l;if(n=s.precision,e.eq(uL))return fr(l,n);if(a=(t=e.e)>=(r=e.d.length-1),i=l.s,a){if((r=c<0?-c:c)<=0x1fffffffffffff){for(o=new s(uL),t=Math.ceil(n/7+4),uG=!1;r%2&&fi((o=o.times(l)).d,t),0!==(r=uZ(r/2));)fi((l=l.times(l)).d,t);return uG=!0,e.s<0?new s(uL).div(o):fr(o,n)}}else if(i<0)throw Error(uX+"NaN");return i=i<0&&1&e.d[Math.max(t,r)]?-1:1,l.s=1,uG=!1,o=e.times(fe(l,n+12)),uG=!0,(o=u6(o)).s=i,o},u1.toPrecision=function(e,t){var r,n,o=this,i=o.constructor;return void 0===e?(r=u8(o),n=fo(o,r<=i.toExpNeg||r>=i.toExpPos)):(u5(e,1,1e9),void 0===t?t=i.rounding:u5(t,0,8),r=u8(o=fr(new i(o),e,t)),n=fo(o,e<=r||r<=i.toExpNeg,e)),n},u1.toSignificantDigits=u1.tosd=function(e,t){var r=this.constructor;return void 0===e?(e=r.precision,t=r.rounding):(u5(e,1,1e9),void 0===t?t=r.rounding:u5(t,0,8)),fr(new r(this),e,t)},u1.toString=u1.valueOf=u1.val=u1.toJSON=u1[Symbol.for("nodejs.util.inspect.custom")]=function(){var e=u8(this),t=this.constructor;return fo(this,e<=t.toExpNeg||e>=t.toExpPos)};var u3=function(){function e(e,t){var r,n=0,o=e.length;for(e=e.slice();o--;)r=e[o]*t+n,e[o]=r%1e7|0,n=r/1e7|0;return n&&e.unshift(n),e}function t(e,t,r,n){var o,i;if(r!=n)i=r>n?1:-1;else for(o=i=0;o<r;o++)if(e[o]!=t[o]){i=e[o]>t[o]?1:-1;break}return i}function r(e,t,r){for(var n=0;r--;)e[r]-=n,n=+(e[r]<t[r]),e[r]=1e7*n+e[r]-t[r];for(;!e[0]&&e.length>1;)e.shift()}return function(n,o,i,a){var l,s,c,u,f,d,p,h,y,m,v,g,b,x,w,j,O,S,A=n.constructor,P=n.s==o.s?1:-1,k=n.d,E=o.d;if(!n.s)return new A(n);if(!o.s)throw Error(uX+"Division by zero");for(c=0,s=n.e-o.e,O=E.length,w=k.length,h=(p=new A(P)).d=[];E[c]==(k[c]||0);)++c;if(E[c]>(k[c]||0)&&--s,(g=null==i?i=A.precision:a?i+(u8(n)-u8(o))+1:i)<0)return new A(0);if(g=g/7+2|0,c=0,1==O)for(u=0,E=E[0],g++;(c<w||u)&&g--;c++)b=1e7*u+(k[c]||0),h[c]=b/E|0,u=b%E|0;else{for((u=1e7/(E[0]+1)|0)>1&&(E=e(E,u),k=e(k,u),O=E.length,w=k.length),x=O,m=(y=k.slice(0,O)).length;m<O;)y[m++]=0;(S=E.slice()).unshift(0),j=E[0],E[1]>=1e7/2&&++j;do u=0,(l=t(E,y,O,m))<0?(v=y[0],O!=m&&(v=1e7*v+(y[1]||0)),(u=v/j|0)>1?(u>=1e7&&(u=1e7-1),d=(f=e(E,u)).length,m=y.length,1==(l=t(f,y,d,m))&&(u--,r(f,O<d?S:E,d))):(0==u&&(l=u=1),f=E.slice()),(d=f.length)<m&&f.unshift(0),r(y,f,m),-1==l&&(m=y.length,(l=t(E,y,O,m))<1&&(u++,r(y,O<m?S:E,m))),m=y.length):0===l&&(u++,y=[0]),h[c++]=u,l&&y[0]?y[m++]=k[x]||0:(y=[k[x]],m=1);while((x++<w||void 0!==y[0])&&g--)}return h[0]||h.shift(),p.e=s,fr(p,a?i+u8(p)+1:i)}}();function u6(e,t){var r,n,o,i,a,l=0,s=0,c=e.constructor,u=c.precision;if(u8(e)>16)throw Error(uK+u8(e));if(!e.s)return new c(uL);for(null==t?(uG=!1,a=u):a=t,i=new c(.03125);e.abs().gte(.1);)e=e.times(i),s+=5;for(a+=Math.log(uJ(2,s))/Math.LN10*2+5|0,r=n=o=new c(uL),c.precision=a;;){if(n=fr(n.times(e),a),r=r.times(++l),u4((i=o.plus(u3(n,r,a))).d).slice(0,a)===u4(o.d).slice(0,a)){for(;s--;)o=fr(o.times(o),a);return c.precision=u,null==t?(uG=!0,fr(o,u)):o}o=i}}function u8(e){for(var t=7*e.e,r=e.d[0];r>=10;r/=10)t++;return t}function u7(e,t,r){if(t>e.LN10.sd())throw uG=!0,r&&(e.precision=r),Error(uX+"LN10 precision limit exceeded");return fr(new e(e.LN10),t)}function u9(e){for(var t="";e--;)t+="0";return t}function fe(e,t){var r,n,o,i,a,l,s,c,u,f=1,d=e,p=d.d,h=d.constructor,y=h.precision;if(d.s<1)throw Error(uX+(d.s?"NaN":"-Infinity"));if(d.eq(uL))return new h(0);if(null==t?(uG=!1,c=y):c=t,d.eq(10))return null==t&&(uG=!0),u7(h,c);if(h.precision=c+=10,n=(r=u4(p)).charAt(0),!(15e14>Math.abs(i=u8(d))))return s=u7(h,c+2,y).times(i+""),d=fe(new h(n+"."+r.slice(1)),c-10).plus(s),h.precision=y,null==t?(uG=!0,fr(d,y)):d;for(;n<7&&1!=n||1==n&&r.charAt(1)>3;)n=(r=u4((d=d.times(e)).d)).charAt(0),f++;for(i=u8(d),n>1?(d=new h("0."+r),i++):d=new h(n+"."+r.slice(1)),l=a=d=u3(d.minus(uL),d.plus(uL),c),u=fr(d.times(d),c),o=3;;){if(a=fr(a.times(u),c),u4((s=l.plus(u3(a,new h(o),c))).d).slice(0,c)===u4(l.d).slice(0,c))return l=l.times(2),0!==i&&(l=l.plus(u7(h,c+2,y).times(i+""))),l=u3(l,new h(f),c),h.precision=y,null==t?(uG=!0,fr(l,y)):l;l=s,o+=2}}function ft(e,t){var r,n,o;for((r=t.indexOf("."))>-1&&(t=t.replace(".","")),(n=t.search(/e/i))>0?(r<0&&(r=n),r+=+t.slice(n+1),t=t.substring(0,n)):r<0&&(r=t.length),n=0;48===t.charCodeAt(n);)++n;for(o=t.length;48===t.charCodeAt(o-1);)--o;if(t=t.slice(n,o)){if(o-=n,e.e=uZ((r=r-n-1)/7),e.d=[],n=(r+1)%7,r<0&&(n+=7),n<o){for(n&&e.d.push(+t.slice(0,n)),o-=7;n<o;)e.d.push(+t.slice(n,n+=7));n=7-(t=t.slice(n)).length}else n-=o;for(;n--;)t+="0";if(e.d.push(+t),uG&&(e.e>u0||e.e<-u0))throw Error(uK+r)}else e.s=0,e.e=0,e.d=[0];return e}function fr(e,t,r){var n,o,i,a,l,s,c,u,f=e.d;for(a=1,i=f[0];i>=10;i/=10)a++;if((n=t-a)<0)n+=7,o=t,c=f[u=0];else{if((u=Math.ceil((n+1)/7))>=(i=f.length))return e;for(a=1,c=i=f[u];i>=10;i/=10)a++;n%=7,o=n-7+a}if(void 0!==r&&(l=c/(i=uJ(10,a-o-1))%10|0,s=t<0||void 0!==f[u+1]||c%i,s=r<4?(l||s)&&(0==r||r==(e.s<0?3:2)):l>5||5==l&&(4==r||s||6==r&&(n>0?o>0?c/uJ(10,a-o):0:f[u-1])%10&1||r==(e.s<0?8:7))),t<1||!f[0])return s?(i=u8(e),f.length=1,t=t-i-1,f[0]=uJ(10,(7-t%7)%7),e.e=uZ(-t/7)||0):(f.length=1,f[0]=e.e=e.s=0),e;if(0==n?(f.length=u,i=1,u--):(f.length=u+1,i=uJ(10,7-n),f[u]=o>0?(c/uJ(10,a-o)%uJ(10,o)|0)*i:0),s)for(;;){if(0==u){1e7==(f[0]+=i)&&(f[0]=1,++e.e);break}if(f[u]+=i,1e7!=f[u])break;f[u--]=0,i=1}for(n=f.length;0===f[--n];)f.pop();if(uG&&(e.e>u0||e.e<-u0))throw Error(uK+u8(e));return e}function fn(e,t){var r,n,o,i,a,l,s,c,u,f,d=e.constructor,p=d.precision;if(!e.s||!t.s)return t.s?t.s=-t.s:t=new d(e),uG?fr(t,p):t;if(s=e.d,f=t.d,n=t.e,c=e.e,s=s.slice(),a=c-n){for((u=a<0)?(r=s,a=-a,l=f.length):(r=f,n=c,l=s.length),a>(o=Math.max(Math.ceil(p/7),l)+2)&&(a=o,r.length=1),r.reverse(),o=a;o--;)r.push(0);r.reverse()}else{for((u=(o=s.length)<(l=f.length))&&(l=o),o=0;o<l;o++)if(s[o]!=f[o]){u=s[o]<f[o];break}a=0}for(u&&(r=s,s=f,f=r,t.s=-t.s),l=s.length,o=f.length-l;o>0;--o)s[l++]=0;for(o=f.length;o>a;){if(s[--o]<f[o]){for(i=o;i&&0===s[--i];)s[i]=1e7-1;--s[i],s[o]+=1e7}s[o]-=f[o]}for(;0===s[--l];)s.pop();for(;0===s[0];s.shift())--n;return s[0]?(t.d=s,t.e=n,uG?fr(t,p):t):new d(0)}function fo(e,t,r){var n,o=u8(e),i=u4(e.d),a=i.length;return t?(r&&(n=r-a)>0?i=i.charAt(0)+"."+i.slice(1)+u9(n):a>1&&(i=i.charAt(0)+"."+i.slice(1)),i=i+(o<0?"e":"e+")+o):o<0?(i="0."+u9(-o-1)+i,r&&(n=r-a)>0&&(i+=u9(n))):o>=a?(i+=u9(o+1-a),r&&(n=r-o-1)>0&&(i=i+"."+u9(n))):((n=o+1)<a&&(i=i.slice(0,n)+"."+i.slice(n)),r&&(n=r-a)>0&&(o+1===a&&(i+="."),i+=u9(n))),e.s<0?"-"+i:i}function fi(e,t){if(e.length>t)return e.length=t,!0}function fa(e){if(!e||"object"!=typeof e)throw Error(uX+"Object expected");var t,r,n,o=["precision",1,1e9,"rounding",0,8,"toExpNeg",-1/0,0,"toExpPos",0,1/0];for(t=0;t<o.length;t+=3)if(void 0!==(n=e[r=o[t]])){if(uZ(n)===n&&n>=o[t+1]&&n<=o[t+2])this[r]=n;else throw Error(uY+r+": "+n)}if(void 0!==(n=e[r="LN10"])){if(n==Math.LN10)this[r]=new this(n);else throw Error(uY+r+": "+n)}return this}var uB=function e(t){var r,n,o;function i(e){if(!(this instanceof i))return new i(e);if(this.constructor=i,e instanceof i){this.s=e.s,this.e=e.e,this.d=(e=e.d)?e.slice():e;return}if("number"==typeof e){if(0*e!=0)throw Error(uY+e);if(e>0)this.s=1;else if(e<0)e=-e,this.s=-1;else{this.s=0,this.e=0,this.d=[0];return}if(e===~~e&&e<1e7){this.e=0,this.d=[e];return}return ft(this,e.toString())}if("string"!=typeof e)throw Error(uY+e);if(45===e.charCodeAt(0)?(e=e.slice(1),this.s=-1):this.s=1,uQ.test(e))ft(this,e);else throw Error(uY+e)}if(i.prototype=u1,i.ROUND_UP=0,i.ROUND_DOWN=1,i.ROUND_CEIL=2,i.ROUND_FLOOR=3,i.ROUND_HALF_UP=4,i.ROUND_HALF_DOWN=5,i.ROUND_HALF_EVEN=6,i.ROUND_HALF_CEIL=7,i.ROUND_HALF_FLOOR=8,i.clone=e,i.config=i.set=fa,void 0===t&&(t={}),t)for(r=0,o=["precision","rounding","toExpNeg","toExpPos","LN10"];r<o.length;)t.hasOwnProperty(n=o[r++])||(t[n]=this[n]);return i.config(t),i}({precision:20,rounding:4,toExpNeg:-7,toExpPos:21,LN10:"2.302585092994045684017991454684364207601101488628772976033327900967572609677352480235997205089598298341967784042286"});uL=new uB(1);let fl=uB;function fs(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}var fc=function(e){return e},fu={},ff=function(e){return e===fu},fd=function(e){return function t(){return 0==arguments.length||1==arguments.length&&ff(arguments.length<=0?void 0:arguments[0])?t:e.apply(void 0,arguments)}},fp=function(e){return function e(t,r){return 1===t?r:fd(function(){for(var n=arguments.length,o=Array(n),i=0;i<n;i++)o[i]=arguments[i];var a=o.filter(function(e){return e!==fu}).length;return a>=t?r.apply(void 0,o):e(t-a,fd(function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];var i=o.map(function(e){return ff(e)?t.shift():e});return r.apply(void 0,((function(e){if(Array.isArray(e))return fs(e)})(i)||function(e){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(e))return Array.from(e)}(i)||function(e,t){if(e){if("string"==typeof e)return fs(e,void 0);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return fs(e,t)}}(i)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()).concat(t))}))})}(e.length,e)},fh=function(e,t){for(var r=[],n=e;n<t;++n)r[n-e]=n;return r},fy=fp(function(e,t){return Array.isArray(t)?t.map(e):Object.keys(t).map(function(e){return t[e]}).map(e)}),fm=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];if(!t.length)return fc;var n=t.reverse(),o=n[0],i=n.slice(1);return function(){return i.reduce(function(e,t){return t(e)},o.apply(void 0,arguments))}},fv=function(e){return Array.isArray(e)?e.reverse():e.split("").reverse.join("")},fg=function(e){var t=null,r=null;return function(){for(var n=arguments.length,o=Array(n),i=0;i<n;i++)o[i]=arguments[i];return t&&o.every(function(e,r){return e===t[r]})?r:(t=o,r=e.apply(void 0,o))}};fp(function(e,t,r){var n=+e;return n+r*(+t-n)}),fp(function(e,t,r){var n=t-+e;return(r-e)/(n=n||1/0)}),fp(function(e,t,r){var n=t-+e;return Math.max(0,Math.min(1,(r-e)/(n=n||1/0)))});let fb={rangeStep:function(e,t,r){for(var n=new fl(e),o=0,i=[];n.lt(t)&&o<1e5;)i.push(n.toNumber()),n=n.add(r),o++;return i},getDigitCount:function(e){var t;return 0===e?1:Math.floor(new fl(e).abs().log(10).toNumber())+1}};function fx(e){return function(e){if(Array.isArray(e))return fO(e)}(e)||function(e){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(e))return Array.from(e)}(e)||fj(e)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function fw(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(e)){var r=[],n=!0,o=!1,i=void 0;try{for(var a,l=e[Symbol.iterator]();!(n=(a=l.next()).done)&&(r.push(a.value),!t||r.length!==t);n=!0);}catch(e){o=!0,i=e}finally{try{n||null==l.return||l.return()}finally{if(o)throw i}}return r}}(e,t)||fj(e,t)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function fj(e,t){if(e){if("string"==typeof e)return fO(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return fO(e,t)}}function fO(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function fS(e){var t=fw(e,2),r=t[0],n=t[1],o=r,i=n;return r>n&&(o=n,i=r),[o,i]}function fA(e,t,r){if(e.lte(0))return new fl(0);var n=fb.getDigitCount(e.toNumber()),o=new fl(10).pow(n),i=e.div(o),a=1!==n?.05:.1,l=new fl(Math.ceil(i.div(a).toNumber())).add(r).mul(a).mul(o);return t?l:new fl(Math.ceil(l))}function fP(e,t,r){var n=1,o=new fl(e);if(!o.isint()&&r){var i=Math.abs(e);i<1?(n=new fl(10).pow(fb.getDigitCount(e)-1),o=new fl(Math.floor(o.div(n).toNumber())).mul(n)):i>1&&(o=new fl(Math.floor(e)))}else 0===e?o=new fl(Math.floor((t-1)/2)):r||(o=new fl(Math.floor(e)));var a=Math.floor((t-1)/2);return fm(fy(function(e){return o.add(new fl(e-a).mul(n)).toNumber()}),fh)(0,t)}var fk=fg(function(e){var t=fw(e,2),r=t[0],n=t[1],o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:6,i=!(arguments.length>2)||void 0===arguments[2]||arguments[2],a=Math.max(o,2),l=fw(fS([r,n]),2),s=l[0],c=l[1];if(s===-1/0||c===1/0){var u=c===1/0?[s].concat(fx(fh(0,o-1).map(function(){return 1/0}))):[].concat(fx(fh(0,o-1).map(function(){return-1/0})),[c]);return r>n?fv(u):u}if(s===c)return fP(s,o,i);var f=function e(t,r,n,o){var i,a=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0;if(!Number.isFinite((r-t)/(n-1)))return{step:new fl(0),tickMin:new fl(0),tickMax:new fl(0)};var l=fA(new fl(r).sub(t).div(n-1),o,a),s=Math.ceil((i=t<=0&&r>=0?new fl(0):(i=new fl(t).add(r).div(2)).sub(new fl(i).mod(l))).sub(t).div(l).toNumber()),c=Math.ceil(new fl(r).sub(i).div(l).toNumber()),u=s+c+1;return u>n?e(t,r,n,o,a+1):(u<n&&(c=r>0?c+(n-u):c,s=r>0?s:s+(n-u)),{step:l,tickMin:i.sub(new fl(s).mul(l)),tickMax:i.add(new fl(c).mul(l))})}(s,c,a,i),d=f.step,p=f.tickMin,h=f.tickMax,y=fb.rangeStep(p,h.add(new fl(.1).mul(d)),d);return r>n?fv(y):y});fg(function(e){var t=fw(e,2),r=t[0],n=t[1],o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:6,i=!(arguments.length>2)||void 0===arguments[2]||arguments[2],a=Math.max(o,2),l=fw(fS([r,n]),2),s=l[0],c=l[1];if(s===-1/0||c===1/0)return[r,n];if(s===c)return fP(s,o,i);var u=fA(new fl(c).sub(s).div(a-1),i,0),f=fm(fy(function(e){return new fl(s).add(new fl(e).mul(u)).toNumber()}),fh)(0,a).filter(function(e){return e>=s&&e<=c});return r>n?fv(f):f});var fE=fg(function(e,t){var r=fw(e,2),n=r[0],o=r[1],i=!(arguments.length>2)||void 0===arguments[2]||arguments[2],a=fw(fS([n,o]),2),l=a[0],s=a[1];if(l===-1/0||s===1/0)return[n,o];if(l===s)return[l];var c=Math.max(t,2),u=fA(new fl(s).sub(l).div(c-1),i,0),f=[].concat(fx(fb.rangeStep(new fl(l),new fl(s).sub(new fl(.99).mul(u)),u)),[s]);return n>o?fv(f):f}),fN=["offset","layout","width","dataKey","data","dataPointFormatter","xAxis","yAxis"];function fM(e){return(fM="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function fC(){return(fC=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function fT(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function f_(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(f_=function(){return!!e})()}function fI(e){return(fI=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function fD(e,t){return(fD=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function fR(e,t,r){return(t=fB(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function fB(e){var t=function(e,t){if("object"!=fM(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=fM(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==fM(t)?t:t+""}var fL=function(e){var t;function r(){var e,t;return function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}(this,r),e=r,t=arguments,e=fI(e),function(e,t){if(t&&("object"===fM(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,f_()?Reflect.construct(e,t||[],fI(this).constructor):e.apply(this,t))}return function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&fD(e,t)}(r,e),t=[{key:"render",value:function(){var e=this.props,t=e.offset,r=e.layout,n=e.width,o=e.dataKey,i=e.data,l=e.dataPointFormatter,s=e.xAxis,c=e.yAxis,u=nt(function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(e,fN),!1);"x"===this.props.direction&&"number"!==s.type&&oZ(!1);var f=i.map(function(e){var i,f,d=l(e,o),p=d.x,h=d.y,y=d.value,m=d.errorVal;if(!m)return null;var v=[];if(Array.isArray(m)){var g=function(e){if(Array.isArray(e))return e}(m)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,a,l=[],s=!0,c=!1;try{i=(r=r.call(e)).next;for(;!(s=(n=i.call(r)).done)&&(l.push(n.value),l.length!==t);s=!0);}catch(e){c=!0,o=e}finally{try{if(!s&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(c)throw o}}return l}}(m,2)||function(e,t){if(e){if("string"==typeof e)return fT(e,2);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return fT(e,t)}}(m,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}();i=g[0],f=g[1]}else i=f=m;if("vertical"===r){var b=s.scale,x=h+t,w=x+n,j=x-n,O=b(y-i),S=b(y+f);v.push({x1:S,y1:w,x2:S,y2:j}),v.push({x1:O,y1:x,x2:S,y2:x}),v.push({x1:O,y1:w,x2:O,y2:j})}else if("horizontal"===r){var A=c.scale,P=p+t,k=P-n,E=P+n,N=A(y-i),M=A(y+f);v.push({x1:k,y1:M,x2:E,y2:M}),v.push({x1:P,y1:N,x2:P,y2:M}),v.push({x1:k,y1:N,x2:E,y2:N})}return a().createElement(o0,fC({className:"recharts-errorBar",key:"bar-".concat(v.map(function(e){return"".concat(e.x1,"-").concat(e.x2,"-").concat(e.y1,"-").concat(e.y2)}))},u),v.map(function(e){return a().createElement("line",fC({},e,{key:"line-".concat(e.x1,"-").concat(e.x2,"-").concat(e.y1,"-").concat(e.y2)}))}))});return a().createElement(o0,{className:"recharts-errorBars"},f)}}],function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,fB(n.key),n)}}(r.prototype,t),Object.defineProperty(r,"prototype",{writable:!1}),r}(a().Component);function f$(e){return(f$="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function fz(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function fU(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?fz(Object(r),!0).forEach(function(t){var n,o,i;n=e,o=t,i=r[t],(o=function(e){var t=function(e,t){if("object"!=f$(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=f$(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==f$(t)?t:t+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):fz(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}fR(fL,"defaultProps",{stroke:"black",strokeWidth:1.5,width:5,offset:0,layout:"horizontal"}),fR(fL,"displayName","ErrorBar");var fF=function(e){var t,r=e.children,n=e.formattedGraphicalItems,o=e.legendWidth,i=e.legendContent,a=r8(r,oz);if(!a)return null;var l=oz.defaultProps,s=void 0!==l?fU(fU({},l),a.props):{};return t=a.props&&a.props.payload?a.props&&a.props.payload:"children"===i?(n||[]).reduce(function(e,t){var r=t.item,n=t.props,o=n.sectors||n.data||[];return e.concat(o.map(function(e){return{type:a.props.iconType||r.props.legendType,value:e.name,color:e.fill,payload:e}}))},[]):(n||[]).map(function(e){var t=e.item,r=t.type.defaultProps,n=void 0!==r?fU(fU({},r),t.props):{},o=n.dataKey,i=n.name,a=n.legendType;return{inactive:n.hide,dataKey:o,type:s.iconType||a||"square",color:fJ(t),value:i||o,payload:n}}),fU(fU(fU({},s),oz.getWithHeight(a,o)),{},{payload:t,item:a})};function fq(e){return(fq="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function fW(e){return function(e){if(Array.isArray(e))return fV(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return fV(e,void 0);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return fV(e,t)}}(e)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function fV(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function fH(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function fG(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?fH(Object(r),!0).forEach(function(t){fX(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):fH(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function fX(e,t,r){var n;return(n=function(e,t){if("object"!=fq(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=fq(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"),(t="symbol"==fq(n)?n:n+"")in e)?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function fY(e,t,r){return rL()(e)||rL()(t)?r:rE(t)?rj()(e,t,r):rz()(t)?t(e):r}function fK(e,t,r,n){var o=uW()(e,function(e){return fY(e,t)});if("number"===r){var i=o.filter(function(e){return rk(e)||parseFloat(e)});return i.length?[uF()(i),uz()(i)]:[1/0,-1/0]}return(n?o.filter(function(e){return!rL()(e)}):o).map(function(e){return rE(e)||e instanceof Date?e:""})}var fZ=function(e){var t,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=arguments.length>2?arguments[2]:void 0,o=arguments.length>3?arguments[3]:void 0,i=-1,a=null!==(t=null==r?void 0:r.length)&&void 0!==t?t:0;if(a<=1)return 0;if(o&&"angleAxis"===o.axisType&&1e-6>=Math.abs(Math.abs(o.range[1]-o.range[0])-360))for(var l=o.range,s=0;s<a;s++){var c=s>0?n[s-1].coordinate:n[a-1].coordinate,u=n[s].coordinate,f=s>=a-1?n[0].coordinate:n[s+1].coordinate,d=void 0;if(rA(u-c)!==rA(f-u)){var p=[];if(rA(f-u)===rA(l[1]-l[0])){d=f;var h=u+l[1]-l[0];p[0]=Math.min(h,(h+c)/2),p[1]=Math.max(h,(h+c)/2)}else{d=c;var y=f+l[1]-l[0];p[0]=Math.min(u,(y+u)/2),p[1]=Math.max(u,(y+u)/2)}var m=[Math.min(u,(d+u)/2),Math.max(u,(d+u)/2)];if(e>m[0]&&e<=m[1]||e>=p[0]&&e<=p[1]){i=n[s].index;break}}else{var v=Math.min(c,f),g=Math.max(c,f);if(e>(v+u)/2&&e<=(g+u)/2){i=n[s].index;break}}}else for(var b=0;b<a;b++)if(0===b&&e<=(r[b].coordinate+r[b+1].coordinate)/2||b>0&&b<a-1&&e>(r[b].coordinate+r[b-1].coordinate)/2&&e<=(r[b].coordinate+r[b+1].coordinate)/2||b===a-1&&e>(r[b].coordinate+r[b-1].coordinate)/2){i=r[b].index;break}return i},fJ=function(e){var t,r,n=e.type.displayName,o=null!==(t=e.type)&&void 0!==t&&t.defaultProps?fG(fG({},e.type.defaultProps),e.props):e.props,i=o.stroke,a=o.fill;switch(n){case"Line":r=i;break;case"Area":case"Radar":r=i&&"none"!==i?i:a;break;default:r=a}return r},fQ=function(e){var t=e.barSize,r=e.totalSize,n=e.stackGroups,o=void 0===n?{}:n;if(!o)return{};for(var i={},a=Object.keys(o),l=0,s=a.length;l<s;l++)for(var c=o[a[l]].stackGroups,u=Object.keys(c),f=0,d=u.length;f<d;f++){var p=c[u[f]],h=p.items,y=p.cateAxisId,m=h.filter(function(e){return r2(e.type).indexOf("Bar")>=0});if(m&&m.length){var v=m[0].type.defaultProps,g=void 0!==v?fG(fG({},v),m[0].props):m[0].props,b=g.barSize,x=g[y];i[x]||(i[x]=[]);var w=rL()(b)?t:b;i[x].push({item:m[0],stackList:m.slice(1),barSize:rL()(w)?void 0:rC(w,r,0)})}}return i},f0=function(e){var t,r=e.barGap,n=e.barCategoryGap,o=e.bandSize,i=e.sizeList,a=void 0===i?[]:i,l=e.maxBarSize,s=a.length;if(s<1)return null;var c=rC(r,o,0,!0),u=[];if(a[0].barSize===+a[0].barSize){var f=!1,d=o/s,p=a.reduce(function(e,t){return e+t.barSize||0},0);(p+=(s-1)*c)>=o&&(p-=(s-1)*c,c=0),p>=o&&d>0&&(f=!0,d*=.9,p=s*d);var h={offset:((o-p)/2>>0)-c,size:0};t=a.reduce(function(e,t){var r={item:t.item,position:{offset:h.offset+h.size+c,size:f?d:t.barSize}},n=[].concat(fW(e),[r]);return h=n[n.length-1].position,t.stackList&&t.stackList.length&&t.stackList.forEach(function(e){n.push({item:e,position:h})}),n},u)}else{var y=rC(n,o,0,!0);o-2*y-(s-1)*c<=0&&(c=0);var m=(o-2*y-(s-1)*c)/s;m>1&&(m>>=0);var v=l===+l?Math.min(m,l):m;t=a.reduce(function(e,t,r){var n=[].concat(fW(e),[{item:t.item,position:{offset:y+(m+c)*r+(m-v)/2,size:v}}]);return t.stackList&&t.stackList.length&&t.stackList.forEach(function(e){n.push({item:e,position:n[n.length-1].position})}),n},u)}return t},f1=function(e,t,r,n){var o=r.children,i=r.width,a=r.margin,l=fF({children:o,legendWidth:i-(a.left||0)-(a.right||0)});if(l){var s=n||{},c=s.width,u=s.height,f=l.align,d=l.verticalAlign,p=l.layout;if(("vertical"===p||"horizontal"===p&&"middle"===d)&&"center"!==f&&rk(e[f]))return fG(fG({},e),{},fX({},f,e[f]+(c||0)));if(("horizontal"===p||"vertical"===p&&"center"===f)&&"middle"!==d&&rk(e[d]))return fG(fG({},e),{},fX({},d,e[d]+(u||0)))}return e},f2=function(e,t,r,n,o){var i=r6(t.props.children,fL).filter(function(e){var t;return t=e.props.direction,!!rL()(o)||("horizontal"===n?"yAxis"===o:"vertical"===n||"x"===t?"xAxis"===o:"y"!==t||"yAxis"===o)});if(i&&i.length){var a=i.map(function(e){return e.props.dataKey});return e.reduce(function(e,t){var n=fY(t,r);if(rL()(n))return e;var o=Array.isArray(n)?[uF()(n),uz()(n)]:[n,n],i=a.reduce(function(e,r){var n=fY(t,r,0),i=o[0]-Math.abs(Array.isArray(n)?n[0]:n),a=o[1]+Math.abs(Array.isArray(n)?n[1]:n);return[Math.min(i,e[0]),Math.max(a,e[1])]},[1/0,-1/0]);return[Math.min(i[0],e[0]),Math.max(i[1],e[1])]},[1/0,-1/0])}return null},f5=function(e,t,r,n,o){var i=t.map(function(t){return f2(e,t,r,o,n)}).filter(function(e){return!rL()(e)});return i&&i.length?i.reduce(function(e,t){return[Math.min(e[0],t[0]),Math.max(e[1],t[1])]},[1/0,-1/0]):null},f4=function(e,t,r,n,o){var i=t.map(function(t){var i=t.props.dataKey;return"number"===r&&i&&f2(e,t,i,n)||fK(e,i,r,o)});if("number"===r)return i.reduce(function(e,t){return[Math.min(e[0],t[0]),Math.max(e[1],t[1])]},[1/0,-1/0]);var a={};return i.reduce(function(e,t){for(var r=0,n=t.length;r<n;r++)a[t[r]]||(a[t[r]]=!0,e.push(t[r]));return e},[])},f3=function(e,t){return"horizontal"===e&&"xAxis"===t||"vertical"===e&&"yAxis"===t||"centric"===e&&"angleAxis"===t||"radial"===e&&"radiusAxis"===t},f6=function(e,t,r){if(!e)return null;var n=e.scale,o=e.duplicateDomain,i=e.type,a=e.range,l="scaleBand"===e.realScaleType?n.bandwidth()/2:2,s=(t||r)&&"category"===i&&n.bandwidth?n.bandwidth()/l:0;return(s="angleAxis"===e.axisType&&(null==a?void 0:a.length)>=2?2*rA(a[0]-a[1])*s:s,t&&(e.ticks||e.niceTicks))?(e.ticks||e.niceTicks).map(function(e){return{coordinate:n(o?o.indexOf(e):e)+s,value:e,offset:s}}).filter(function(e){return!rx()(e.coordinate)}):e.isCategorical&&e.categoricalDomain?e.categoricalDomain.map(function(e,t){return{coordinate:n(e)+s,value:e,index:t,offset:s}}):n.ticks&&!r?n.ticks(e.tickCount).map(function(e){return{coordinate:n(e)+s,value:e,offset:s}}):n.domain().map(function(e,t){return{coordinate:n(e)+s,value:o?o[e]:e,index:t,offset:s}})},f8=new WeakMap,f7=function(e,t){if("function"!=typeof t)return e;f8.has(e)||f8.set(e,new WeakMap);var r=f8.get(e);if(r.has(t))return r.get(t);var n=function(){e.apply(void 0,arguments),t.apply(void 0,arguments)};return r.set(t,n),n},f9=function(e,t,r){var o=e.scale,i=e.type,a=e.layout,l=e.axisType;if("auto"===o)return"radial"===a&&"radiusAxis"===l?{scale:aw(),realScaleType:"band"}:"radial"===a&&"angleAxis"===l?{scale:sl(),realScaleType:"linear"}:"category"===i&&t&&(t.indexOf("LineChart")>=0||t.indexOf("AreaChart")>=0||t.indexOf("ComposedChart")>=0&&!r)?{scale:aj(),realScaleType:"point"}:"category"===i?{scale:aw(),realScaleType:"band"}:{scale:sl(),realScaleType:"linear"};if(rg()(o)){var s="scale".concat(nJ()(o));return{scale:(n[s]||aj)(),realScaleType:n[s]?s:"point"}}return rz()(o)?{scale:o}:{scale:aj(),realScaleType:"point"}},de=function(e){var t=e.domain();if(t&&!(t.length<=2)){var r=t.length,n=e.range(),o=Math.min(n[0],n[1])-1e-4,i=Math.max(n[0],n[1])+1e-4,a=e(t[0]),l=e(t[r-1]);(a<o||a>i||l<o||l>i)&&e.domain([t[0],t[r-1]])}},dt={sign:function(e){var t=e.length;if(!(t<=0))for(var r=0,n=e[0].length;r<n;++r)for(var o=0,i=0,a=0;a<t;++a){var l=rx()(e[a][r][1])?e[a][r][0]:e[a][r][1];l>=0?(e[a][r][0]=o,e[a][r][1]=o+l,o=e[a][r][1]):(e[a][r][0]=i,e[a][r][1]=i+l,i=e[a][r][1])}},expand:function(e,t){if((n=e.length)>0){for(var r,n,o,i=0,a=e[0].length;i<a;++i){for(o=r=0;r<n;++r)o+=e[r][i][1]||0;if(o)for(r=0;r<n;++r)e[r][i][1]/=o}uA(e,t)}},none:uA,silhouette:function(e,t){if((r=e.length)>0){for(var r,n=0,o=e[t[0]],i=o.length;n<i;++n){for(var a=0,l=0;a<r;++a)l+=e[a][n][1]||0;o[n][1]+=o[n][0]=-l/2}uA(e,t)}},wiggle:function(e,t){if((o=e.length)>0&&(n=(r=e[t[0]]).length)>0){for(var r,n,o,i=0,a=1;a<n;++a){for(var l=0,s=0,c=0;l<o;++l){for(var u=e[t[l]],f=u[a][1]||0,d=(f-(u[a-1][1]||0))/2,p=0;p<l;++p){var h=e[t[p]];d+=(h[a][1]||0)-(h[a-1][1]||0)}s+=f,c+=d*f}r[a-1][1]+=r[a-1][0]=i,s&&(i-=c/s)}r[a-1][1]+=r[a-1][0]=i,uA(e,t)}},positive:function(e){var t=e.length;if(!(t<=0))for(var r=0,n=e[0].length;r<n;++r)for(var o=0,i=0;i<t;++i){var a=rx()(e[i][r][1])?e[i][r][0]:e[i][r][1];a>=0?(e[i][r][0]=o,e[i][r][1]=o+a,o=e[i][r][1]):(e[i][r][0]=0,e[i][r][1]=0)}}},dr=function(e,t,r){var n=t.map(function(e){return e.props.dataKey}),o=dt[r];return(function(){var e=oo([]),t=uk,r=uA,n=uE;function o(o){var i,a,l=Array.from(e.apply(this,arguments),uN),s=l.length,c=-1;for(let e of o)for(i=0,++c;i<s;++i)(l[i][c]=[0,+n(e,l[i].key,c,o)]).data=e;for(i=0,a=uP(t(l));i<s;++i)l[a[i]].index=i;return r(l,a),l}return o.keys=function(t){return arguments.length?(e="function"==typeof t?t:oo(Array.from(t)),o):e},o.value=function(e){return arguments.length?(n="function"==typeof e?e:oo(+e),o):n},o.order=function(e){return arguments.length?(t=null==e?uk:"function"==typeof e?e:oo(Array.from(e)),o):t},o.offset=function(e){return arguments.length?(r=null==e?uA:e,o):r},o})().keys(n).value(function(e,t){return+fY(e,t,0)}).order(uk).offset(o)(e)},dn=function(e,t,r,n,o,i){if(!e)return null;var a=(i?t.reverse():t).reduce(function(e,t){var o,i=null!==(o=t.type)&&void 0!==o&&o.defaultProps?fG(fG({},t.type.defaultProps),t.props):t.props,a=i.stackId;if(i.hide)return e;var l=i[r],s=e[l]||{hasStack:!1,stackGroups:{}};if(rE(a)){var c=s.stackGroups[a]||{numericAxisId:r,cateAxisId:n,items:[]};c.items.push(t),s.hasStack=!0,s.stackGroups[a]=c}else s.stackGroups[rM("_stackId_")]={numericAxisId:r,cateAxisId:n,items:[t]};return fG(fG({},e),{},fX({},l,s))},{});return Object.keys(a).reduce(function(t,i){var l=a[i];return l.hasStack&&(l.stackGroups=Object.keys(l.stackGroups).reduce(function(t,i){var a=l.stackGroups[i];return fG(fG({},t),{},fX({},i,{numericAxisId:r,cateAxisId:n,items:a.items,stackedData:dr(e,a.items,o)}))},{})),fG(fG({},t),{},fX({},i,l))},{})},di=function(e,t){var r=t.realScaleType,n=t.type,o=t.tickCount,i=t.originalDomain,a=t.allowDecimals,l=r||t.scale;if("auto"!==l&&"linear"!==l)return null;if(o&&"number"===n&&i&&("auto"===i[0]||"auto"===i[1])){var s=e.domain();if(!s.length)return null;var c=fk(s,o,a);return e.domain([uF()(c),uz()(c)]),{niceTicks:c}}return o&&"number"===n?{niceTicks:fE(e.domain(),o,a)}:null},da=function(e,t){var r,n=(null!==(r=e.type)&&void 0!==r&&r.defaultProps?fG(fG({},e.type.defaultProps),e.props):e.props).stackId;if(rE(n)){var o=t[n];if(o){var i=o.items.indexOf(e);return i>=0?o.stackedData[i]:null}}return null},dl=function(e,t,r){return Object.keys(e).reduce(function(n,o){var i=e[o].stackedData.reduce(function(e,n){var o=n.slice(t,r+1).reduce(function(e,t){return[uF()(t.concat([e[0]]).filter(rk)),uz()(t.concat([e[1]]).filter(rk))]},[1/0,-1/0]);return[Math.min(e[0],o[0]),Math.max(e[1],o[1])]},[1/0,-1/0]);return[Math.min(i[0],n[0]),Math.max(i[1],n[1])]},[1/0,-1/0]).map(function(e){return e===1/0||e===-1/0?0:e})},ds=/^dataMin[\s]*-[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,dc=/^dataMax[\s]*\+[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,du=function(e,t,r){if(rz()(e))return e(t,r);if(!Array.isArray(e))return t;var n=[];if(rk(e[0]))n[0]=r?e[0]:Math.min(e[0],t[0]);else if(ds.test(e[0])){var o=+ds.exec(e[0])[1];n[0]=t[0]-o}else rz()(e[0])?n[0]=e[0](t[0]):n[0]=t[0];if(rk(e[1]))n[1]=r?e[1]:Math.max(e[1],t[1]);else if(dc.test(e[1])){var i=+dc.exec(e[1])[1];n[1]=t[1]+i}else rz()(e[1])?n[1]=e[1](t[1]):n[1]=t[1];return n},df=function(e,t,r){if(e&&e.scale&&e.scale.bandwidth){var n=e.scale.bandwidth();if(!r||n>0)return n}if(e&&t&&t.length>=2){for(var o=nd()(t,function(e){return e.coordinate}),i=1/0,a=1,l=o.length;a<l;a++){var s=o[a],c=o[a-1];i=Math.min((s.coordinate||0)-(c.coordinate||0),i)}return i===1/0?0:i}return r?void 0:0},dd=function(e,t,r){return!e||!e.length||uH()(e,rj()(r,"type.defaultProps.domain"))?t:e},dp=function(e,t){var r=e.type.defaultProps?fG(fG({},e.type.defaultProps),e.props):e.props,n=r.dataKey,o=r.name,i=r.unit,a=r.formatter,l=r.tooltipType,s=r.chartType,c=r.hide;return fG(fG({},nt(e,!1)),{},{dataKey:n,unit:i,formatter:a,name:o||n,color:fJ(e),value:fY(t,n),type:l,payload:t,chartType:s,hide:c})};function dh(e){return(dh="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function dy(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function dm(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?dy(Object(r),!0).forEach(function(t){dv(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):dy(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function dv(e,t,r){var n;return(n=function(e,t){if("object"!=dh(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=dh(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"),(t="symbol"==dh(n)?n:n+"")in e)?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var dg=["Webkit","Moz","O","ms"],db=function(e,t){if(!e)return null;var r=e.replace(/(\w)/,function(e){return e.toUpperCase()}),n=dg.reduce(function(e,n){return dm(dm({},e),{},dv({},n+r,t))},{});return n[e]=t,n};function dx(e){return(dx="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function dw(){return(dw=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function dj(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function dO(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?dj(Object(r),!0).forEach(function(t){dE(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):dj(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function dS(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,dN(n.key),n)}}function dA(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(dA=function(){return!!e})()}function dP(e){return(dP=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function dk(e,t){return(dk=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function dE(e,t,r){return(t=dN(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function dN(e){var t=function(e,t){if("object"!=dx(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=dx(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==dx(t)?t:t+""}var dM=function(e){var t=e.data,r=e.startIndex,n=e.endIndex,o=e.x,i=e.width,a=e.travellerWidth;if(!t||!t.length)return{};var l=t.length,s=aj().domain(oK()(0,l)).range([o,o+i-a]),c=s.domain().map(function(e){return s(e)});return{isTextActive:!1,isSlideMoving:!1,isTravellerMoving:!1,isTravellerFocused:!1,startX:s(r),endX:s(n),scale:s,scaleValues:c}},dC=function(e){return e.changedTouches&&!!e.changedTouches.length},dT=function(e){var t,r;function n(e){var t,r,o;return function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}(this,n),r=n,o=[e],r=dP(r),dE(t=function(e,t){if(t&&("object"===dx(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,dA()?Reflect.construct(r,o||[],dP(this).constructor):r.apply(this,o)),"handleDrag",function(e){t.leaveTimer&&(clearTimeout(t.leaveTimer),t.leaveTimer=null),t.state.isTravellerMoving?t.handleTravellerMove(e):t.state.isSlideMoving&&t.handleSlideDrag(e)}),dE(t,"handleTouchMove",function(e){null!=e.changedTouches&&e.changedTouches.length>0&&t.handleDrag(e.changedTouches[0])}),dE(t,"handleDragEnd",function(){t.setState({isTravellerMoving:!1,isSlideMoving:!1},function(){var e=t.props,r=e.endIndex,n=e.onDragEnd,o=e.startIndex;null==n||n({endIndex:r,startIndex:o})}),t.detachDragEndListener()}),dE(t,"handleLeaveWrapper",function(){(t.state.isTravellerMoving||t.state.isSlideMoving)&&(t.leaveTimer=window.setTimeout(t.handleDragEnd,t.props.leaveTimeOut))}),dE(t,"handleEnterSlideOrTraveller",function(){t.setState({isTextActive:!0})}),dE(t,"handleLeaveSlideOrTraveller",function(){t.setState({isTextActive:!1})}),dE(t,"handleSlideDragStart",function(e){var r=dC(e)?e.changedTouches[0]:e;t.setState({isTravellerMoving:!1,isSlideMoving:!0,slideMoveStartX:r.pageX}),t.attachDragEndListener()}),t.travellerDragStartHandlers={startX:t.handleTravellerDragStart.bind(t,"startX"),endX:t.handleTravellerDragStart.bind(t,"endX")},t.state={},t}return function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&dk(e,t)}(n,e),t=[{key:"componentWillUnmount",value:function(){this.leaveTimer&&(clearTimeout(this.leaveTimer),this.leaveTimer=null),this.detachDragEndListener()}},{key:"getIndex",value:function(e){var t=e.startX,r=e.endX,o=this.state.scaleValues,i=this.props,a=i.gap,l=i.data.length-1,s=Math.min(t,r),c=Math.max(t,r),u=n.getIndexInRange(o,s),f=n.getIndexInRange(o,c);return{startIndex:u-u%a,endIndex:f===l?l:f-f%a}}},{key:"getTextOfTick",value:function(e){var t=this.props,r=t.data,n=t.tickFormatter,o=t.dataKey,i=fY(r[e],o,e);return rz()(n)?n(i,e):i}},{key:"attachDragEndListener",value:function(){window.addEventListener("mouseup",this.handleDragEnd,!0),window.addEventListener("touchend",this.handleDragEnd,!0),window.addEventListener("mousemove",this.handleDrag,!0)}},{key:"detachDragEndListener",value:function(){window.removeEventListener("mouseup",this.handleDragEnd,!0),window.removeEventListener("touchend",this.handleDragEnd,!0),window.removeEventListener("mousemove",this.handleDrag,!0)}},{key:"handleSlideDrag",value:function(e){var t=this.state,r=t.slideMoveStartX,n=t.startX,o=t.endX,i=this.props,a=i.x,l=i.width,s=i.travellerWidth,c=i.startIndex,u=i.endIndex,f=i.onChange,d=e.pageX-r;d>0?d=Math.min(d,a+l-s-o,a+l-s-n):d<0&&(d=Math.max(d,a-n,a-o));var p=this.getIndex({startX:n+d,endX:o+d});(p.startIndex!==c||p.endIndex!==u)&&f&&f(p),this.setState({startX:n+d,endX:o+d,slideMoveStartX:e.pageX})}},{key:"handleTravellerDragStart",value:function(e,t){var r=dC(t)?t.changedTouches[0]:t;this.setState({isSlideMoving:!1,isTravellerMoving:!0,movingTravellerId:e,brushMoveStartX:r.pageX}),this.attachDragEndListener()}},{key:"handleTravellerMove",value:function(e){var t=this.state,r=t.brushMoveStartX,n=t.movingTravellerId,o=t.endX,i=t.startX,a=this.state[n],l=this.props,s=l.x,c=l.width,u=l.travellerWidth,f=l.onChange,d=l.gap,p=l.data,h={startX:this.state.startX,endX:this.state.endX},y=e.pageX-r;y>0?y=Math.min(y,s+c-u-a):y<0&&(y=Math.max(y,s-a)),h[n]=a+y;var m=this.getIndex(h),v=m.startIndex,g=m.endIndex,b=function(){var e=p.length-1;return"startX"===n&&(o>i?v%d==0:g%d==0)||!!(o<i)&&g===e||"endX"===n&&(o>i?g%d==0:v%d==0)||!!(o>i)&&g===e};this.setState(dE(dE({},n,a+y),"brushMoveStartX",e.pageX),function(){f&&b()&&f(m)})}},{key:"handleTravellerMoveKeyboard",value:function(e,t){var r=this,n=this.state,o=n.scaleValues,i=n.startX,a=n.endX,l=this.state[t],s=o.indexOf(l);if(-1!==s){var c=s+e;if(-1!==c&&!(c>=o.length)){var u=o[c];("startX"!==t||!(u>=a))&&("endX"!==t||!(u<=i))&&this.setState(dE({},t,u),function(){r.props.onChange(r.getIndex({startX:r.state.startX,endX:r.state.endX}))})}}}},{key:"renderBackground",value:function(){var e=this.props,t=e.x,r=e.y,n=e.width,o=e.height,i=e.fill,l=e.stroke;return a().createElement("rect",{stroke:l,fill:i,x:t,y:r,width:n,height:o})}},{key:"renderPanorama",value:function(){var e=this.props,t=e.x,r=e.y,n=e.width,o=e.height,l=e.data,s=e.children,c=e.padding,u=i.Children.only(s);return u?a().cloneElement(u,{x:t,y:r,width:n,height:o,margin:c,compact:!0,data:l}):null}},{key:"renderTravellerLayer",value:function(e,t){var r,o,i=this,l=this.props,s=l.y,c=l.travellerWidth,u=l.height,f=l.traveller,d=l.ariaLabel,p=l.data,h=l.startIndex,y=l.endIndex,m=Math.max(e,this.props.x),v=dO(dO({},nt(this.props,!1)),{},{x:m,y:s,width:c,height:u}),g=d||"Min value: ".concat(null===(r=p[h])||void 0===r?void 0:r.name,", Max value: ").concat(null===(o=p[y])||void 0===o?void 0:o.name);return a().createElement(o0,{tabIndex:0,role:"slider","aria-label":g,"aria-valuenow":e,className:"recharts-brush-traveller",onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.travellerDragStartHandlers[t],onTouchStart:this.travellerDragStartHandlers[t],onKeyDown:function(e){["ArrowLeft","ArrowRight"].includes(e.key)&&(e.preventDefault(),e.stopPropagation(),i.handleTravellerMoveKeyboard("ArrowRight"===e.key?1:-1,t))},onFocus:function(){i.setState({isTravellerFocused:!0})},onBlur:function(){i.setState({isTravellerFocused:!1})},style:{cursor:"col-resize"}},n.renderTraveller(f,v))}},{key:"renderSlide",value:function(e,t){var r=this.props,n=r.y,o=r.height,i=r.stroke,l=r.travellerWidth,s=Math.min(e,t)+l,c=Math.max(Math.abs(t-e)-l,0);return a().createElement("rect",{className:"recharts-brush-slide",onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.handleSlideDragStart,onTouchStart:this.handleSlideDragStart,style:{cursor:"move"},stroke:"none",fill:i,fillOpacity:.2,x:s,y:n,width:c,height:o})}},{key:"renderText",value:function(){var e=this.props,t=e.startIndex,r=e.endIndex,n=e.y,o=e.height,i=e.travellerWidth,l=e.stroke,s=this.state,c=s.startX,u=s.endX,f={pointerEvents:"none",fill:l};return a().createElement(o0,{className:"recharts-brush-texts"},a().createElement(a5,dw({textAnchor:"end",verticalAnchor:"middle",x:Math.min(c,u)-5,y:n+o/2},f),this.getTextOfTick(t)),a().createElement(a5,dw({textAnchor:"start",verticalAnchor:"middle",x:Math.max(c,u)+i+5,y:n+o/2},f),this.getTextOfTick(r)))}},{key:"render",value:function(){var e=this.props,t=e.data,r=e.className,n=e.children,o=e.x,i=e.y,l=e.width,s=e.height,c=e.alwaysShowText,u=this.state,f=u.startX,d=u.endX,p=u.isTextActive,h=u.isSlideMoving,y=u.isTravellerMoving,m=u.isTravellerFocused;if(!t||!t.length||!rk(o)||!rk(i)||!rk(l)||!rk(s)||l<=0||s<=0)return null;var v=(0,rh.A)("recharts-brush",r),g=1===a().Children.count(n),b=db("userSelect","none");return a().createElement(o0,{className:v,onMouseLeave:this.handleLeaveWrapper,onTouchMove:this.handleTouchMove,style:b},this.renderBackground(),g&&this.renderPanorama(),this.renderSlide(f,d),this.renderTravellerLayer(f,"startX"),this.renderTravellerLayer(d,"endX"),(p||h||y||m||c)&&this.renderText())}}],r=[{key:"renderDefaultTraveller",value:function(e){var t=e.x,r=e.y,n=e.width,o=e.height,i=e.stroke,l=Math.floor(r+o/2)-1;return a().createElement(a().Fragment,null,a().createElement("rect",{x:t,y:r,width:n,height:o,fill:i,stroke:"none"}),a().createElement("line",{x1:t+1,y1:l,x2:t+n-1,y2:l,fill:"none",stroke:"#fff"}),a().createElement("line",{x1:t+1,y1:l+2,x2:t+n-1,y2:l+2,fill:"none",stroke:"#fff"}))}},{key:"renderTraveller",value:function(e,t){var r;return a().isValidElement(e)?a().cloneElement(e,t):rz()(e)?e(t):n.renderDefaultTraveller(t)}},{key:"getDerivedStateFromProps",value:function(e,t){var r=e.data,n=e.width,o=e.x,i=e.travellerWidth,a=e.updateId,l=e.startIndex,s=e.endIndex;if(r!==t.prevData||a!==t.prevUpdateId)return dO({prevData:r,prevTravellerWidth:i,prevUpdateId:a,prevX:o,prevWidth:n},r&&r.length?dM({data:r,width:n,x:o,travellerWidth:i,startIndex:l,endIndex:s}):{scale:null,scaleValues:null});if(t.scale&&(n!==t.prevWidth||o!==t.prevX||i!==t.prevTravellerWidth)){t.scale.range([o,o+n-i]);var c=t.scale.domain().map(function(e){return t.scale(e)});return{prevData:r,prevTravellerWidth:i,prevUpdateId:a,prevX:o,prevWidth:n,startX:t.scale(e.startIndex),endX:t.scale(e.endIndex),scaleValues:c}}return null}},{key:"getIndexInRange",value:function(e,t){for(var r=e.length,n=0,o=r-1;o-n>1;){var i=Math.floor((n+o)/2);e[i]>t?o=i:n=i}return t>=e[o]?o:n}}],t&&dS(n.prototype,t),r&&dS(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(i.PureComponent);function d_(e){return(d_="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function dI(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function dD(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?dI(Object(r),!0).forEach(function(t){dR(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):dI(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function dR(e,t,r){var n;return(n=function(e,t){if("object"!=d_(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=d_(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"),(t="symbol"==d_(n)?n:n+"")in e)?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function dB(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}dE(dT,"displayName","Brush"),dE(dT,"defaultProps",{height:40,travellerWidth:5,gap:1,fill:"#fff",stroke:"#666",padding:{top:1,right:1,bottom:1,left:1},leaveTimeOut:1e3,alwaysShowText:!1});var dL=Math.PI/180,d$=function(e,t,r,n){return{x:e+Math.cos(-dL*n)*r,y:t+Math.sin(-dL*n)*r}},dz=function(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{top:0,right:0,bottom:0,left:0};return Math.min(Math.abs(e-(r.left||0)-(r.right||0)),Math.abs(t-(r.top||0)-(r.bottom||0)))/2},dU=function(e,t){var r=e.x,n=e.y;return Math.sqrt(Math.pow(r-t.x,2)+Math.pow(n-t.y,2))},dF=function(e,t){var r=e.x,n=e.y,o=t.cx,i=t.cy,a=dU({x:r,y:n},{x:o,y:i});if(a<=0)return{radius:a};var l=Math.acos((r-o)/a);return n>i&&(l=2*Math.PI-l),{radius:a,angle:180*l/Math.PI,angleInRadian:l}},dq=function(e){var t=e.startAngle,r=e.endAngle,n=Math.min(Math.floor(t/360),Math.floor(r/360));return{startAngle:t-360*n,endAngle:r-360*n}},dW=function(e,t){var r,n=dF({x:e.x,y:e.y},t),o=n.radius,i=n.angle,a=t.innerRadius,l=t.outerRadius;if(o<a||o>l)return!1;if(0===o)return!0;var s=dq(t),c=s.startAngle,u=s.endAngle,f=i;if(c<=u){for(;f>u;)f-=360;for(;f<c;)f+=360;r=f>=c&&f<=u}else{for(;f>c;)f-=360;for(;f<u;)f+=360;r=f>=u&&f<=c}return r?dD(dD({},t),{},{radius:o,angle:f+360*Math.min(Math.floor(t.startAngle/360),Math.floor(t.endAngle/360))}):null},dV=function(e){return(0,i.isValidElement)(e)||rz()(e)||"boolean"==typeof e?"":e.className};function dH(e){return(dH="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var dG=["offset"];function dX(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function dY(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function dK(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?dY(Object(r),!0).forEach(function(t){var n,o,i;n=e,o=t,i=r[t],(o=function(e){var t=function(e,t){if("object"!=dH(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=dH(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==dH(t)?t:t+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):dY(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function dZ(){return(dZ=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}var dJ=function(e){var t=e.value,r=e.formatter,n=rL()(e.children)?t:e.children;return rz()(r)?r(n):n},dQ=function(e,t,r){var n,o,i=e.position,l=e.viewBox,s=e.offset,c=e.className,u=l.cx,f=l.cy,d=l.innerRadius,p=l.outerRadius,h=l.startAngle,y=l.endAngle,m=l.clockWise,v=(d+p)/2,g=rA(y-h)*Math.min(Math.abs(y-h),360),b=g>=0?1:-1;"insideStart"===i?(n=h+b*s,o=m):"insideEnd"===i?(n=y-b*s,o=!m):"end"===i&&(n=y+b*s,o=m),o=g<=0?o:!o;var x=d$(u,f,v,n),w=d$(u,f,v,n+(o?1:-1)*359),j="M".concat(x.x,",").concat(x.y,"\n    A").concat(v,",").concat(v,",0,1,").concat(+!o,",\n    ").concat(w.x,",").concat(w.y),O=rL()(e.id)?rM("recharts-radial-line-"):e.id;return a().createElement("text",dZ({},r,{dominantBaseline:"central",className:(0,rh.A)("recharts-radial-bar-label",c)}),a().createElement("defs",null,a().createElement("path",{id:O,d:j})),a().createElement("textPath",{xlinkHref:"#".concat(O)},t))},d0=function(e){var t=e.viewBox,r=e.offset,n=e.position,o=t.cx,i=t.cy,a=t.innerRadius,l=t.outerRadius,s=(t.startAngle+t.endAngle)/2;if("outside"===n){var c=d$(o,i,l+r,s),u=c.x;return{x:u,y:c.y,textAnchor:u>=o?"start":"end",verticalAnchor:"middle"}}if("center"===n)return{x:o,y:i,textAnchor:"middle",verticalAnchor:"middle"};if("centerTop"===n)return{x:o,y:i,textAnchor:"middle",verticalAnchor:"start"};if("centerBottom"===n)return{x:o,y:i,textAnchor:"middle",verticalAnchor:"end"};var f=d$(o,i,(a+l)/2,s);return{x:f.x,y:f.y,textAnchor:"middle",verticalAnchor:"middle"}},d1=function(e){var t=e.viewBox,r=e.parentViewBox,n=e.offset,o=e.position,i=t.x,a=t.y,l=t.width,s=t.height,c=s>=0?1:-1,u=c*n,f=c>0?"end":"start",d=c>0?"start":"end",p=l>=0?1:-1,h=p*n,y=p>0?"end":"start",m=p>0?"start":"end";if("top"===o)return dK(dK({},{x:i+l/2,y:a-c*n,textAnchor:"middle",verticalAnchor:f}),r?{height:Math.max(a-r.y,0),width:l}:{});if("bottom"===o)return dK(dK({},{x:i+l/2,y:a+s+u,textAnchor:"middle",verticalAnchor:d}),r?{height:Math.max(r.y+r.height-(a+s),0),width:l}:{});if("left"===o){var v={x:i-h,y:a+s/2,textAnchor:y,verticalAnchor:"middle"};return dK(dK({},v),r?{width:Math.max(v.x-r.x,0),height:s}:{})}if("right"===o){var g={x:i+l+h,y:a+s/2,textAnchor:m,verticalAnchor:"middle"};return dK(dK({},g),r?{width:Math.max(r.x+r.width-g.x,0),height:s}:{})}var b=r?{width:l,height:s}:{};return"insideLeft"===o?dK({x:i+h,y:a+s/2,textAnchor:m,verticalAnchor:"middle"},b):"insideRight"===o?dK({x:i+l-h,y:a+s/2,textAnchor:y,verticalAnchor:"middle"},b):"insideTop"===o?dK({x:i+l/2,y:a+u,textAnchor:"middle",verticalAnchor:d},b):"insideBottom"===o?dK({x:i+l/2,y:a+s-u,textAnchor:"middle",verticalAnchor:f},b):"insideTopLeft"===o?dK({x:i+h,y:a+u,textAnchor:m,verticalAnchor:d},b):"insideTopRight"===o?dK({x:i+l-h,y:a+u,textAnchor:y,verticalAnchor:d},b):"insideBottomLeft"===o?dK({x:i+h,y:a+s-u,textAnchor:m,verticalAnchor:f},b):"insideBottomRight"===o?dK({x:i+l-h,y:a+s-u,textAnchor:y,verticalAnchor:f},b):rF()(o)&&(rk(o.x)||rP(o.x))&&(rk(o.y)||rP(o.y))?dK({x:i+rC(o.x,l),y:a+rC(o.y,s),textAnchor:"end",verticalAnchor:"end"},b):dK({x:i+l/2,y:a+s/2,textAnchor:"middle",verticalAnchor:"middle"},b)};function d2(e){var t,r=e.offset,n=dK({offset:void 0===r?5:r},function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(e,dG)),o=n.viewBox,l=n.position,s=n.value,c=n.children,u=n.content,f=n.className,d=n.textBreakAll;if(!o||rL()(s)&&rL()(c)&&!(0,i.isValidElement)(u)&&!rz()(u))return null;if((0,i.isValidElement)(u))return(0,i.cloneElement)(u,n);if(rz()(u)){if(t=(0,i.createElement)(u,n),(0,i.isValidElement)(t))return t}else t=dJ(n);var p="cx"in o&&rk(o.cx),h=nt(n,!0);if(p&&("insideStart"===l||"insideEnd"===l||"end"===l))return dQ(n,t,h);var y=p?d0(n):d1(n);return a().createElement(a5,dZ({className:(0,rh.A)("recharts-label",void 0===f?"":f)},h,y,{breakAll:d}),t)}d2.displayName="Label";var d5=function(e){var t=e.cx,r=e.cy,n=e.angle,o=e.startAngle,i=e.endAngle,a=e.r,l=e.radius,s=e.innerRadius,c=e.outerRadius,u=e.x,f=e.y,d=e.top,p=e.left,h=e.width,y=e.height,m=e.clockWise,v=e.labelViewBox;if(v)return v;if(rk(h)&&rk(y)){if(rk(u)&&rk(f))return{x:u,y:f,width:h,height:y};if(rk(d)&&rk(p))return{x:d,y:p,width:h,height:y}}return rk(u)&&rk(f)?{x:u,y:f,width:0,height:0}:rk(t)&&rk(r)?{cx:t,cy:r,startAngle:o||n||0,endAngle:i||n||0,innerRadius:s||0,outerRadius:c||l||a||0,clockWise:m}:e.viewBox?e.viewBox:{}};d2.parseViewBox=d5,d2.renderCallByParent=function(e,t){var r,n,o=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(!e||!e.children&&o&&!e.label)return null;var l=e.children,s=d5(e),c=r6(l,d2).map(function(e,r){return(0,i.cloneElement)(e,{viewBox:t||s,key:"label-".concat(r)})});if(!o)return c;return[(r=e.label,n=t||s,r?!0===r?a().createElement(d2,{key:"label-implicit",viewBox:n}):rE(r)?a().createElement(d2,{key:"label-implicit",viewBox:n,value:r}):(0,i.isValidElement)(r)?r.type===d2?(0,i.cloneElement)(r,{key:"label-implicit",viewBox:n}):a().createElement(d2,{key:"label-implicit",content:r,viewBox:n}):rz()(r)?a().createElement(d2,{key:"label-implicit",content:r,viewBox:n}):rF()(r)?a().createElement(d2,dZ({viewBox:n},r,{key:"label-implicit"})):null:null)].concat(function(e){if(Array.isArray(e))return dX(e)}(c)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(c)||function(e,t){if(e){if("string"==typeof e)return dX(e,void 0);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return dX(e,t)}}(c)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}())};var d4=function(e,t){var r=e.alwaysShow,n=e.ifOverflow;return r&&(n="extendDomain"),n===t},d3=r(69691),d6=r.n(d3),d8=r(47212),d7=r.n(d8);function d9(e){return(d9="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function pe(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,po(n.key),n)}}function pt(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function pr(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?pt(Object(r),!0).forEach(function(t){pn(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):pt(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function pn(e,t,r){return(t=po(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function po(e){var t=function(e,t){if("object"!=d9(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=d9(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==d9(t)?t:t+""}var pi=function(e,t){var r=e.x,n=e.y,o=t.x,i=t.y;return{x:Math.min(r,o),y:Math.min(n,i),width:Math.abs(o-r),height:Math.abs(i-n)}},pa=function(){var e,t;function r(e){(function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")})(this,r),this.scale=e}return e=[{key:"domain",get:function(){return this.scale.domain}},{key:"range",get:function(){return this.scale.range}},{key:"rangeMin",get:function(){return this.range()[0]}},{key:"rangeMax",get:function(){return this.range()[1]}},{key:"bandwidth",get:function(){return this.scale.bandwidth}},{key:"apply",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=t.bandAware,n=t.position;if(void 0!==e){if(n)switch(n){case"start":default:return this.scale(e);case"middle":var o=this.bandwidth?this.bandwidth()/2:0;return this.scale(e)+o;case"end":var i=this.bandwidth?this.bandwidth():0;return this.scale(e)+i}if(r){var a=this.bandwidth?this.bandwidth()/2:0;return this.scale(e)+a}return this.scale(e)}}},{key:"isInRange",value:function(e){var t=this.range(),r=t[0],n=t[t.length-1];return r<=n?e>=r&&e<=n:e>=n&&e<=r}}],t=[{key:"create",value:function(e){return new r(e)}}],e&&pe(r.prototype,e),t&&pe(r,t),Object.defineProperty(r,"prototype",{writable:!1}),r}();pn(pa,"EPS",1e-4);var pl=function(e){var t=Object.keys(e).reduce(function(t,r){return pr(pr({},t),{},pn({},r,pa.create(e[r])))},{});return pr(pr({},t),{},{apply:function(e){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=r.bandAware,o=r.position;return d6()(e,function(e,r){return t[r].apply(e,{bandAware:n,position:o})})},isInRange:function(e){return d7()(e,function(e,r){return t[r].isInRange(e)})}})};function ps(){return(ps=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function pc(e){return(pc="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function pu(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function pf(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?pu(Object(r),!0).forEach(function(t){py(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):pu(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function pd(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(pd=function(){return!!e})()}function pp(e){return(pp=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function ph(e,t){return(ph=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function py(e,t,r){return(t=pm(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function pm(e){var t=function(e,t){if("object"!=pc(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=pc(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==pc(t)?t:t+""}var pv=function(e){var t=e.x,r=e.y,n=e.xAxis,o=e.yAxis,i=pl({x:n.scale,y:o.scale}),a=i.apply({x:t,y:r},{bandAware:!0});return d4(e,"discard")&&!i.isInRange(a)?null:a},pg=function(e){var t;function r(){var e,t;return function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}(this,r),e=r,t=arguments,e=pp(e),function(e,t){if(t&&("object"===pc(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,pd()?Reflect.construct(e,t||[],pp(this).constructor):e.apply(this,t))}return function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&ph(e,t)}(r,e),t=[{key:"render",value:function(){var e=this.props,t=e.x,n=e.y,o=e.r,i=e.alwaysShow,l=e.clipPathId,s=rE(t),c=rE(n);if(rR(void 0===i,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.'),!s||!c)return null;var u=pv(this.props);if(!u)return null;var f=u.x,d=u.y,p=this.props,h=p.shape,y=p.className,m=pf(pf({clipPath:d4(this.props,"hidden")?"url(#".concat(l,")"):void 0},nt(this.props,!0)),{},{cx:f,cy:d});return a().createElement(o0,{className:(0,rh.A)("recharts-reference-dot",y)},r.renderDot(h,m),d2.renderCallByParent(this.props,{x:f-o,y:d-o,width:2*o,height:2*o}))}}],function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,pm(n.key),n)}}(r.prototype,t),Object.defineProperty(r,"prototype",{writable:!1}),r}(a().Component);py(pg,"displayName","ReferenceDot"),py(pg,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#fff",stroke:"#ccc",fillOpacity:1,strokeWidth:1}),py(pg,"renderDot",function(e,t){var r;return a().isValidElement(e)?a().cloneElement(e,t):rz()(e)?e(t):a().createElement(o2,ps({},t,{cx:t.cx,cy:t.cy,className:"recharts-reference-dot-dot"}))});var pb=r(67367),px=r.n(pb);r(22964);var pw=r(86451),pj=r.n(pw)()(function(e){return{x:e.left,y:e.top,width:e.width,height:e.height}},function(e){return["l",e.left,"t",e.top,"w",e.width,"h",e.height].join("")}),pO=(0,i.createContext)(void 0),pS=(0,i.createContext)(void 0),pA=(0,i.createContext)(void 0),pP=(0,i.createContext)({}),pk=(0,i.createContext)(void 0),pE=(0,i.createContext)(0),pN=(0,i.createContext)(0),pM=function(e){var t=e.state,r=t.xAxisMap,n=t.yAxisMap,o=t.offset,i=e.clipPathId,l=e.children,s=e.width,c=e.height,u=pj(o);return a().createElement(pO.Provider,{value:r},a().createElement(pS.Provider,{value:n},a().createElement(pP.Provider,{value:o},a().createElement(pA.Provider,{value:u},a().createElement(pk.Provider,{value:i},a().createElement(pE.Provider,{value:c},a().createElement(pN.Provider,{value:s},l)))))))},pC=function(e){var t=(0,i.useContext)(pO);null==t&&oZ(!1);var r=t[e];return null==r&&oZ(!1),r},pT=function(e){var t=(0,i.useContext)(pS);null==t&&oZ(!1);var r=t[e];return null==r&&oZ(!1),r};function p_(e){return(p_="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function pI(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(pI=function(){return!!e})()}function pD(e){return(pD=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function pR(e,t){return(pR=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function pB(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function pL(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?pB(Object(r),!0).forEach(function(t){p$(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):pB(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function p$(e,t,r){return(t=pz(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function pz(e){var t=function(e,t){if("object"!=p_(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=p_(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==p_(t)?t:t+""}function pU(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function pF(){return(pF=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}var pq=function(e,t){var r;return a().isValidElement(e)?a().cloneElement(e,t):rz()(e)?e(t):a().createElement("line",pF({},t,{className:"recharts-reference-line-line"}))},pW=function(e,t,r,n,o,i,a,l,s){var c=o.x,u=o.y,f=o.width,d=o.height;if(r){var p=s.y,h=e.y.apply(p,{position:i});if(d4(s,"discard")&&!e.y.isInRange(h))return null;var y=[{x:c+f,y:h},{x:c,y:h}];return"left"===l?y.reverse():y}if(t){var m=s.x,v=e.x.apply(m,{position:i});if(d4(s,"discard")&&!e.x.isInRange(v))return null;var g=[{x:v,y:u+d},{x:v,y:u}];return"top"===a?g.reverse():g}if(n){var b=s.segment.map(function(t){return e.apply(t,{position:i})});return d4(s,"discard")&&px()(b,function(t){return!e.isInRange(t)})?null:b}return null};function pV(e){var t,r=e.x,n=e.y,o=e.segment,l=e.xAxisId,s=e.yAxisId,c=e.shape,u=e.className,f=e.alwaysShow,d=(0,i.useContext)(pk),p=pC(l),h=pT(s),y=(0,i.useContext)(pA);if(!d||!y)return null;rR(void 0===f,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.');var m=pW(pl({x:p.scale,y:h.scale}),rE(r),rE(n),o&&2===o.length,y,e.position,p.orientation,h.orientation,e);if(!m)return null;var v=function(e){if(Array.isArray(e))return e}(m)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,a,l=[],s=!0,c=!1;try{i=(r=r.call(e)).next;for(;!(s=(n=i.call(r)).done)&&(l.push(n.value),l.length!==t);s=!0);}catch(e){c=!0,o=e}finally{try{if(!s&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(c)throw o}}return l}}(m,2)||function(e,t){if(e){if("string"==typeof e)return pU(e,2);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return pU(e,t)}}(m,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),g=v[0],b=g.x,x=g.y,w=v[1],j=w.x,O=w.y,S=pL(pL({clipPath:d4(e,"hidden")?"url(#".concat(d,")"):void 0},nt(e,!0)),{},{x1:b,y1:x,x2:j,y2:O});return a().createElement(o0,{className:(0,rh.A)("recharts-reference-line",u)},pq(c,S),d2.renderCallByParent(e,pi({x:(t={x1:b,y1:x,x2:j,y2:O}).x1,y:t.y1},{x:t.x2,y:t.y2})))}var pH=function(e){var t;function r(){var e,t;return function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}(this,r),e=r,t=arguments,e=pD(e),function(e,t){if(t&&("object"===p_(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,pI()?Reflect.construct(e,t||[],pD(this).constructor):e.apply(this,t))}return function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&pR(e,t)}(r,e),t=[{key:"render",value:function(){return a().createElement(pV,this.props)}}],function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,pz(n.key),n)}}(r.prototype,t),Object.defineProperty(r,"prototype",{writable:!1}),r}(a().Component);function pG(){return(pG=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function pX(e){return(pX="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function pY(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function pK(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?pY(Object(r),!0).forEach(function(t){p0(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):pY(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}p$(pH,"displayName","ReferenceLine"),p$(pH,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,fill:"none",stroke:"#ccc",fillOpacity:1,strokeWidth:1,position:"middle"});function pZ(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(pZ=function(){return!!e})()}function pJ(e){return(pJ=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function pQ(e,t){return(pQ=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function p0(e,t,r){return(t=p1(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function p1(e){var t=function(e,t){if("object"!=pX(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=pX(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==pX(t)?t:t+""}var p2=function(e,t,r,n,o){var i=o.x1,a=o.x2,l=o.y1,s=o.y2,c=o.xAxis,u=o.yAxis;if(!c||!u)return null;var f=pl({x:c.scale,y:u.scale}),d={x:e?f.x.apply(i,{position:"start"}):f.x.rangeMin,y:r?f.y.apply(l,{position:"start"}):f.y.rangeMin},p={x:t?f.x.apply(a,{position:"end"}):f.x.rangeMax,y:n?f.y.apply(s,{position:"end"}):f.y.rangeMax};return!d4(o,"discard")||f.isInRange(d)&&f.isInRange(p)?pi(d,p):null},p5=function(e){var t;function r(){var e,t;return function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}(this,r),e=r,t=arguments,e=pJ(e),function(e,t){if(t&&("object"===pX(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,pZ()?Reflect.construct(e,t||[],pJ(this).constructor):e.apply(this,t))}return function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&pQ(e,t)}(r,e),t=[{key:"render",value:function(){var e=this.props,t=e.x1,n=e.x2,o=e.y1,i=e.y2,l=e.className,s=e.alwaysShow,c=e.clipPathId;rR(void 0===s,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.');var u=rE(t),f=rE(n),d=rE(o),p=rE(i),h=this.props.shape;if(!u&&!f&&!d&&!p&&!h)return null;var y=p2(u,f,d,p,this.props);if(!y&&!h)return null;var m=d4(this.props,"hidden")?"url(#".concat(c,")"):void 0;return a().createElement(o0,{className:(0,rh.A)("recharts-reference-area",l)},r.renderRect(h,pK(pK({clipPath:m},nt(this.props,!0)),y)),d2.renderCallByParent(this.props,y))}}],function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,p1(n.key),n)}}(r.prototype,t),Object.defineProperty(r,"prototype",{writable:!1}),r}(a().Component);function p4(e){return function(e){if(Array.isArray(e))return p3(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return p3(e,void 0);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return p3(e,t)}}(e)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function p3(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}p0(p5,"displayName","ReferenceArea"),p0(p5,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#ccc",fillOpacity:.5,stroke:"none",strokeWidth:1}),p0(p5,"renderRect",function(e,t){var r;return a().isValidElement(e)?a().cloneElement(e,t):rz()(e)?e(t):a().createElement(ap,pG({},t,{className:"recharts-reference-area-rect"}))});var p6=function(e,t,r,n,o){var i=r6(e,pH),a=r6(e,pg),l=[].concat(p4(i),p4(a)),s=r6(e,p5),c="".concat(n,"Id"),u=n[0],f=t;if(l.length&&(f=l.reduce(function(e,t){if(t.props[c]===r&&d4(t.props,"extendDomain")&&rk(t.props[u])){var n=t.props[u];return[Math.min(e[0],n),Math.max(e[1],n)]}return e},f)),s.length){var d="".concat(u,"1"),p="".concat(u,"2");f=s.reduce(function(e,t){if(t.props[c]===r&&d4(t.props,"extendDomain")&&rk(t.props[d])&&rk(t.props[p])){var n=t.props[d],o=t.props[p];return[Math.min(e[0],n,o),Math.max(e[1],n,o)]}return e},f)}return o&&o.length&&(f=o.reduce(function(e,t){return rk(t)?[Math.min(e[0],t),Math.max(e[1],t)]:e},f)),f},p8=r(11117),p7=new(r.n(p8)()),p9="recharts.syncMouseEvents";function he(e){return(he="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function ht(e,t,r){return(t=hr(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function hr(e){var t=function(e,t){if("object"!=he(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=he(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==he(t)?t:t+""}var hn=function(){var e,t;return e=function e(){(function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")})(this,e),ht(this,"activeIndex",0),ht(this,"coordinateList",[]),ht(this,"layout","horizontal")},t=[{key:"setDetails",value:function(e){var t,r=e.coordinateList,n=void 0===r?null:r,o=e.container,i=void 0===o?null:o,a=e.layout,l=void 0===a?null:a,s=e.offset,c=void 0===s?null:s,u=e.mouseHandlerCallback,f=void 0===u?null:u;this.coordinateList=null!==(t=null!=n?n:this.coordinateList)&&void 0!==t?t:[],this.container=null!=i?i:this.container,this.layout=null!=l?l:this.layout,this.offset=null!=c?c:this.offset,this.mouseHandlerCallback=null!=f?f:this.mouseHandlerCallback,this.activeIndex=Math.min(Math.max(this.activeIndex,0),this.coordinateList.length-1)}},{key:"focus",value:function(){this.spoofMouse()}},{key:"keyboardEvent",value:function(e){if(0!==this.coordinateList.length)switch(e.key){case"ArrowRight":if("horizontal"!==this.layout)return;this.activeIndex=Math.min(this.activeIndex+1,this.coordinateList.length-1),this.spoofMouse();break;case"ArrowLeft":if("horizontal"!==this.layout)return;this.activeIndex=Math.max(this.activeIndex-1,0),this.spoofMouse()}}},{key:"setIndex",value:function(e){this.activeIndex=e}},{key:"spoofMouse",value:function(){if("horizontal"===this.layout&&0!==this.coordinateList.length){var e,t,r=this.container.getBoundingClientRect(),n=r.x,o=r.y,i=r.height,a=this.coordinateList[this.activeIndex].coordinate,l=(null===(e=window)||void 0===e?void 0:e.scrollX)||0,s=(null===(t=window)||void 0===t?void 0:t.scrollY)||0,c=o+this.offset.top+i/2+s;this.mouseHandlerCallback({pageX:n+a+l,pageY:c})}}}],function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,hr(n.key),n)}}(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}(),ho=r(38404),hi=r.n(ho),ha=r(98451),hl=r.n(ha);function hs(e){return(hs="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function hc(){return(hc=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function hu(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function hf(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function hd(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?hf(Object(r),!0).forEach(function(t){var n,o,i;n=e,o=t,i=r[t],(o=function(e){var t=function(e,t){if("object"!=hs(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=hs(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==hs(t)?t:t+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):hf(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var hp=function(e,t,r,n,o){var i,a=r-n;return"M ".concat(e,",").concat(t)+"L ".concat(e+r,",").concat(t)+"L ".concat(e+r-a/2,",").concat(t+o)+"L ".concat(e+r-a/2-n,",").concat(t+o)+"L ".concat(e,",").concat(t," Z")},hh={x:0,y:0,upperWidth:0,lowerWidth:0,height:0,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},hy=function(e){var t,r=hd(hd({},hh),e),n=(0,i.useRef)(),o=function(e){if(Array.isArray(e))return e}(t=(0,i.useState)(-1))||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,a,l=[],s=!0,c=!1;try{i=(r=r.call(e)).next;for(;!(s=(n=i.call(r)).done)&&(l.push(n.value),l.length!==t);s=!0);}catch(e){c=!0,o=e}finally{try{if(!s&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(c)throw o}}return l}}(t,2)||function(e,t){if(e){if("string"==typeof e)return hu(e,2);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return hu(e,t)}}(t,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),l=o[0],s=o[1];(0,i.useEffect)(function(){if(n.current&&n.current.getTotalLength)try{var e=n.current.getTotalLength();e&&s(e)}catch(e){}},[]);var c=r.x,u=r.y,f=r.upperWidth,d=r.lowerWidth,p=r.height,h=r.className,y=r.animationEasing,m=r.animationDuration,v=r.animationBegin,g=r.isUpdateAnimationActive;if(c!==+c||u!==+u||f!==+f||d!==+d||p!==+p||0===f&&0===d||0===p)return null;var b=(0,rh.A)("recharts-trapezoid",h);return g?a().createElement(ao,{canBegin:l>0,from:{upperWidth:0,lowerWidth:0,height:p,x:c,y:u},to:{upperWidth:f,lowerWidth:d,height:p,x:c,y:u},duration:m,animationEasing:y,isActive:g},function(e){var t=e.upperWidth,o=e.lowerWidth,i=e.height,s=e.x,c=e.y;return a().createElement(ao,{canBegin:l>0,from:"0px ".concat(-1===l?1:l,"px"),to:"".concat(l,"px 0px"),attributeName:"strokeDasharray",begin:v,duration:m,easing:y},a().createElement("path",hc({},nt(r,!0),{className:b,d:hp(s,c,t,o,i),ref:n})))}):a().createElement("g",null,a().createElement("path",hc({},nt(r,!0),{className:b,d:hp(c,u,f,d,p)})))};function hm(e){return(hm="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function hv(){return(hv=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function hg(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function hb(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?hg(Object(r),!0).forEach(function(t){var n,o,i;n=e,o=t,i=r[t],(o=function(e){var t=function(e,t){if("object"!=hm(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=hm(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==hm(t)?t:t+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):hg(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var hx=function(e){var t=e.cx,r=e.cy,n=e.radius,o=e.angle,i=e.sign,a=e.isExternal,l=e.cornerRadius,s=e.cornerIsExternal,c=l*(a?1:-1)+n,u=Math.asin(l/c)/dL,f=s?o:o+i*u;return{center:d$(t,r,c,f),circleTangency:d$(t,r,n,f),lineTangency:d$(t,r,c*Math.cos(u*dL),s?o-i*u:o),theta:u}},hw=function(e){var t=e.cx,r=e.cy,n=e.innerRadius,o=e.outerRadius,i=e.startAngle,a=e.endAngle,l=rA(a-i)*Math.min(Math.abs(a-i),359.999),s=i+l,c=d$(t,r,o,i),u=d$(t,r,o,s),f="M ".concat(c.x,",").concat(c.y,"\n    A ").concat(o,",").concat(o,",0,\n    ").concat(+(Math.abs(l)>180),",").concat(+(i>s),",\n    ").concat(u.x,",").concat(u.y,"\n  ");if(n>0){var d=d$(t,r,n,i),p=d$(t,r,n,s);f+="L ".concat(p.x,",").concat(p.y,"\n            A ").concat(n,",").concat(n,",0,\n            ").concat(+(Math.abs(l)>180),",").concat(+(i<=s),",\n            ").concat(d.x,",").concat(d.y," Z")}else f+="L ".concat(t,",").concat(r," Z");return f},hj=function(e){var t=e.cx,r=e.cy,n=e.innerRadius,o=e.outerRadius,i=e.cornerRadius,a=e.forceCornerRadius,l=e.cornerIsExternal,s=e.startAngle,c=e.endAngle,u=rA(c-s),f=hx({cx:t,cy:r,radius:o,angle:s,sign:u,cornerRadius:i,cornerIsExternal:l}),d=f.circleTangency,p=f.lineTangency,h=f.theta,y=hx({cx:t,cy:r,radius:o,angle:c,sign:-u,cornerRadius:i,cornerIsExternal:l}),m=y.circleTangency,v=y.lineTangency,g=y.theta,b=l?Math.abs(s-c):Math.abs(s-c)-h-g;if(b<0)return a?"M ".concat(p.x,",").concat(p.y,"\n        a").concat(i,",").concat(i,",0,0,1,").concat(2*i,",0\n        a").concat(i,",").concat(i,",0,0,1,").concat(-(2*i),",0\n      "):hw({cx:t,cy:r,innerRadius:n,outerRadius:o,startAngle:s,endAngle:c});var x="M ".concat(p.x,",").concat(p.y,"\n    A").concat(i,",").concat(i,",0,0,").concat(+(u<0),",").concat(d.x,",").concat(d.y,"\n    A").concat(o,",").concat(o,",0,").concat(+(b>180),",").concat(+(u<0),",").concat(m.x,",").concat(m.y,"\n    A").concat(i,",").concat(i,",0,0,").concat(+(u<0),",").concat(v.x,",").concat(v.y,"\n  ");if(n>0){var w=hx({cx:t,cy:r,radius:n,angle:s,sign:u,isExternal:!0,cornerRadius:i,cornerIsExternal:l}),j=w.circleTangency,O=w.lineTangency,S=w.theta,A=hx({cx:t,cy:r,radius:n,angle:c,sign:-u,isExternal:!0,cornerRadius:i,cornerIsExternal:l}),P=A.circleTangency,k=A.lineTangency,E=A.theta,N=l?Math.abs(s-c):Math.abs(s-c)-S-E;if(N<0&&0===i)return"".concat(x,"L").concat(t,",").concat(r,"Z");x+="L".concat(k.x,",").concat(k.y,"\n      A").concat(i,",").concat(i,",0,0,").concat(+(u<0),",").concat(P.x,",").concat(P.y,"\n      A").concat(n,",").concat(n,",0,").concat(+(N>180),",").concat(+(u>0),",").concat(j.x,",").concat(j.y,"\n      A").concat(i,",").concat(i,",0,0,").concat(+(u<0),",").concat(O.x,",").concat(O.y,"Z")}else x+="L".concat(t,",").concat(r,"Z");return x},hO={cx:0,cy:0,innerRadius:0,outerRadius:0,startAngle:0,endAngle:0,cornerRadius:0,forceCornerRadius:!1,cornerIsExternal:!1},hS=function(e){var t,r=hb(hb({},hO),e),n=r.cx,o=r.cy,i=r.innerRadius,l=r.outerRadius,s=r.cornerRadius,c=r.forceCornerRadius,u=r.cornerIsExternal,f=r.startAngle,d=r.endAngle,p=r.className;if(l<i||f===d)return null;var h=(0,rh.A)("recharts-sector",p),y=l-i,m=rC(s,y,0,!0);return t=m>0&&360>Math.abs(f-d)?hj({cx:n,cy:o,innerRadius:i,outerRadius:l,cornerRadius:Math.min(m,y/2),forceCornerRadius:c,cornerIsExternal:u,startAngle:f,endAngle:d}):hw({cx:n,cy:o,innerRadius:i,outerRadius:l,startAngle:f,endAngle:d}),a().createElement("path",hv({},nt(r,!0),{className:h,d:t,role:"img"}))},hA=["option","shapeType","propTransformer","activeClassName","isActive"];function hP(e){return(hP="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function hk(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function hE(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?hk(Object(r),!0).forEach(function(t){var n,o,i;n=e,o=t,i=r[t],(o=function(e){var t=function(e,t){if("object"!=hP(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=hP(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==hP(t)?t:t+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):hk(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function hN(e){var t=e.shapeType,r=e.elementProps;switch(t){case"rectangle":return a().createElement(ap,r);case"trapezoid":return a().createElement(hy,r);case"sector":return a().createElement(hS,r);case"symbols":if("symbols"===t)return a().createElement(ob,r);break;default:return null}}function hM(e){var t,r=e.option,n=e.shapeType,o=e.propTransformer,l=e.activeClassName,s=e.isActive,c=function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(e,hA);if((0,i.isValidElement)(r))t=(0,i.cloneElement)(r,hE(hE({},c),(0,i.isValidElement)(r)?r.props:r));else if(rz()(r))t=r(c);else if(hi()(r)&&!hl()(r)){var u=(void 0===o?function(e,t){return hE(hE({},t),e)}:o)(r,c);t=a().createElement(hN,{shapeType:n,elementProps:u})}else t=a().createElement(hN,{shapeType:n,elementProps:c});return s?a().createElement(o0,{className:void 0===l?"recharts-active-shape":l},t):t}function hC(e,t){return null!=t&&"trapezoids"in e.props}function hT(e,t){return null!=t&&"sectors"in e.props}function h_(e,t){return null!=t&&"points"in e.props}function hI(e,t){var r,n,o=e.x===(null==t||null===(r=t.labelViewBox)||void 0===r?void 0:r.x)||e.x===t.x,i=e.y===(null==t||null===(n=t.labelViewBox)||void 0===n?void 0:n.y)||e.y===t.y;return o&&i}function hD(e,t){var r=e.endAngle===t.endAngle,n=e.startAngle===t.startAngle;return r&&n}function hR(e,t){var r=e.x===t.x,n=e.y===t.y,o=e.z===t.z;return r&&n&&o}function hB(){}function hL(e,t,r){e._context.bezierCurveTo((2*e._x0+e._x1)/3,(2*e._y0+e._y1)/3,(e._x0+2*e._x1)/3,(e._y0+2*e._y1)/3,(e._x0+4*e._x1+t)/6,(e._y0+4*e._y1+r)/6)}function h$(e){this._context=e}function hz(e){this._context=e}function hU(e){this._context=e}h$.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){switch(this._point){case 3:hL(this,this._x1,this._y1);case 2:this._context.lineTo(this._x1,this._y1)}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e*=1,t*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;break;case 2:this._point=3,this._context.lineTo((5*this._x0+this._x1)/6,(5*this._y0+this._y1)/6);default:hL(this,e,t)}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}},hz.prototype={areaStart:hB,areaEnd:hB,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._y0=this._y1=this._y2=this._y3=this._y4=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:this._context.moveTo(this._x2,this._y2),this._context.closePath();break;case 2:this._context.moveTo((this._x2+2*this._x3)/3,(this._y2+2*this._y3)/3),this._context.lineTo((this._x3+2*this._x2)/3,(this._y3+2*this._y2)/3),this._context.closePath();break;case 3:this.point(this._x2,this._y2),this.point(this._x3,this._y3),this.point(this._x4,this._y4)}},point:function(e,t){switch(e*=1,t*=1,this._point){case 0:this._point=1,this._x2=e,this._y2=t;break;case 1:this._point=2,this._x3=e,this._y3=t;break;case 2:this._point=3,this._x4=e,this._y4=t,this._context.moveTo((this._x0+4*this._x1+e)/6,(this._y0+4*this._y1+t)/6);break;default:hL(this,e,t)}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}},hU.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){(this._line||0!==this._line&&3===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e*=1,t*=1,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3;var r=(this._x0+4*this._x1+e)/6,n=(this._y0+4*this._y1+t)/6;this._line?this._context.lineTo(r,n):this._context.moveTo(r,n);break;case 3:this._point=4;default:hL(this,e,t)}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}};class hF{constructor(e,t){this._context=e,this._x=t}areaStart(){this._line=0}areaEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line}point(e,t){switch(e*=1,t*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:this._x?this._context.bezierCurveTo(this._x0=(this._x0+e)/2,this._y0,this._x0,t,e,t):this._context.bezierCurveTo(this._x0,this._y0=(this._y0+t)/2,e,this._y0,e,t)}this._x0=e,this._y0=t}}function hq(e){this._context=e}function hW(e){this._context=e}function hV(e){return new hW(e)}hq.prototype={areaStart:hB,areaEnd:hB,lineStart:function(){this._point=0},lineEnd:function(){this._point&&this._context.closePath()},point:function(e,t){e*=1,t*=1,this._point?this._context.lineTo(e,t):(this._point=1,this._context.moveTo(e,t))}};function hH(e,t,r){var n=e._x1-e._x0,o=t-e._x1,i=(e._y1-e._y0)/(n||o<0&&-0),a=(r-e._y1)/(o||n<0&&-0);return((i<0?-1:1)+(a<0?-1:1))*Math.min(Math.abs(i),Math.abs(a),.5*Math.abs((i*o+a*n)/(n+o)))||0}function hG(e,t){var r=e._x1-e._x0;return r?(3*(e._y1-e._y0)/r-t)/2:t}function hX(e,t,r){var n=e._x0,o=e._y0,i=e._x1,a=e._y1,l=(i-n)/3;e._context.bezierCurveTo(n+l,o+l*t,i-l,a-l*r,i,a)}function hY(e){this._context=e}function hK(e){this._context=new hZ(e)}function hZ(e){this._context=e}function hJ(e){this._context=e}function hQ(e){var t,r,n=e.length-1,o=Array(n),i=Array(n),a=Array(n);for(o[0]=0,i[0]=2,a[0]=e[0]+2*e[1],t=1;t<n-1;++t)o[t]=1,i[t]=4,a[t]=4*e[t]+2*e[t+1];for(o[n-1]=2,i[n-1]=7,a[n-1]=8*e[n-1]+e[n],t=1;t<n;++t)r=o[t]/i[t-1],i[t]-=r,a[t]-=r*a[t-1];for(o[n-1]=a[n-1]/i[n-1],t=n-2;t>=0;--t)o[t]=(a[t]-o[t+1])/i[t];for(t=0,i[n-1]=(e[n]+o[n-1])/2;t<n-1;++t)i[t]=2*e[t+1]-o[t+1];return[o,i]}function h0(e,t){this._context=e,this._t=t}function h1(e){return e[0]}function h2(e){return e[1]}function h5(e,t){var r=oo(!0),n=null,o=hV,i=null,a=ou(l);function l(l){var s,c,u,f=(l=uP(l)).length,d=!1;for(null==n&&(i=o(u=a())),s=0;s<=f;++s)!(s<f&&r(c=l[s],s,l))===d&&((d=!d)?i.lineStart():i.lineEnd()),d&&i.point(+e(c,s,l),+t(c,s,l));if(u)return i=null,u+""||null}return e="function"==typeof e?e:void 0===e?h1:oo(e),t="function"==typeof t?t:void 0===t?h2:oo(t),l.x=function(t){return arguments.length?(e="function"==typeof t?t:oo(+t),l):e},l.y=function(e){return arguments.length?(t="function"==typeof e?e:oo(+e),l):t},l.defined=function(e){return arguments.length?(r="function"==typeof e?e:oo(!!e),l):r},l.curve=function(e){return arguments.length?(o=e,null!=n&&(i=o(n)),l):o},l.context=function(e){return arguments.length?(null==e?n=i=null:i=o(n=e),l):n},l}function h4(e,t,r){var n=null,o=oo(!0),i=null,a=hV,l=null,s=ou(c);function c(c){var u,f,d,p,h,y=(c=uP(c)).length,m=!1,v=Array(y),g=Array(y);for(null==i&&(l=a(h=s())),u=0;u<=y;++u){if(!(u<y&&o(p=c[u],u,c))===m){if(m=!m)f=u,l.areaStart(),l.lineStart();else{for(l.lineEnd(),l.lineStart(),d=u-1;d>=f;--d)l.point(v[d],g[d]);l.lineEnd(),l.areaEnd()}}m&&(v[u]=+e(p,u,c),g[u]=+t(p,u,c),l.point(n?+n(p,u,c):v[u],r?+r(p,u,c):g[u]))}if(h)return l=null,h+""||null}function u(){return h5().defined(o).curve(a).context(i)}return e="function"==typeof e?e:void 0===e?h1:oo(+e),t="function"==typeof t?t:void 0===t?oo(0):oo(+t),r="function"==typeof r?r:void 0===r?h2:oo(+r),c.x=function(t){return arguments.length?(e="function"==typeof t?t:oo(+t),n=null,c):e},c.x0=function(t){return arguments.length?(e="function"==typeof t?t:oo(+t),c):e},c.x1=function(e){return arguments.length?(n=null==e?null:"function"==typeof e?e:oo(+e),c):n},c.y=function(e){return arguments.length?(t="function"==typeof e?e:oo(+e),r=null,c):t},c.y0=function(e){return arguments.length?(t="function"==typeof e?e:oo(+e),c):t},c.y1=function(e){return arguments.length?(r=null==e?null:"function"==typeof e?e:oo(+e),c):r},c.lineX0=c.lineY0=function(){return u().x(e).y(t)},c.lineY1=function(){return u().x(e).y(r)},c.lineX1=function(){return u().x(n).y(t)},c.defined=function(e){return arguments.length?(o="function"==typeof e?e:oo(!!e),c):o},c.curve=function(e){return arguments.length?(a=e,null!=i&&(l=a(i)),c):a},c.context=function(e){return arguments.length?(null==e?i=l=null:l=a(i=e),c):i},c}function h3(e){return(h3="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function h6(){return(h6=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function h8(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function h7(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?h8(Object(r),!0).forEach(function(t){var n,o,i;n=e,o=t,i=r[t],(o=function(e){var t=function(e,t){if("object"!=h3(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=h3(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==h3(t)?t:t+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):h8(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}hW.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e*=1,t*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:this._context.lineTo(e,t)}}},hY.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=this._t0=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x1,this._y1);break;case 3:hX(this,this._t0,hG(this,this._t0))}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){var r=NaN;if(t*=1,(e*=1)!==this._x1||t!==this._y1){switch(this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;break;case 2:this._point=3,hX(this,hG(this,r=hH(this,e,t)),r);break;default:hX(this,this._t0,r=hH(this,e,t))}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t,this._t0=r}}},(hK.prototype=Object.create(hY.prototype)).point=function(e,t){hY.prototype.point.call(this,t,e)},hZ.prototype={moveTo:function(e,t){this._context.moveTo(t,e)},closePath:function(){this._context.closePath()},lineTo:function(e,t){this._context.lineTo(t,e)},bezierCurveTo:function(e,t,r,n,o,i){this._context.bezierCurveTo(t,e,n,r,i,o)}},hJ.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=[],this._y=[]},lineEnd:function(){var e=this._x,t=this._y,r=e.length;if(r){if(this._line?this._context.lineTo(e[0],t[0]):this._context.moveTo(e[0],t[0]),2===r)this._context.lineTo(e[1],t[1]);else for(var n=hQ(e),o=hQ(t),i=0,a=1;a<r;++i,++a)this._context.bezierCurveTo(n[0][i],o[0][i],n[1][i],o[1][i],e[a],t[a])}(this._line||0!==this._line&&1===r)&&this._context.closePath(),this._line=1-this._line,this._x=this._y=null},point:function(e,t){this._x.push(+e),this._y.push(+t)}},h0.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=this._y=NaN,this._point=0},lineEnd:function(){0<this._t&&this._t<1&&2===this._point&&this._context.lineTo(this._x,this._y),(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line>=0&&(this._t=1-this._t,this._line=1-this._line)},point:function(e,t){switch(e*=1,t*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:if(this._t<=0)this._context.lineTo(this._x,t),this._context.lineTo(e,t);else{var r=this._x*(1-this._t)+e*this._t;this._context.lineTo(r,this._y),this._context.lineTo(r,t)}}this._x=e,this._y=t}};var h9={curveBasisClosed:function(e){return new hz(e)},curveBasisOpen:function(e){return new hU(e)},curveBasis:function(e){return new h$(e)},curveBumpX:function(e){return new hF(e,!0)},curveBumpY:function(e){return new hF(e,!1)},curveLinearClosed:function(e){return new hq(e)},curveLinear:hV,curveMonotoneX:function(e){return new hY(e)},curveMonotoneY:function(e){return new hK(e)},curveNatural:function(e){return new hJ(e)},curveStep:function(e){return new h0(e,.5)},curveStepAfter:function(e){return new h0(e,1)},curveStepBefore:function(e){return new h0(e,0)}},ye=function(e){return e.x===+e.x&&e.y===+e.y},yt=function(e){return e.x},yr=function(e){return e.y},yn=function(e,t){if(rz()(e))return e;var r="curve".concat(nJ()(e));return("curveMonotone"===r||"curveBump"===r)&&t?h9["".concat(r).concat("vertical"===t?"Y":"X")]:h9[r]||hV},yo=function(e){var t,r=e.type,n=e.points,o=void 0===n?[]:n,i=e.baseLine,a=e.layout,l=e.connectNulls,s=void 0!==l&&l,c=yn(void 0===r?"linear":r,a),u=s?o.filter(function(e){return ye(e)}):o;if(Array.isArray(i)){var f=s?i.filter(function(e){return ye(e)}):i,d=u.map(function(e,t){return h7(h7({},e),{},{base:f[t]})});return(t="vertical"===a?h4().y(yr).x1(yt).x0(function(e){return e.base.x}):h4().x(yt).y1(yr).y0(function(e){return e.base.y})).defined(ye).curve(c),t(d)}return(t="vertical"===a&&rk(i)?h4().y(yr).x1(yt).x0(i):rk(i)?h4().x(yt).y1(yr).y0(i):h5().x(yt).y(yr)).defined(ye).curve(c),t(u)},yi=function(e){var t=e.className,r=e.points,n=e.path,o=e.pathRef;if((!r||!r.length)&&!n)return null;var i=r&&r.length?yo(e):n;return a().createElement("path",h6({},nt(e,!1),rK(e),{className:(0,rh.A)("recharts-curve",t),d:i,ref:o}))};function ya(e){return(ya="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var yl=["x","y","top","left","width","height","className"];function ys(){return(ys=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function yc(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}var yu=function(e){var t=e.x,r=void 0===t?0:t,n=e.y,o=void 0===n?0:n,i=e.top,l=void 0===i?0:i,s=e.left,c=void 0===s?0:s,u=e.width,f=void 0===u?0:u,d=e.height,p=void 0===d?0:d,h=e.className,y=function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?yc(Object(r),!0).forEach(function(t){var n,o,i;n=e,o=t,i=r[t],(o=function(e){var t=function(e,t){if("object"!=ya(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=ya(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==ya(t)?t:t+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):yc(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}({x:r,y:o,top:l,left:c,width:f,height:p},function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(e,yl));return rk(r)&&rk(o)&&rk(f)&&rk(p)&&rk(l)&&rk(c)?a().createElement("path",ys({},nt(y,!0),{className:(0,rh.A)("recharts-cross",h),d:"M".concat(r,",").concat(l,"v").concat(p,"M").concat(c,",").concat(o,"h").concat(f)})):null};function yf(e){var t=e.cx,r=e.cy,n=e.radius,o=e.startAngle,i=e.endAngle;return{points:[d$(t,r,n,o),d$(t,r,n,i)],cx:t,cy:r,radius:n,startAngle:o,endAngle:i}}function yd(e){return(yd="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function yp(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function yh(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?yp(Object(r),!0).forEach(function(t){var n,o,i;n=e,o=t,i=r[t],(o=function(e){var t=function(e,t){if("object"!=yd(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=yd(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==yd(t)?t:t+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):yp(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function yy(e){var t,r,n,o,a=e.element,l=e.tooltipEventType,s=e.isActive,c=e.activeCoordinate,u=e.activePayload,f=e.offset,d=e.activeTooltipIndex,p=e.tooltipAxisBandSize,h=e.layout,y=e.chartName,m=null!==(r=a.props.cursor)&&void 0!==r?r:null===(n=a.type.defaultProps)||void 0===n?void 0:n.cursor;if(!a||!m||!s||!c||"ScatterChart"!==y&&"axis"!==l)return null;var v=yi;if("ScatterChart"===y)o=c,v=yu;else if("BarChart"===y)t=p/2,o={stroke:"none",fill:"#ccc",x:"horizontal"===h?c.x-t:f.left+.5,y:"horizontal"===h?f.top+.5:c.y-t,width:"horizontal"===h?p:f.width-1,height:"horizontal"===h?f.height-1:p},v=ap;else if("radial"===h){var g=yf(c),b=g.cx,x=g.cy,w=g.radius;o={cx:b,cy:x,startAngle:g.startAngle,endAngle:g.endAngle,innerRadius:w,outerRadius:w},v=hS}else o={points:function(e,t,r){var n,o,i,a;if("horizontal"===e)i=n=t.x,o=r.top,a=r.top+r.height;else if("vertical"===e)a=o=t.y,n=r.left,i=r.left+r.width;else if(null!=t.cx&&null!=t.cy){if("centric"!==e)return yf(t);var l=t.cx,s=t.cy,c=t.innerRadius,u=t.outerRadius,f=t.angle,d=d$(l,s,c,f),p=d$(l,s,u,f);n=d.x,o=d.y,i=p.x,a=p.y}return[{x:n,y:o},{x:i,y:a}]}(h,c,f)},v=yi;var j=yh(yh(yh(yh({stroke:"#ccc",pointerEvents:"none"},f),o),nt(m,!1)),{},{payload:u,payloadIndex:d,className:(0,rh.A)("recharts-tooltip-cursor",m.className)});return(0,i.isValidElement)(m)?(0,i.cloneElement)(m,j):(0,i.createElement)(v,j)}var ym=["item"],yv=["children","className","width","height","style","compact","title","desc"];function yg(e){return(yg="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function yb(){return(yb=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function yx(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,a,l=[],s=!0,c=!1;try{if(i=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;s=!1}else for(;!(s=(n=i.call(r)).done)&&(l.push(n.value),l.length!==t);s=!0);}catch(e){c=!0,o=e}finally{try{if(!s&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(c)throw o}}return l}}(e,t)||yP(e,t)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function yw(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}function yj(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(yj=function(){return!!e})()}function yO(e){return(yO=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function yS(e,t){return(yS=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function yA(e){return function(e){if(Array.isArray(e))return yk(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||yP(e)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function yP(e,t){if(e){if("string"==typeof e)return yk(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return yk(e,t)}}function yk(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function yE(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function yN(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?yE(Object(r),!0).forEach(function(t){yM(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):yE(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function yM(e,t,r){return(t=yC(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function yC(e){var t=function(e,t){if("object"!=yg(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=yg(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==yg(t)?t:t+""}var yT={xAxis:["bottom","top"],yAxis:["left","right"]},y_={width:"100%",height:"100%"},yI={x:0,y:0};function yD(e){return e}var yR=function(e,t,r,n){var o=t.find(function(e){return e&&e.index===r});if(o){if("horizontal"===e)return{x:o.coordinate,y:n.y};if("vertical"===e)return{x:n.x,y:o.coordinate};if("centric"===e){var i=o.coordinate,a=n.radius;return yN(yN(yN({},n),d$(n.cx,n.cy,a,i)),{},{angle:i,radius:a})}var l=o.coordinate,s=n.angle;return yN(yN(yN({},n),d$(n.cx,n.cy,l,s)),{},{angle:s,radius:l})}return yI},yB=function(e,t){var r=t.graphicalItems,n=t.dataStartIndex,o=t.dataEndIndex,i=(null!=r?r:[]).reduce(function(e,t){var r=t.props.data;return r&&r.length?[].concat(yA(e),yA(r)):e},[]);return i.length>0?i:e&&e.length&&rk(n)&&rk(o)?e.slice(n,o+1):[]};function yL(e){return"number"===e?[0,"auto"]:void 0}var y$=function(e,t,r,n){var o=e.graphicalItems,i=e.tooltipAxis,a=yB(t,e);return r<0||!o||!o.length||r>=a.length?null:o.reduce(function(o,l){var s,c,u=null!==(s=l.props.data)&&void 0!==s?s:t;return(u&&e.dataStartIndex+e.dataEndIndex!==0&&e.dataEndIndex-e.dataStartIndex>=r&&(u=u.slice(e.dataStartIndex,e.dataEndIndex+1)),c=i.dataKey&&!i.allowDuplicatedCategory?rD(void 0===u?a:u,i.dataKey,n):u&&u[r]||a[r])?[].concat(yA(o),[dp(l,c)]):o},[])},yz=function(e,t,r,n){var o=n||{x:e.chartX,y:e.chartY},i="horizontal"===r?o.x:"vertical"===r?o.y:"centric"===r?o.angle:o.radius,a=e.orderedTooltipTicks,l=e.tooltipAxis,s=e.tooltipTicks,c=fZ(i,a,s,l);if(c>=0&&s){var u=s[c]&&s[c].value,f=y$(e,t,c,u),d=yR(r,a,c,o);return{activeTooltipIndex:c,activeLabel:u,activePayload:f,activeCoordinate:d}}return null},yU=function(e,t){var r=t.axes,n=t.graphicalItems,o=t.axisType,i=t.axisIdKey,a=t.stackGroups,l=t.dataStartIndex,s=t.dataEndIndex,c=e.layout,u=e.children,f=e.stackOffset,d=f3(c,o);return r.reduce(function(t,r){var p=void 0!==r.type.defaultProps?yN(yN({},r.type.defaultProps),r.props):r.props,h=p.type,y=p.dataKey,m=p.allowDataOverflow,v=p.allowDuplicatedCategory,g=p.scale,b=p.ticks,x=p.includeHidden,w=p[i];if(t[w])return t;var j=yB(e.data,{graphicalItems:n.filter(function(e){var t;return(i in e.props?e.props[i]:null===(t=e.type.defaultProps)||void 0===t?void 0:t[i])===w}),dataStartIndex:l,dataEndIndex:s}),O=j.length;(function(e,t,r){if("number"===r&&!0===t&&Array.isArray(e)){var n=null==e?void 0:e[0],o=null==e?void 0:e[1];if(n&&o&&rk(n)&&rk(o))return!0}return!1})(p.domain,m,h)&&(P=du(p.domain,null,m),d&&("number"===h||"auto"!==g)&&(E=fK(j,y,"category")));var S=yL(h);if(!P||0===P.length){var A,P,k,E,N,M=null!==(N=p.domain)&&void 0!==N?N:S;if(y){if(P=fK(j,y,h),"category"===h&&d){var C=r_(P);v&&C?(k=P,P=oK()(0,O)):v||(P=dd(M,P,r).reduce(function(e,t){return e.indexOf(t)>=0?e:[].concat(yA(e),[t])},[]))}else if("category"===h)P=v?P.filter(function(e){return""!==e&&!rL()(e)}):dd(M,P,r).reduce(function(e,t){return e.indexOf(t)>=0||""===t||rL()(t)?e:[].concat(yA(e),[t])},[]);else if("number"===h){var T=f5(j,n.filter(function(e){var t,r,n=i in e.props?e.props[i]:null===(t=e.type.defaultProps)||void 0===t?void 0:t[i],o="hide"in e.props?e.props.hide:null===(r=e.type.defaultProps)||void 0===r?void 0:r.hide;return n===w&&(x||!o)}),y,o,c);T&&(P=T)}d&&("number"===h||"auto"!==g)&&(E=fK(j,y,"category"))}else P=d?oK()(0,O):a&&a[w]&&a[w].hasStack&&"number"===h?"expand"===f?[0,1]:dl(a[w].stackGroups,l,s):f4(j,n.filter(function(e){var t=i in e.props?e.props[i]:e.type.defaultProps[i],r="hide"in e.props?e.props.hide:e.type.defaultProps.hide;return t===w&&(x||!r)}),h,c,!0);"number"===h?(P=p6(u,P,w,o,b),M&&(P=du(M,P,m))):"category"===h&&M&&P.every(function(e){return M.indexOf(e)>=0})&&(P=M)}return yN(yN({},t),{},yM({},w,yN(yN({},p),{},{axisType:o,domain:P,categoricalDomain:E,duplicateDomain:k,originalDomain:null!==(A=p.domain)&&void 0!==A?A:S,isCategorical:d,layout:c})))},{})},yF=function(e,t){var r=t.graphicalItems,n=t.Axis,o=t.axisType,i=t.axisIdKey,a=t.stackGroups,l=t.dataStartIndex,s=t.dataEndIndex,c=e.layout,u=e.children,f=yB(e.data,{graphicalItems:r,dataStartIndex:l,dataEndIndex:s}),d=f.length,p=f3(c,o),h=-1;return r.reduce(function(e,t){var y,m=(void 0!==t.type.defaultProps?yN(yN({},t.type.defaultProps),t.props):t.props)[i],v=yL("number");return e[m]?e:(h++,y=p?oK()(0,d):a&&a[m]&&a[m].hasStack?p6(u,y=dl(a[m].stackGroups,l,s),m,o):p6(u,y=du(v,f4(f,r.filter(function(e){var t,r,n=i in e.props?e.props[i]:null===(t=e.type.defaultProps)||void 0===t?void 0:t[i],o="hide"in e.props?e.props.hide:null===(r=e.type.defaultProps)||void 0===r?void 0:r.hide;return n===m&&!o}),"number",c),n.defaultProps.allowDataOverflow),m,o),yN(yN({},e),{},yM({},m,yN(yN({axisType:o},n.defaultProps),{},{hide:!0,orientation:rj()(yT,"".concat(o,".").concat(h%2),null),domain:y,originalDomain:v,isCategorical:p,layout:c}))))},{})},yq=function(e,t){var r=t.axisType,n=void 0===r?"xAxis":r,o=t.AxisComp,i=t.graphicalItems,a=t.stackGroups,l=t.dataStartIndex,s=t.dataEndIndex,c=e.children,u="".concat(n,"Id"),f=r6(c,o),d={};return f&&f.length?d=yU(e,{axes:f,graphicalItems:i,axisType:n,axisIdKey:u,stackGroups:a,dataStartIndex:l,dataEndIndex:s}):i&&i.length&&(d=yF(e,{Axis:o,graphicalItems:i,axisType:n,axisIdKey:u,stackGroups:a,dataStartIndex:l,dataEndIndex:s})),d},yW=function(e){var t=rT(e),r=f6(t,!1,!0);return{tooltipTicks:r,orderedTooltipTicks:nd()(r,function(e){return e.coordinate}),tooltipAxis:t,tooltipAxisBandSize:df(t,r)}},yV=function(e){var t=e.children,r=e.defaultShowTooltip,n=r8(t,dT),o=0,i=0;return e.data&&0!==e.data.length&&(i=e.data.length-1),n&&n.props&&(n.props.startIndex>=0&&(o=n.props.startIndex),n.props.endIndex>=0&&(i=n.props.endIndex)),{chartX:0,chartY:0,dataStartIndex:o,dataEndIndex:i,activeTooltipIndex:-1,isTooltipActive:!!r}},yH=function(e){return"horizontal"===e?{numericAxisName:"yAxis",cateAxisName:"xAxis"}:"vertical"===e?{numericAxisName:"xAxis",cateAxisName:"yAxis"}:"centric"===e?{numericAxisName:"radiusAxis",cateAxisName:"angleAxis"}:{numericAxisName:"angleAxis",cateAxisName:"radiusAxis"}},yG=function(e,t){var r=e.props,n=e.graphicalItems,o=e.xAxisMap,i=void 0===o?{}:o,a=e.yAxisMap,l=void 0===a?{}:a,s=r.width,c=r.height,u=r.children,f=r.margin||{},d=r8(u,dT),p=r8(u,oz),h=Object.keys(l).reduce(function(e,t){var r=l[t],n=r.orientation;return r.mirror||r.hide?e:yN(yN({},e),{},yM({},n,e[n]+r.width))},{left:f.left||0,right:f.right||0}),y=Object.keys(i).reduce(function(e,t){var r=i[t],n=r.orientation;return r.mirror||r.hide?e:yN(yN({},e),{},yM({},n,rj()(e,"".concat(n))+r.height))},{top:f.top||0,bottom:f.bottom||0}),m=yN(yN({},y),h),v=m.bottom;d&&(m.bottom+=d.props.height||dT.defaultProps.height),p&&t&&(m=f1(m,n,r,t));var g=s-m.left-m.right,b=c-m.top-m.bottom;return yN(yN({brushBottom:v},m),{},{width:Math.max(g,0),height:Math.max(b,0)})},yX=["points","className","baseLinePoints","connectNulls"];function yY(){return(yY=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function yK(e){return function(e){if(Array.isArray(e))return yZ(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return yZ(e,void 0);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return yZ(e,t)}}(e)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function yZ(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}var yJ=function(e){return e&&e.x===+e.x&&e.y===+e.y},yQ=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=[[]];return e.forEach(function(e){yJ(e)?t[t.length-1].push(e):t[t.length-1].length>0&&t.push([])}),yJ(e[0])&&t[t.length-1].push(e[0]),t[t.length-1].length<=0&&(t=t.slice(0,-1)),t},y0=function(e,t){var r=yQ(e);t&&(r=[r.reduce(function(e,t){return[].concat(yK(e),yK(t))},[])]);var n=r.map(function(e){return e.reduce(function(e,t,r){return"".concat(e).concat(0===r?"M":"L").concat(t.x,",").concat(t.y)},"")}).join("");return 1===r.length?"".concat(n,"Z"):n},y1=function(e,t,r){var n=y0(e,r);return"".concat("Z"===n.slice(-1)?n.slice(0,-1):n,"L").concat(y0(t.reverse(),r).slice(1))},y2=function(e){var t=e.points,r=e.className,n=e.baseLinePoints,o=e.connectNulls,i=function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(e,yX);if(!t||!t.length)return null;var l=(0,rh.A)("recharts-polygon",r);if(n&&n.length){var s=i.stroke&&"none"!==i.stroke,c=y1(t,n,o);return a().createElement("g",{className:l},a().createElement("path",yY({},nt(i,!0),{fill:"Z"===c.slice(-1)?i.fill:"none",stroke:"none",d:c})),s?a().createElement("path",yY({},nt(i,!0),{fill:"none",d:y0(t,o)})):null,s?a().createElement("path",yY({},nt(i,!0),{fill:"none",d:y0(n,o)})):null)}var u=y0(t,o);return a().createElement("path",yY({},nt(i,!0),{fill:"Z"===u.slice(-1)?i.fill:"none",className:l,d:u}))};function y5(e){return(y5="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function y4(){return(y4=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function y3(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function y6(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?y3(Object(r),!0).forEach(function(t){mt(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):y3(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function y8(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,mr(n.key),n)}}function y7(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(y7=function(){return!!e})()}function y9(e){return(y9=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function me(e,t){return(me=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function mt(e,t,r){return(t=mr(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function mr(e){var t=function(e,t){if("object"!=y5(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=y5(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==y5(t)?t:t+""}var mn=Math.PI/180,mo=function(e){var t,r;function n(){var e,t;return function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}(this,n),e=n,t=arguments,e=y9(e),function(e,t){if(t&&("object"===y5(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,y7()?Reflect.construct(e,t||[],y9(this).constructor):e.apply(this,t))}return function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&me(e,t)}(n,e),t=[{key:"getTickLineCoord",value:function(e){var t=this.props,r=t.cx,n=t.cy,o=t.radius,i=t.orientation,a=t.tickSize,l=d$(r,n,o,e.coordinate),s=d$(r,n,o+("inner"===i?-1:1)*(a||8),e.coordinate);return{x1:l.x,y1:l.y,x2:s.x,y2:s.y}}},{key:"getTickTextAnchor",value:function(e){var t=this.props.orientation,r=Math.cos(-e.coordinate*mn);return r>1e-5?"outer"===t?"start":"end":r<-1e-5?"outer"===t?"end":"start":"middle"}},{key:"renderAxisLine",value:function(){var e=this.props,t=e.cx,r=e.cy,n=e.radius,o=e.axisLine,i=e.axisLineType,l=y6(y6({},nt(this.props,!1)),{},{fill:"none"},nt(o,!1));if("circle"===i)return a().createElement(o2,y4({className:"recharts-polar-angle-axis-line"},l,{cx:t,cy:r,r:n}));var s=this.props.ticks.map(function(e){return d$(t,r,n,e.coordinate)});return a().createElement(y2,y4({className:"recharts-polar-angle-axis-line"},l,{points:s}))}},{key:"renderTicks",value:function(){var e=this,t=this.props,r=t.ticks,o=t.tick,i=t.tickLine,l=t.tickFormatter,s=t.stroke,c=nt(this.props,!1),u=nt(o,!1),f=y6(y6({},c),{},{fill:"none"},nt(i,!1)),d=r.map(function(t,r){var d=e.getTickLineCoord(t),p=y6(y6(y6({textAnchor:e.getTickTextAnchor(t)},c),{},{stroke:"none",fill:s},u),{},{index:r,payload:t,x:d.x2,y:d.y2});return a().createElement(o0,y4({className:(0,rh.A)("recharts-polar-angle-axis-tick",dV(o)),key:"tick-".concat(t.coordinate)},rZ(e.props,t,r)),i&&a().createElement("line",y4({className:"recharts-polar-angle-axis-tick-line"},f,d)),o&&n.renderTickItem(o,p,l?l(t.value,r):t.value))});return a().createElement(o0,{className:"recharts-polar-angle-axis-ticks"},d)}},{key:"render",value:function(){var e=this.props,t=e.ticks,r=e.radius,n=e.axisLine;return!(r<=0)&&t&&t.length?a().createElement(o0,{className:(0,rh.A)("recharts-polar-angle-axis",this.props.className)},n&&this.renderAxisLine(),this.renderTicks()):null}}],r=[{key:"renderTickItem",value:function(e,t,r){var n;return a().isValidElement(e)?a().cloneElement(e,t):rz()(e)?e(t):a().createElement(a5,y4({},t,{className:"recharts-polar-angle-axis-tick-value"}),r)}}],t&&y8(n.prototype,t),r&&y8(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(i.PureComponent);mt(mo,"displayName","PolarAngleAxis"),mt(mo,"axisType","angleAxis"),mt(mo,"defaultProps",{type:"category",angleAxisId:0,scale:"auto",cx:0,cy:0,orientation:"outer",axisLine:!0,tickLine:!0,tickSize:8,tick:!0,hide:!1,allowDuplicatedCategory:!0});var mi=r(57088),ma=r.n(mi),ml=r(10034),ms=r.n(ml),mc=["cx","cy","angle","ticks","axisLine"],mu=["ticks","tick","angle","tickFormatter","stroke"];function mf(e){return(mf="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function md(){return(md=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function mp(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function mh(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?mp(Object(r),!0).forEach(function(t){mx(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):mp(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function my(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}function mm(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,mw(n.key),n)}}function mv(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(mv=function(){return!!e})()}function mg(e){return(mg=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function mb(e,t){return(mb=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function mx(e,t,r){return(t=mw(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function mw(e){var t=function(e,t){if("object"!=mf(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=mf(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==mf(t)?t:t+""}var mj=function(e){var t,r;function n(){var e,t;return function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}(this,n),e=n,t=arguments,e=mg(e),function(e,t){if(t&&("object"===mf(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,mv()?Reflect.construct(e,t||[],mg(this).constructor):e.apply(this,t))}return function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&mb(e,t)}(n,e),t=[{key:"getTickValueCoord",value:function(e){var t=e.coordinate,r=this.props,n=r.angle;return d$(r.cx,r.cy,t,n)}},{key:"getTickTextAnchor",value:function(){var e;switch(this.props.orientation){case"left":e="end";break;case"right":e="start";break;default:e="middle"}return e}},{key:"getViewBox",value:function(){var e=this.props,t=e.cx,r=e.cy,n=e.angle,o=e.ticks,i=ma()(o,function(e){return e.coordinate||0});return{cx:t,cy:r,startAngle:n,endAngle:n,innerRadius:ms()(o,function(e){return e.coordinate||0}).coordinate||0,outerRadius:i.coordinate||0}}},{key:"renderAxisLine",value:function(){var e=this.props,t=e.cx,r=e.cy,n=e.angle,o=e.ticks,i=e.axisLine,l=my(e,mc),s=o.reduce(function(e,t){return[Math.min(e[0],t.coordinate),Math.max(e[1],t.coordinate)]},[1/0,-1/0]),c=d$(t,r,s[0],n),u=d$(t,r,s[1],n),f=mh(mh(mh({},nt(l,!1)),{},{fill:"none"},nt(i,!1)),{},{x1:c.x,y1:c.y,x2:u.x,y2:u.y});return a().createElement("line",md({className:"recharts-polar-radius-axis-line"},f))}},{key:"renderTicks",value:function(){var e=this,t=this.props,r=t.ticks,o=t.tick,i=t.angle,l=t.tickFormatter,s=t.stroke,c=my(t,mu),u=this.getTickTextAnchor(),f=nt(c,!1),d=nt(o,!1),p=r.map(function(t,r){var c=e.getTickValueCoord(t),p=mh(mh(mh(mh({textAnchor:u,transform:"rotate(".concat(90-i,", ").concat(c.x,", ").concat(c.y,")")},f),{},{stroke:"none",fill:s},d),{},{index:r},c),{},{payload:t});return a().createElement(o0,md({className:(0,rh.A)("recharts-polar-radius-axis-tick",dV(o)),key:"tick-".concat(t.coordinate)},rZ(e.props,t,r)),n.renderTickItem(o,p,l?l(t.value,r):t.value))});return a().createElement(o0,{className:"recharts-polar-radius-axis-ticks"},p)}},{key:"render",value:function(){var e=this.props,t=e.ticks,r=e.axisLine,n=e.tick;return t&&t.length?a().createElement(o0,{className:(0,rh.A)("recharts-polar-radius-axis",this.props.className)},r&&this.renderAxisLine(),n&&this.renderTicks(),d2.renderCallByParent(this.props,this.getViewBox())):null}}],r=[{key:"renderTickItem",value:function(e,t,r){var n;return a().isValidElement(e)?a().cloneElement(e,t):rz()(e)?e(t):a().createElement(a5,md({},t,{className:"recharts-polar-radius-axis-tick-value"}),r)}}],t&&mm(n.prototype,t),r&&mm(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(i.PureComponent);mx(mj,"displayName","PolarRadiusAxis"),mx(mj,"axisType","radiusAxis"),mx(mj,"defaultProps",{type:"number",radiusAxisId:0,cx:0,cy:0,angle:0,orientation:"right",stroke:"#ccc",axisLine:!0,tick:!0,tickCount:5,allowDataOverflow:!1,scale:"auto",allowDuplicatedCategory:!0});var mO=r(5359),mS=r.n(mO);function mA(e){return(mA="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var mP=["valueAccessor"],mk=["data","dataKey","clockWise","id","textBreakAll"];function mE(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function mN(){return(mN=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function mM(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function mC(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?mM(Object(r),!0).forEach(function(t){var n,o,i;n=e,o=t,i=r[t],(o=function(e){var t=function(e,t){if("object"!=mA(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=mA(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==mA(t)?t:t+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):mM(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function mT(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}var m_=function(e){return Array.isArray(e.value)?mS()(e.value):e.value};function mI(e){var t=e.valueAccessor,r=void 0===t?m_:t,n=mT(e,mP),o=n.data,i=n.dataKey,l=n.clockWise,s=n.id,c=n.textBreakAll,u=mT(n,mk);return o&&o.length?a().createElement(o0,{className:"recharts-label-list"},o.map(function(e,t){var n=rL()(i)?r(e,t):fY(e&&e.payload,i),o=rL()(s)?{}:{id:"".concat(s,"-").concat(t)};return a().createElement(d2,mN({},nt(e,!0),u,o,{parentViewBox:e.parentViewBox,value:n,textBreakAll:c,viewBox:d2.parseViewBox(rL()(l)?e:mC(mC({},e),{},{clockWise:l})),key:"label-".concat(t),index:t}))})):null}mI.displayName="LabelList",mI.renderCallByParent=function(e,t){var r,n=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(!e||!e.children&&n&&!e.label)return null;var o=r6(e.children,mI).map(function(e,r){return(0,i.cloneElement)(e,{data:t,key:"labelList-".concat(r)})});return n?[(r=e.label)?!0===r?a().createElement(mI,{key:"labelList-implicit",data:t}):a().isValidElement(r)||rz()(r)?a().createElement(mI,{key:"labelList-implicit",data:t,content:r}):rF()(r)?a().createElement(mI,mN({data:t},r,{key:"labelList-implicit"})):null:null].concat(function(e){if(Array.isArray(e))return mE(e)}(o)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(o)||function(e,t){if(e){if("string"==typeof e)return mE(e,void 0);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return mE(e,t)}}(o)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()):o};var mD=function(e){return null};function mR(e){return(mR="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function mB(){return(mB=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function mL(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function m$(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?mL(Object(r),!0).forEach(function(t){mW(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):mL(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function mz(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,mV(n.key),n)}}function mU(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(mU=function(){return!!e})()}function mF(e){return(mF=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function mq(e,t){return(mq=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function mW(e,t,r){return(t=mV(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function mV(e){var t=function(e,t){if("object"!=mR(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=mR(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==mR(t)?t:t+""}mD.displayName="Cell";var mH=function(e){var t,r;function n(e){var t,r,o;return function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}(this,n),r=n,o=[e],r=mF(r),mW(t=function(e,t){if(t&&("object"===mR(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,mU()?Reflect.construct(r,o||[],mF(this).constructor):r.apply(this,o)),"pieRef",null),mW(t,"sectorRefs",[]),mW(t,"id",rM("recharts-pie-")),mW(t,"handleAnimationEnd",function(){var e=t.props.onAnimationEnd;t.setState({isAnimationFinished:!0}),rz()(e)&&e()}),mW(t,"handleAnimationStart",function(){var e=t.props.onAnimationStart;t.setState({isAnimationFinished:!1}),rz()(e)&&e()}),t.state={isAnimationFinished:!e.isAnimationActive,prevIsAnimationActive:e.isAnimationActive,prevAnimationId:e.animationId,sectorToFocus:0},t}return function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&mq(e,t)}(n,e),t=[{key:"isActiveIndex",value:function(e){var t=this.props.activeIndex;return Array.isArray(t)?-1!==t.indexOf(e):e===t}},{key:"hasActiveIndex",value:function(){var e=this.props.activeIndex;return Array.isArray(e)?0!==e.length:e||0===e}},{key:"renderLabels",value:function(e){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var t=this.props,r=t.label,o=t.labelLine,i=t.dataKey,l=t.valueKey,s=nt(this.props,!1),c=nt(r,!1),u=nt(o,!1),f=r&&r.offsetRadius||20,d=e.map(function(e,t){var d=(e.startAngle+e.endAngle)/2,p=d$(e.cx,e.cy,e.outerRadius+f,d),h=m$(m$(m$(m$({},s),e),{},{stroke:"none"},c),{},{index:t,textAnchor:n.getTextAnchor(p.x,e.cx)},p),y=m$(m$(m$(m$({},s),e),{},{fill:"none",stroke:e.fill},u),{},{index:t,points:[d$(e.cx,e.cy,e.outerRadius,d),p]}),m=i;return rL()(i)&&rL()(l)?m="value":rL()(i)&&(m=l),a().createElement(o0,{key:"label-".concat(e.startAngle,"-").concat(e.endAngle,"-").concat(e.midAngle,"-").concat(t)},o&&n.renderLabelLineItem(o,y,"line"),n.renderLabelItem(r,h,fY(e,m)))});return a().createElement(o0,{className:"recharts-pie-labels"},d)}},{key:"renderSectorsStatically",value:function(e){var t=this,r=this.props,n=r.activeShape,o=r.blendStroke,i=r.inactiveShape;return e.map(function(r,l){if((null==r?void 0:r.startAngle)===0&&(null==r?void 0:r.endAngle)===0&&1!==e.length)return null;var s=t.isActiveIndex(l),c=i&&t.hasActiveIndex()?i:null,u=m$(m$({},r),{},{stroke:o?r.fill:r.stroke,tabIndex:-1});return a().createElement(o0,mB({ref:function(e){e&&!t.sectorRefs.includes(e)&&t.sectorRefs.push(e)},tabIndex:-1,className:"recharts-pie-sector"},rZ(t.props,r,l),{key:"sector-".concat(null==r?void 0:r.startAngle,"-").concat(null==r?void 0:r.endAngle,"-").concat(r.midAngle,"-").concat(l)}),a().createElement(hM,mB({option:s?n:c,isActive:s,shapeType:"sector"},u)))})}},{key:"renderSectorsWithAnimation",value:function(){var e=this,t=this.props,r=t.sectors,n=t.isAnimationActive,o=t.animationBegin,i=t.animationDuration,l=t.animationEasing,s=t.animationId,c=this.state,u=c.prevSectors,f=c.prevIsAnimationActive;return a().createElement(ao,{begin:o,duration:i,isActive:n,easing:l,from:{t:0},to:{t:1},key:"pie-".concat(s,"-").concat(f),onAnimationStart:this.handleAnimationStart,onAnimationEnd:this.handleAnimationEnd},function(t){var n=t.t,o=[],i=(r&&r[0]).startAngle;return r.forEach(function(e,t){var r=u&&u[t],a=t>0?rj()(e,"paddingAngle",0):0;if(r){var l=rI(r.endAngle-r.startAngle,e.endAngle-e.startAngle),s=m$(m$({},e),{},{startAngle:i+a,endAngle:i+l(n)+a});o.push(s),i=s.endAngle}else{var c=rI(0,e.endAngle-e.startAngle)(n),f=m$(m$({},e),{},{startAngle:i+a,endAngle:i+c+a});o.push(f),i=f.endAngle}}),a().createElement(o0,null,e.renderSectorsStatically(o))})}},{key:"attachKeyboardHandlers",value:function(e){var t=this;e.onkeydown=function(e){if(!e.altKey)switch(e.key){case"ArrowLeft":var r=++t.state.sectorToFocus%t.sectorRefs.length;t.sectorRefs[r].focus(),t.setState({sectorToFocus:r});break;case"ArrowRight":var n=--t.state.sectorToFocus<0?t.sectorRefs.length-1:t.state.sectorToFocus%t.sectorRefs.length;t.sectorRefs[n].focus(),t.setState({sectorToFocus:n});break;case"Escape":t.sectorRefs[t.state.sectorToFocus].blur(),t.setState({sectorToFocus:0})}}}},{key:"renderSectors",value:function(){var e=this.props,t=e.sectors,r=e.isAnimationActive,n=this.state.prevSectors;return r&&t&&t.length&&(!n||!uH()(n,t))?this.renderSectorsWithAnimation():this.renderSectorsStatically(t)}},{key:"componentDidMount",value:function(){this.pieRef&&this.attachKeyboardHandlers(this.pieRef)}},{key:"render",value:function(){var e=this,t=this.props,r=t.hide,n=t.sectors,o=t.className,i=t.label,l=t.cx,s=t.cy,c=t.innerRadius,u=t.outerRadius,f=t.isAnimationActive,d=this.state.isAnimationFinished;if(r||!n||!n.length||!rk(l)||!rk(s)||!rk(c)||!rk(u))return null;var p=(0,rh.A)("recharts-pie",o);return a().createElement(o0,{tabIndex:this.props.rootTabIndex,className:p,ref:function(t){e.pieRef=t}},this.renderSectors(),i&&this.renderLabels(n),d2.renderCallByParent(this.props,null,!1),(!f||d)&&mI.renderCallByParent(this.props,n,!1))}}],r=[{key:"getDerivedStateFromProps",value:function(e,t){return t.prevIsAnimationActive!==e.isAnimationActive?{prevIsAnimationActive:e.isAnimationActive,prevAnimationId:e.animationId,curSectors:e.sectors,prevSectors:[],isAnimationFinished:!0}:e.isAnimationActive&&e.animationId!==t.prevAnimationId?{prevAnimationId:e.animationId,curSectors:e.sectors,prevSectors:t.curSectors,isAnimationFinished:!0}:e.sectors!==t.curSectors?{curSectors:e.sectors,isAnimationFinished:!0}:null}},{key:"getTextAnchor",value:function(e,t){return e>t?"start":e<t?"end":"middle"}},{key:"renderLabelLineItem",value:function(e,t,r){if(a().isValidElement(e))return a().cloneElement(e,t);if(rz()(e))return e(t);var n=(0,rh.A)("recharts-pie-label-line","boolean"!=typeof e?e.className:"");return a().createElement(yi,mB({},t,{key:r,type:"linear",className:n}))}},{key:"renderLabelItem",value:function(e,t,r){if(a().isValidElement(e))return a().cloneElement(e,t);var n=r;if(rz()(e)&&(n=e(t),a().isValidElement(n)))return n;var o=(0,rh.A)("recharts-pie-label-text","boolean"==typeof e||rz()(e)?"":e.className);return a().createElement(a5,mB({},t,{alignmentBaseline:"middle",className:o}),n)}}],t&&mz(n.prototype,t),r&&mz(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(i.PureComponent);mW(mH,"displayName","Pie"),mW(mH,"defaultProps",{stroke:"#fff",fill:"#808080",legendType:"rect",cx:"50%",cy:"50%",startAngle:0,endAngle:360,innerRadius:0,outerRadius:"80%",paddingAngle:0,labelLine:!0,hide:!1,minAngle:0,isAnimationActive:!nI.isSsr,animationBegin:400,animationDuration:1500,animationEasing:"ease",nameKey:"name",blendStroke:!1,rootTabIndex:0}),mW(mH,"parseDeltaAngle",function(e,t){return rA(t-e)*Math.min(Math.abs(t-e),360)}),mW(mH,"getRealPieData",function(e){var t=e.data,r=e.children,n=nt(e,!1),o=r6(r,mD);return t&&t.length?t.map(function(e,t){return m$(m$(m$({payload:e},n),e),o&&o[t]&&o[t].props)}):o&&o.length?o.map(function(e){return m$(m$({},n),e.props)}):[]}),mW(mH,"parseCoordinateOfPie",function(e,t){var r=t.top,n=t.left,o=t.width,i=t.height,a=dz(o,i);return{cx:n+rC(e.cx,o,o/2),cy:r+rC(e.cy,i,i/2),innerRadius:rC(e.innerRadius,a,0),outerRadius:rC(e.outerRadius,a,.8*a),maxRadius:e.maxRadius||Math.sqrt(o*o+i*i)/2}}),mW(mH,"getComposedData",function(e){var t,r,n=e.item,o=e.offset,i=void 0!==n.type.defaultProps?m$(m$({},n.type.defaultProps),n.props):n.props,a=mH.getRealPieData(i);if(!a||!a.length)return null;var l=i.cornerRadius,s=i.startAngle,c=i.endAngle,u=i.paddingAngle,f=i.dataKey,d=i.nameKey,p=i.valueKey,h=i.tooltipType,y=Math.abs(i.minAngle),m=mH.parseCoordinateOfPie(i,o),v=mH.parseDeltaAngle(s,c),g=Math.abs(v),b=f;rL()(f)&&rL()(p)?(rR(!1,'Use "dataKey" to specify the value of pie,\n      the props "valueKey" will be deprecated in 1.1.0'),b="value"):rL()(f)&&(rR(!1,'Use "dataKey" to specify the value of pie,\n      the props "valueKey" will be deprecated in 1.1.0'),b=p);var x=a.filter(function(e){return 0!==fY(e,b,0)}).length,w=g-x*y-(g>=360?x:x-1)*u,j=a.reduce(function(e,t){var r=fY(t,b,0);return e+(rk(r)?r:0)},0);return j>0&&(t=a.map(function(e,t){var n,o=fY(e,b,0),i=fY(e,d,t),a=(rk(o)?o:0)/j,c=(n=t?r.endAngle+rA(v)*u*+(0!==o):s)+rA(v)*((0!==o?y:0)+a*w),f=(n+c)/2,p=(m.innerRadius+m.outerRadius)/2,g=[{name:i,value:o,payload:e,dataKey:b,type:h}],x=d$(m.cx,m.cy,p,f);return r=m$(m$(m$({percent:a,cornerRadius:l,name:i,tooltipPayload:g,midAngle:f,middleRadius:p,tooltipPosition:x},e),m),{},{value:fY(e,b),startAngle:n,endAngle:c,payload:e,paddingAngle:rA(v)*u})})),m$(m$({},m),{},{sectors:t,data:a})});var mG=function(e){var t=e.chartName,r=e.GraphicalChild,n=e.defaultTooltipEventType,o=void 0===n?"axis":n,l=e.validateTooltipEventTypes,s=void 0===l?["axis"]:l,c=e.axisComponents,u=e.legendContent,f=e.formatAxisMap,d=e.defaultProps,p=function(e,t){var r=t.graphicalItems,n=t.stackGroups,o=t.offset,i=t.updateId,a=t.dataStartIndex,l=t.dataEndIndex,s=e.barSize,u=e.layout,f=e.barGap,d=e.barCategoryGap,p=e.maxBarSize,h=yH(u),y=h.numericAxisName,m=h.cateAxisName,v=!!r&&!!r.length&&r.some(function(e){var t=r2(e&&e.type);return t&&t.indexOf("Bar")>=0}),g=[];return r.forEach(function(r,h){var b=yB(e.data,{graphicalItems:[r],dataStartIndex:a,dataEndIndex:l}),x=void 0!==r.type.defaultProps?yN(yN({},r.type.defaultProps),r.props):r.props,w=x.dataKey,j=x.maxBarSize,O=x["".concat(y,"Id")],S=x["".concat(m,"Id")],A=c.reduce(function(e,r){var n=t["".concat(r.axisType,"Map")],o=x["".concat(r.axisType,"Id")];n&&n[o]||"zAxis"===r.axisType||oZ(!1);var i=n[o];return yN(yN({},e),{},yM(yM({},r.axisType,i),"".concat(r.axisType,"Ticks"),f6(i)))},{}),P=A[m],k=A["".concat(m,"Ticks")],E=n&&n[O]&&n[O].hasStack&&da(r,n[O].stackGroups),N=r2(r.type).indexOf("Bar")>=0,M=df(P,k),C=[],T=v&&fQ({barSize:s,stackGroups:n,totalSize:"xAxis"===m?A[m].width:"yAxis"===m?A[m].height:void 0});if(N){var _,I,D=rL()(j)?p:j,R=null!==(_=null!==(I=df(P,k,!0))&&void 0!==I?I:D)&&void 0!==_?_:0;C=f0({barGap:f,barCategoryGap:d,bandSize:R!==M?R:M,sizeList:T[S],maxBarSize:D}),R!==M&&(C=C.map(function(e){return yN(yN({},e),{},{position:yN(yN({},e.position),{},{offset:e.position.offset-R/2})})}))}var B=r&&r.type&&r.type.getComposedData;B&&g.push({props:yN(yN({},B(yN(yN({},A),{},{displayedData:b,props:e,dataKey:w,item:r,bandSize:M,barPosition:C,offset:o,stackedData:E,layout:u,dataStartIndex:a,dataEndIndex:l}))),{},yM(yM(yM({key:r.key||"item-".concat(h)},y,A[y]),m,A[m]),"animationId",i)),childIndex:r3(e.children).indexOf(r),item:r})}),g},h=function(e,n){var o=e.props,i=e.dataStartIndex,a=e.dataEndIndex,l=e.updateId;if(!r7({props:o}))return null;var s=o.children,u=o.layout,d=o.stackOffset,h=o.data,y=o.reverseStackOrder,m=yH(u),v=m.numericAxisName,g=m.cateAxisName,b=r6(s,r),x=dn(h,b,"".concat(v,"Id"),"".concat(g,"Id"),d,y),w=c.reduce(function(e,t){var r="".concat(t.axisType,"Map");return yN(yN({},e),{},yM({},r,yq(o,yN(yN({},t),{},{graphicalItems:b,stackGroups:t.axisType===v&&x,dataStartIndex:i,dataEndIndex:a}))))},{}),j=yG(yN(yN({},w),{},{props:o,graphicalItems:b}),null==n?void 0:n.legendBBox);Object.keys(w).forEach(function(e){w[e]=f(o,w[e],j,e.replace("Map",""),t)});var O=yW(w["".concat(g,"Map")]),S=p(o,yN(yN({},w),{},{dataStartIndex:i,dataEndIndex:a,updateId:l,graphicalItems:b,stackGroups:x,offset:j}));return yN(yN({formattedGraphicalItems:S,graphicalItems:b,offset:j,stackGroups:x},O),w)},y=function(e){var r;function n(e){var r,o,l,s,c;return function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}(this,n),s=n,c=[e],s=yO(s),yM(l=function(e,t){if(t&&("object"===yg(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,yj()?Reflect.construct(s,c||[],yO(this).constructor):s.apply(this,c)),"eventEmitterSymbol",Symbol("rechartsEventEmitter")),yM(l,"accessibilityManager",new hn),yM(l,"handleLegendBBoxUpdate",function(e){if(e){var t=l.state,r=t.dataStartIndex,n=t.dataEndIndex,o=t.updateId;l.setState(yN({legendBBox:e},h({props:l.props,dataStartIndex:r,dataEndIndex:n,updateId:o},yN(yN({},l.state),{},{legendBBox:e}))))}}),yM(l,"handleReceiveSyncEvent",function(e,t,r){l.props.syncId===e&&(r!==l.eventEmitterSymbol||"function"==typeof l.props.syncMethod)&&l.applySyncEvent(t)}),yM(l,"handleBrushChange",function(e){var t=e.startIndex,r=e.endIndex;if(t!==l.state.dataStartIndex||r!==l.state.dataEndIndex){var n=l.state.updateId;l.setState(function(){return yN({dataStartIndex:t,dataEndIndex:r},h({props:l.props,dataStartIndex:t,dataEndIndex:r,updateId:n},l.state))}),l.triggerSyncEvent({dataStartIndex:t,dataEndIndex:r})}}),yM(l,"handleMouseEnter",function(e){var t=l.getMouseInfo(e);if(t){var r=yN(yN({},t),{},{isTooltipActive:!0});l.setState(r),l.triggerSyncEvent(r);var n=l.props.onMouseEnter;rz()(n)&&n(r,e)}}),yM(l,"triggeredAfterMouseMove",function(e){var t=l.getMouseInfo(e),r=t?yN(yN({},t),{},{isTooltipActive:!0}):{isTooltipActive:!1};l.setState(r),l.triggerSyncEvent(r);var n=l.props.onMouseMove;rz()(n)&&n(r,e)}),yM(l,"handleItemMouseEnter",function(e){l.setState(function(){return{isTooltipActive:!0,activeItem:e,activePayload:e.tooltipPayload,activeCoordinate:e.tooltipPosition||{x:e.cx,y:e.cy}}})}),yM(l,"handleItemMouseLeave",function(){l.setState(function(){return{isTooltipActive:!1}})}),yM(l,"handleMouseMove",function(e){e.persist(),l.throttleTriggeredAfterMouseMove(e)}),yM(l,"handleMouseLeave",function(e){l.throttleTriggeredAfterMouseMove.cancel();var t={isTooltipActive:!1};l.setState(t),l.triggerSyncEvent(t);var r=l.props.onMouseLeave;rz()(r)&&r(t,e)}),yM(l,"handleOuterEvent",function(e){var t,r,n=ni(e),o=rj()(l.props,"".concat(n));n&&rz()(o)&&o(null!==(t=/.*touch.*/i.test(n)?l.getMouseInfo(e.changedTouches[0]):l.getMouseInfo(e))&&void 0!==t?t:{},e)}),yM(l,"handleClick",function(e){var t=l.getMouseInfo(e);if(t){var r=yN(yN({},t),{},{isTooltipActive:!0});l.setState(r),l.triggerSyncEvent(r);var n=l.props.onClick;rz()(n)&&n(r,e)}}),yM(l,"handleMouseDown",function(e){var t=l.props.onMouseDown;rz()(t)&&t(l.getMouseInfo(e),e)}),yM(l,"handleMouseUp",function(e){var t=l.props.onMouseUp;rz()(t)&&t(l.getMouseInfo(e),e)}),yM(l,"handleTouchMove",function(e){null!=e.changedTouches&&e.changedTouches.length>0&&l.throttleTriggeredAfterMouseMove(e.changedTouches[0])}),yM(l,"handleTouchStart",function(e){null!=e.changedTouches&&e.changedTouches.length>0&&l.handleMouseDown(e.changedTouches[0])}),yM(l,"handleTouchEnd",function(e){null!=e.changedTouches&&e.changedTouches.length>0&&l.handleMouseUp(e.changedTouches[0])}),yM(l,"handleDoubleClick",function(e){var t=l.props.onDoubleClick;rz()(t)&&t(l.getMouseInfo(e),e)}),yM(l,"handleContextMenu",function(e){var t=l.props.onContextMenu;rz()(t)&&t(l.getMouseInfo(e),e)}),yM(l,"triggerSyncEvent",function(e){void 0!==l.props.syncId&&p7.emit(p9,l.props.syncId,e,l.eventEmitterSymbol)}),yM(l,"applySyncEvent",function(e){var t=l.props,r=t.layout,n=t.syncMethod,o=l.state.updateId,i=e.dataStartIndex,a=e.dataEndIndex;if(void 0!==e.dataStartIndex||void 0!==e.dataEndIndex)l.setState(yN({dataStartIndex:i,dataEndIndex:a},h({props:l.props,dataStartIndex:i,dataEndIndex:a,updateId:o},l.state)));else if(void 0!==e.activeTooltipIndex){var s=e.chartX,c=e.chartY,u=e.activeTooltipIndex,f=l.state,d=f.offset,p=f.tooltipTicks;if(!d)return;if("function"==typeof n)u=n(p,e);else if("value"===n){u=-1;for(var y=0;y<p.length;y++)if(p[y].value===e.activeLabel){u=y;break}}var m=yN(yN({},d),{},{x:d.left,y:d.top}),v=Math.min(s,m.x+m.width),g=Math.min(c,m.y+m.height),b=p[u]&&p[u].value,x=y$(l.state,l.props.data,u),w=p[u]?{x:"horizontal"===r?p[u].coordinate:v,y:"horizontal"===r?g:p[u].coordinate}:yI;l.setState(yN(yN({},e),{},{activeLabel:b,activeCoordinate:w,activePayload:x,activeTooltipIndex:u}))}else l.setState(e)}),yM(l,"renderCursor",function(e){var r,n=l.state,o=n.isTooltipActive,i=n.activeCoordinate,s=n.activePayload,c=n.offset,u=n.activeTooltipIndex,f=n.tooltipAxisBandSize,d=l.getTooltipEventType(),p=null!==(r=e.props.active)&&void 0!==r?r:o,h=l.props.layout,y=e.key||"_recharts-cursor";return a().createElement(yy,{key:y,activeCoordinate:i,activePayload:s,activeTooltipIndex:u,chartName:t,element:e,isActive:p,layout:h,offset:c,tooltipAxisBandSize:f,tooltipEventType:d})}),yM(l,"renderPolarAxis",function(e,t,r){var n=rj()(e,"type.axisType"),o=rj()(l.state,"".concat(n,"Map")),a=e.type.defaultProps,s=void 0!==a?yN(yN({},a),e.props):e.props,c=o&&o[s["".concat(n,"Id")]];return(0,i.cloneElement)(e,yN(yN({},c),{},{className:(0,rh.A)(n,c.className),key:e.key||"".concat(t,"-").concat(r),ticks:f6(c,!0)}))}),yM(l,"renderPolarGrid",function(e){var t=e.props,r=t.radialLines,n=t.polarAngles,o=t.polarRadius,a=l.state,s=a.radiusAxisMap,c=a.angleAxisMap,u=rT(s),f=rT(c),d=f.cx,p=f.cy,h=f.innerRadius,y=f.outerRadius;return(0,i.cloneElement)(e,{polarAngles:Array.isArray(n)?n:f6(f,!0).map(function(e){return e.coordinate}),polarRadius:Array.isArray(o)?o:f6(u,!0).map(function(e){return e.coordinate}),cx:d,cy:p,innerRadius:h,outerRadius:y,key:e.key||"polar-grid",radialLines:r})}),yM(l,"renderLegend",function(){var e=l.state.formattedGraphicalItems,t=l.props,r=t.children,n=t.width,o=t.height,a=l.props.margin||{},s=fF({children:r,formattedGraphicalItems:e,legendWidth:n-(a.left||0)-(a.right||0),legendContent:u});if(!s)return null;var c=s.item,f=yw(s,ym);return(0,i.cloneElement)(c,yN(yN({},f),{},{chartWidth:n,chartHeight:o,margin:a,onBBoxUpdate:l.handleLegendBBoxUpdate}))}),yM(l,"renderTooltip",function(){var e,t=l.props,r=t.children,n=t.accessibilityLayer,o=r8(r,nG);if(!o)return null;var a=l.state,s=a.isTooltipActive,c=a.activeCoordinate,u=a.activePayload,f=a.activeLabel,d=a.offset,p=null!==(e=o.props.active)&&void 0!==e?e:s;return(0,i.cloneElement)(o,{viewBox:yN(yN({},d),{},{x:d.left,y:d.top}),active:p,label:f,payload:p?u:[],coordinate:c,accessibilityLayer:n})}),yM(l,"renderBrush",function(e){var t=l.props,r=t.margin,n=t.data,o=l.state,a=o.offset,s=o.dataStartIndex,c=o.dataEndIndex,u=o.updateId;return(0,i.cloneElement)(e,{key:e.key||"_recharts-brush",onChange:f7(l.handleBrushChange,e.props.onChange),data:n,x:rk(e.props.x)?e.props.x:a.left,y:rk(e.props.y)?e.props.y:a.top+a.height+a.brushBottom-(r.bottom||0),width:rk(e.props.width)?e.props.width:a.width,startIndex:s,endIndex:c,updateId:"brush-".concat(u)})}),yM(l,"renderReferenceElement",function(e,t,r){if(!e)return null;var n=l.clipPathId,o=l.state,a=o.xAxisMap,s=o.yAxisMap,c=o.offset,u=e.type.defaultProps||{},f=e.props,d=f.xAxisId,p=void 0===d?u.xAxisId:d,h=f.yAxisId,y=void 0===h?u.yAxisId:h;return(0,i.cloneElement)(e,{key:e.key||"".concat(t,"-").concat(r),xAxis:a[p],yAxis:s[y],viewBox:{x:c.left,y:c.top,width:c.width,height:c.height},clipPathId:n})}),yM(l,"renderActivePoints",function(e){var t=e.item,r=e.activePoint,o=e.basePoint,i=e.childIndex,a=e.isRange,l=[],s=t.props.key,c=void 0!==t.item.type.defaultProps?yN(yN({},t.item.type.defaultProps),t.item.props):t.item.props,u=c.activeDot,f=yN(yN({index:i,dataKey:c.dataKey,cx:r.x,cy:r.y,r:4,fill:fJ(t.item),strokeWidth:2,stroke:"#fff",payload:r.payload,value:r.value},nt(u,!1)),rK(u));return l.push(n.renderActiveDot(u,f,"".concat(s,"-activePoint-").concat(i))),o?l.push(n.renderActiveDot(u,yN(yN({},f),{},{cx:o.x,cy:o.y}),"".concat(s,"-basePoint-").concat(i))):a&&l.push(null),l}),yM(l,"renderGraphicChild",function(e,t,r){var n=l.filterFormatItem(e,t,r);if(!n)return null;var o=l.getTooltipEventType(),a=l.state,s=a.isTooltipActive,c=a.tooltipAxis,u=a.activeTooltipIndex,f=a.activeLabel,d=r8(l.props.children,nG),p=n.props,h=p.points,y=p.isRange,m=p.baseLine,v=void 0!==n.item.type.defaultProps?yN(yN({},n.item.type.defaultProps),n.item.props):n.item.props,g=v.activeDot,b=v.hide,x=v.activeBar,w=v.activeShape,j=!!(!b&&s&&d&&(g||x||w)),O={};"axis"!==o&&d&&"click"===d.props.trigger?O={onClick:f7(l.handleItemMouseEnter,e.props.onClick)}:"axis"!==o&&(O={onMouseLeave:f7(l.handleItemMouseLeave,e.props.onMouseLeave),onMouseEnter:f7(l.handleItemMouseEnter,e.props.onMouseEnter)});var S=(0,i.cloneElement)(e,yN(yN({},n.props),O));if(j){if(u>=0){if(c.dataKey&&!c.allowDuplicatedCategory){var A="function"==typeof c.dataKey?function(e){return"function"==typeof c.dataKey?c.dataKey(e.payload):null}:"payload.".concat(c.dataKey.toString());k=rD(h,A,f),E=y&&m&&rD(m,A,f)}else k=null==h?void 0:h[u],E=y&&m&&m[u];if(w||x){var P=void 0!==e.props.activeIndex?e.props.activeIndex:u;return[(0,i.cloneElement)(e,yN(yN(yN({},n.props),O),{},{activeIndex:P})),null,null]}if(!rL()(k))return[S].concat(yA(l.renderActivePoints({item:n,activePoint:k,basePoint:E,childIndex:u,isRange:y})))}else{var k,E,N,M=(null!==(N=l.getItemByXY(l.state.activeCoordinate))&&void 0!==N?N:{graphicalItem:S}).graphicalItem,C=M.item,T=void 0===C?e:C,_=M.childIndex,I=yN(yN(yN({},n.props),O),{},{activeIndex:_});return[(0,i.cloneElement)(T,I),null,null]}}return y?[S,null,null]:[S,null]}),yM(l,"renderCustomized",function(e,t,r){return(0,i.cloneElement)(e,yN(yN({key:"recharts-customized-".concat(r)},l.props),l.state))}),yM(l,"renderMap",{CartesianGrid:{handler:yD,once:!0},ReferenceArea:{handler:l.renderReferenceElement},ReferenceLine:{handler:yD},ReferenceDot:{handler:l.renderReferenceElement},XAxis:{handler:yD},YAxis:{handler:yD},Brush:{handler:l.renderBrush,once:!0},Bar:{handler:l.renderGraphicChild},Line:{handler:l.renderGraphicChild},Area:{handler:l.renderGraphicChild},Radar:{handler:l.renderGraphicChild},RadialBar:{handler:l.renderGraphicChild},Scatter:{handler:l.renderGraphicChild},Pie:{handler:l.renderGraphicChild},Funnel:{handler:l.renderGraphicChild},Tooltip:{handler:l.renderCursor,once:!0},PolarGrid:{handler:l.renderPolarGrid,once:!0},PolarAngleAxis:{handler:l.renderPolarAxis},PolarRadiusAxis:{handler:l.renderPolarAxis},Customized:{handler:l.renderCustomized}}),l.clipPathId="".concat(null!==(r=e.id)&&void 0!==r?r:rM("recharts"),"-clip"),l.throttleTriggeredAfterMouseMove=rm()(l.triggeredAfterMouseMove,null!==(o=e.throttleDelay)&&void 0!==o?o:1e3/60),l.state={},l}return function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&yS(e,t)}(n,e),r=[{key:"componentDidMount",value:function(){var e,t;this.addListener(),this.accessibilityManager.setDetails({container:this.container,offset:{left:null!==(e=this.props.margin.left)&&void 0!==e?e:0,top:null!==(t=this.props.margin.top)&&void 0!==t?t:0},coordinateList:this.state.tooltipTicks,mouseHandlerCallback:this.triggeredAfterMouseMove,layout:this.props.layout}),this.displayDefaultTooltip()}},{key:"displayDefaultTooltip",value:function(){var e=this.props,t=e.children,r=e.data,n=e.height,o=e.layout,i=r8(t,nG);if(i){var a=i.props.defaultIndex;if("number"==typeof a&&!(a<0)&&!(a>this.state.tooltipTicks.length-1)){var l=this.state.tooltipTicks[a]&&this.state.tooltipTicks[a].value,s=y$(this.state,r,a,l),c=this.state.tooltipTicks[a].coordinate,u=(this.state.offset.top+n)/2,f="horizontal"===o?{x:c,y:u}:{y:c,x:u},d=this.state.formattedGraphicalItems.find(function(e){return"Scatter"===e.item.type.name});d&&(f=yN(yN({},f),d.props.points[a].tooltipPosition),s=d.props.points[a].tooltipPayload);var p={activeTooltipIndex:a,isTooltipActive:!0,activeLabel:l,activePayload:s,activeCoordinate:f};this.setState(p),this.renderCursor(i),this.accessibilityManager.setIndex(a)}}}},{key:"getSnapshotBeforeUpdate",value:function(e,t){if(!this.props.accessibilityLayer)return null;if(this.state.tooltipTicks!==t.tooltipTicks&&this.accessibilityManager.setDetails({coordinateList:this.state.tooltipTicks}),this.props.layout!==e.layout&&this.accessibilityManager.setDetails({layout:this.props.layout}),this.props.margin!==e.margin){var r,n;this.accessibilityManager.setDetails({offset:{left:null!==(r=this.props.margin.left)&&void 0!==r?r:0,top:null!==(n=this.props.margin.top)&&void 0!==n?n:0}})}return null}},{key:"componentDidUpdate",value:function(e){nr([r8(e.children,nG)],[r8(this.props.children,nG)])||this.displayDefaultTooltip()}},{key:"componentWillUnmount",value:function(){this.removeListener(),this.throttleTriggeredAfterMouseMove.cancel()}},{key:"getTooltipEventType",value:function(){var e=r8(this.props.children,nG);if(e&&"boolean"==typeof e.props.shared){var t=e.props.shared?"axis":"item";return s.indexOf(t)>=0?t:o}return o}},{key:"getMouseInfo",value:function(e){if(!this.container)return null;var t=this.container,r=t.getBoundingClientRect(),n={top:r.top+window.scrollY-document.documentElement.clientTop,left:r.left+window.scrollX-document.documentElement.clientLeft},o={chartX:Math.round(e.pageX-n.left),chartY:Math.round(e.pageY-n.top)},i=r.width/t.offsetWidth||1,a=this.inRange(o.chartX,o.chartY,i);if(!a)return null;var l=this.state,s=l.xAxisMap,c=l.yAxisMap;if("axis"!==this.getTooltipEventType()&&s&&c){var u=rT(s).scale,f=rT(c).scale,d=u&&u.invert?u.invert(o.chartX):null,p=f&&f.invert?f.invert(o.chartY):null;return yN(yN({},o),{},{xValue:d,yValue:p})}var h=yz(this.state,this.props.data,this.props.layout,a);return h?yN(yN({},o),h):null}},{key:"inRange",value:function(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,n=this.props.layout,o=e/r,i=t/r;if("horizontal"===n||"vertical"===n){var a=this.state.offset;return o>=a.left&&o<=a.left+a.width&&i>=a.top&&i<=a.top+a.height?{x:o,y:i}:null}var l=this.state,s=l.angleAxisMap,c=l.radiusAxisMap;return s&&c?dW({x:o,y:i},rT(s)):null}},{key:"parseEventsOfWrapper",value:function(){var e=this.props.children,t=this.getTooltipEventType(),r=r8(e,nG),n={};return r&&"axis"===t&&(n="click"===r.props.trigger?{onClick:this.handleClick}:{onMouseEnter:this.handleMouseEnter,onDoubleClick:this.handleDoubleClick,onMouseMove:this.handleMouseMove,onMouseLeave:this.handleMouseLeave,onTouchMove:this.handleTouchMove,onTouchStart:this.handleTouchStart,onTouchEnd:this.handleTouchEnd,onContextMenu:this.handleContextMenu}),yN(yN({},rK(this.props,this.handleOuterEvent)),n)}},{key:"addListener",value:function(){p7.on(p9,this.handleReceiveSyncEvent)}},{key:"removeListener",value:function(){p7.removeListener(p9,this.handleReceiveSyncEvent)}},{key:"filterFormatItem",value:function(e,t,r){for(var n=this.state.formattedGraphicalItems,o=0,i=n.length;o<i;o++){var a=n[o];if(a.item===e||a.props.key===e.key||t===r2(a.item.type)&&r===a.childIndex)return a}return null}},{key:"renderClipPath",value:function(){var e=this.clipPathId,t=this.state.offset,r=t.left,n=t.top,o=t.height,i=t.width;return a().createElement("defs",null,a().createElement("clipPath",{id:e},a().createElement("rect",{x:r,y:n,height:o,width:i})))}},{key:"getXScales",value:function(){var e=this.state.xAxisMap;return e?Object.entries(e).reduce(function(e,t){var r=yx(t,2),n=r[0],o=r[1];return yN(yN({},e),{},yM({},n,o.scale))},{}):null}},{key:"getYScales",value:function(){var e=this.state.yAxisMap;return e?Object.entries(e).reduce(function(e,t){var r=yx(t,2),n=r[0],o=r[1];return yN(yN({},e),{},yM({},n,o.scale))},{}):null}},{key:"getXScaleByAxisId",value:function(e){var t;return null===(t=this.state.xAxisMap)||void 0===t||null===(t=t[e])||void 0===t?void 0:t.scale}},{key:"getYScaleByAxisId",value:function(e){var t;return null===(t=this.state.yAxisMap)||void 0===t||null===(t=t[e])||void 0===t?void 0:t.scale}},{key:"getItemByXY",value:function(e){var t=this.state,r=t.formattedGraphicalItems,n=t.activeItem;if(r&&r.length)for(var o=0,i=r.length;o<i;o++){var a=r[o],l=a.props,s=a.item,c=void 0!==s.type.defaultProps?yN(yN({},s.type.defaultProps),s.props):s.props,u=r2(s.type);if("Bar"===u){var f=(l.data||[]).find(function(t){return af(e,t)});if(f)return{graphicalItem:a,payload:f}}else if("RadialBar"===u){var d=(l.data||[]).find(function(t){return dW(e,t)});if(d)return{graphicalItem:a,payload:d}}else if(hC(a,n)||hT(a,n)||h_(a,n)){var p=function(e){var t,r,n,o=e.activeTooltipItem,i=e.graphicalItem,a=e.itemData,l=(hC(i,o)?t="trapezoids":hT(i,o)?t="sectors":h_(i,o)&&(t="points"),t),s=hC(i,o)?null===(r=o.tooltipPayload)||void 0===r||null===(r=r[0])||void 0===r||null===(r=r.payload)||void 0===r?void 0:r.payload:hT(i,o)?null===(n=o.tooltipPayload)||void 0===n||null===(n=n[0])||void 0===n||null===(n=n.payload)||void 0===n?void 0:n.payload:h_(i,o)?o.payload:{},c=a.filter(function(e,t){var r=uH()(s,e),n=i.props[l].filter(function(e){var t;return(hC(i,o)?t=hI:hT(i,o)?t=hD:h_(i,o)&&(t=hR),t)(e,o)}),a=i.props[l].indexOf(n[n.length-1]);return r&&t===a});return a.indexOf(c[c.length-1])}({graphicalItem:a,activeTooltipItem:n,itemData:c.data}),h=void 0===c.activeIndex?p:c.activeIndex;return{graphicalItem:yN(yN({},a),{},{childIndex:h}),payload:h_(a,n)?c.data[p]:a.props.data[p]}}}return null}},{key:"render",value:function(){var e,t,r=this;if(!r7(this))return null;var n=this.props,o=n.children,i=n.className,l=n.width,s=n.height,c=n.style,u=n.compact,f=n.title,d=n.desc,p=nt(yw(n,yv),!1);if(u)return a().createElement(pM,{state:this.state,width:this.props.width,height:this.props.height,clipPathId:this.clipPathId},a().createElement(nK,yb({},p,{width:l,height:s,title:f,desc:d}),this.renderClipPath(),no(o,this.renderMap)));this.props.accessibilityLayer&&(p.tabIndex=null!==(e=this.props.tabIndex)&&void 0!==e?e:0,p.role=null!==(t=this.props.role)&&void 0!==t?t:"application",p.onKeyDown=function(e){r.accessibilityManager.keyboardEvent(e)},p.onFocus=function(){r.accessibilityManager.focus()});var h=this.parseEventsOfWrapper();return a().createElement(pM,{state:this.state,width:this.props.width,height:this.props.height,clipPathId:this.clipPathId},a().createElement("div",yb({className:(0,rh.A)("recharts-wrapper",i),style:yN({position:"relative",cursor:"default",width:l,height:s},c)},h,{ref:function(e){r.container=e}}),a().createElement(nK,yb({},p,{width:l,height:s,title:f,desc:d,style:y_}),this.renderClipPath(),no(o,this.renderMap)),this.renderLegend(),this.renderTooltip()))}}],function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,yC(n.key),n)}}(n.prototype,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(i.Component);yM(y,"displayName",t),yM(y,"defaultProps",yN({layout:"horizontal",stackOffset:"none",barCategoryGap:"10%",barGap:4,margin:{top:5,right:5,bottom:5,left:5},reverseStackOrder:!1,syncMethod:"index"},d)),yM(y,"getDerivedStateFromProps",function(e,t){var r=e.dataKey,n=e.data,o=e.children,i=e.width,a=e.height,l=e.layout,s=e.stackOffset,c=e.margin,u=t.dataStartIndex,f=t.dataEndIndex;if(void 0===t.updateId){var d=yV(e);return yN(yN(yN({},d),{},{updateId:0},h(yN(yN({props:e},d),{},{updateId:0}),t)),{},{prevDataKey:r,prevData:n,prevWidth:i,prevHeight:a,prevLayout:l,prevStackOffset:s,prevMargin:c,prevChildren:o})}if(r!==t.prevDataKey||n!==t.prevData||i!==t.prevWidth||a!==t.prevHeight||l!==t.prevLayout||s!==t.prevStackOffset||!rW(c,t.prevMargin)){var p=yV(e),y={chartX:t.chartX,chartY:t.chartY,isTooltipActive:t.isTooltipActive},m=yN(yN({},yz(t,n,l)),{},{updateId:t.updateId+1}),v=yN(yN(yN({},p),y),m);return yN(yN(yN({},v),h(yN({props:e},v),t)),{},{prevDataKey:r,prevData:n,prevWidth:i,prevHeight:a,prevLayout:l,prevStackOffset:s,prevMargin:c,prevChildren:o})}if(!nr(o,t.prevChildren)){var g,b,x,w,j=r8(o,dT),O=j&&null!==(g=null===(b=j.props)||void 0===b?void 0:b.startIndex)&&void 0!==g?g:u,S=j&&null!==(x=null===(w=j.props)||void 0===w?void 0:w.endIndex)&&void 0!==x?x:f,A=rL()(n)||O!==u||S!==f?t.updateId+1:t.updateId;return yN(yN({updateId:A},h(yN(yN({props:e},t),{},{updateId:A,dataStartIndex:O,dataEndIndex:S}),t)),{},{prevChildren:o,dataStartIndex:O,dataEndIndex:S})}return null}),yM(y,"renderActiveDot",function(e,t,r){var n;return n=(0,i.isValidElement)(e)?(0,i.cloneElement)(e,t):rz()(e)?e(t):a().createElement(o2,t),a().createElement(o0,{className:"recharts-active-dot",key:r},n)});var m=(0,i.forwardRef)(function(e,t){return a().createElement(y,yb({},e,{ref:t}))});return m.displayName=y.displayName,m}({chartName:"PieChart",GraphicalChild:mH,validateTooltipEventTypes:["item"],defaultTooltipEventType:"item",legendContent:"children",axisComponents:[{axisType:"angleAxis",AxisComp:mo},{axisType:"radiusAxis",AxisComp:mj}],formatAxisMap:function(e,t,r,n,o){var i=e.width,a=e.height,l=e.startAngle,s=e.endAngle,c=rC(e.cx,i,i/2),u=rC(e.cy,a,a/2),f=dz(i,a,r),d=rC(e.innerRadius,f,0),p=rC(e.outerRadius,f,.8*f);return Object.keys(t).reduce(function(e,r){var i,a=t[r],f=a.domain,h=a.reversed;if(rL()(a.range))"angleAxis"===n?i=[l,s]:"radiusAxis"===n&&(i=[d,p]),h&&(i=[i[1],i[0]]);else{var y,m=function(e){if(Array.isArray(e))return e}(y=i=a.range)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,a,l=[],s=!0,c=!1;try{i=(r=r.call(e)).next;for(;!(s=(n=i.call(r)).done)&&(l.push(n.value),l.length!==t);s=!0);}catch(e){c=!0,o=e}finally{try{if(!s&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(c)throw o}}return l}}(y,2)||function(e,t){if(e){if("string"==typeof e)return dB(e,2);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return dB(e,t)}}(y,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}();l=m[0],s=m[1]}var v=f9(a,o),g=v.realScaleType,b=v.scale;b.domain(f).range(i),de(b);var x=di(b,dD(dD({},a),{},{realScaleType:g})),w=dD(dD(dD({},a),x),{},{range:i,radius:p,realScaleType:g,scale:b,cx:c,cy:u,innerRadius:d,outerRadius:p,startAngle:l,endAngle:s});return dD(dD({},e),{},dR({},r,w))},{})},defaultProps:{layout:"centric",startAngle:0,endAngle:360,cx:"50%",cy:"50%",innerRadius:0,outerRadius:"80%"}});let mX=["hsl(var(--chart-1))","hsl(var(--chart-2))","hsl(var(--chart-3))","hsl(var(--chart-4))","hsl(var(--chart-5))","hsl(200 70% 50%)","hsl(300 70% 50%)","hsl(50 70% 50%)"];function mY({totalIncome:e,categories:t,balancesVisible:r}){let n=e-t.reduce((e,t)=>e+t.budget,0),i=t.filter(e=>e.budget>0).map((e,t)=>({name:e.name,value:e.budget,fill:mX[t%mX.length],isVisible:e.isVisible??!0}));n>0&&i.push({name:"Unallocated",value:n,fill:"hsl(var(--muted))",isVisible:!0});let a=i.reduce((e,t)=>(e[t.name]={label:t.name,color:t.fill},e),{});if(0===e&&0===t.length)return(0,o.jsxs)(A.Zp,{children:[(0,o.jsx)(A.aR,{className:"pb-2",children:(0,o.jsxs)(A.ZB,{className:"text-lg flex items-center gap-2 font-headline",children:[(0,o.jsx)(rp,{className:"h-5 w-5 text-primary"}),"Budget Overview"]})}),(0,o.jsx)(A.Wu,{children:(0,o.jsx)("p",{className:"text-sm text-muted-foreground text-center py-4",children:"Enter your income and add categories to see a visual breakdown."})})]});let l=0===i.length||i.every(e=>0===e.value);return(0,o.jsxs)(A.Zp,{children:[(0,o.jsx)(A.aR,{className:"pb-2",children:(0,o.jsxs)(A.ZB,{className:"text-lg flex items-center gap-2 font-headline",children:[(0,o.jsx)(rp,{className:"h-5 w-5 text-primary"}),"Budget Overview"]})}),(0,o.jsxs)(A.Wu,{children:[l?(0,o.jsx)("p",{className:"text-sm text-muted-foreground text-center py-4",children:"Allocate funds to categories to see the chart."}):(0,o.jsx)(oW,{config:a,className:"mx-auto aspect-square max-h-[250px] h-auto",children:(0,o.jsxs)(mG,{children:[(0,o.jsx)(nG,{formatter:(e,t,n)=>{let o=n.payload?.isVisible??!0;return[r&&o?`R ${Number(e).toFixed(2)}`:"R ••••",t]},content:(0,o.jsx)(oH,{nameKey:"name",hideLabel:!1})}),(0,o.jsx)(mH,{data:i,dataKey:"value",nameKey:"name",cx:"50%",cy:"50%",outerRadius:80,labelLine:!1,label:({percent:e,payload:t})=>{let n=t?.isVisible??!0;return r&&n?`${(100*e).toFixed(0)}%`:""},children:i.map((e,t)=>(0,o.jsx)(mD,{fill:e.fill},`cell-${t}`))}),(0,o.jsx)(oz,{content:(0,o.jsx)(oG,{nameKey:"name",className:"text-xs flex-wrap justify-center gap-x-2 gap-y-1"})})]})}),t.map(e=>{if(0===e.budget)return null;let t=r&&(e.isVisible??!0),n=e.subCategories.reduce((e,t)=>e+t.allocatedAmount,0),i=e.budget>0?n/e.budget*100:0,a=n>e.budget;return(0,o.jsxs)("div",{className:"mt-3 text-xs",children:[(0,o.jsxs)("div",{className:"flex justify-between items-center mb-0.5",children:[(0,o.jsx)("span",{className:"font-medium",children:e.name}),(0,o.jsx)("span",{className:"text-muted-foreground",children:t?`${n.toFixed(2)} / ${e.budget.toFixed(2)}`:"•••• / ••••"})]}),(0,o.jsx)(V,{value:Math.min(i,100),className:`h-1.5 ${a?"bg-destructive/70 [&>*]:bg-destructive":"[&>*]:bg-primary"}`})]},e.id)})]})]})}var mK=r(51215);function mZ(e,[t,r]){return Math.min(r,Math.max(t,e))}var mJ=r(38674),mQ=r(13495),m0=r(69024),m1=[" ","Enter","ArrowUp","ArrowDown"],m2=[" ","Enter"],m5="Select",[m4,m3,m6]=(0,Z.N)(m5),[m8,m7]=(0,N.A)(m5,[m6,mJ.Bk]),m9=(0,mJ.Bk)(),[ve,vt]=m8(m5),[vr,vn]=m8(m5),vo=e=>{let{__scopeSelect:t,children:r,open:n,defaultOpen:a,onOpenChange:l,value:s,defaultValue:c,onValueChange:u,dir:f,name:d,autoComplete:p,disabled:h,required:y,form:m}=e,v=m9(t),[g,b]=i.useState(null),[x,w]=i.useState(null),[j,O]=i.useState(!1),S=(0,em.jH)(f),[A=!1,P]=(0,ee.i)({prop:n,defaultProp:a,onChange:l}),[k,E]=(0,ee.i)({prop:s,defaultProp:c,onChange:u}),N=i.useRef(null),M=!g||m||!!g.closest("form"),[C,T]=i.useState(new Set),_=Array.from(C).map(e=>e.props.value).join(";");return(0,o.jsx)(mJ.bL,{...v,children:(0,o.jsxs)(ve,{required:y,scope:t,trigger:g,onTriggerChange:b,valueNode:x,onValueNodeChange:w,valueNodeHasChildren:j,onValueNodeHasChildrenChange:O,contentId:(0,en.B)(),value:k,onValueChange:E,open:A,onOpenChange:P,dir:S,triggerPointerDownPosRef:N,disabled:h,children:[(0,o.jsx)(m4.Provider,{scope:t,children:(0,o.jsx)(vr,{scope:e.__scopeSelect,onNativeOptionAdd:i.useCallback(e=>{T(t=>new Set(t).add(e))},[]),onNativeOptionRemove:i.useCallback(e=>{T(t=>{let r=new Set(t);return r.delete(e),r})},[]),children:r})}),M?(0,o.jsxs)(vq,{"aria-hidden":!0,required:y,tabIndex:-1,name:d,autoComplete:p,value:k,onChange:e=>E(e.target.value),disabled:h,form:m,children:[void 0===k?(0,o.jsx)("option",{value:""}):null,Array.from(C)]},_):null]})})};vo.displayName=m5;var vi="SelectTrigger",va=i.forwardRef((e,t)=>{let{__scopeSelect:r,disabled:n=!1,...a}=e,l=m9(r),s=vt(vi,r),c=s.disabled||n,u=(0,J.s)(t,s.onTriggerChange),f=m3(r),d=i.useRef("touch"),[p,h,y]=vW(e=>{let t=f().filter(e=>!e.disabled),r=t.find(e=>e.value===s.value),n=vV(t,e,r);void 0!==n&&s.onValueChange(n.value)}),m=e=>{c||(s.onOpenChange(!0),y()),e&&(s.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return(0,o.jsx)(mJ.Mz,{asChild:!0,...l,children:(0,o.jsx)(M.sG.button,{type:"button",role:"combobox","aria-controls":s.contentId,"aria-expanded":s.open,"aria-required":s.required,"aria-autocomplete":"none",dir:s.dir,"data-state":s.open?"open":"closed",disabled:c,"data-disabled":c?"":void 0,"data-placeholder":vF(s.value)?"":void 0,...a,ref:u,onClick:(0,Q.m)(a.onClick,e=>{e.currentTarget.focus(),"mouse"!==d.current&&m(e)}),onPointerDown:(0,Q.m)(a.onPointerDown,e=>{d.current=e.pointerType;let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(m(e),e.preventDefault())}),onKeyDown:(0,Q.m)(a.onKeyDown,e=>{let t=""!==p.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||h(e.key),(!t||" "!==e.key)&&m1.includes(e.key)&&(m(),e.preventDefault())})})})});va.displayName=vi;var vl="SelectValue",vs=i.forwardRef((e,t)=>{let{__scopeSelect:r,className:n,style:i,children:a,placeholder:l="",...s}=e,c=vt(vl,r),{onValueNodeHasChildrenChange:u}=c,f=void 0!==a,d=(0,J.s)(t,c.onValueNodeChange);return(0,et.N)(()=>{u(f)},[u,f]),(0,o.jsx)(M.sG.span,{...s,ref:d,style:{pointerEvents:"none"},children:vF(c.value)?(0,o.jsx)(o.Fragment,{children:l}):a})});vs.displayName=vl;var vc=i.forwardRef((e,t)=>{let{__scopeSelect:r,children:n,...i}=e;return(0,o.jsx)(M.sG.span,{"aria-hidden":!0,...i,ref:t,children:n||"▼"})});vc.displayName="SelectIcon";var vu=e=>(0,o.jsx)(eJ.Z,{asChild:!0,...e});vu.displayName="SelectPortal";var vf="SelectContent",vd=i.forwardRef((e,t)=>{let r=vt(vf,e.__scopeSelect),[n,a]=i.useState();return((0,et.N)(()=>{a(new DocumentFragment)},[]),r.open)?(0,o.jsx)(vy,{...e,ref:t}):n?mK.createPortal((0,o.jsx)(vp,{scope:e.__scopeSelect,children:(0,o.jsx)(m4.Slot,{scope:e.__scopeSelect,children:(0,o.jsx)("div",{children:e.children})})}),n):null});vd.displayName=vf;var[vp,vh]=m8(vf),vy=i.forwardRef((e,t)=>{let{__scopeSelect:r,position:n="item-aligned",onCloseAutoFocus:a,onEscapeKeyDown:l,onPointerDownOutside:s,side:c,sideOffset:u,align:f,alignOffset:d,arrowPadding:p,collisionBoundary:h,collisionPadding:y,sticky:m,hideWhenDetached:v,avoidCollisions:g,...b}=e,x=vt(vf,r),[w,j]=i.useState(null),[O,S]=i.useState(null),A=(0,J.s)(t,e=>j(e)),[P,k]=i.useState(null),[E,N]=i.useState(null),M=m3(r),[C,T]=i.useState(!1),_=i.useRef(!1);i.useEffect(()=>{if(w)return(0,e1.Eq)(w)},[w]),(0,eQ.Oh)();let I=i.useCallback(e=>{let[t,...r]=M().map(e=>e.ref.current),[n]=r.slice(-1),o=document.activeElement;for(let r of e)if(r===o||(r?.scrollIntoView({block:"nearest"}),r===t&&O&&(O.scrollTop=0),r===n&&O&&(O.scrollTop=O.scrollHeight),r?.focus(),document.activeElement!==o))return},[M,O]),D=i.useCallback(()=>I([P,w]),[I,P,w]);i.useEffect(()=>{C&&D()},[C,D]);let{onOpenChange:R,triggerPointerDownPosRef:B}=x;i.useEffect(()=>{if(w){let e={x:0,y:0},t=t=>{e={x:Math.abs(Math.round(t.pageX)-(B.current?.x??0)),y:Math.abs(Math.round(t.pageY)-(B.current?.y??0))}},r=r=>{e.x<=10&&e.y<=10?r.preventDefault():w.contains(r.target)||R(!1),document.removeEventListener("pointermove",t),B.current=null};return null!==B.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",r,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",r,{capture:!0})}}},[w,R,B]),i.useEffect(()=>{let e=()=>R(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}},[R]);let[L,$]=vW(e=>{let t=M().filter(e=>!e.disabled),r=t.find(e=>e.ref.current===document.activeElement),n=vV(t,e,r);n&&setTimeout(()=>n.ref.current.focus())}),z=i.useCallback((e,t,r)=>{let n=!_.current&&!r;(void 0!==x.value&&x.value===t||n)&&(k(e),n&&(_.current=!0))},[x.value]),U=i.useCallback(()=>w?.focus(),[w]),F=i.useCallback((e,t,r)=>{let n=!_.current&&!r;(void 0!==x.value&&x.value===t||n)&&N(e)},[x.value]),q="popper"===n?vv:vm,W=q===vv?{side:c,sideOffset:u,align:f,alignOffset:d,arrowPadding:p,collisionBoundary:h,collisionPadding:y,sticky:m,hideWhenDetached:v,avoidCollisions:g}:{};return(0,o.jsx)(vp,{scope:r,content:w,viewport:O,onViewportChange:S,itemRefCallback:z,selectedItem:P,onItemLeave:U,itemTextRefCallback:F,focusSelectedItem:D,selectedItemText:E,position:n,isPositioned:C,searchRef:L,children:(0,o.jsx)(e0.A,{as:e2.DX,allowPinchZoom:!0,children:(0,o.jsx)(eZ.n,{asChild:!0,trapped:x.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:(0,Q.m)(a,e=>{x.trigger?.focus({preventScroll:!0}),e.preventDefault()}),children:(0,o.jsx)(eK.qW,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:l,onPointerDownOutside:s,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>x.onOpenChange(!1),children:(0,o.jsx)(q,{role:"listbox",id:x.contentId,"data-state":x.open?"open":"closed",dir:x.dir,onContextMenu:e=>e.preventDefault(),...b,...W,onPlaced:()=>T(!0),ref:A,style:{display:"flex",flexDirection:"column",outline:"none",...b.style},onKeyDown:(0,Q.m)(b.onKeyDown,e=>{let t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||$(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=M().filter(e=>!e.disabled).map(e=>e.ref.current);if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){let r=e.target,n=t.indexOf(r);t=t.slice(n+1)}setTimeout(()=>I(t)),e.preventDefault()}})})})})})})});vy.displayName="SelectContentImpl";var vm=i.forwardRef((e,t)=>{let{__scopeSelect:r,onPlaced:n,...a}=e,l=vt(vf,r),s=vh(vf,r),[c,u]=i.useState(null),[f,d]=i.useState(null),p=(0,J.s)(t,e=>d(e)),h=m3(r),y=i.useRef(!1),m=i.useRef(!0),{viewport:v,selectedItem:g,selectedItemText:b,focusSelectedItem:x}=s,w=i.useCallback(()=>{if(l.trigger&&l.valueNode&&c&&f&&v&&g&&b){let e=l.trigger.getBoundingClientRect(),t=f.getBoundingClientRect(),r=l.valueNode.getBoundingClientRect(),o=b.getBoundingClientRect();if("rtl"!==l.dir){let n=o.left-t.left,i=r.left-n,a=e.left-i,l=e.width+a,s=Math.max(l,t.width),u=mZ(i,[10,Math.max(10,window.innerWidth-10-s)]);c.style.minWidth=l+"px",c.style.left=u+"px"}else{let n=t.right-o.right,i=window.innerWidth-r.right-n,a=window.innerWidth-e.right-i,l=e.width+a,s=Math.max(l,t.width),u=mZ(i,[10,Math.max(10,window.innerWidth-10-s)]);c.style.minWidth=l+"px",c.style.right=u+"px"}let i=h(),a=window.innerHeight-20,s=v.scrollHeight,u=window.getComputedStyle(f),d=parseInt(u.borderTopWidth,10),p=parseInt(u.paddingTop,10),m=parseInt(u.borderBottomWidth,10),x=d+p+s+parseInt(u.paddingBottom,10)+m,w=Math.min(5*g.offsetHeight,x),j=window.getComputedStyle(v),O=parseInt(j.paddingTop,10),S=parseInt(j.paddingBottom,10),A=e.top+e.height/2-10,P=g.offsetHeight/2,k=d+p+(g.offsetTop+P);if(k<=A){let e=i.length>0&&g===i[i.length-1].ref.current;c.style.bottom="0px";let t=Math.max(a-A,P+(e?S:0)+(f.clientHeight-v.offsetTop-v.offsetHeight)+m);c.style.height=k+t+"px"}else{let e=i.length>0&&g===i[0].ref.current;c.style.top="0px";let t=Math.max(A,d+v.offsetTop+(e?O:0)+P);c.style.height=t+(x-k)+"px",v.scrollTop=k-A+v.offsetTop}c.style.margin="10px 0",c.style.minHeight=w+"px",c.style.maxHeight=a+"px",n?.(),requestAnimationFrame(()=>y.current=!0)}},[h,l.trigger,l.valueNode,c,f,v,g,b,l.dir,n]);(0,et.N)(()=>w(),[w]);let[j,O]=i.useState();(0,et.N)(()=>{f&&O(window.getComputedStyle(f).zIndex)},[f]);let S=i.useCallback(e=>{e&&!0===m.current&&(w(),x?.(),m.current=!1)},[w,x]);return(0,o.jsx)(vg,{scope:r,contentWrapper:c,shouldExpandOnScrollRef:y,onScrollButtonChange:S,children:(0,o.jsx)("div",{ref:u,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:j},children:(0,o.jsx)(M.sG.div,{...a,ref:p,style:{boxSizing:"border-box",maxHeight:"100%",...a.style}})})})});vm.displayName="SelectItemAlignedPosition";var vv=i.forwardRef((e,t)=>{let{__scopeSelect:r,align:n="start",collisionPadding:i=10,...a}=e,l=m9(r);return(0,o.jsx)(mJ.UC,{...l,...a,ref:t,align:n,collisionPadding:i,style:{boxSizing:"border-box",...a.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});vv.displayName="SelectPopperPosition";var[vg,vb]=m8(vf,{}),vx="SelectViewport",vw=i.forwardRef((e,t)=>{let{__scopeSelect:r,nonce:n,...a}=e,l=vh(vx,r),s=vb(vx,r),c=(0,J.s)(t,l.onViewportChange),u=i.useRef(0);return(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:n}),(0,o.jsx)(m4.Slot,{scope:r,children:(0,o.jsx)(M.sG.div,{"data-radix-select-viewport":"",role:"presentation",...a,ref:c,style:{position:"relative",flex:1,overflow:"hidden auto",...a.style},onScroll:(0,Q.m)(a.onScroll,e=>{let t=e.currentTarget,{contentWrapper:r,shouldExpandOnScrollRef:n}=s;if(n?.current&&r){let e=Math.abs(u.current-t.scrollTop);if(e>0){let n=window.innerHeight-20,o=Math.max(parseFloat(r.style.minHeight),parseFloat(r.style.height));if(o<n){let i=o+e,a=Math.min(n,i),l=i-a;r.style.height=a+"px","0px"===r.style.bottom&&(t.scrollTop=l>0?l:0,r.style.justifyContent="flex-end")}}}u.current=t.scrollTop})})})]})});vw.displayName=vx;var vj="SelectGroup",[vO,vS]=m8(vj);i.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,i=(0,en.B)();return(0,o.jsx)(vO,{scope:r,id:i,children:(0,o.jsx)(M.sG.div,{role:"group","aria-labelledby":i,...n,ref:t})})}).displayName=vj;var vA="SelectLabel",vP=i.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,i=vS(vA,r);return(0,o.jsx)(M.sG.div,{id:i.id,...n,ref:t})});vP.displayName=vA;var vk="SelectItem",[vE,vN]=m8(vk),vM=i.forwardRef((e,t)=>{let{__scopeSelect:r,value:n,disabled:a=!1,textValue:l,...s}=e,c=vt(vk,r),u=vh(vk,r),f=c.value===n,[d,p]=i.useState(l??""),[h,y]=i.useState(!1),m=(0,J.s)(t,e=>u.itemRefCallback?.(e,n,a)),v=(0,en.B)(),g=i.useRef("touch"),b=()=>{a||(c.onValueChange(n),c.onOpenChange(!1))};if(""===n)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,o.jsx)(vE,{scope:r,value:n,disabled:a,textId:v,isSelected:f,onItemTextChange:i.useCallback(e=>{p(t=>t||(e?.textContent??"").trim())},[]),children:(0,o.jsx)(m4.ItemSlot,{scope:r,value:n,disabled:a,textValue:d,children:(0,o.jsx)(M.sG.div,{role:"option","aria-labelledby":v,"data-highlighted":h?"":void 0,"aria-selected":f&&h,"data-state":f?"checked":"unchecked","aria-disabled":a||void 0,"data-disabled":a?"":void 0,tabIndex:a?void 0:-1,...s,ref:m,onFocus:(0,Q.m)(s.onFocus,()=>y(!0)),onBlur:(0,Q.m)(s.onBlur,()=>y(!1)),onClick:(0,Q.m)(s.onClick,()=>{"mouse"!==g.current&&b()}),onPointerUp:(0,Q.m)(s.onPointerUp,()=>{"mouse"===g.current&&b()}),onPointerDown:(0,Q.m)(s.onPointerDown,e=>{g.current=e.pointerType}),onPointerMove:(0,Q.m)(s.onPointerMove,e=>{g.current=e.pointerType,a?u.onItemLeave?.():"mouse"===g.current&&e.currentTarget.focus({preventScroll:!0})}),onPointerLeave:(0,Q.m)(s.onPointerLeave,e=>{e.currentTarget===document.activeElement&&u.onItemLeave?.()}),onKeyDown:(0,Q.m)(s.onKeyDown,e=>{(u.searchRef?.current===""||" "!==e.key)&&(m2.includes(e.key)&&b()," "===e.key&&e.preventDefault())})})})})});vM.displayName=vk;var vC="SelectItemText",vT=i.forwardRef((e,t)=>{let{__scopeSelect:r,className:n,style:a,...l}=e,s=vt(vC,r),c=vh(vC,r),u=vN(vC,r),f=vn(vC,r),[d,p]=i.useState(null),h=(0,J.s)(t,e=>p(e),u.onItemTextChange,e=>c.itemTextRefCallback?.(e,u.value,u.disabled)),y=d?.textContent,m=i.useMemo(()=>(0,o.jsx)("option",{value:u.value,disabled:u.disabled,children:y},u.value),[u.disabled,u.value,y]),{onNativeOptionAdd:v,onNativeOptionRemove:g}=f;return(0,et.N)(()=>(v(m),()=>g(m)),[v,g,m]),(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(M.sG.span,{id:u.textId,...l,ref:h}),u.isSelected&&s.valueNode&&!s.valueNodeHasChildren?mK.createPortal(l.children,s.valueNode):null]})});vT.displayName=vC;var v_="SelectItemIndicator",vI=i.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e;return vN(v_,r).isSelected?(0,o.jsx)(M.sG.span,{"aria-hidden":!0,...n,ref:t}):null});vI.displayName=v_;var vD="SelectScrollUpButton",vR=i.forwardRef((e,t)=>{let r=vh(vD,e.__scopeSelect),n=vb(vD,e.__scopeSelect),[a,l]=i.useState(!1),s=(0,J.s)(t,n.onScrollButtonChange);return(0,et.N)(()=>{if(r.viewport&&r.isPositioned){let e=function(){l(t.scrollTop>0)},t=r.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[r.viewport,r.isPositioned]),a?(0,o.jsx)(v$,{...e,ref:s,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=r;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null});vR.displayName=vD;var vB="SelectScrollDownButton",vL=i.forwardRef((e,t)=>{let r=vh(vB,e.__scopeSelect),n=vb(vB,e.__scopeSelect),[a,l]=i.useState(!1),s=(0,J.s)(t,n.onScrollButtonChange);return(0,et.N)(()=>{if(r.viewport&&r.isPositioned){let e=function(){let e=t.scrollHeight-t.clientHeight;l(Math.ceil(t.scrollTop)<e)},t=r.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[r.viewport,r.isPositioned]),a?(0,o.jsx)(v$,{...e,ref:s,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=r;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null});vL.displayName=vB;var v$=i.forwardRef((e,t)=>{let{__scopeSelect:r,onAutoScroll:n,...a}=e,l=vh("SelectScrollButton",r),s=i.useRef(null),c=m3(r),u=i.useCallback(()=>{null!==s.current&&(window.clearInterval(s.current),s.current=null)},[]);return i.useEffect(()=>()=>u(),[u]),(0,et.N)(()=>{let e=c().find(e=>e.ref.current===document.activeElement);e?.ref.current?.scrollIntoView({block:"nearest"})},[c]),(0,o.jsx)(M.sG.div,{"aria-hidden":!0,...a,ref:t,style:{flexShrink:0,...a.style},onPointerDown:(0,Q.m)(a.onPointerDown,()=>{null===s.current&&(s.current=window.setInterval(n,50))}),onPointerMove:(0,Q.m)(a.onPointerMove,()=>{l.onItemLeave?.(),null===s.current&&(s.current=window.setInterval(n,50))}),onPointerLeave:(0,Q.m)(a.onPointerLeave,()=>{u()})})}),vz=i.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e;return(0,o.jsx)(M.sG.div,{"aria-hidden":!0,...n,ref:t})});vz.displayName="SelectSeparator";var vU="SelectArrow";function vF(e){return""===e||void 0===e}i.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,i=m9(r),a=vt(vU,r),l=vh(vU,r);return a.open&&"popper"===l.position?(0,o.jsx)(mJ.i3,{...i,...n,ref:t}):null}).displayName=vU;var vq=i.forwardRef((e,t)=>{let{value:r,...n}=e,a=i.useRef(null),l=(0,J.s)(t,a),s=function(e){let t=i.useRef({value:e,previous:e});return i.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}(r);return i.useEffect(()=>{let e=a.current,t=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(s!==r&&t){let n=new Event("change",{bubbles:!0});t.call(e,r),e.dispatchEvent(n)}},[s,r]),(0,o.jsx)(m0.s,{asChild:!0,children:(0,o.jsx)("select",{...n,ref:l,defaultValue:r})})});function vW(e){let t=(0,mQ.c)(e),r=i.useRef(""),n=i.useRef(0),o=i.useCallback(e=>{let o=r.current+e;t(o),function e(t){r.current=t,window.clearTimeout(n.current),""!==t&&(n.current=window.setTimeout(()=>e(""),1e3))}(o)},[t]),a=i.useCallback(()=>{r.current="",window.clearTimeout(n.current)},[]);return i.useEffect(()=>()=>window.clearTimeout(n.current),[]),[r,o,a]}function vV(e,t,r){var n,o;let i=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,a=r?e.indexOf(r):-1,l=(n=e,o=Math.max(a,0),n.map((e,t)=>n[(o+t)%n.length]));1===i.length&&(l=l.filter(e=>e!==r));let s=l.find(e=>e.textValue.toLowerCase().startsWith(i.toLowerCase()));return s!==r?s:void 0}vq.displayName="BubbleSelect";let vH=(0,P.A)("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]]);var vG=r(58450);let vX=i.forwardRef(({className:e,children:t,...r},n)=>(0,o.jsxs)(va,{ref:n,className:(0,W.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",e),...r,children:[t,(0,o.jsx)(vc,{asChild:!0,children:(0,o.jsx)(eH,{className:"h-4 w-4 opacity-50"})})]}));vX.displayName=va.displayName;let vY=i.forwardRef(({className:e,...t},r)=>(0,o.jsx)(vR,{ref:r,className:(0,W.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,o.jsx)(vH,{className:"h-4 w-4"})}));vY.displayName=vR.displayName;let vK=i.forwardRef(({className:e,...t},r)=>(0,o.jsx)(vL,{ref:r,className:(0,W.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,o.jsx)(eH,{className:"h-4 w-4"})}));vK.displayName=vL.displayName;let vZ=i.forwardRef(({className:e,children:t,position:r="popper",...n},i)=>(0,o.jsx)(vu,{children:(0,o.jsxs)(vd,{ref:i,className:(0,W.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===r&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:r,...n,children:[(0,o.jsx)(vY,{}),(0,o.jsx)(vw,{className:(0,W.cn)("p-1","popper"===r&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:t}),(0,o.jsx)(vK,{})]})}));vZ.displayName=vd.displayName,i.forwardRef(({className:e,...t},r)=>(0,o.jsx)(vP,{ref:r,className:(0,W.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",e),...t})).displayName=vP.displayName;let vJ=i.forwardRef(({className:e,children:t,...r},n)=>(0,o.jsxs)(vM,{ref:n,className:(0,W.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...r,children:[(0,o.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,o.jsx)(vI,{children:(0,o.jsx)(vG.A,{className:"h-4 w-4"})})}),(0,o.jsx)(vT,{children:t})]}));vJ.displayName=vM.displayName,i.forwardRef(({className:e,...t},r)=>(0,o.jsx)(vz,{ref:r,className:(0,W.cn)("-mx-1 my-1 h-px bg-muted",e),...t})).displayName=vz.displayName;let vQ=(0,P.A)("Flag",[["path",{d:"M4 15s1-1 4-1 5 2 8 2 4-1 4-1V3s-1 1-4 1-5-2-8-2-4 1-4 1z",key:"i9b6wo"}],["line",{x1:"4",x2:"4",y1:"22",y2:"15",key:"1cm3nv"}]]),v0=(0,P.A)("Plane",[["path",{d:"M17.8 19.2 16 11l3.5-3.5C21 6 21.5 4 21 3c-1-.5-3 0-4.5 1.5L13 8 4.8 6.2c-.5-.1-.9.1-1.1.5l-.3.5c-.2.5-.1 1 .3 1.3L9 12l-2 3H4l-1 1 3 2 2 3 1-1v-3l3-2 3.5 5.3c.3.4.8.5 1.3.3l.5-.2c.4-.3.6-.7.5-1.2z",key:"1v9wt8"}]]),v1=(0,P.A)("ShoppingBag",[["path",{d:"M6 2 3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4Z",key:"hou9p0"}],["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M16 10a4 4 0 0 1-8 0",key:"1ltviw"}]]),v2=(0,P.A)("Car",[["path",{d:"M19 17h2c.6 0 1-.4 1-1v-3c0-.9-.7-1.7-1.5-1.9C18.7 10.6 16 10 16 10s-1.3-1.4-2.2-2.3c-.5-.4-1.1-.7-1.8-.7H5c-.6 0-1.1.4-1.4.9l-1.4 2.9A3.7 3.7 0 0 0 2 12v4c0 .6.4 1 1 1h2",key:"5owen"}],["circle",{cx:"7",cy:"17",r:"2",key:"u2ysq9"}],["path",{d:"M9 17h6",key:"r8uit2"}],["circle",{cx:"17",cy:"17",r:"2",key:"axvx0g"}]]),v5=(0,P.A)("House",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]]),v4=(0,P.A)("Briefcase",[["path",{d:"M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16",key:"jecpp"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]]),v3=(0,P.A)("GraduationCap",[["path",{d:"M21.42 10.922a1 1 0 0 0-.019-1.838L12.83 5.18a2 2 0 0 0-1.66 0L2.6 9.08a1 1 0 0 0 0 1.832l8.57 3.908a2 2 0 0 0 1.66 0z",key:"j76jl0"}],["path",{d:"M22 10v6",key:"1lu8f3"}],["path",{d:"M6 12.5V16a6 3 0 0 0 12 0v-3.5",key:"1r8lef"}]]),v6=(0,P.A)("Heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]]),v8=(0,P.A)("Gift",[["rect",{x:"3",y:"8",width:"18",height:"4",rx:"1",key:"bkv52"}],["path",{d:"M12 8v13",key:"1c76mn"}],["path",{d:"M19 12v7a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2v-7",key:"6wjy6b"}],["path",{d:"M7.5 8a2.5 2.5 0 0 1 0-5A4.8 8 0 0 1 12 8a4.8 8 0 0 1 4.5-5 2.5 2.5 0 0 1 0 5",key:"1ihvrl"}]]),v7=tD.Ik({name:tD.Yj().min(1,{message:"Goal name is required."}).max(50,{message:"Name must be 50 characters or less."}),targetAmount:tD.vk(e=>"string"==typeof e?parseFloat(e):e,tD.ai().min(1,{message:"Target amount must be greater than 0."})),icon:tD.Yj().optional()}),v9=[{value:"Default",label:"Default",Icon:vQ},{value:"Savings",label:"Savings",Icon:({className:e})=>(0,o.jsxs)("svg",{className:e,xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[(0,o.jsx)("path",{d:"M10 21h4"}),(0,o.jsx)("path",{d:"M12 17v4"}),(0,o.jsx)("path",{d:"M10 3h4c2.2 0 4 1.8 4 4v2c0 1.1-.9 2-2 2h-1"}),(0,o.jsx)("path",{d:"M8 11V7c0-2.2 1.8-4 4-4"}),(0,o.jsx)("path",{d:"M19 13c0-1.66-1.34-3-3-3h-2V7"}),(0,o.jsx)("path",{d:"M10 13c2.2 0 4-1.8 4-4"}),(0,o.jsx)("path",{d:"M2 13c2.5 0 2.5-3 5-3s2.5 3 5 3c2.5 0 2.5-3 5-3s2.5 3 5 3"}),(0,o.jsx)("path",{d:"M7.5 13s.5-1 2.5-1 2.5 1 2.5 1"}),(0,o.jsx)("path",{d:"M14 13c2 0 2.5-1 2.5-1"}),(0,o.jsx)("path",{d:"M2 17h.01"})]})},{value:"Vacation",label:"Vacation",Icon:v0},{value:"Shopping",label:"Shopping",Icon:v1},{value:"Car",label:"Car",Icon:v2},{value:"Home",label:"Home Renovation",Icon:v5},{value:"Business",label:"Business",Icon:v4},{value:"Education",label:"Education",Icon:v3},{value:"Wedding",label:"Wedding",Icon:v6},{value:"Gift",label:"Gift",Icon:v8}];function ge({onSubmit:e,initialData:t,onClose:r}){let n=(0,tI.mN)({resolver:(0,t_.u)(v7),defaultValues:{name:t?.name||"",targetAmount:t?.targetAmount||0,icon:t?.icon||"Default"}});return(0,o.jsx)(tR.lV,{...n,children:(0,o.jsxs)("form",{onSubmit:n.handleSubmit(o=>{e(o.name,o.targetAmount,o.icon),t||n.reset({name:"",targetAmount:0,icon:"Default"}),r()}),className:"space-y-4 pt-2",children:[(0,o.jsx)(tR.zB,{control:n.control,name:"name",render:({field:e})=>(0,o.jsxs)(tR.eI,{children:[(0,o.jsx)(tR.lR,{children:"Goal Name"}),(0,o.jsx)(tR.MJ,{children:(0,o.jsx)(O.p,{placeholder:"e.g., New Laptop, Vacation Fund",...e})}),(0,o.jsx)(tR.C5,{})]})}),(0,o.jsx)(tR.zB,{control:n.control,name:"targetAmount",render:({field:e})=>(0,o.jsxs)(tR.eI,{children:[(0,o.jsx)(tR.lR,{children:"Target Amount (R)"}),(0,o.jsx)(tR.MJ,{children:(0,o.jsx)(O.p,{type:"number",placeholder:"e.g., 15000",...e,step:"any"})}),(0,o.jsx)(tR.C5,{})]})}),(0,o.jsx)(tR.zB,{control:n.control,name:"icon",render:({field:e})=>(0,o.jsxs)(tR.eI,{children:[(0,o.jsx)(tR.lR,{children:"Goal Icon (Optional)"}),(0,o.jsxs)(vo,{onValueChange:e.onChange,defaultValue:e.value,children:[(0,o.jsx)(tR.MJ,{children:(0,o.jsx)(vX,{children:(0,o.jsx)(vs,{placeholder:"Select an icon"})})}),(0,o.jsx)(vZ,{children:v9.map(e=>(0,o.jsx)(vJ,{value:e.value,children:(0,o.jsxs)("div",{className:"flex items-center gap-2",children:[(0,o.jsx)(e.Icon,{className:"h-4 w-4"}),e.label]})},e.value))})]}),(0,o.jsx)(tR.C5,{})]})}),(0,o.jsxs)("div",{className:"flex justify-end gap-2 pt-2",children:[(0,o.jsx)(K.$,{type:"button",variant:"outline",onClick:r,children:"Cancel"}),(0,o.jsx)(K.$,{type:"submit",children:t?"Save Changes":"Set Goal"})]})]})})}var gt=r(35849);let gr=(0,P.A)("Award",[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]]),gn=(0,P.A)("Target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]]),go=(0,P.A)("CircleCheck",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]]),gi={Default:vQ,Vacation:gt.A,Gadget:gr};function ga({goal:e,onSetGoal:t,onUpdateProgress:r,onClearGoal:n,overallRemaining:a,balancesVisible:l}){let[s,c]=(0,i.useState)(!1),[u,f]=(0,i.useState)(!1),[d,p]=(0,i.useState)(0),[h,y]=(0,i.useState)(!1),m=e=>l?`R ${e.toFixed(2)}`:"R ••••",v=e?.icon&&gi[e.icon]?gi[e.icon]:gn;if(!e||e.dateAchieved)return(0,o.jsxs)(A.Zp,{children:[(0,o.jsxs)(A.aR,{className:"pb-2",children:[(0,o.jsxs)(A.ZB,{className:"text-lg flex items-center gap-2 font-headline",children:[e?.dateAchieved?(0,o.jsx)(go,{className:"h-5 w-5 text-green-500"}):(0,o.jsx)(gn,{className:"h-5 w-5 text-primary"}),e?.dateAchieved?"Goal Achieved!":"Financial Goal"]}),e?.dateAchieved&&e&&(0,o.jsxs)(A.BT,{className:"text-sm",children:["Congrats on achieving: ",e.name,"!"]})]}),(0,o.jsxs)(A.Wu,{children:[e?.dateAchieved&&e&&(0,o.jsxs)("div",{className:"space-y-1 text-center",children:[(0,o.jsx)("p",{className:"text-2xl font-bold text-green-600",children:m(e.targetAmount)}),(0,o.jsxs)("p",{className:"text-xs text-muted-foreground",children:["Achieved on ",new Date(e.dateAchieved).toLocaleDateString()]})]}),(0,o.jsx)("p",{className:`text-sm text-muted-foreground ${e?.dateAchieved?"mt-2 text-center":"text-center py-4"}`,children:e?.dateAchieved?"Ready for a new challenge?":"Set a financial goal to start saving towards something important!"})]}),(0,o.jsx)(A.wL,{children:(0,o.jsxs)(e7,{open:s,onOpenChange:c,children:[(0,o.jsx)(te,{asChild:!0,children:(0,o.jsxs)(K.$,{className:"w-full",onClick:()=>c(!0),children:[(0,o.jsx)(tU,{className:"mr-2 h-4 w-4"})," ",e?.dateAchieved?"Set New Goal":"Set a Goal"]})}),(0,o.jsxs)(tk,{className:"sm:max-w-[425px]",children:[(0,o.jsx)(tE,{children:(0,o.jsx)(tN,{className:"font-headline",children:e?.dateAchieved?"Set New Financial Goal":"Set Financial Goal"})}),(0,o.jsx)(ge,{onSubmit:(e,r,n)=>{t(e,r,n),c(!1)},initialData:null,onClose:()=>c(!1)})]})]})})]});let g=e.targetAmount>0?e.savedAmount/e.targetAmount*100:0;return(0,o.jsxs)(A.Zp,{children:[(0,o.jsxs)(A.aR,{className:"pb-3 pt-4",children:[(0,o.jsxs)("div",{className:"flex items-center justify-between",children:[(0,o.jsxs)(A.ZB,{className:"text-lg flex items-center gap-2 font-headline",children:[(0,o.jsx)(v,{className:"h-5 w-5 text-primary"}),e.name]}),(0,o.jsxs)("div",{className:"flex gap-1",children:[(0,o.jsxs)(e7,{open:s,onOpenChange:c,children:[(0,o.jsx)(te,{asChild:!0,children:(0,o.jsx)(K.$,{variant:"ghost",size:"icon",className:"h-7 w-7",onClick:()=>c(!0),children:(0,o.jsx)(tM,{className:"h-4 w-4"})})}),(0,o.jsxs)(tk,{className:"sm:max-w-[425px]",children:[(0,o.jsx)(tE,{children:(0,o.jsx)(tN,{className:"font-headline",children:"Edit Financial Goal"})}),(0,o.jsx)(ge,{onSubmit:(e,r,n)=>{t(e,r,n),c(!1)},initialData:e,onClose:()=>c(!1)})]})]}),(0,o.jsx)(K.$,{variant:"ghost",size:"icon",className:"h-7 w-7 text-destructive hover:text-destructive",onClick:()=>y(!0),children:(0,o.jsx)(tC.A,{className:"h-4 w-4"})})]})]}),(0,o.jsxs)(A.BT,{className:"text-xs pt-1",children:["Target: ",m(e.targetAmount)," | Saved: ",m(e.savedAmount)]})]}),(0,o.jsxs)(A.Wu,{className:"space-y-3",children:[(0,o.jsx)(V,{value:g,className:"h-2 [&>*]:bg-primary"}),(0,o.jsxs)("div",{className:"text-xs text-muted-foreground",children:[m(e.targetAmount-e.savedAmount)," still to go. You can do it!"]}),l&&a>0&&(0,o.jsxs)("p",{className:"text-xs text-green-600 bg-green-500/10 p-1.5 rounded-md",children:["You have ",m(a)," unallocated in your budget. Consider putting some towards your goal!"]})]}),(0,o.jsx)(A.wL,{children:(0,o.jsxs)(e7,{open:u,onOpenChange:f,children:[(0,o.jsx)(te,{asChild:!0,children:(0,o.jsx)(K.$,{className:"w-full",variant:"outline",onClick:()=>{p(e.savedAmount),f(!0)},children:"Log Progress"})}),(0,o.jsxs)(tk,{className:"sm:max-w-[425px]",children:[(0,o.jsx)(tE,{children:(0,o.jsxs)(tN,{className:"font-headline",children:["Log Progress for ",e.name]})}),(0,o.jsxs)("div",{className:"space-y-4 py-2",children:[(0,o.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Current Target: ",m(e.targetAmount)]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{htmlFor:"progressAmount",className:"block text-sm font-medium text-foreground mb-1",children:"Total Amount Saved Towards Goal (R)"}),(0,o.jsx)("input",{id:"progressAmount",type:l?"number":"text",value:l?d:"••••",onChange:e=>{let t=parseFloat(e.target.value);p(Math.max(0,isNaN(t)?0:t))},readOnly:!l,className:"w-full p-2 border rounded-md border-input",step:"any"})]}),(0,o.jsx)(K.$,{onClick:()=>{r(d),f(!1)},className:"w-full",children:"Save Progress"})]})]})]})}),(0,o.jsx)(tK,{open:h,onOpenChange:y,children:(0,o.jsxs)(rr,{children:[(0,o.jsxs)(rn,{children:[(0,o.jsx)(ri,{children:"Are you sure?"}),(0,o.jsxs)(ra,{children:['This will clear your current financial goal "',e.name,'". This action cannot be undone.']})]}),(0,o.jsxs)(ro,{children:[(0,o.jsx)(rs,{children:"Cancel"}),(0,o.jsx)(rl,{onClick:()=>{n(),y(!1)},className:"bg-destructive hover:bg-destructive/90",children:"Clear Goal"})]})]})})]})}var gl=r(85726);let gs=(0,P.A)("Trophy",[["path",{d:"M6 9H4.5a2.5 2.5 0 0 1 0-5H6",key:"17hqa7"}],["path",{d:"M18 9h1.5a2.5 2.5 0 0 0 0-5H18",key:"lmptdp"}],["path",{d:"M4 22h16",key:"57wxv0"}],["path",{d:"M10 14.66V17c0 .55-.47.98-.97 1.21C7.85 18.75 7 20.24 7 22",key:"1nw9bq"}],["path",{d:"M14 14.66V17c0 .55.47.98.97 1.21C16.15 18.75 17 20.24 17 22",key:"1np0yb"}],["path",{d:"M18 2H6v7a6 6 0 0 0 12 0V2Z",key:"u46fv3"}]]),gc=(0,P.A)("Star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]]),gu={trophy:gs,star:gc,target:gn,zap:(0,P.A)("Zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]]),award:gr,crown:(0,P.A)("Crown",[["path",{d:"M11.562 3.266a.5.5 0 0 1 .876 0L15.39 8.87a1 1 0 0 0 1.516.294L21.183 5.5a.5.5 0 0 1 .798.519l-2.834 10.246a1 1 0 0 1-.956.734H5.81a1 1 0 0 1-.957-.734L2.02 6.02a.5.5 0 0 1 .798-.519l4.276 3.664a1 1 0 0 0 1.516-.294z",key:"1vdc57"}],["path",{d:"M5 21h14",key:"11awu3"}]])},gf={gold:"bg-gradient-to-r from-yellow-400 to-yellow-600 text-yellow-900 border-yellow-500/30",silver:"bg-gradient-to-r from-gray-300 to-gray-500 text-gray-900 border-gray-400/30",bronze:"bg-gradient-to-r from-orange-400 to-orange-600 text-orange-900 border-orange-500/30",primary:"bg-gradient-to-r from-primary to-primary/80 text-primary-foreground border-primary/30",success:"bg-gradient-to-r from-green-400 to-green-600 text-green-900 border-green-500/30"},gd={sm:"px-2 py-1 text-xs",md:"px-3 py-1.5 text-sm",lg:"px-4 py-2 text-base"};function gp({title:e,description:t,icon:r="trophy",variant:n="gold",size:i="md",animated:a=!0,showConfetti:l=!1,className:s}){let c=gu[r],u=(0,o.jsxs)("div",{className:(0,W.cn)("inline-flex items-center gap-2 rounded-full font-medium border shadow-lg",gf[n],gd[i],a&&"hover:scale-105 transition-transform duration-200",s),children:[(0,o.jsx)(c,{className:(0,W.cn)("shrink-0","sm"===i?"h-3 w-3":"md"===i?"h-4 w-4":"h-5 w-5")}),(0,o.jsx)("span",{className:"font-semibold",children:e})]});return a?(0,o.jsxs)(w.P.div,{initial:{scale:0,opacity:0},animate:{scale:1,opacity:1},transition:{type:"spring",stiffness:260,damping:20,delay:.1},whileHover:{scale:1.05},whileTap:{scale:.95},className:"inline-block",children:[u,t&&(0,o.jsx)(w.P.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.3},className:"text-xs text-muted-foreground mt-1 text-center",children:t})]}):u}function gh({title:e,description:t,icon:r="trophy",variant:n="gold",onClose:i}){let a=gu[r];return(0,o.jsxs)(w.P.div,{initial:{x:300,opacity:0},animate:{x:0,opacity:1},exit:{x:300,opacity:0},transition:{type:"spring",stiffness:300,damping:30},className:(0,W.cn)("flex items-center gap-3 p-4 rounded-lg border shadow-lg max-w-sm","bg-card/95 backdrop-blur-sm border-border/50"),children:[(0,o.jsx)(w.P.div,{animate:{rotate:[0,10,-10,0]},transition:{duration:.6,repeat:2},className:(0,W.cn)("flex items-center justify-center w-10 h-10 rounded-full",gf[n]),children:(0,o.jsx)(a,{className:"h-5 w-5"})}),(0,o.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,o.jsx)("h4",{className:"font-semibold text-foreground",children:e}),(0,o.jsx)("p",{className:"text-sm text-muted-foreground",children:t})]}),i&&(0,o.jsx)("button",{onClick:i,className:"text-muted-foreground hover:text-foreground transition-colors",children:"\xd7"})]})}var gy=r(78850);let gm=(0,P.A)("WalletCards",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2",key:"4125el"}],["path",{d:"M3 11h3c.8 0 1.6.3 2.1.9l1.1.9c1.6 1.6 4.1 1.6 5.7 0l1.1-.9c.5-.5 1.3-.9 2.1-.9H21",key:"1dpki6"}]]),gv={BUDGET_STARTER:"BUDGET_STARTER",GOAL_SETTER:"GOAL_SETTER",GOAL_CRUSHER:"GOAL_CRUSHER"},gg={[gv.BUDGET_STARTER]:{title:"Budget Starter!",description:"You've set your income and added your first category!",IconComponent:gm},[gv.GOAL_SETTER]:{title:"Goal Setter!",description:"You've set your first financial goal!",IconComponent:gn},[gv.GOAL_CRUSHER]:{title:"Goal Crusher!",description:"Congratulations! You've achieved your financial goal!",IconComponent:gs}};function gb(){let{currentUser:e,loading:t}=(0,gy.A)();(0,l.useRouter)();let[r,n]=(0,i.useState)(!1),[a,s]=(0,i.useState)(0),[c,u]=(0,i.useState)([]),[f,d]=(0,i.useState)(null),[p,h]=(0,i.useState)([]),[y,m]=(0,i.useState)(!0),[v,g]=(0,i.useState)(null),[b,O]=(0,i.useState)(!0),{toast:S}=(0,tF.dj)();(0,i.useCallback)(()=>e?`budgetWiseData_${e.uid}`:null,[e]);let A=(0,i.useCallback)(e=>{if(!p.includes(e)){h(t=>[...t,e]),g(e);let t=gg[e];t&&(S({title:(0,o.jsxs)("div",{className:"flex items-center gap-2",children:[(0,o.jsx)(t.IconComponent,{className:"h-5 w-5 text-accent"}),(0,o.jsx)("span",{children:t.title})]}),description:t.description}),setTimeout(()=>g(null),5e3))}},[p,S]),P=(0,i.useCallback)(()=>{m(e=>!e)},[]),k=(0,i.useCallback)(e=>{u(t=>t.map(t=>t.id===e?{...t,isVisible:!(t.isVisible??!0)}:t))},[]),N=(0,i.useCallback)(e=>{s(e)},[]),M=(0,i.useCallback)((e,t)=>{let r={id:crypto.randomUUID(),name:e,budget:t,subCategories:[],isVisible:!0};u(e=>[...e,r])},[]),C=(0,i.useCallback)((e,t,r)=>{u(n=>n.map(n=>n.id===e?{...n,name:t,budget:r}:n))},[]),T=(0,i.useCallback)(e=>{u(t=>t.filter(t=>t.id!==e))},[]),_=(0,i.useCallback)((e,t,r)=>{let n=!1;return u(o=>o.map(o=>{if(o.id===e){if(o.subCategories.reduce((e,t)=>e+t.allocatedAmount,0)+r>o.budget)return n=!1,o;let e={id:crypto.randomUUID(),name:t,allocatedAmount:r};return n=!0,{...o,subCategories:[...o.subCategories,e]}}return o})),n},[]),I=(0,i.useCallback)((e,t,r,n)=>{let o=!1;return u(i=>i.map(i=>i.id===e?i.subCategories.filter(e=>e.id!==t).reduce((e,t)=>e+t.allocatedAmount,0)+n>i.budget?(o=!1,i):(o=!0,{...i,subCategories:i.subCategories.map(e=>e.id===t?{...e,name:r,allocatedAmount:n}:e)}):i)),o},[]),D=(0,i.useCallback)((e,t)=>{u(r=>r.map(r=>r.id===e?{...r,subCategories:r.subCategories.filter(e=>e.id!==t)}:r)),S({title:"Subcategory Deleted",description:"Subcategory has been removed."})},[S]),R=(0,i.useCallback)((e,t,r)=>{d({id:f?.id||crypto.randomUUID(),name:e,targetAmount:t,savedAmount:f?.id?f.savedAmount:0,icon:r,dateSet:f?.dateSet||new Date().toISOString(),dateAchieved:null}),p.includes(gv.GOAL_SETTER)&&f||A(gv.GOAL_SETTER),S({title:"Financial Goal Updated!",description:`Your goal "${e}" has been set/updated.`})},[f,A,S,p]),B=(0,i.useCallback)(e=>{if(f){let t={...f,savedAmount:e};e>=f.targetAmount&&!f.dateAchieved&&(t.dateAchieved=new Date().toISOString(),A(gv.GOAL_CRUSHER),S({title:"Goal Achieved!",description:`Congratulations on reaching your goal: ${f.name}!`,duration:5e3})),d(t)}},[f,A,S]),L=(0,i.useCallback)(()=>{d(null),S({title:"Financial Goal Cleared",description:"Your financial goal has been removed."})},[S]),$=c.reduce((e,t)=>e+t.budget,0),z=a-$;return!t&&r&&e?(0,o.jsxs)("div",{className:"flex flex-col min-h-screen bg-background",children:[(0,o.jsx)(j.default,{title:"BudgetWise",balancesVisible:y,onToggleBalances:P}),(0,o.jsx)(x,{children:v&&(0,o.jsx)(w.P.div,{initial:{y:-100,opacity:0},animate:{y:0,opacity:1},exit:{y:-100,opacity:0},className:"fixed top-20 right-4 z-50",children:(0,o.jsx)(gh,{title:gg[v]?.title||"Achievement Unlocked!",description:gg[v]?.description||"You've earned a new achievement!",icon:"trophy",variant:"gold",onClose:()=>g(null)})})}),(0,o.jsxs)("main",{className:"flex-grow container mx-auto py-4 sm:py-6 px-2 sm:px-4",children:[p.length>0&&(0,o.jsx)(w.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"mb-6 flex flex-wrap gap-2",children:p.map(e=>(0,o.jsx)(gp,{title:gg[e]?.title||"Achievement",icon:"trophy",variant:"gold",size:"sm",animated:!0},e))}),(0,o.jsxs)(w.P.div,{initial:{opacity:0},animate:{opacity:1},transition:{duration:.5},className:"grid grid-cols-1 md:grid-cols-3 gap-4 sm:gap-6",children:[(0,o.jsxs)(w.P.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{duration:.6,delay:.1},className:"md:col-span-1 space-y-4 sm:space-y-6",children:[(0,o.jsx)(E,{totalIncome:a,onIncomeChange:N,balancesVisible:y}),(0,o.jsx)(Y,{totalIncome:a,overallTotalAllocated:$,balancesVisible:y}),(0,o.jsx)(ga,{goal:f,onSetGoal:R,onUpdateProgress:B,onClearGoal:L,overallRemaining:z,balancesVisible:y}),(0,o.jsx)(mY,{totalIncome:a,categories:c,balancesVisible:y})]}),(0,o.jsx)(w.P.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},transition:{duration:.6,delay:.2},className:"md:col-span-2",children:(0,o.jsx)(rd,{categories:c,onAddCategory:M,onUpdateCategory:C,onDeleteCategory:T,onAddSubCategory:_,onUpdateSubCategory:I,onDeleteSubCategory:D,onToggleCategoryVisibility:k,balancesVisible:y})})]})]})]}):(0,o.jsxs)("div",{className:"flex flex-col min-h-screen",children:[(0,o.jsx)(j.default,{title:"BudgetWise",balancesVisible:y,onToggleBalances:P}),(0,o.jsx)(gl.eX,{})]})}},38404:(e,t,r)=>{var n=r(29395),o=r(65932),i=r(27467),a=Object.prototype,l=Function.prototype.toString,s=a.hasOwnProperty,c=l.call(Object);e.exports=function(e){if(!i(e)||"[object Object]"!=n(e))return!1;var t=o(e);if(null===t)return!0;var r=s.call(t,"constructor")&&t.constructor;return"function"==typeof r&&r instanceof r&&l.call(r)==c}},38428:e=>{var t=/^(?:0|[1-9]\d*)$/;e.exports=function(e,r){var n=typeof e;return!!(r=null==r?0x1fffffffffffff:r)&&("number"==n||"symbol"!=n&&t.test(e))&&e>-1&&e%1==0&&e<r}},39672:(e,t,r)=>{var n=r(58141);e.exports=function(e,t){var r=this.__data__;return this.size+=+!this.has(e),r[e]=n&&void 0===t?"__lodash_hash_undefined__":t,this}},39774:e=>{e.exports=function(e){return e!=e}},40491:(e,t,r)=>{var n=r(1707);e.exports=function(e,t,r){var o=null==e?void 0:n(e,t);return void 0===o?r:o}},40542:e=>{e.exports=Array.isArray},41011:(e,t,r)=>{var n=r(41353);e.exports=function(e,t,r){var o=e.length;return r=void 0===r?o:r,!t&&r>=o?e:n(e,t,r)}},41132:e=>{e.exports=function(e,t){return function(r){return null!=r&&r[e]===t&&(void 0!==t||e in Object(r))}}},41157:(e,t,r)=>{var n=r(91928);e.exports=function(e,t,r){"__proto__"==t&&n?n(e,t,{configurable:!0,enumerable:!0,value:r,writable:!0}):e[t]=r}},41353:e=>{e.exports=function(e,t,r){var n=-1,o=e.length;t<0&&(t=-t>o?0:o+t),(r=r>o?o:r)<0&&(r+=o),o=t>r?0:r-t>>>0,t>>>=0;for(var i=Array(o);++n<o;)i[n]=e[n+t];return i}},41547:(e,t,r)=>{var n=r(61548),o=r(90851);e.exports=function(e,t){var r=o(e,t);return n(r)?r:void 0}},41693:e=>{e.exports=function(e,t){for(var r=-1,n=t.length,o=e.length;++r<n;)e[o+r]=t[r];return e}},42082:e=>{e.exports=function(e){return function(t){return null==t?void 0:t[e]}}},42205:(e,t,r)=>{var n=r(41693),o=r(85450);e.exports=function e(t,r,i,a,l){var s=-1,c=t.length;for(i||(i=o),l||(l=[]);++s<c;){var u=t[s];r>0&&i(u)?r>1?e(u,r-1,i,a,l):n(l,u):a||(l[l.length]=u)}return l}},42403:(e,t,r)=>{var n=r(80195);e.exports=function(e){return null==e?"":n(e)}},43378:e=>{e.exports=function(e,t){var r=e.length;for(e.sort(t);r--;)e[r]=e[r].value;return e}},43707:(e,t,r)=>{Promise.resolve().then(r.bind(r,37733))},44493:(e,t,r)=>{"use strict";r.d(t,{BT:()=>c,Wu:()=>u,ZB:()=>s,Zp:()=>a,aR:()=>l,wL:()=>f});var n=r(60687),o=r(43210),i=r(4780);let a=o.forwardRef(({className:e,...t},r)=>(0,n.jsx)("div",{ref:r,className:(0,i.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...t}));a.displayName="Card";let l=o.forwardRef(({className:e,...t},r)=>(0,n.jsx)("div",{ref:r,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",e),...t}));l.displayName="CardHeader";let s=o.forwardRef(({className:e,...t},r)=>(0,n.jsx)("div",{ref:r,className:(0,i.cn)("text-2xl font-semibold leading-none tracking-tight",e),...t}));s.displayName="CardTitle";let c=o.forwardRef(({className:e,...t},r)=>(0,n.jsx)("div",{ref:r,className:(0,i.cn)("text-sm text-muted-foreground",e),...t}));c.displayName="CardDescription";let u=o.forwardRef(({className:e,...t},r)=>(0,n.jsx)("div",{ref:r,className:(0,i.cn)("p-6 pt-0",e),...t}));u.displayName="CardContent";let f=o.forwardRef(({className:e,...t},r)=>(0,n.jsx)("div",{ref:r,className:(0,i.cn)("flex items-center p-6 pt-0",e),...t}));f.displayName="CardFooter"},45058:(e,t,r)=>{var n=r(42082),o=r(8852),i=r(67619),a=r(46436);e.exports=function(e){return i(e)?n(a(e)):o(e)}},45603:(e,t,r)=>{var n=r(20540),o=r(55048);e.exports=function(e,t,r){var i=!0,a=!0;if("function"!=typeof e)throw TypeError("Expected a function");return o(r)&&(i="leading"in r?!!r.leading:i,a="trailing"in r?!!r.trailing:a),n(e,t,{leading:i,maxWait:t,trailing:a})}},45803:e=>{e.exports=function(e){var t=typeof e;return"string"==t||"number"==t||"symbol"==t||"boolean"==t?"__proto__"!==e:null===e}},46063:e=>{e.exports=function(e,t){return e<t}},46229:(e,t,r)=>{var n=r(48169),o=r(66354),i=r(11424);e.exports=function(e,t){return i(o(e,t,n),e+"")}},46328:(e,t,r)=>{var n=r(95746),o=r(89185),i=r(16854);function a(e){var t=-1,r=null==e?0:e.length;for(this.__data__=new n;++t<r;)this.add(e[t])}a.prototype.add=a.prototype.push=o,a.prototype.has=i,e.exports=a},46436:(e,t,r)=>{var n=r(49227),o=1/0;e.exports=function(e){if("string"==typeof e||n(e))return e;var t=e+"";return"0"==t&&1/e==-o?"-0":t}},47212:(e,t,r)=>{var n=r(87270),o=r(30316),i=r(22),a=r(40542),l=r(7383);e.exports=function(e,t,r){var s=a(e)?n:o;return r&&l(e,t,r)&&(t=void 0),s(e,i(t,3))}},47282:(e,t,r)=>{e=r.nmd(e);var n=r(10663),o=t&&!t.nodeType&&t,i=o&&e&&!e.nodeType&&e,a=i&&i.exports===o&&n.process,l=function(){try{var e=i&&i.require&&i.require("util").types;if(e)return e;return a&&a.binding&&a.binding("util")}catch(e){}}();e.exports=l},47603:(e,t,r)=>{var n=r(14675),o=r(91928),i=r(48169);e.exports=o?function(e,t){return o(e,"toString",{configurable:!0,enumerable:!1,value:n(t),writable:!0})}:i},48169:e=>{e.exports=function(e){return e}},48385:e=>{var t="\ud800-\udfff",r="[\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff]",n="\ud83c[\udffb-\udfff]",o="[^"+t+"]",i="(?:\ud83c[\udde6-\uddff]){2}",a="[\ud800-\udbff][\udc00-\udfff]",l="(?:"+r+"|"+n+")?",s="[\\ufe0e\\ufe0f]?",c="(?:\\u200d(?:"+[o,i,a].join("|")+")"+s+l+")*",u=RegExp(n+"(?="+n+")|"+("(?:"+[o+r+"?",r,i,a,"["+t+"]"].join("|"))+")"+(s+l+c),"g");e.exports=function(e){return e.match(u)||[]}},49227:(e,t,r)=>{var n=r(29395),o=r(27467);e.exports=function(e){return"symbol"==typeof e||o(e)&&"[object Symbol]"==n(e)}},51449:(e,t,r)=>{var n=r(85745),o=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,i=/\\(\\)?/g;e.exports=n(function(e){var t=[];return 46===e.charCodeAt(0)&&t.push(""),e.replace(o,function(e,r,n,o){t.push(n?o.replace(i,"$1"):r||e)}),t})},52599:e=>{e.exports=function(e,t){for(var r=-1,n=null==e?0:e.length,o=0,i=[];++r<n;){var a=e[r];t(a,r,e)&&(i[o++]=a)}return i}},52823:(e,t,r)=>{var n=r(85406),o=function(){var e=/[^.]+$/.exec(n&&n.keys&&n.keys.IE_PROTO||"");return e?"Symbol(src)_1."+e:""}();e.exports=function(e){return!!o&&o in e}},52931:(e,t,r)=>{var n=r(77834),o=r(89605),i=Object.prototype.hasOwnProperty;e.exports=function(e){if(!n(e))return o(e);var t=[];for(var r in Object(e))i.call(e,r)&&"constructor"!=r&&t.push(r);return t}},54070:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>a.a,__next_app__:()=>f,pages:()=>u,routeModule:()=>d,tree:()=>c});var n=r(65239),o=r(48088),i=r(88170),a=r.n(i),l=r(30893),s={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(s[e]=()=>l[e]);r.d(t,s);let c={children:["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,21204)),"C:\\Users\\<USER>\\Desktop\\Programming\\BudgetWise\\src\\app\\page.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\Desktop\\Programming\\BudgetWise\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,u=["C:\\Users\\<USER>\\Desktop\\Programming\\BudgetWise\\src\\app\\page.tsx"],f={require:r,loadChunk:()=>Promise.resolve()},d=new n.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},54765:(e,t,r)=>{var n=r(67554),o=r(32269);e.exports=function(e,t){var r=-1,i=o(e)?Array(e.length):[];return n(e,function(e,n,o){i[++r]=t(e,n,o)}),i}},55048:e=>{e.exports=function(e){var t=typeof e;return null!=e&&("object"==t||"function"==t)}},56506:(e,t,r)=>{var n=r(32269);e.exports=function(e,t){return function(r,o){if(null==r)return r;if(!n(r))return e(r,o);for(var i=r.length,a=t?i:-1,l=Object(r);(t?a--:++a<i)&&!1!==o(l[a],a,l););return r}}},57088:(e,t,r)=>{var n=r(2984),o=r(99180),i=r(22);e.exports=function(e,t){return e&&e.length?n(e,i(t,2),o):void 0}},57207:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(82614).A)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},57797:(e,t,r)=>{var n=r(67009);e.exports=function(e,t){for(var r=e.length;r--;)if(n(e[r][0],t))return r;return -1}},58141:(e,t,r)=>{e.exports=r(41547)(Object,"create")},58276:e=>{e.exports=function(e,t){return e.has(t)}},58744:(e,t,r)=>{var n=r(57797);e.exports=function(e,t){var r=this.__data__,o=n(r,e);return o<0?(++this.size,r.push([e,t])):r[o][1]=t,this}},59467:(e,t,r)=>{var n=r(35142),o=r(35163),i=r(40542),a=r(38428),l=r(69619),s=r(46436);e.exports=function(e,t,r){t=n(t,e);for(var c=-1,u=t.length,f=!1;++c<u;){var d=s(t[c]);if(!(f=null!=e&&r(e,d)))break;e=e[d]}return f||++c!=u?f:!!(u=null==e?0:e.length)&&l(u)&&a(d,u)&&(i(e)||o(e))}},59774:e=>{e.exports=function(e){var t=-1,r=Array(e.size);return e.forEach(function(e,n){r[++t]=[n,e]}),r}},61320:(e,t,r)=>{var n=r(8336);e.exports=function(e){return n(this,e).has(e)}},61548:(e,t,r)=>{var n=r(5231),o=r(52823),i=r(55048),a=r(12290),l=/^\[object .+?Constructor\]$/,s=Object.prototype,c=Function.prototype.toString,u=s.hasOwnProperty,f=RegExp("^"+c.call(u).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");e.exports=function(e){return!(!i(e)||o(e))&&(n(e)?f:l).test(a(e))}},61837:(e,t,r)=>{var n=r(21367),o=r(22),i=r(54765),a=r(40542);e.exports=function(e,t){return(a(e)?n:i)(e,o(t,3))}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63866:(e,t,r)=>{var n=r(29395),o=r(40542),i=r(27467);e.exports=function(e){return"string"==typeof e||!o(e)&&i(e)&&"[object String]"==n(e)}},63979:(e,t,r)=>{var n=r(52599),o=r(6330),i=Object.prototype.propertyIsEnumerable,a=Object.getOwnPropertySymbols;e.exports=a?function(e){return null==e?[]:n(a(e=Object(e)),function(t){return i.call(e,t)})}:o},65662:e=>{e.exports=function(e,t){return function(r){return e(t(r))}}},65727:(e,t,r)=>{var n=r(81957);e.exports=function(e,t,r){for(var o=-1,i=e.criteria,a=t.criteria,l=i.length,s=r.length;++o<l;){var c=n(i[o],a[o]);if(c){if(o>=s)return c;return c*("desc"==r[o]?-1:1)}}return e.index-t.index}},65932:(e,t,r)=>{e.exports=r(65662)(Object.getPrototypeOf,Object)},65984:e=>{e.exports=function(e){return function(t,r,n){for(var o=-1,i=Object(t),a=n(t),l=a.length;l--;){var s=a[e?l:++o];if(!1===r(i[s],s,i))break}return t}}},66354:(e,t,r)=>{var n=r(85244),o=Math.max;e.exports=function(e,t,r){return t=o(void 0===t?e.length-1:t,0),function(){for(var i=arguments,a=-1,l=o(i.length-t,0),s=Array(l);++a<l;)s[a]=i[t+a];a=-1;for(var c=Array(t+1);++a<t;)c[a]=i[a];return c[t]=r(s),n(e,this,c)}}},66400:e=>{var t=Date.now;e.exports=function(e){var r=0,n=0;return function(){var o=t(),i=16-(o-n);if(n=o,i>0){if(++r>=800)return arguments[0]}else r=0;return e.apply(void 0,arguments)}}},66713:(e,t,r)=>{var n=r(3105),o=r(34117),i=r(48385);e.exports=function(e){return o(e)?i(e):n(e)}},66837:(e,t,r)=>{var n=r(58141);e.exports=function(){this.__data__=n?n(null):{},this.size=0}},66930:(e,t,r)=>{var n=r(27669),o=r(28837),i=r(94388),a=r(35800),l=r(58744);function s(e){var t=-1,r=null==e?0:e.length;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}s.prototype.clear=n,s.prototype.delete=o,s.prototype.get=i,s.prototype.has=a,s.prototype.set=l,e.exports=s},67009:e=>{e.exports=function(e,t){return e===t||e!=e&&t!=t}},67200:(e,t,r)=>{var n=r(66930),o=r(37575),i=r(75411),a=r(34746),l=r(25118),s=r(30854);function c(e){var t=this.__data__=new n(e);this.size=t.size}c.prototype.clear=o,c.prototype.delete=i,c.prototype.get=a,c.prototype.has=l,c.prototype.set=s,e.exports=c},67367:(e,t,r)=>{var n=r(99525),o=r(22),i=r(75847),a=r(40542),l=r(7383);e.exports=function(e,t,r){var s=a(e)?n:i;return r&&l(e,t,r)&&(t=void 0),s(e,o(t,3))}},67554:(e,t,r)=>{var n=r(99114);e.exports=r(56506)(n)},67619:(e,t,r)=>{var n=r(40542),o=r(49227),i=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,a=/^\w*$/;e.exports=function(e,t){if(n(e))return!1;var r=typeof e;return!!("number"==r||"symbol"==r||"boolean"==r||null==e||o(e))||a.test(e)||!i.test(e)||null!=t&&e in Object(t)}},69433:(e,t,r)=>{e.exports=r(5566)("toUpperCase")},69619:e=>{e.exports=function(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=0x1fffffffffffff}},69691:(e,t,r)=>{var n=r(41157),o=r(99114),i=r(22);e.exports=function(e,t){var r={};return t=i(t,3),o(e,function(e,o,i){n(r,o,t(e,o,i))}),r}},70151:(e,t,r)=>{var n=r(85718);e.exports=function(){return n.Date.now()}},70222:(e,t,r)=>{var n=r(79474),o=Object.prototype,i=o.hasOwnProperty,a=o.toString,l=n?n.toStringTag:void 0;e.exports=function(e){var t=i.call(e,l),r=e[l];try{e[l]=void 0;var n=!0}catch(e){}var o=a.call(e);return n&&(t?e[l]=r:delete e[l]),o}},71669:(e,t,r)=>{"use strict";r.d(t,{C5:()=>g,MJ:()=>m,Rr:()=>v,eI:()=>h,lR:()=>y,lV:()=>c,zB:()=>f});var n=r(60687),o=r(43210),i=r(8730),a=r(27605),l=r(4780),s=r(80013);let c=a.Op,u=o.createContext({}),f=({...e})=>(0,n.jsx)(u.Provider,{value:{name:e.name},children:(0,n.jsx)(a.xI,{...e})}),d=()=>{let e=o.useContext(u),t=o.useContext(p),{getFieldState:r,formState:n}=(0,a.xW)(),i=r(e.name,n);if(!e)throw Error("useFormField should be used within <FormField>");let{id:l}=t;return{id:l,name:e.name,formItemId:`${l}-form-item`,formDescriptionId:`${l}-form-item-description`,formMessageId:`${l}-form-item-message`,...i}},p=o.createContext({}),h=o.forwardRef(({className:e,...t},r)=>{let i=o.useId();return(0,n.jsx)(p.Provider,{value:{id:i},children:(0,n.jsx)("div",{ref:r,className:(0,l.cn)("space-y-2",e),...t})})});h.displayName="FormItem";let y=o.forwardRef(({className:e,...t},r)=>{let{error:o,formItemId:i}=d();return(0,n.jsx)(s.J,{ref:r,className:(0,l.cn)(o&&"text-destructive",e),htmlFor:i,...t})});y.displayName="FormLabel";let m=o.forwardRef(({...e},t)=>{let{error:r,formItemId:o,formDescriptionId:a,formMessageId:l}=d();return(0,n.jsx)(i.DX,{ref:t,id:o,"aria-describedby":r?`${a} ${l}`:`${a}`,"aria-invalid":!!r,...e})});m.displayName="FormControl";let v=o.forwardRef(({className:e,...t},r)=>{let{formDescriptionId:o}=d();return(0,n.jsx)("p",{ref:r,id:o,className:(0,l.cn)("text-sm text-muted-foreground",e),...t})});v.displayName="FormDescription";let g=o.forwardRef(({className:e,children:t,...r},o)=>{let{error:i,formMessageId:a}=d(),s=i?String(i?.message??""):t;return s?(0,n.jsx)("p",{ref:o,id:a,className:(0,l.cn)("text-sm font-medium text-destructive",e),...r,children:s}):null});g.displayName="FormMessage"},71960:e=>{e.exports=function(e,t,r){for(var n=-1,o=null==e?0:e.length;++n<o;)if(r(t,e[n]))return!0;return!1}},71967:(e,t,r)=>{var n=r(15871);e.exports=function(e,t){return n(e,t)}},74610:e=>{e.exports=function(e,t,r){for(var n=r-1,o=e.length;++n<o;)if(e[n]===t)return n;return -1}},75254:(e,t,r)=>{var n=r(78418),o=r(93311),i=r(41132);e.exports=function(e){var t=o(e);return 1==t.length&&t[0][2]?i(t[0][0],t[0][1]):function(r){return r===e||n(r,e,t)}}},75411:e=>{e.exports=function(e){var t=this.__data__,r=t.delete(e);return this.size=t.size,r}},75847:(e,t,r)=>{var n=r(67554);e.exports=function(e,t){var r;return n(e,function(e,n,o){return!(r=t(e,n,o))}),!!r}},77822:(e,t,r)=>{var n=r(93490);e.exports=function(e){return n(e)&&e!=+e}},77834:e=>{var t=Object.prototype;e.exports=function(e){var r=e&&e.constructor;return e===("function"==typeof r&&r.prototype||t)}},78051:(e,t,r)=>{Promise.resolve().then(r.bind(r,21204))},78418:(e,t,r)=>{var n=r(67200),o=r(15871);e.exports=function(e,t,r,i){var a=r.length,l=a,s=!i;if(null==e)return!l;for(e=Object(e);a--;){var c=r[a];if(s&&c[2]?c[1]!==e[c[0]]:!(c[0]in e))return!1}for(;++a<l;){var u=(c=r[a])[0],f=e[u],d=c[1];if(s&&c[2]){if(void 0===f&&!(u in e))return!1}else{var p=new n;if(i)var h=i(f,d,u,e,t,p);if(!(void 0===h?o(d,f,3,i,p):h))return!1}}return!0}},79474:(e,t,r)=>{e.exports=r(85718).Symbol},79551:e=>{"use strict";e.exports=require("url")},80013:(e,t,r)=>{"use strict";r.d(t,{J:()=>c});var n=r(60687),o=r(43210),i=r(78148),a=r(24224),l=r(4780);let s=(0,a.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=o.forwardRef(({className:e,...t},r)=>(0,n.jsx)(i.b,{ref:r,className:(0,l.cn)(s(),e),...t}));c.displayName=i.b.displayName},80195:(e,t,r)=>{var n=r(79474),o=r(21367),i=r(40542),a=r(49227),l=1/0,s=n?n.prototype:void 0,c=s?s.toString:void 0;e.exports=function e(t){if("string"==typeof t)return t;if(i(t))return o(t,e)+"";if(a(t))return c?c.call(t):"";var r=t+"";return"0"==r&&1/t==-l?"-0":r}},80329:(e,t,r)=>{e=r.nmd(e);var n=r(85718),o=r(1944),i=t&&!t.nodeType&&t,a=i&&e&&!e.nodeType&&e,l=a&&a.exports===i?n.Buffer:void 0,s=l?l.isBuffer:void 0;e.exports=s||o},80458:(e,t,r)=>{var n=r(29395),o=r(69619),i=r(27467),a={};a["[object Float32Array]"]=a["[object Float64Array]"]=a["[object Int8Array]"]=a["[object Int16Array]"]=a["[object Int32Array]"]=a["[object Uint8Array]"]=a["[object Uint8ClampedArray]"]=a["[object Uint16Array]"]=a["[object Uint32Array]"]=!0,a["[object Arguments]"]=a["[object Array]"]=a["[object ArrayBuffer]"]=a["[object Boolean]"]=a["[object DataView]"]=a["[object Date]"]=a["[object Error]"]=a["[object Function]"]=a["[object Map]"]=a["[object Number]"]=a["[object Object]"]=a["[object RegExp]"]=a["[object Set]"]=a["[object String]"]=a["[object WeakMap]"]=!1,e.exports=function(e){return i(e)&&o(e.length)&&!!a[n(e)]}},80704:(e,t,r)=>{var n=r(96678);e.exports=function(e,t){return!!(null==e?0:e.length)&&n(e,t,0)>-1}},81488:e=>{e.exports=function(e,t){return null!=e&&t in Object(e)}},81957:(e,t,r)=>{var n=r(49227);e.exports=function(e,t){if(e!==t){var r=void 0!==e,o=null===e,i=e==e,a=n(e),l=void 0!==t,s=null===t,c=t==t,u=n(t);if(!s&&!u&&!a&&e>t||a&&l&&c&&!s&&!u||o&&l&&c||!r&&c||!i)return 1;if(!o&&!a&&!u&&e<t||u&&r&&i&&!o&&!a||s&&r&&i||!l&&i||!c)return -1}return 0}},82038:(e,t,r)=>{var n=r(34821),o=r(35163),i=r(40542),a=r(80329),l=r(38428),s=r(10090),c=Object.prototype.hasOwnProperty;e.exports=function(e,t){var r=i(e),u=!r&&o(e),f=!r&&!u&&a(e),d=!r&&!u&&!f&&s(e),p=r||u||f||d,h=p?n(e.length,String):[],y=h.length;for(var m in e)(t||c.call(e,m))&&!(p&&("length"==m||f&&("offset"==m||"parent"==m)||d&&("buffer"==m||"byteLength"==m||"byteOffset"==m)||l(m,y)))&&h.push(m);return h}},84031:(e,t,r)=>{"use strict";var n=r(34452);function o(){}function i(){}i.resetWarningCache=o,e.exports=function(){function e(e,t,r,o,i,a){if(a!==n){var l=Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw l.name="Invariant Violation",l}}function t(){return e}e.isRequired=e;var r={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:i,resetWarningCache:o};return r.PropTypes=r,r}},84261:e=>{e.exports=function(e){var t=this.has(e)&&delete this.__data__[e];return this.size-=+!!t,t}},84482:(e,t,r)=>{var n=r(28977);e.exports=function(e){var t=n(e),r=t%1;return t==t?r?t-r:t:0}},84713:e=>{var t=Object.prototype.toString;e.exports=function(e){return t.call(e)}},85244:e=>{e.exports=function(e,t,r){switch(r.length){case 0:return e.call(t);case 1:return e.call(t,r[0]);case 2:return e.call(t,r[0],r[1]);case 3:return e.call(t,r[0],r[1],r[2])}return e.apply(t,r)}},85406:(e,t,r)=>{e.exports=r(85718)["__core-js_shared__"]},85450:(e,t,r)=>{var n=r(79474),o=r(35163),i=r(40542),a=n?n.isConcatSpreadable:void 0;e.exports=function(e){return i(e)||o(e)||!!(a&&e&&e[a])}},85718:(e,t,r)=>{var n=r(10663),o="object"==typeof self&&self&&self.Object===Object&&self;e.exports=n||o||Function("return this")()},85726:(e,t,r)=>{"use strict";r.d(t,{EA:()=>i,eX:()=>s});var n=r(60687),o=r(4780);function i({className:e,...t}){return(0,n.jsx)("div",{className:(0,o.cn)("animate-pulse rounded-md bg-muted",e),...t})}function a(){return(0,n.jsxs)("div",{className:"rounded-lg border bg-card p-6 shadow-sm animate-fade-in",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,n.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,n.jsx)(i,{className:"h-5 w-5 rounded"}),(0,n.jsx)(i,{className:"h-5 w-32"})]}),(0,n.jsx)(i,{className:"h-8 w-20 rounded-md"})]}),(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsxs)("div",{className:"flex justify-between items-center",children:[(0,n.jsx)(i,{className:"h-4 w-24"}),(0,n.jsx)(i,{className:"h-4 w-16"})]}),(0,n.jsx)(i,{className:"h-2 w-full rounded-full"}),(0,n.jsxs)("div",{className:"flex justify-between items-center",children:[(0,n.jsx)(i,{className:"h-4 w-20"}),(0,n.jsx)(i,{className:"h-4 w-16"})]})]})]})}function l(){return(0,n.jsxs)("div",{className:"rounded-lg border bg-card p-6 shadow-sm animate-fade-in",children:[(0,n.jsxs)("div",{className:"flex items-center space-x-2 mb-6",children:[(0,n.jsx)(i,{className:"h-5 w-5 rounded"}),(0,n.jsx)(i,{className:"h-5 w-32"})]}),(0,n.jsx)("div",{className:"flex items-center justify-center",children:(0,n.jsx)(i,{className:"h-48 w-48 rounded-full"})}),(0,n.jsxs)("div",{className:"mt-6 flex flex-wrap gap-2 justify-center",children:[(0,n.jsx)(i,{className:"h-4 w-16 rounded-full"}),(0,n.jsx)(i,{className:"h-4 w-20 rounded-full"}),(0,n.jsx)(i,{className:"h-4 w-14 rounded-full"}),(0,n.jsx)(i,{className:"h-4 w-18 rounded-full"})]})]})}function s(){return(0,n.jsx)("div",{className:"container mx-auto py-4 sm:py-6 px-2 sm:px-4",children:(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 sm:gap-6",children:[(0,n.jsxs)("div",{className:"md:col-span-1 space-y-4 sm:space-y-6",children:[(0,n.jsx)(a,{}),(0,n.jsx)(a,{}),(0,n.jsx)(a,{})]}),(0,n.jsxs)("div",{className:"md:col-span-2 space-y-4 sm:space-y-6",children:[(0,n.jsx)(a,{}),(0,n.jsx)(l,{})]})]})})}},85745:(e,t,r)=>{var n=r(86451);e.exports=function(e){var t=n(e,function(e){return 500===r.size&&r.clear(),e}),r=t.cache;return t}},85938:(e,t,r)=>{var n=r(42205),o=r(17518),i=r(46229),a=r(7383);e.exports=i(function(e,t){if(null==e)return[];var r=t.length;return r>1&&a(e,t[0],t[1])?t=[]:r>2&&a(t[0],t[1],t[2])&&(t=[t[0]]),o(e,n(t,1),[])})},86451:(e,t,r)=>{var n=r(95746);function o(e,t){if("function"!=typeof e||null!=t&&"function"!=typeof t)throw TypeError("Expected a function");var r=function(){var n=arguments,o=t?t.apply(this,n):n[0],i=r.cache;if(i.has(o))return i.get(o);var a=e.apply(this,n);return r.cache=i.set(o,a)||i,a};return r.cache=new(o.Cache||n),r}o.Cache=n,e.exports=o},87270:e=>{e.exports=function(e,t){for(var r=-1,n=null==e?0:e.length;++r<n;)if(!t(e[r],r,e))return!1;return!0}},87321:(e,t,r)=>{var n=r(98798),o=r(7383),i=r(28977);e.exports=function(e){return function(t,r,a){return a&&"number"!=typeof a&&o(t,r,a)&&(r=a=void 0),t=i(t),void 0===r?(r=t,t=0):r=i(r),a=void 0===a?t<r?1:-1:i(a),n(t,r,a,e)}}},87506:(e,t,r)=>{var n=r(66837),o=r(84261),i=r(89492),a=r(90200),l=r(39672);function s(e){var t=-1,r=null==e?0:e.length;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}s.prototype.clear=n,s.prototype.delete=o,s.prototype.get=i,s.prototype.has=a,s.prototype.set=l,e.exports=s},87955:(e,t,r)=>{e.exports=r(84031)()},89167:(e,t,r)=>{e.exports=r(41547)(r(85718),"DataView")},89185:e=>{e.exports=function(e){return this.__data__.set(e,"__lodash_hash_undefined__"),this}},89492:(e,t,r)=>{var n=r(58141),o=Object.prototype.hasOwnProperty;e.exports=function(e){var t=this.__data__;if(n){var r=t[e];return"__lodash_hash_undefined__"===r?void 0:r}return o.call(t,e)?t[e]:void 0}},89605:(e,t,r)=>{e.exports=r(65662)(Object.keys,Object)},89624:e=>{e.exports=function(e){return function(t){return e(t)}}},89667:(e,t,r)=>{"use strict";r.d(t,{p:()=>a});var n=r(60687),o=r(43210),i=r(4780);let a=o.forwardRef(({className:e,type:t,...r},o)=>(0,n.jsx)("input",{type:t,className:(0,i.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:o,...r}));a.displayName="Input"},90200:(e,t,r)=>{var n=r(58141),o=Object.prototype.hasOwnProperty;e.exports=function(e){var t=this.__data__;return n?void 0!==t[e]:o.call(t,e)}},90453:(e,t,r)=>{var n=r(2984),o=r(99180),i=r(48169);e.exports=function(e){return e&&e.length?n(e,i,o):void 0}},90851:e=>{e.exports=function(e,t){return null==e?void 0:e[t]}},91290:e=>{e.exports=function(e,t,r,n){for(var o=e.length,i=r+(n?1:-1);n?i--:++i<o;)if(t(e[i],i,e))return i;return -1}},91928:(e,t,r)=>{var n=r(41547);e.exports=function(){try{var e=n(Object,"defineProperty");return e({},"",{}),e}catch(e){}}()},92662:(e,t,r)=>{var n=r(46328),o=r(80704),i=r(71960),a=r(58276),l=r(95308),s=r(2408);e.exports=function(e,t,r){var c=-1,u=o,f=e.length,d=!0,p=[],h=p;if(r)d=!1,u=i;else if(f>=200){var y=t?null:l(e);if(y)return s(y);d=!1,u=a,h=new n}else h=t?[]:p;t:for(;++c<f;){var m=e[c],v=t?t(m):m;if(m=r||0!==m?m:0,d&&v==v){for(var g=h.length;g--;)if(h[g]===v)continue t;t&&h.push(v),p.push(m)}else u(h,v,r)||(h!==p&&h.push(v),p.push(m))}return p}},93311:(e,t,r)=>{var n=r(34883),o=r(7651);e.exports=function(e){for(var t=o(e),r=t.length;r--;){var i=t[r],a=e[i];t[r]=[i,a,n(a)]}return t}},93490:(e,t,r)=>{var n=r(29395),o=r(27467);e.exports=function(e){return"number"==typeof e||o(e)&&"[object Number]"==n(e)}},94388:(e,t,r)=>{var n=r(57797);e.exports=function(e){var t=this.__data__,r=n(t,e);return r<0?void 0:t[r][1]}},95308:(e,t,r)=>{var n=r(34772),o=r(36959),i=r(2408);e.exports=n&&1/i(new n([,-0]))[1]==1/0?function(e){return new n(e)}:o},95746:(e,t,r)=>{var n=r(15909),o=r(29205),i=r(29508),a=r(61320),l=r(19976);function s(e){var t=-1,r=null==e?0:e.length;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}s.prototype.clear=n,s.prototype.delete=o,s.prototype.get=i,s.prototype.has=a,s.prototype.set=l,e.exports=s},96678:(e,t,r)=>{var n=r(91290),o=r(39774),i=r(74610);e.exports=function(e,t,r){return t==t?i(e,t,r):n(e,o,r)}},97668:(e,t)=>{"use strict";var r,n=Symbol.for("react.element"),o=Symbol.for("react.portal"),i=Symbol.for("react.fragment"),a=Symbol.for("react.strict_mode"),l=Symbol.for("react.profiler"),s=Symbol.for("react.provider"),c=Symbol.for("react.context"),u=Symbol.for("react.server_context"),f=Symbol.for("react.forward_ref"),d=Symbol.for("react.suspense"),p=Symbol.for("react.suspense_list"),h=Symbol.for("react.memo"),y=Symbol.for("react.lazy");Symbol.for("react.offscreen");Symbol.for("react.module.reference"),t.isFragment=function(e){return function(e){if("object"==typeof e&&null!==e){var t=e.$$typeof;switch(t){case n:switch(e=e.type){case i:case l:case a:case d:case p:return e;default:switch(e=e&&e.$$typeof){case u:case c:case f:case y:case h:case s:return e;default:return t}}case o:return t}}}(e)===i}},98451:(e,t,r)=>{var n=r(29395),o=r(27467);e.exports=function(e){return!0===e||!1===e||o(e)&&"[object Boolean]"==n(e)}},98798:e=>{var t=Math.ceil,r=Math.max;e.exports=function(e,n,o,i){for(var a=-1,l=r(t((n-e)/(o||1)),0),s=Array(l);l--;)s[i?l:++a]=e,e+=o;return s}},99114:(e,t,r)=>{var n=r(12344),o=r(7651);e.exports=function(e,t){return e&&n(e,t,o)}},99180:e=>{e.exports=function(e,t){return e>t}},99525:e=>{e.exports=function(e,t){for(var r=-1,n=null==e?0:e.length;++r<n;)if(t(e[r],r,e))return!0;return!1}}};var t=require("../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[447,242,99,758,330,253,529],()=>r(54070));module.exports=n})();