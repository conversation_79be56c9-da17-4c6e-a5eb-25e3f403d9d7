
@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  font-family: '<PERSON> Sans', sans-serif;
}

@layer base {
  :root {
    --background: 220 40% 10%; /* Dark Slate Blue */
    --foreground: 220 15% 85%; /* Light Grayish Blue */

    --card: 220 40% 15%;
    --card-foreground: 220 15% 80%;

    --popover: 220 40% 12%; /* Slightly darker popover */
    --popover-foreground: 220 15% 85%;

    --primary: 180 70% 45%; /* Teal */
    --primary-foreground: 180 50% 98%; /* Very light for text on primary */

    --secondary: 220 30% 25%;
    --secondary-foreground: 220 10% 70%;

    --muted: 220 20% 30%;
    --muted-foreground: 220 10% 60%;

    --accent: 15 90% 60%; /* Coral */
    --accent-foreground: 15 90% 10%; /* Dark brown/black for text on accent */

    --destructive: 0 72% 51%; /* Default Red */
    --destructive-foreground: 0 0% 98%;

    --border: 220 30% 20%;
    --input: 220 30% 18%;
    --ring: 180 70% 50%; /* Primary color for focus rings */

    --chart-1: 180 70% 45%; /* <PERSON>l (Primary) */
    --chart-2: 15 90% 60%;  /* Coral (Accent) */
    --chart-3: 200 70% 55%; /* Lighter Blue */
    --chart-4: 300 60% 60%; /* Magenta/Purple */
    --chart-5: 50 80% 55%;  /* Yellow/Gold */

    /* Enhanced color palette */
    --success: 142 76% 36%;
    --success-foreground: 355 100% 97%;
    --warning: 38 92% 50%;
    --warning-foreground: 48 96% 89%;
    --info: 199 89% 48%;
    --info-foreground: 210 40% 98%;

    /* Gradient colors */
    --gradient-primary: linear-gradient(135deg, hsl(180 70% 45%) 0%, hsl(200 70% 55%) 100%);
    --gradient-accent: linear-gradient(135deg, hsl(15 90% 60%) 0%, hsl(300 60% 60%) 100%);
    --gradient-card: linear-gradient(135deg, hsl(220 40% 15%) 0%, hsl(220 40% 18%) 100%);

    --radius: 0.5rem;
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  }

  .dark {
    --background: 220 40% 5%; /* Even Darker Slate Blue */
    --foreground: 220 15% 75%; /* Lighter Grayish Blue for text */

    --card: 220 40% 8%;
    --card-foreground: 220 15% 70%;

    --popover: 220 40% 4%;
    --popover-foreground: 220 15% 75%;

    --primary: 180 70% 55%; /* Brighter Teal for Dark Mode */
    --primary-foreground: 180 50% 10%; /* Dark text on bright teal */

    --secondary: 220 30% 15%;
    --secondary-foreground: 220 10% 60%;

    --muted: 220 20% 20%;
    --muted-foreground: 220 10% 50%;

    --accent: 15 90% 65%; /* Brighter Coral for Dark Mode */
    --accent-foreground: 15 90% 5%; /* Dark text on bright coral */

    --destructive: 0 62.8% 50.6%;
    --destructive-foreground: 0 0% 98%;

    --border: 220 30% 12%;
    --input: 220 30% 10%;
    --ring: 180 70% 60%; /* Brighter Teal for ring */

    /* Enhanced dark mode colors */
    --success: 142 76% 40%;
    --success-foreground: 355 100% 97%;
    --warning: 38 92% 55%;
    --warning-foreground: 48 96% 89%;
    --info: 199 89% 52%;
    --info-foreground: 210 40% 98%;

    /* Dark mode gradients */
    --gradient-primary: linear-gradient(135deg, hsl(180 70% 55%) 0%, hsl(200 70% 60%) 100%);
    --gradient-accent: linear-gradient(135deg, hsl(15 90% 65%) 0%, hsl(300 60% 65%) 100%);
    --gradient-card: linear-gradient(135deg, hsl(220 40% 8%) 0%, hsl(220 40% 12%) 100%);
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    @apply min-h-screen antialiased;
    @apply scroll-smooth;
  }
  .container {
    @apply px-2 sm:px-4;
  }
}

@layer components {
  /* Enhanced card styles */
  .card-gradient {
    background: var(--gradient-card);
    @apply border border-border/50 backdrop-blur-sm;
  }

  .card-hover {
    @apply transition-all duration-300 ease-in-out;
    @apply hover:shadow-lg hover:-translate-y-1;
  }

  /* Button enhancements */
  .btn-gradient-primary {
    background: var(--gradient-primary);
    @apply text-primary-foreground border-0;
    @apply transition-all duration-300 ease-in-out;
    @apply hover:shadow-lg hover:scale-105;
  }

  .btn-gradient-accent {
    background: var(--gradient-accent);
    @apply text-accent-foreground border-0;
    @apply transition-all duration-300 ease-in-out;
    @apply hover:shadow-lg hover:scale-105;
  }

  /* Status indicators */
  .status-success {
    @apply bg-green-500/10 text-green-600 border border-green-500/20;
  }

  .status-warning {
    @apply bg-yellow-500/10 text-yellow-600 border border-yellow-500/20;
  }

  .status-error {
    @apply bg-red-500/10 text-red-600 border border-red-500/20;
  }

  .status-info {
    @apply bg-blue-500/10 text-blue-600 border border-blue-500/20;
  }

  /* Loading states */
  .skeleton {
    @apply animate-pulse bg-muted rounded;
  }

  /* Focus enhancements */
  .focus-enhanced {
    @apply focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2;
    @apply transition-all duration-200 ease-in-out;
  }

  /* Glassmorphism effect */
  .glass {
    @apply bg-card/80 backdrop-blur-md border border-border/50;
  }

  /* Achievement badge styles */
  .achievement-badge {
    @apply inline-flex items-center gap-2 px-3 py-1 rounded-full text-xs font-medium;
    @apply bg-gradient-to-r from-yellow-500/20 to-orange-500/20;
    @apply border border-yellow-500/30 text-yellow-600;
    @apply animate-pulse;
  }
}
