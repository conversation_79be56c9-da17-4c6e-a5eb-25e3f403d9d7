{"version": 3, "sources": ["../../src/tracing/processor.ts"], "sourcesContent": ["/**\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Context } from '@opentelemetry/api';\nimport {\n  ReadableSpan,\n  Span,\n  SpanProcessor,\n} from '@opentelemetry/sdk-trace-base';\nimport { ATTR_PREFIX } from './instrumentation.js';\n\n// Experimental, WIP\n\nexport class GenkitSpanProcessorWrapper implements SpanProcessor {\n  constructor(private processor: SpanProcessor) {}\n\n  forceFlush(): Promise<void> {\n    return this.processor.forceFlush();\n  }\n\n  onStart(span: Span, parentContext: Context): void {\n    return this.processor.onStart(span, parentContext);\n  }\n\n  onEnd(span: ReadableSpan): void {\n    if (\n      Object.keys(span.attributes).find((k) => k.startsWith(ATTR_PREFIX + ':'))\n    ) {\n      return this.processor.onEnd(new FilteringReadableSpanProxy(span));\n    } else {\n      return this.processor.onEnd(span);\n    }\n  }\n\n  async shutdown(): Promise<void> {\n    return this.processor.shutdown();\n  }\n}\n\nclass FilteringReadableSpanProxy implements ReadableSpan {\n  constructor(private span: ReadableSpan) {}\n\n  get name() {\n    return this.span.name;\n  }\n  get kind() {\n    return this.span.kind;\n  }\n  get parentSpanId() {\n    return this.span.parentSpanId;\n  }\n  get startTime() {\n    return this.span.startTime;\n  }\n  get endTime() {\n    return this.span.endTime;\n  }\n  get status() {\n    return this.span.status;\n  }\n  get attributes() {\n    const out = {} as Record<string, any>;\n    for (const [key, value] of Object.entries(this.span.attributes)) {\n      if (!key.startsWith(ATTR_PREFIX + ':')) {\n        out[key] = value;\n      }\n    }\n    return out;\n  }\n  get links() {\n    return this.span.links;\n  }\n  get events() {\n    return this.span.events;\n  }\n  get duration() {\n    return this.span.duration;\n  }\n  get ended() {\n    return this.span.ended;\n  }\n  get resource() {\n    return this.span.resource;\n  }\n  get instrumentationLibrary() {\n    return this.span.instrumentationLibrary;\n  }\n  get droppedAttributesCount() {\n    return this.span.droppedAttributesCount;\n  }\n  get droppedEventsCount() {\n    return this.span.droppedEventsCount;\n  }\n  get droppedLinksCount() {\n    return this.span.droppedLinksCount;\n  }\n\n  spanContext() {\n    return this.span.spanContext();\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAsBA,6BAA4B;AAIrB,MAAM,2BAAoD;AAAA,EAC/D,YAAoB,WAA0B;AAA1B;AAAA,EAA2B;AAAA,EAE/C,aAA4B;AAC1B,WAAO,KAAK,UAAU,WAAW;AAAA,EACnC;AAAA,EAEA,QAAQ,MAAY,eAA8B;AAChD,WAAO,KAAK,UAAU,QAAQ,MAAM,aAAa;AAAA,EACnD;AAAA,EAEA,MAAM,MAA0B;AAC9B,QACE,OAAO,KAAK,KAAK,UAAU,EAAE,KAAK,CAAC,MAAM,EAAE,WAAW,qCAAc,GAAG,CAAC,GACxE;AACA,aAAO,KAAK,UAAU,MAAM,IAAI,2BAA2B,IAAI,CAAC;AAAA,IAClE,OAAO;AACL,aAAO,KAAK,UAAU,MAAM,IAAI;AAAA,IAClC;AAAA,EACF;AAAA,EAEA,MAAM,WAA0B;AAC9B,WAAO,KAAK,UAAU,SAAS;AAAA,EACjC;AACF;AAEA,MAAM,2BAAmD;AAAA,EACvD,YAAoB,MAAoB;AAApB;AAAA,EAAqB;AAAA,EAEzC,IAAI,OAAO;AACT,WAAO,KAAK,KAAK;AAAA,EACnB;AAAA,EACA,IAAI,OAAO;AACT,WAAO,KAAK,KAAK;AAAA,EACnB;AAAA,EACA,IAAI,eAAe;AACjB,WAAO,KAAK,KAAK;AAAA,EACnB;AAAA,EACA,IAAI,YAAY;AACd,WAAO,KAAK,KAAK;AAAA,EACnB;AAAA,EACA,IAAI,UAAU;AACZ,WAAO,KAAK,KAAK;AAAA,EACnB;AAAA,EACA,IAAI,SAAS;AACX,WAAO,KAAK,KAAK;AAAA,EACnB;AAAA,EACA,IAAI,aAAa;AACf,UAAM,MAAM,CAAC;AACb,eAAW,CAAC,KAAK,KAAK,KAAK,OAAO,QAAQ,KAAK,KAAK,UAAU,GAAG;AAC/D,UAAI,CAAC,IAAI,WAAW,qCAAc,GAAG,GAAG;AACtC,YAAI,GAAG,IAAI;AAAA,MACb;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EACA,IAAI,QAAQ;AACV,WAAO,KAAK,KAAK;AAAA,EACnB;AAAA,EACA,IAAI,SAAS;AACX,WAAO,KAAK,KAAK;AAAA,EACnB;AAAA,EACA,IAAI,WAAW;AACb,WAAO,KAAK,KAAK;AAAA,EACnB;AAAA,EACA,IAAI,QAAQ;AACV,WAAO,KAAK,KAAK;AAAA,EACnB;AAAA,EACA,IAAI,WAAW;AACb,WAAO,KAAK,KAAK;AAAA,EACnB;AAAA,EACA,IAAI,yBAAyB;AAC3B,WAAO,KAAK,KAAK;AAAA,EACnB;AAAA,EACA,IAAI,yBAAyB;AAC3B,WAAO,KAAK,KAAK;AAAA,EACnB;AAAA,EACA,IAAI,qBAAqB;AACvB,WAAO,KAAK,KAAK;AAAA,EACnB;AAAA,EACA,IAAI,oBAAoB;AACtB,WAAO,KAAK,KAAK;AAAA,EACnB;AAAA,EAEA,cAAc;AACZ,WAAO,KAAK,KAAK,YAAY;AAAA,EAC/B;AACF;", "names": []}