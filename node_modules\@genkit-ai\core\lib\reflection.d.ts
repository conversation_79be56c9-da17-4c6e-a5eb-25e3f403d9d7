import * as z from 'zod';
import { L as Registry } from './action-Bk3Kwgxc.js';
import 'json-schema';
import './statusTypes.js';
import 'dotprompt';
import 'ajv';

/**
 * Copyright 2024 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

declare const RunActionResponseSchema: z.ZodObject<{
    result: z.ZodOptional<z.ZodUnknown>;
    error: z.ZodOptional<z.ZodUnknown>;
    telemetry: z.ZodOptional<z.ZodObject<{
        traceId: z.ZodOptional<z.ZodString>;
    }, "strip", z.ZodTypeAny, {
        traceId?: string | undefined;
    }, {
        traceId?: string | undefined;
    }>>;
}, "strip", z.ZodTypeAny, {
    error?: unknown;
    result?: unknown;
    telemetry?: {
        traceId?: string | undefined;
    } | undefined;
}, {
    error?: unknown;
    result?: unknown;
    telemetry?: {
        traceId?: string | undefined;
    } | undefined;
}>;
type RunActionResponse = z.infer<typeof RunActionResponseSchema>;
interface ReflectionServerOptions {
    /** Port to run the server on. Actual port may be different if chosen port is occupied. Defaults to 3100. */
    port?: number;
    /** Body size limit for the server. Defaults to `30mb`. */
    bodyLimit?: string;
    /** Configured environments. Defaults to `dev`. */
    configuredEnvs?: string[];
}
/**
 * Reflection server exposes an API for inspecting and interacting with Genkit in development.
 *
 * This is for use in development environments.
 *
 * @hidden
 */
declare class ReflectionServer {
    /** List of all running servers needed to be cleaned up on process exit. */
    private static RUNNING_SERVERS;
    /** Registry instance to be used for API calls. */
    private registry;
    /** Options for the reflection server. */
    private options;
    /** Port the server is actually running on. This may differ from `options.port` if the original was occupied. Null if server is not running. */
    private port;
    /** Express server instance. Null if server is not running. */
    private server;
    /** Path to the runtime file. Null if server is not running. */
    private runtimeFilePath;
    constructor(registry: Registry, options?: ReflectionServerOptions);
    /**
     * Finds a free port to run the server on based on the original chosen port and environment.
     */
    findPort(): Promise<number>;
    /**
     * Starts the server.
     *
     * The server will be registered to be shut down on process exit.
     */
    start(): Promise<void>;
    /**
     * Stops the server and removes it from the list of running servers to clean up on exit.
     */
    stop(): Promise<void>;
    /**
     * Writes the runtime file to the project root.
     */
    private writeRuntimeFile;
    /**
     * Cleans up the port file.
     */
    private cleanupRuntimeFile;
    /**
     * Stops all running reflection servers.
     */
    static stopAll(): Promise<void[]>;
}

export { ReflectionServer, type ReflectionServerOptions, type RunActionResponse, RunActionResponseSchema };
