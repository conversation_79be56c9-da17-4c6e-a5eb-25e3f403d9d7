'use client';

import { motion } from 'framer-motion';
import { Trophy, Star, Target, Zap, Award, Crown } from 'lucide-react';
import { cn } from '@/lib/utils';

interface AchievementBadgeProps {
  title: string;
  description?: string;
  icon?: 'trophy' | 'star' | 'target' | 'zap' | 'award' | 'crown';
  variant?: 'gold' | 'silver' | 'bronze' | 'primary' | 'success';
  size?: 'sm' | 'md' | 'lg';
  animated?: boolean;
  showConfetti?: boolean;
  className?: string;
}

const iconMap = {
  trophy: Trophy,
  star: Star,
  target: Target,
  zap: Zap,
  award: Award,
  crown: Crown,
};

const variantStyles = {
  gold: 'bg-gradient-to-r from-yellow-400 to-yellow-600 text-yellow-900 border-yellow-500/30',
  silver: 'bg-gradient-to-r from-gray-300 to-gray-500 text-gray-900 border-gray-400/30',
  bronze: 'bg-gradient-to-r from-orange-400 to-orange-600 text-orange-900 border-orange-500/30',
  primary: 'bg-gradient-to-r from-primary to-primary/80 text-primary-foreground border-primary/30',
  success: 'bg-gradient-to-r from-green-400 to-green-600 text-green-900 border-green-500/30',
};

const sizeStyles = {
  sm: 'px-2 py-1 text-xs',
  md: 'px-3 py-1.5 text-sm',
  lg: 'px-4 py-2 text-base',
};

export function AchievementBadge({
  title,
  description,
  icon = 'trophy',
  variant = 'gold',
  size = 'md',
  animated = true,
  showConfetti = false,
  className,
}: AchievementBadgeProps) {
  const IconComponent = iconMap[icon];

  const badgeContent = (
    <div
      className={cn(
        'inline-flex items-center gap-2 rounded-full font-medium border shadow-lg',
        variantStyles[variant],
        sizeStyles[size],
        animated && 'hover:scale-105 transition-transform duration-200',
        className
      )}
    >
      <IconComponent className={cn(
        'shrink-0',
        size === 'sm' ? 'h-3 w-3' : size === 'md' ? 'h-4 w-4' : 'h-5 w-5'
      )} />
      <span className="font-semibold">{title}</span>
    </div>
  );

  if (!animated) {
    return badgeContent;
  }

  return (
    <motion.div
      initial={{ scale: 0, opacity: 0 }}
      animate={{ scale: 1, opacity: 1 }}
      transition={{
        type: "spring",
        stiffness: 260,
        damping: 20,
        delay: 0.1
      }}
      whileHover={{ scale: 1.05 }}
      whileTap={{ scale: 0.95 }}
      className="inline-block"
    >
      {badgeContent}
      {description && (
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="text-xs text-muted-foreground mt-1 text-center"
        >
          {description}
        </motion.div>
      )}
    </motion.div>
  );
}

interface AchievementToastProps {
  title: string;
  description: string;
  icon?: 'trophy' | 'star' | 'target' | 'zap' | 'award' | 'crown';
  variant?: 'gold' | 'silver' | 'bronze' | 'primary' | 'success';
  onClose?: () => void;
}

export function AchievementToast({
  title,
  description,
  icon = 'trophy',
  variant = 'gold',
  onClose,
}: AchievementToastProps) {
  const IconComponent = iconMap[icon];

  return (
    <motion.div
      initial={{ x: 300, opacity: 0 }}
      animate={{ x: 0, opacity: 1 }}
      exit={{ x: 300, opacity: 0 }}
      transition={{ type: "spring", stiffness: 300, damping: 30 }}
      className={cn(
        'flex items-center gap-3 p-4 rounded-lg border shadow-lg max-w-sm',
        'bg-card/95 backdrop-blur-sm border-border/50'
      )}
    >
      <motion.div
        animate={{ rotate: [0, 10, -10, 0] }}
        transition={{ duration: 0.6, repeat: 2 }}
        className={cn(
          'flex items-center justify-center w-10 h-10 rounded-full',
          variantStyles[variant]
        )}
      >
        <IconComponent className="h-5 w-5" />
      </motion.div>
      
      <div className="flex-1 min-w-0">
        <h4 className="font-semibold text-foreground">{title}</h4>
        <p className="text-sm text-muted-foreground">{description}</p>
      </div>
      
      {onClose && (
        <button
          onClick={onClose}
          className="text-muted-foreground hover:text-foreground transition-colors"
        >
          ×
        </button>
      )}
    </motion.div>
  );
}

interface AchievementGridProps {
  achievements: Array<{
    id: string;
    title: string;
    description: string;
    icon?: 'trophy' | 'star' | 'target' | 'zap' | 'award' | 'crown';
    variant?: 'gold' | 'silver' | 'bronze' | 'primary' | 'success';
    unlocked: boolean;
    unlockedAt?: Date;
  }>;
  className?: string;
}

export function AchievementGrid({ achievements, className }: AchievementGridProps) {
  return (
    <div className={cn('grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 gap-4', className)}>
      {achievements.map((achievement, index) => (
        <motion.div
          key={achievement.id}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: index * 0.1 }}
          className={cn(
            'p-4 rounded-lg border text-center transition-all duration-300',
            achievement.unlocked
              ? 'bg-card border-border shadow-sm hover:shadow-md'
              : 'bg-muted/50 border-muted opacity-60'
          )}
        >
          <div className={cn(
            'w-12 h-12 mx-auto mb-3 rounded-full flex items-center justify-center',
            achievement.unlocked
              ? variantStyles[achievement.variant || 'gold']
              : 'bg-muted text-muted-foreground'
          )}>
            {React.createElement(iconMap[achievement.icon || 'trophy'], {
              className: 'h-6 w-6'
            })}
          </div>
          
          <h3 className={cn(
            'font-semibold text-sm mb-1',
            achievement.unlocked ? 'text-foreground' : 'text-muted-foreground'
          )}>
            {achievement.title}
          </h3>
          
          <p className={cn(
            'text-xs',
            achievement.unlocked ? 'text-muted-foreground' : 'text-muted-foreground/60'
          )}>
            {achievement.description}
          </p>
          
          {achievement.unlocked && achievement.unlockedAt && (
            <p className="text-xs text-muted-foreground mt-2">
              Unlocked {achievement.unlockedAt.toLocaleDateString()}
            </p>
          )}
        </motion.div>
      ))}
    </div>
  );
}
