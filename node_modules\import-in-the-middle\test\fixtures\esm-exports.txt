// Exporting declarations
export let name1, name2/*, … */; // also var //| name1,name2
export const name1 = 1, name2 = 2/*, … */; // also var, let //| name1,name2
export function functionName() { /* … */ } //| functionName
export class ClassName { /* … */ } //| ClassName
export function* generatorFunctionName() { /* … */ } //| generatorFunctionName
export const { name1, name2: bar } = o; //| name1,bar
export const [ name1, name2 ] = array; //| name1,name2

// Export list
let name1, nameN; export { name1, /* …, */ nameN }; //| name1,nameN
let variable1, variable2, nameN; export { variable1 as name1, variable2 as name2, /* …, */ nameN }; //| name1,name2,nameN
let variable1; export { variable1 as "string name" }; //| string name
let name1; export { name1 as default /*, … */ }; //| default

// Default exports
export default expression; //| default
export default function functionName() { /* … */ } //| default
export default class ClassName { /* … */ } //| default
export default function* generatorFunctionName() { /* … */ } //| default
export default function () { /* … */ } //| default
export default class { /* … */ } //| default
export default function* () { /* … */ } //| default

// Aggregating modules
export * from "module-name"; //| * from module-name
export * as name1 from "module-name"; //| name1
export { name1, /* …, */ nameN } from "module-name"; //| name1,nameN
export { import1 as name1, import2 as name2, /* …, */ nameN } from "module-name"; //| name1,name2,nameN
export { default, /* …, */ } from "module-name"; //| default
export { default as name1 } from "module-name"; //| name1

