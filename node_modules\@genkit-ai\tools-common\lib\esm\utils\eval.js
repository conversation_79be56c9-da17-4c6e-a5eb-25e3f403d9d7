import { randomUUID } from 'crypto';
import { createReadStream } from 'fs';
import { readFile } from 'fs/promises';
import * as inquirer from 'inquirer';
import { createInterface } from 'readline';
import { findToolsConfig, isEvalField, } from '../plugin';
import { DatasetSchema, EvalInputDatasetSchema, EvaluationDatasetSchema, EvaluationSampleSchema, GenerateRequestSchema, InferenceDatasetSchema, InferenceSampleSchema, } from '../types';
import { logger } from './logger';
import { stackTraceSpans } from './trace';
export const EVALUATOR_ACTION_PREFIX = '/evaluator';
export const EVALUATOR_METADATA_KEY_DISPLAY_NAME = 'evaluatorDisplayName';
export const EVALUATOR_METADATA_KEY_DEFINITION = 'evaluatorDefinition';
export const EVALUATOR_METADATA_KEY_IS_BILLED = 'evaluatorIsBilled';
export function evaluatorName(action) {
    return `${EVALUATOR_ACTION_PREFIX}/${action.name}`;
}
export function isEvaluator(key) {
    return key.startsWith(EVALUATOR_ACTION_PREFIX);
}
export async function confirmLlmUse(evaluatorActions) {
    const isBilled = evaluatorActions.some((action) => action.metadata && action.metadata[EVALUATOR_METADATA_KEY_IS_BILLED]);
    if (!isBilled) {
        return true;
    }
    const answers = await inquirer.prompt([
        {
            type: 'confirm',
            name: 'confirm',
            message: 'For each example, the evaluation makes calls to APIs that may result in being charged. Do you wish to proceed?',
            default: false,
        },
    ]);
    return answers.confirm;
}
function getRootSpan(trace) {
    return stackTraceSpans(trace);
}
function safeParse(value) {
    if (value) {
        try {
            return JSON.parse(value);
        }
        catch (e) {
            return '';
        }
    }
    return '';
}
const DEFAULT_INPUT_EXTRACTOR = (trace) => {
    const rootSpan = getRootSpan(trace);
    return safeParse(rootSpan?.attributes['genkit:input']);
};
const DEFAULT_OUTPUT_EXTRACTOR = (trace) => {
    const rootSpan = getRootSpan(trace);
    return safeParse(rootSpan?.attributes['genkit:output']);
};
const DEFAULT_CONTEXT_EXTRACTOR = (trace) => {
    return Object.values(trace.spans)
        .filter((s) => s.attributes['genkit:metadata:subtype'] === 'retriever')
        .flatMap((s) => {
        const output = safeParse(s.attributes['genkit:output']);
        if (!output) {
            return [];
        }
        return output.documents.flatMap((d) => d.content.map((c) => c.text).filter((text) => !!text));
    });
};
const DEFAULT_FLOW_EXTRACTORS = {
    input: DEFAULT_INPUT_EXTRACTOR,
    output: DEFAULT_OUTPUT_EXTRACTOR,
    context: DEFAULT_CONTEXT_EXTRACTOR,
};
const DEFAULT_MODEL_EXTRACTORS = {
    input: DEFAULT_INPUT_EXTRACTOR,
    output: DEFAULT_OUTPUT_EXTRACTOR,
    context: () => [],
};
function getStepAttribute(trace, stepName, attributeName) {
    const attr = attributeName ?? 'genkit:output';
    const values = Object.values(trace.spans)
        .filter((step) => step.displayName === stepName)
        .flatMap((step) => {
        return safeParse(step.attributes[attr]);
    });
    if (values.length === 0) {
        return '';
    }
    if (values.length === 1) {
        return values[0];
    }
    return values;
}
function getExtractorFromStepName(stepName) {
    return (trace) => {
        return getStepAttribute(trace, stepName);
    };
}
function getExtractorFromStepSelector(stepSelector) {
    return (trace) => {
        let stepName = undefined;
        let selectedAttribute = 'genkit:output';
        if (Object.hasOwn(stepSelector, 'inputOf')) {
            stepName = stepSelector.inputOf;
            selectedAttribute = 'genkit:input';
        }
        else {
            stepName = stepSelector.outputOf;
            selectedAttribute = 'genkit:output';
        }
        if (!stepName) {
            return '';
        }
        else {
            return getStepAttribute(trace, stepName, selectedAttribute);
        }
    };
}
function getExtractorMap(extractor) {
    let extractorMap = {};
    for (const [key, value] of Object.entries(extractor)) {
        if (isEvalField(key)) {
            if (typeof value === 'string') {
                extractorMap[key] = getExtractorFromStepName(value);
            }
            else if (typeof value === 'object') {
                extractorMap[key] = getExtractorFromStepSelector(value);
            }
            else if (typeof value === 'function') {
                extractorMap[key] = value;
            }
        }
    }
    return extractorMap;
}
export async function getEvalExtractors(actionRef) {
    if (actionRef.startsWith('/model')) {
        logger.debug('getEvalExtractors - modelRef provided, using default extractors');
        return Promise.resolve(DEFAULT_MODEL_EXTRACTORS);
    }
    const config = await findToolsConfig();
    const extractors = config?.evaluators
        ?.filter((e) => e.actionRef === actionRef)
        .map((e) => e.extractors);
    if (!extractors) {
        return Promise.resolve(DEFAULT_FLOW_EXTRACTORS);
    }
    let composedExtractors = DEFAULT_FLOW_EXTRACTORS;
    for (const extractor of extractors) {
        const extractorFunction = getExtractorMap(extractor);
        composedExtractors = { ...composedExtractors, ...extractorFunction };
    }
    return Promise.resolve(composedExtractors);
}
export function generateTestCaseId() {
    return randomUUID();
}
export async function loadInferenceDatasetFile(fileName) {
    const isJsonl = fileName.endsWith('.jsonl');
    if (isJsonl) {
        return await readJsonlForInference(fileName);
    }
    else {
        const parsedData = JSON.parse(await readFile(fileName, 'utf8'));
        let dataset = InferenceDatasetSchema.parse(parsedData);
        dataset = dataset.map((sample) => ({
            ...sample,
            testCaseId: sample.testCaseId ?? generateTestCaseId(),
        }));
        return DatasetSchema.parse(dataset);
    }
}
export async function loadEvaluationDatasetFile(fileName) {
    const isJsonl = fileName.endsWith('.jsonl');
    if (isJsonl) {
        return await readJsonlForEvaluation(fileName);
    }
    else {
        const parsedData = JSON.parse(await readFile(fileName, 'utf8'));
        let evaluationInput = EvaluationDatasetSchema.parse(parsedData);
        evaluationInput = evaluationInput.map((evalSample) => ({
            ...evalSample,
            testCaseId: evalSample.testCaseId ?? generateTestCaseId(),
            traceIds: evalSample.traceIds ?? [],
        }));
        return EvalInputDatasetSchema.parse(evaluationInput);
    }
}
async function readJsonlForInference(fileName) {
    const lines = await readLines(fileName);
    const samples = [];
    for (const line of lines) {
        const parsedSample = InferenceSampleSchema.parse(JSON.parse(line));
        samples.push({
            ...parsedSample,
            testCaseId: parsedSample.testCaseId ?? generateTestCaseId(),
        });
    }
    return samples;
}
async function readJsonlForEvaluation(fileName) {
    const lines = await readLines(fileName);
    const inputs = [];
    for (const line of lines) {
        const parsedSample = EvaluationSampleSchema.parse(JSON.parse(line));
        inputs.push({
            ...parsedSample,
            testCaseId: parsedSample.testCaseId ?? generateTestCaseId(),
            traceIds: parsedSample.traceIds ?? [],
        });
    }
    return inputs;
}
async function readLines(fileName) {
    const lines = [];
    const fileStream = createReadStream(fileName);
    const rl = createInterface({
        input: fileStream,
        crlfDelay: Infinity,
    });
    for await (const line of rl) {
        lines.push(line);
    }
    return lines;
}
export async function hasAction(params) {
    const { manager, actionRef } = { ...params };
    const actionsRecord = await manager.listActions();
    return actionsRecord.hasOwnProperty(actionRef);
}
export function getModelInput(data, modelConfig) {
    let message;
    if (typeof data === 'string') {
        message = {
            role: 'user',
            content: [
                {
                    text: data,
                },
            ],
        };
        return {
            messages: message ? [message] : [],
            config: modelConfig,
        };
    }
    else {
        const maybeRequest = GenerateRequestSchema.safeParse(data);
        if (maybeRequest.success) {
            return maybeRequest.data;
        }
        else {
            throw new Error(`Unable to parse model input as MessageSchema. Details: ${maybeRequest.error}`);
        }
    }
}
export function groupBy(arr, criteria) {
    return arr.reduce(function (obj, item) {
        const key = typeof criteria === 'function' ? criteria(item) : item[criteria];
        if (!obj.hasOwnProperty(key)) {
            obj[key] = [];
        }
        obj[key].push(item);
        return obj;
    }, {});
}
export function countBy(arr, criteria) {
    return arr.reduce((acc, item) => {
        const key = typeof criteria === 'function' ? criteria(item) : item[criteria];
        acc[key] = (acc[key] || 0) + 1;
        return acc;
    }, {});
}
export function meanBy(arr, criteria) {
    if (!arr || arr.length === 0) {
        return undefined;
    }
    let sum = 0;
    for (const item of arr) {
        const value = typeof criteria === 'function' ? criteria(item) : item[criteria];
        sum += value;
    }
    return sum / arr.length;
}
//# sourceMappingURL=eval.js.map