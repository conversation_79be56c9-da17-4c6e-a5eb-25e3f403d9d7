
'use client';

import Link from 'next/link';
import { motion } from 'framer-motion';
import { Eye, EyeOff, PiggyBank, Info, LogOut, LogIn, UserPlus, Settings } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/context/AuthContext';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";

interface HeaderProps {
  title: string;
  balancesVisible?: boolean;
  onToggleBalances?: () => void;
}

export default function Header({ title, balancesVisible, onToggleBalances }: HeaderProps) {
  const { currentUser, signOut, loading } = useAuth();

  const getInitials = (email?: string | null, name?: string | null) => {
    if (name) {
      const nameParts = name.split(' ').filter(Boolean);
      if (nameParts.length > 1) {
        return (nameParts[0][0] + nameParts[1][0]).toUpperCase();
      }
      return name.substring(0, 2).toUpperCase();
    }
    if (email) return email.substring(0, 2).toUpperCase();
    return 'U';
  };

  return (
    <motion.header
      initial={{ y: -100, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      transition={{ duration: 0.5, ease: "easeOut" }}
      className="bg-card/95 backdrop-blur-md shadow-sm sticky top-0 z-40 border-b border-border/50"
    >
      <div className="container mx-auto flex items-center justify-between py-3 px-4 sm:px-6">
        <motion.div
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          <Link href="/" className="flex items-center gap-2 group">
            <motion.div
              animate={{ rotate: [0, 10, -10, 0] }}
              transition={{ duration: 2, repeat: Infinity, repeatDelay: 5 }}
              className="p-1 rounded-full bg-gradient-to-r from-primary/20 to-primary/10 group-hover:from-primary/30 group-hover:to-primary/20 transition-all duration-300"
            >
              <PiggyBank className="h-7 w-7 text-primary" />
            </motion.div>
            <h1 className="text-xl sm:text-2xl font-headline font-bold bg-gradient-to-r from-foreground to-foreground/80 bg-clip-text text-transparent">
              {title}
            </h1>
          </Link>
        </motion.div>

        <nav className="flex items-center gap-2 sm:gap-3">
          {onToggleBalances && (
            <motion.div
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
            >
              <Button
                variant="ghost"
                size="icon"
                onClick={onToggleBalances}
                className="text-foreground hover:text-foreground/80 h-8 w-8 sm:h-9 sm:w-9 hover:bg-primary/10 transition-all duration-200"
              >
                <motion.div
                  initial={false}
                  animate={{ rotate: balancesVisible ? 0 : 180 }}
                  transition={{ duration: 0.3 }}
                >
                  {balancesVisible ? <Eye className="h-4 w-4 sm:h-5 sm:w-5" /> : <EyeOff className="h-4 w-4 sm:h-5 sm:w-5" />}
                </motion.div>
                <span className="sr-only">{balancesVisible ? 'Hide Balances' : 'Show Balances'}</span>
              </Button>
            </motion.div>
          )}

          <motion.div
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <Link href="/tips" className="text-sm sm:text-base text-primary hover:text-primary/80 transition-all duration-200 hidden sm:flex items-center gap-1 px-3 py-2 rounded-md hover:bg-primary/10">
              <Info className="h-4 w-4" />
              Budgeting Tips
            </Link>
          </motion.div>

          <motion.div
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
            className="sm:hidden"
          >
            <Button variant="ghost" size="icon" asChild className="text-primary hover:text-primary/80 h-8 w-8 hover:bg-primary/10">
              <Link href="/tips">
                <Info className="h-5 w-5" />
                <span className="sr-only">Budgeting Tips</span>
              </Link>
            </Button>
          </motion.div>

          {loading ? (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              className="h-8 w-20 bg-muted rounded animate-pulse"
            />
          ) : currentUser ? (
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.2 }}
            >
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <motion.div
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                  >
                    <Button variant="ghost" className="relative h-8 w-8 rounded-full hover:bg-primary/10 transition-all duration-200">
                      <Avatar className="h-8 w-8 ring-2 ring-primary/20 hover:ring-primary/40 transition-all duration-200">
                        <AvatarImage src={currentUser.photoURL || undefined} alt={currentUser.displayName || currentUser.email || "User"} />
                        <AvatarFallback className="bg-gradient-to-r from-primary/20 to-accent/20 text-foreground font-semibold">
                          {getInitials(currentUser.email, currentUser.displayName)}
                        </AvatarFallback>
                      </Avatar>
                    </Button>
                  </motion.div>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="w-56 glass" align="end" forceMount>
                  <DropdownMenuLabel className="font-normal">
                    <div className="flex flex-col space-y-1">
                      <p className="text-sm font-medium leading-none">
                        {currentUser.displayName || currentUser.email?.split('@')[0]}
                      </p>
                      <p className="text-xs leading-none text-muted-foreground">
                        {currentUser.email}
                      </p>
                    </div>
                  </DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem asChild className="cursor-pointer hover:bg-primary/10 transition-colors">
                    <Link href="/profile">
                      <Settings className="mr-2 h-4 w-4" />
                      <span>Profile Settings</span>
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={signOut} className="cursor-pointer hover:bg-destructive/10 text-destructive transition-colors">
                    <LogOut className="mr-2 h-4 w-4" />
                    <span>Log out</span>
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </motion.div>
          ) : (
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.3 }}
              className="flex items-center gap-2"
            >
              <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                <Button variant="ghost" asChild size="sm" className="hover:bg-primary/10">
                  <Link href="/login">
                    <LogIn className="mr-1 h-4 w-4 sm:mr-2" /> Login
                  </Link>
                </Button>
              </motion.div>
              <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                <Button asChild size="sm" className="bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70">
                  <Link href="/register">
                    <UserPlus className="mr-1 h-4 w-4 sm:mr-2" /> Sign Up
                  </Link>
                </Button>
              </motion.div>
            </motion.div>
          )}
        </nav>
      </div>
    </motion.header>
  );
}
