(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{67813:(e,a,t)=>{Promise.resolve().then(t.bind(t,93733))},68856:(e,a,t)=>{"use strict";t.d(a,{EA:()=>n,eX:()=>o});var s=t(95155),l=t(59434);function n(e){let{className:a,...t}=e;return(0,s.jsx)("div",{className:(0,l.cn)("animate-pulse rounded-md bg-muted",a),...t})}function r(){return(0,s.jsxs)("div",{className:"rounded-lg border bg-card p-6 shadow-sm animate-fade-in",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(n,{className:"h-5 w-5 rounded"}),(0,s.jsx)(n,{className:"h-5 w-32"})]}),(0,s.jsx)(n,{className:"h-8 w-20 rounded-md"})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsx)(n,{className:"h-4 w-24"}),(0,s.jsx)(n,{className:"h-4 w-16"})]}),(0,s.jsx)(n,{className:"h-2 w-full rounded-full"}),(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsx)(n,{className:"h-4 w-20"}),(0,s.jsx)(n,{className:"h-4 w-16"})]})]})]})}function i(){return(0,s.jsxs)("div",{className:"rounded-lg border bg-card p-6 shadow-sm animate-fade-in",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2 mb-6",children:[(0,s.jsx)(n,{className:"h-5 w-5 rounded"}),(0,s.jsx)(n,{className:"h-5 w-32"})]}),(0,s.jsx)("div",{className:"flex items-center justify-center",children:(0,s.jsx)(n,{className:"h-48 w-48 rounded-full"})}),(0,s.jsxs)("div",{className:"mt-6 flex flex-wrap gap-2 justify-center",children:[(0,s.jsx)(n,{className:"h-4 w-16 rounded-full"}),(0,s.jsx)(n,{className:"h-4 w-20 rounded-full"}),(0,s.jsx)(n,{className:"h-4 w-14 rounded-full"}),(0,s.jsx)(n,{className:"h-4 w-18 rounded-full"})]})]})}function o(){return(0,s.jsx)("div",{className:"container mx-auto py-4 sm:py-6 px-2 sm:px-4",children:(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 sm:gap-6",children:[(0,s.jsxs)("div",{className:"md:col-span-1 space-y-4 sm:space-y-6",children:[(0,s.jsx)(r,{}),(0,s.jsx)(r,{}),(0,s.jsx)(r,{})]}),(0,s.jsxs)("div",{className:"md:col-span-2 space-y-4 sm:space-y-6",children:[(0,s.jsx)(r,{}),(0,s.jsx)(i,{})]})]})})}},87481:(e,a,t)=>{"use strict";t.d(a,{dj:()=>u});var s=t(12115);let l=0,n=new Map,r=e=>{if(n.has(e))return;let a=setTimeout(()=>{n.delete(e),c({type:"REMOVE_TOAST",toastId:e})},1e6);n.set(e,a)},i=(e,a)=>{switch(a.type){case"ADD_TOAST":return{...e,toasts:[a.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===a.toast.id?{...e,...a.toast}:e)};case"DISMISS_TOAST":{let{toastId:t}=a;return t?r(t):e.toasts.forEach(e=>{r(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===t||void 0===t?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===a.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==a.toastId)}}},o=[],d={toasts:[]};function c(e){d=i(d,e),o.forEach(e=>{e(d)})}function m(e){let{...a}=e,t=(l=(l+1)%Number.MAX_SAFE_INTEGER).toString(),s=()=>c({type:"DISMISS_TOAST",toastId:t});return c({type:"ADD_TOAST",toast:{...a,id:t,open:!0,onOpenChange:e=>{e||s()}}}),{id:t,dismiss:s,update:e=>c({type:"UPDATE_TOAST",toast:{...e,id:t}})}}function u(){let[e,a]=s.useState(d);return s.useEffect(()=>(o.push(a),()=>{let e=o.indexOf(a);e>-1&&o.splice(e,1)}),[e]),{...e,toast:m,dismiss:e=>c({type:"DISMISS_TOAST",toastId:e})}}},93733:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>am});var s=t(95155),l=t(12115),n=t(35695),r=t(60760),i=t(1978),o=t(44321),d=t(62523),c=t(85057),m=t(66695),u=t(88390);function x(e){let{totalIncome:a,onIncomeChange:t,balancesVisible:n}=e,[r,i]=(0,l.useState)("");return(0,l.useEffect)(()=>{n?i(a.toString()):i("••••")},[a,n]),(0,s.jsxs)(m.Zp,{children:[(0,s.jsx)(m.aR,{className:"pb-2",children:(0,s.jsxs)(m.ZB,{className:"text-lg flex items-center gap-2 font-headline",children:[(0,s.jsx)(u.A,{className:"h-5 w-5 text-primary"}),"Total Income"]})}),(0,s.jsx)(m.Wu,{children:(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(c.J,{htmlFor:"totalIncome",className:"sr-only",children:"Total Income"}),(0,s.jsx)("span",{className:"text-lg font-semibold text-muted-foreground",children:"R"}),(0,s.jsx)(d.p,{id:"totalIncome",type:n?"number":"text",placeholder:"e.g., 6000",value:r,onChange:e=>{let a=e.target.value;n&&i(a);let s=""===a?0:parseFloat(a);!isNaN(s)&&s>=0?t(s):""===a&&t(0)},className:"text-lg h-10 flex-grow",min:"0",step:"any",readOnly:!n&&"••••"===r})]})})]})}var h=t(55863),p=t(59434);let g=l.forwardRef((e,a)=>{let{className:t,value:l,...n}=e;return(0,s.jsx)(h.bL,{ref:a,className:(0,p.cn)("relative h-4 w-full overflow-hidden rounded-full bg-secondary",t),...n,children:(0,s.jsx)(h.C1,{className:"h-full w-full flex-1 bg-primary transition-all",style:{transform:"translateX(-".concat(100-(l||0),"%)")}})})});g.displayName=h.bL.displayName;var f=t(80659),j=t(31949),b=t(49940);function v(e){let{totalIncome:a,overallTotalAllocated:t,balancesVisible:l}=e,n=a-t,r=a>0?t/a*100:0,i=n<0,o=e=>l?"R ".concat(e.toFixed(2)):"R ••••";return(0,s.jsxs)(m.Zp,{children:[(0,s.jsx)(m.aR,{className:"pb-2",children:(0,s.jsxs)(m.ZB,{className:"text-lg flex items-center gap-2 font-headline",children:[(0,s.jsx)(f.A,{className:"h-5 w-5 text-primary"}),"Budget Summary"]})}),(0,s.jsxs)(m.Wu,{className:"space-y-3",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center text-sm",children:[(0,s.jsx)("span",{className:"text-muted-foreground",children:"Total Income:"}),(0,s.jsx)("span",{className:"font-semibold",children:o(a)})]}),(0,s.jsxs)("div",{className:"flex justify-between items-center text-sm",children:[(0,s.jsx)("span",{className:"text-muted-foreground",children:"Total Allocated:"}),(0,s.jsx)("span",{className:"font-semibold",children:o(t)})]}),(0,s.jsx)(g,{value:Math.min(r,100),className:i?"bg-destructive":""}),(0,s.jsxs)("div",{className:"flex justify-between items-center text-sm font-semibold ".concat(i?"text-destructive":"text-green-600"),children:[(0,s.jsx)("span",{children:i?"Over Allocated:":"Remaining:"}),(0,s.jsx)("span",{children:o(Math.abs(n))})]}),i&&(0,s.jsxs)("div",{className:"flex items-center gap-2 text-destructive text-xs p-2 bg-destructive/10 rounded-md",children:[(0,s.jsx)(j.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{children:"Warning: Your allocations exceed your income!"})]}),!i&&a>0&&t<=a&&(0,s.jsxs)("div",{className:"flex items-center gap-2 text-green-600 text-xs p-2 bg-green-600/10 rounded-md",children:[(0,s.jsx)(b.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{children:"Your budget is balanced or has funds remaining."})]})]})]})}var y=t(30285),N=t(23478),w=t(79556);let C=N.bL,A=l.forwardRef((e,a)=>{let{className:t,...l}=e;return(0,s.jsx)(N.q7,{ref:a,className:(0,p.cn)("border-b",t),...l})});A.displayName="AccordionItem";let S=l.forwardRef((e,a)=>{let{className:t,children:l,...n}=e;return(0,s.jsx)(N.Y9,{className:"flex",children:(0,s.jsxs)(N.l9,{ref:a,className:(0,p.cn)("flex flex-1 items-center justify-between py-4 font-medium transition-all hover:underline [&[data-state=open]>.accordion-trigger-chevron]:rotate-180",t),...n,children:[l,(0,s.jsx)(w.A,{className:"h-4 w-4 shrink-0 transition-transform duration-200 accordion-trigger-chevron"})]})})});S.displayName=N.l9.displayName;let R=l.forwardRef((e,a)=>{let{className:t,children:l,...n}=e;return(0,s.jsx)(N.UC,{ref:a,className:"overflow-hidden text-sm transition-all data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down",...n,children:(0,s.jsx)("div",{className:(0,p.cn)("pb-4 pt-0",t),children:l})})});R.displayName=N.UC.displayName;var k=t(15452),T=t(25318);let E=k.bL,I=k.l9,V=k.ZL;k.bm;let _=l.forwardRef((e,a)=>{let{className:t,...l}=e;return(0,s.jsx)(k.hJ,{ref:a,className:(0,p.cn)("fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",t),...l})});_.displayName=k.hJ.displayName;let D=l.forwardRef((e,a)=>{let{className:t,children:l,...n}=e;return(0,s.jsxs)(V,{children:[(0,s.jsx)(_,{}),(0,s.jsxs)(k.UC,{ref:a,className:(0,p.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",t),...n,children:[l,(0,s.jsxs)(k.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,s.jsx)(T.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})});D.displayName=k.UC.displayName;let O=e=>{let{className:a,...t}=e;return(0,s.jsx)("div",{className:(0,p.cn)("flex flex-col space-y-1.5 text-center sm:text-left",a),...t})};O.displayName="DialogHeader";let B=l.forwardRef((e,a)=>{let{className:t,...l}=e;return(0,s.jsx)(k.hE,{ref:a,className:(0,p.cn)("text-lg font-semibold leading-none tracking-tight",t),...l})});B.displayName=k.hE.displayName,l.forwardRef((e,a)=>{let{className:t,...l}=e;return(0,s.jsx)(k.VY,{ref:a,className:(0,p.cn)("text-sm text-muted-foreground",t),...l})}).displayName=k.VY.displayName;var G=t(15222),U=t(77223);function L(e){let{subCategory:a,onEdit:t,onDelete:l,balancesVisible:n}=e;return(0,s.jsxs)("div",{className:"flex items-center justify-between p-2 border-b border-border/50 last:border-b-0",children:[(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsx)("p",{className:"text-sm font-medium",children:a.name}),(0,s.jsxs)("p",{className:"text-xs text-muted-foreground",children:["Allocated: ",n?"R ".concat(a.allocatedAmount.toFixed(2)):"R ••••"]})]}),(0,s.jsxs)("div",{className:"flex gap-1",children:[(0,s.jsxs)(y.$,{variant:"ghost",size:"icon",onClick:t,className:"h-7 w-7",children:[(0,s.jsx)(G.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"sr-only",children:"Edit Subcategory"})]}),(0,s.jsxs)(y.$,{variant:"ghost",size:"icon",onClick:l,className:"h-7 w-7 text-destructive hover:text-destructive",children:[(0,s.jsx)(U.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"sr-only",children:"Delete Subcategory"})]})]})]})}var F=t(90221),M=t(62177),z=t(55594),P=t(17759);let Z=z.Ik({name:z.Yj().min(1,{message:"Subcategory name is required."}).max(50,{message:"Name must be 50 characters or less."}),allocatedAmount:z.vk(e=>"string"==typeof e?parseFloat(e):e,z.ai().min(0,{message:"Allocated amount must be a positive number."}))});function Y(e){let{onSubmit:a,initialData:t,onClose:l,parentCategoryName:n,balancesVisible:r}=e,i=(0,M.mN)({resolver:(0,F.u)(Z),defaultValues:{name:(null==t?void 0:t.name)||"",allocatedAmount:(null==t?void 0:t.allocatedAmount)||0}});return(0,s.jsx)(P.lV,{...i,children:(0,s.jsxs)("form",{onSubmit:i.handleSubmit(e=>{a(e)&&(i.reset(),l())}),className:"space-y-4",children:[(0,s.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Adding to: ",(0,s.jsx)("span",{className:"font-semibold",children:n})]}),(0,s.jsx)(P.zB,{control:i.control,name:"name",render:e=>{let{field:a}=e;return(0,s.jsxs)(P.eI,{children:[(0,s.jsx)(P.lR,{children:"Subcategory Name"}),(0,s.jsx)(P.MJ,{children:(0,s.jsx)(d.p,{placeholder:"e.g., Fruits & Vegetables",...a})}),(0,s.jsx)(P.C5,{})]})}}),(0,s.jsx)(P.zB,{control:i.control,name:"allocatedAmount",render:e=>{let{field:a}=e;return(0,s.jsxs)(P.eI,{children:[(0,s.jsx)(P.lR,{children:"Allocated Amount (R)"}),(0,s.jsx)(P.MJ,{children:(0,s.jsx)(d.p,{type:r?"number":"password",placeholder:r?"e.g., 100":"••••",...a,value:r?a.value:a.value>0?"••••":"0",onChange:e=>{if(r)a.onChange(""===e.target.value?0:parseFloat(e.target.value));else{let t=parseFloat(e.target.value);isNaN(t)?""===e.target.value&&a.onChange(0):a.onChange(t)}},step:"any"})}),(0,s.jsx)(P.C5,{})]})}}),(0,s.jsxs)("div",{className:"flex justify-end gap-2 pt-2",children:[(0,s.jsx)(y.$,{type:"button",variant:"outline",onClick:l,children:"Cancel"}),(0,s.jsx)(y.$,{type:"submit",children:(null==t?void 0:t.id)?"Save Changes":"Add Subcategory"})]})]})})}var $=t(2682),J=t(43369),W=t(34301),H=t(87481);function K(e){let{category:a,onAddSubCategory:t,onUpdateSubCategory:n,onDeleteSubCategory:r,balancesVisible:i,categoryIsVisible:o}=e,[d,c]=(0,l.useState)(!1),[u,x]=(0,l.useState)(null),{toast:h}=(0,H.dj)(),p=i&&o,f=a.subCategories.reduce((e,a)=>e+a.allocatedAmount,0),b=a.budget-f,v=b<0,N=a.budget>0?f/a.budget*100:0,w=e=>p?"R ".concat(e.toFixed(2)):"R ••••";return(0,s.jsxs)(m.Zp,{className:"mb-3",children:[(0,s.jsx)(m.aR,{className:"flex flex-row items-start justify-between pb-2 pt-3 px-4",children:(0,s.jsxs)("div",{children:[(0,s.jsx)(m.ZB,{className:"text-base font-headline",children:a.name}),(0,s.jsxs)(m.BT,{className:"text-xs",children:["Budget: ",w(a.budget)]})]})}),(0,s.jsxs)(m.Wu,{className:"px-4 pb-3 space-y-2",children:[(0,s.jsxs)("div",{className:"text-xs space-y-1",children:[(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)("span",{children:"Subcategories Total:"}),(0,s.jsx)("span",{children:w(f)})]}),(0,s.jsx)(g,{value:Math.min(N,100),className:v?"bg-destructive/70 [&>*]:bg-destructive":"[&>*]:bg-primary"}),(0,s.jsxs)("div",{className:"flex justify-between font-medium ".concat(v?"text-destructive":"text-green-600"),children:[(0,s.jsx)("span",{children:"Remaining in Budget:"}),(0,s.jsx)("span",{children:w(b)})]})]}),v&&p&&(0,s.jsxs)("div",{className:"flex items-center gap-1 text-destructive text-xs p-1.5 bg-destructive/10 rounded-md",children:[(0,s.jsx)(j.A,{className:"h-3 w-3"}),(0,s.jsx)("span",{children:"Subcategory allocations exceed this category's budget!"})]}),a.subCategories.length>0&&(0,s.jsx)(C,{type:"single",collapsible:!0,className:"w-full text-sm",children:(0,s.jsxs)(A,{value:"cat-".concat(a.id,"-subcategories"),className:"border-t pt-2",children:[(0,s.jsxs)(S,{className:"py-1 text-xs hover:no-underline justify-start gap-1 group",children:[(0,s.jsx)($.A,{className:"h-3 w-3 hidden group-data-[state=open]:block"}),(0,s.jsx)(J.A,{className:"h-3 w-3 block group-data-[state=open]:hidden"}),(0,s.jsxs)("span",{children:["Subcategories (",a.subCategories.length,")"]})]}),(0,s.jsx)(R,{className:"pt-1 pb-0 pl-2 border-l ml-1.5",children:a.subCategories.map(e=>(0,s.jsx)(L,{subCategory:e,onEdit:()=>{x(e),c(!0)},onDelete:()=>r(a.id,e.id),balancesVisible:p},e.id))})]})})]}),(0,s.jsx)(m.wL,{className:"px-4 pb-3 pt-0",children:(0,s.jsxs)(E,{open:d,onOpenChange:e=>{c(e),e||x(null)},children:[(0,s.jsx)(I,{asChild:!0,children:(0,s.jsxs)(y.$,{variant:"outline",size:"sm",className:"w-full text-xs",onClick:()=>{x(null),c(!0)},children:[(0,s.jsx)(W.A,{className:"mr-1 h-3 w-3"})," Add Subcategory"]})}),(0,s.jsxs)(D,{className:"sm:max-w-[425px]",children:[(0,s.jsx)(O,{children:(0,s.jsxs)(B,{className:"font-headline",children:[u?"Edit":"Add"," Subcategory"]})}),(0,s.jsx)(Y,{onSubmit:u?e=>{if(!u)return!1;let t=n(a.id,u.id,e.name,e.allocatedAmount);return t?(h({title:"Subcategory Updated",description:"".concat(e.name," updated.")}),x(null),c(!1)):h({title:"Error",description:"Cannot update allocation. Exceeds remaining budget in ".concat(a.name,"."),variant:"destructive"}),t}:e=>{let s=t(a.id,e.name,e.allocatedAmount);return s?(h({title:"Subcategory Added",description:"".concat(e.name," added to ").concat(a.name,".")}),c(!1)):h({title:"Error",description:"Cannot allocate ".concat(p?"R"+e.allocatedAmount.toFixed(2):"amount",". Exceeds remaining budget in ").concat(a.name,"."),variant:"destructive"}),s},initialData:u||{},onClose:()=>{c(!1),x(null)},parentCategoryName:a.name,balancesVisible:p})]})]})})]})}let q=z.Ik({name:z.Yj().min(1,{message:"Category name is required."}).max(50,{message:"Name must be 50 characters or less."}),budget:z.vk(e=>"string"==typeof e?parseFloat(e):e,z.ai().min(0,{message:"Budget must be a positive number."}))});function X(e){let{onSubmit:a,initialData:t,onClose:l}=e,n=(0,M.mN)({resolver:(0,F.u)(q),defaultValues:{name:(null==t?void 0:t.name)||"",budget:(null==t?void 0:t.budget)||0}});return(0,s.jsx)(P.lV,{...n,children:(0,s.jsxs)("form",{onSubmit:n.handleSubmit(e=>{a(e),n.reset(),l()}),className:"space-y-4",children:[(0,s.jsx)(P.zB,{control:n.control,name:"name",render:e=>{let{field:a}=e;return(0,s.jsxs)(P.eI,{children:[(0,s.jsx)(P.lR,{children:"Category Name"}),(0,s.jsx)(P.MJ,{children:(0,s.jsx)(d.p,{placeholder:"e.g., Groceries",...a})}),(0,s.jsx)(P.C5,{})]})}}),(0,s.jsx)(P.zB,{control:n.control,name:"budget",render:e=>{let{field:a}=e;return(0,s.jsxs)(P.eI,{children:[(0,s.jsx)(P.lR,{children:"Budget Amount (R)"}),(0,s.jsx)(P.MJ,{children:(0,s.jsx)(d.p,{type:"number",placeholder:"e.g., 500",...a,step:"any"})}),(0,s.jsx)(P.C5,{})]})}}),(0,s.jsxs)("div",{className:"flex justify-end gap-2 pt-2",children:[(0,s.jsx)(y.$,{type:"button",variant:"outline",onClick:l,children:"Cancel"}),(0,s.jsx)(y.$,{type:"submit",children:(null==t?void 0:t.id)?"Save Changes":"Add Category"})]})]})})}var Q=t(17649);let ee=Q.bL;Q.l9;let ea=Q.ZL,et=l.forwardRef((e,a)=>{let{className:t,...l}=e;return(0,s.jsx)(Q.hJ,{className:(0,p.cn)("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",t),...l,ref:a})});et.displayName=Q.hJ.displayName;let es=l.forwardRef((e,a)=>{let{className:t,...l}=e;return(0,s.jsxs)(ea,{children:[(0,s.jsx)(et,{}),(0,s.jsx)(Q.UC,{ref:a,className:(0,p.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",t),...l})]})});es.displayName=Q.UC.displayName;let el=e=>{let{className:a,...t}=e;return(0,s.jsx)("div",{className:(0,p.cn)("flex flex-col space-y-2 text-center sm:text-left",a),...t})};el.displayName="AlertDialogHeader";let en=e=>{let{className:a,...t}=e;return(0,s.jsx)("div",{className:(0,p.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",a),...t})};en.displayName="AlertDialogFooter";let er=l.forwardRef((e,a)=>{let{className:t,...l}=e;return(0,s.jsx)(Q.hE,{ref:a,className:(0,p.cn)("text-lg font-semibold",t),...l})});er.displayName=Q.hE.displayName;let ei=l.forwardRef((e,a)=>{let{className:t,...l}=e;return(0,s.jsx)(Q.VY,{ref:a,className:(0,p.cn)("text-sm text-muted-foreground",t),...l})});ei.displayName=Q.VY.displayName;let eo=l.forwardRef((e,a)=>{let{className:t,...l}=e;return(0,s.jsx)(Q.rc,{ref:a,className:(0,p.cn)((0,y.r)(),t),...l})});eo.displayName=Q.rc.displayName;let ed=l.forwardRef((e,a)=>{let{className:t,...l}=e;return(0,s.jsx)(Q.ZD,{ref:a,className:(0,p.cn)((0,y.r)({variant:"outline"}),"mt-2 sm:mt-0",t),...l})});ed.displayName=Q.ZD.displayName;var ec=t(46287),em=t(17607),eu=t(4607);function ex(e){var a,t;let{categories:n,onAddCategory:r,onUpdateCategory:i,onDeleteCategory:o,onAddSubCategory:d,onUpdateSubCategory:c,onDeleteSubCategory:u,onToggleCategoryVisibility:x,balancesVisible:h}=e,[p,g]=(0,l.useState)(!1),[f,j]=(0,l.useState)(null),[b,v]=(0,l.useState)(null),{toast:N}=(0,H.dj)();return(0,s.jsxs)(m.Zp,{children:[(0,s.jsxs)(m.aR,{className:"flex flex-row items-center justify-between pb-2",children:[(0,s.jsxs)(m.ZB,{className:"text-lg flex items-center gap-2 font-headline",children:[(0,s.jsx)(ec.A,{className:"h-5 w-5 text-primary"}),"Budget Categories"]}),(0,s.jsxs)(E,{open:p,onOpenChange:e=>{g(e),e||j(null)},children:[(0,s.jsx)(I,{asChild:!0,children:(0,s.jsxs)(y.$,{size:"sm",onClick:()=>{j(null),g(!0)},children:[(0,s.jsx)(W.A,{className:"mr-2 h-4 w-4"})," Add Category"]})}),(0,s.jsxs)(D,{className:"sm:max-w-[425px]",children:[(0,s.jsx)(O,{children:(0,s.jsxs)(B,{className:"font-headline",children:[f?"Edit":"Add"," Category"]})}),(0,s.jsx)(X,{onSubmit:f?e=>{f&&(i(f.id,e.name,e.budget),N({title:"Category Updated",description:"".concat(e.name," has been updated.")}),j(null),g(!1))}:e=>{r(e.name,e.budget),N({title:"Category Added",description:"".concat(e.name," has been added.")}),g(!1)},initialData:f||{},onClose:()=>{g(!1),j(null)}})]})]})]}),(0,s.jsx)(m.Wu,{className:"pt-2",children:0===n.length?(0,s.jsx)("p",{className:"text-sm text-muted-foreground text-center py-4",children:'No categories added yet. Click "Add Category" to start.'}):(0,s.jsx)("div",{className:"space-y-2",children:n.map(e=>{var a,t,l;return(0,s.jsxs)("div",{className:"relative group",children:[(0,s.jsx)(K,{category:e,onUpdateCategory:i,onDeleteCategory:o,onAddSubCategory:d,onUpdateSubCategory:c,onDeleteSubCategory:u,balancesVisible:h,categoryIsVisible:null===(a=e.isVisible)||void 0===a||a,onToggleVisibility:()=>x(e.id)}),(0,s.jsxs)("div",{className:"absolute top-2 right-2 flex gap-1 opacity-0 group-hover:opacity-100 transition-opacity",children:[(0,s.jsxs)(y.$,{variant:"ghost",size:"icon",className:"h-7 w-7 bg-card hover:bg-muted",onClick:()=>x(e.id),children:[null===(t=e.isVisible)||void 0===t||t?(0,s.jsx)(em.A,{className:"h-4 w-4"}):(0,s.jsx)(eu.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"sr-only",children:null===(l=e.isVisible)||void 0===l||l?"Hide Category Balances":"Show Category Balances"})]}),(0,s.jsxs)(y.$,{variant:"ghost",size:"icon",className:"h-7 w-7 bg-card hover:bg-muted",onClick:()=>{j(e),g(!0)},children:[(0,s.jsx)(G.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"sr-only",children:"Edit Category"})]}),(0,s.jsxs)(y.$,{variant:"ghost",size:"icon",className:"h-7 w-7 text-destructive hover:text-destructive bg-card hover:bg-muted",onClick:()=>v(e.id),children:[(0,s.jsx)(U.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"sr-only",children:"Delete Category"})]})]})]},e.id)})})}),(0,s.jsx)(ee,{open:!!b,onOpenChange:()=>v(null),children:(0,s.jsxs)(es,{children:[(0,s.jsxs)(el,{children:[(0,s.jsx)(er,{children:"Are you sure?"}),(0,s.jsxs)(ei,{children:["This action will delete the category",null!==(t=null===(a=n.find(e=>e.id===b))||void 0===a?void 0:a.subCategories.length)&&void 0!==t&&t?" and all its subcategories":"",". This cannot be undone."]})]}),(0,s.jsxs)(en,{children:[(0,s.jsx)(ed,{children:"Cancel"}),(0,s.jsx)(eo,{onClick:()=>{if(b){let e=n.find(e=>e.id===b);o(b),N({title:"Category Deleted",description:"".concat((null==e?void 0:e.name)||"Category"," has been deleted.")}),v(null)}},className:"bg-destructive hover:bg-destructive/90",children:"Delete"})]})]})})]})}var eh=t(9041),ep=t(83540),eg=t(94517),ef=t(24026);let ej={light:"",dark:".dark"},eb=l.createContext(null);function ev(){let e=l.useContext(eb);if(!e)throw Error("useChart must be used within a <ChartContainer />");return e}let ey=l.forwardRef((e,a)=>{let{id:t,className:n,children:r,config:i,...o}=e,d=l.useId(),c="chart-".concat(t||d.replace(/:/g,""));return(0,s.jsx)(eb.Provider,{value:{config:i},children:(0,s.jsxs)("div",{"data-chart":c,ref:a,className:(0,p.cn)("flex aspect-video justify-center text-xs [&_.recharts-cartesian-axis-tick_text]:fill-muted-foreground [&_.recharts-cartesian-grid_line[stroke='#ccc']]:stroke-border/50 [&_.recharts-curve.recharts-tooltip-cursor]:stroke-border [&_.recharts-dot[stroke='#fff']]:stroke-transparent [&_.recharts-layer]:outline-none [&_.recharts-polar-grid_[stroke='#ccc']]:stroke-border [&_.recharts-radial-bar-background-sector]:fill-muted [&_.recharts-rectangle.recharts-tooltip-cursor]:fill-muted [&_.recharts-reference-line_[stroke='#ccc']]:stroke-border [&_.recharts-sector[stroke='#fff']]:stroke-transparent [&_.recharts-sector]:outline-none [&_.recharts-surface]:outline-none",n),...o,children:[(0,s.jsx)(eN,{id:c,config:i}),(0,s.jsx)(ep.u,{children:r})]})})});ey.displayName="Chart";let eN=e=>{let{id:a,config:t}=e,l=Object.entries(t).filter(e=>{let[,a]=e;return a.theme||a.color});return l.length?(0,s.jsx)("style",{dangerouslySetInnerHTML:{__html:Object.entries(ej).map(e=>{let[t,s]=e;return"\n".concat(s," [data-chart=").concat(a,"] {\n").concat(l.map(e=>{var a;let[s,l]=e,n=(null===(a=l.theme)||void 0===a?void 0:a[t])||l.color;return n?"  --color-".concat(s,": ").concat(n,";"):null}).join("\n"),"\n}\n")}).join("\n")}}):null},ew=eg.m,eC=l.forwardRef((e,a)=>{let{active:t,payload:n,className:r,indicator:i="dot",hideLabel:o=!1,hideIndicator:d=!1,label:c,labelFormatter:m,labelClassName:u,formatter:x,color:h,nameKey:g,labelKey:f}=e,{config:j}=ev(),b=l.useMemo(()=>{var e;if(o||!(null==n?void 0:n.length))return null;let[a]=n,t="".concat(f||a.dataKey||a.name||"value"),l=eR(j,a,t),r=f||"string"!=typeof c?null==l?void 0:l.label:(null===(e=j[c])||void 0===e?void 0:e.label)||c;return m?(0,s.jsx)("div",{className:(0,p.cn)("font-medium",u),children:m(r,n)}):r?(0,s.jsx)("div",{className:(0,p.cn)("font-medium",u),children:r}):null},[c,m,n,o,u,j,f]);if(!t||!(null==n?void 0:n.length))return null;let v=1===n.length&&"dot"!==i;return(0,s.jsxs)("div",{ref:a,className:(0,p.cn)("grid min-w-[8rem] items-start gap-1.5 rounded-lg border border-border/50 bg-background px-2.5 py-1.5 text-xs shadow-xl",r),children:[v?null:b,(0,s.jsx)("div",{className:"grid gap-1.5",children:n.map((e,a)=>{let t="".concat(g||e.name||e.dataKey||"value"),l=eR(j,e,t),n=h||e.payload.fill||e.color;return(0,s.jsx)("div",{className:(0,p.cn)("flex w-full flex-wrap items-stretch gap-2 [&>svg]:h-2.5 [&>svg]:w-2.5 [&>svg]:text-muted-foreground","dot"===i&&"items-center"),children:x&&(null==e?void 0:e.value)!==void 0&&e.name?x(e.value,e.name,e,a,e.payload):(0,s.jsxs)(s.Fragment,{children:[(null==l?void 0:l.icon)?(0,s.jsx)(l.icon,{}):!d&&(0,s.jsx)("div",{className:(0,p.cn)("shrink-0 rounded-[2px] border-[--color-border] bg-[--color-bg]",{"h-2.5 w-2.5":"dot"===i,"w-1":"line"===i,"w-0 border-[1.5px] border-dashed bg-transparent":"dashed"===i,"my-0.5":v&&"dashed"===i}),style:{"--color-bg":n,"--color-border":n}}),(0,s.jsxs)("div",{className:(0,p.cn)("flex flex-1 justify-between leading-none",v?"items-end":"items-center"),children:[(0,s.jsxs)("div",{className:"grid gap-1.5",children:[v?b:null,(0,s.jsx)("span",{className:"text-muted-foreground",children:(null==l?void 0:l.label)||e.name})]}),e.value&&(0,s.jsx)("span",{className:"font-mono font-medium tabular-nums text-foreground",children:e.value.toLocaleString()})]})]})},e.dataKey)})})]})});eC.displayName="ChartTooltip";let eA=ef.s,eS=l.forwardRef((e,a)=>{let{className:t,hideIcon:l=!1,payload:n,verticalAlign:r="bottom",nameKey:i}=e,{config:o}=ev();return(null==n?void 0:n.length)?(0,s.jsx)("div",{ref:a,className:(0,p.cn)("flex items-center justify-center gap-4","top"===r?"pb-3":"pt-3",t),children:n.map(e=>{let a="".concat(i||e.dataKey||"value"),t=eR(o,e,a);return(0,s.jsxs)("div",{className:(0,p.cn)("flex items-center gap-1.5 [&>svg]:h-3 [&>svg]:w-3 [&>svg]:text-muted-foreground"),children:[(null==t?void 0:t.icon)&&!l?(0,s.jsx)(t.icon,{}):(0,s.jsx)("div",{className:"h-2 w-2 shrink-0 rounded-[2px]",style:{backgroundColor:e.color}}),null==t?void 0:t.label]},e.value)})}):null});function eR(e,a,t){if("object"!=typeof a||null===a)return;let s="payload"in a&&"object"==typeof a.payload&&null!==a.payload?a.payload:void 0,l=t;return t in a&&"string"==typeof a[t]?l=a[t]:s&&t in s&&"string"==typeof s[t]&&(l=s[t]),l in e?e[l]:e[t]}eS.displayName="ChartLegend";var ek=t(58995),eT=t(79133),eE=t(54811);let eI=["hsl(var(--chart-1))","hsl(var(--chart-2))","hsl(var(--chart-3))","hsl(var(--chart-4))","hsl(var(--chart-5))","hsl(200 70% 50%)","hsl(300 70% 50%)","hsl(50 70% 50%)"];function eV(e){let{totalIncome:a,categories:t,balancesVisible:l}=e,n=a-t.reduce((e,a)=>e+a.budget,0),r=t.filter(e=>e.budget>0).map((e,a)=>{var t;return{name:e.name,value:e.budget,fill:eI[a%eI.length],isVisible:null===(t=e.isVisible)||void 0===t||t}});n>0&&r.push({name:"Unallocated",value:n,fill:"hsl(var(--muted))",isVisible:!0});let i=r.reduce((e,a)=>(e[a.name]={label:a.name,color:a.fill},e),{});if(0===a&&0===t.length)return(0,s.jsxs)(m.Zp,{children:[(0,s.jsx)(m.aR,{className:"pb-2",children:(0,s.jsxs)(m.ZB,{className:"text-lg flex items-center gap-2 font-headline",children:[(0,s.jsx)(eh.A,{className:"h-5 w-5 text-primary"}),"Budget Overview"]})}),(0,s.jsx)(m.Wu,{children:(0,s.jsx)("p",{className:"text-sm text-muted-foreground text-center py-4",children:"Enter your income and add categories to see a visual breakdown."})})]});let o=0===r.length||r.every(e=>0===e.value);return(0,s.jsxs)(m.Zp,{children:[(0,s.jsx)(m.aR,{className:"pb-2",children:(0,s.jsxs)(m.ZB,{className:"text-lg flex items-center gap-2 font-headline",children:[(0,s.jsx)(eh.A,{className:"h-5 w-5 text-primary"}),"Budget Overview"]})}),(0,s.jsxs)(m.Wu,{children:[o?(0,s.jsx)("p",{className:"text-sm text-muted-foreground text-center py-4",children:"Allocate funds to categories to see the chart."}):(0,s.jsx)(ey,{config:i,className:"mx-auto aspect-square max-h-[250px] h-auto",children:(0,s.jsxs)(ek.r,{children:[(0,s.jsx)(ew,{formatter:(e,a,t)=>{var s,n;let r=null===(n=null===(s=t.payload)||void 0===s?void 0:s.isVisible)||void 0===n||n;return[l&&r?"R ".concat(Number(e).toFixed(2)):"R ••••",a]},content:(0,s.jsx)(eC,{nameKey:"name",hideLabel:!1})}),(0,s.jsx)(eT.F,{data:r,dataKey:"value",nameKey:"name",cx:"50%",cy:"50%",outerRadius:80,labelLine:!1,label:e=>{var a;let{percent:t,payload:s}=e,n=null===(a=null==s?void 0:s.isVisible)||void 0===a||a;return l&&n?"".concat((100*t).toFixed(0),"%"):""},children:r.map((e,a)=>(0,s.jsx)(eE.f,{fill:e.fill},"cell-".concat(a)))}),(0,s.jsx)(eA,{content:(0,s.jsx)(eS,{nameKey:"name",className:"text-xs flex-wrap justify-center gap-x-2 gap-y-1"})})]})}),t.map(e=>{var a;if(0===e.budget)return null;let t=l&&(null===(a=e.isVisible)||void 0===a||a),n=e.subCategories.reduce((e,a)=>e+a.allocatedAmount,0),r=e.budget>0?n/e.budget*100:0,i=n>e.budget;return(0,s.jsxs)("div",{className:"mt-3 text-xs",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-0.5",children:[(0,s.jsx)("span",{className:"font-medium",children:e.name}),(0,s.jsx)("span",{className:"text-muted-foreground",children:t?"".concat(n.toFixed(2)," / ").concat(e.budget.toFixed(2)):"•••• / ••••"})]}),(0,s.jsx)(g,{value:Math.min(r,100),className:"h-1.5 ".concat(i?"bg-destructive/70 [&>*]:bg-destructive":"[&>*]:bg-primary")})]},e.id)})]})]})}var e_=t(93081),eD=t(77381),eO=t(10518);let eB=e_.bL;e_.YJ;let eG=e_.WT,eU=l.forwardRef((e,a)=>{let{className:t,children:l,...n}=e;return(0,s.jsxs)(e_.l9,{ref:a,className:(0,p.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",t),...n,children:[l,(0,s.jsx)(e_.In,{asChild:!0,children:(0,s.jsx)(w.A,{className:"h-4 w-4 opacity-50"})})]})});eU.displayName=e_.l9.displayName;let eL=l.forwardRef((e,a)=>{let{className:t,...l}=e;return(0,s.jsx)(e_.PP,{ref:a,className:(0,p.cn)("flex cursor-default items-center justify-center py-1",t),...l,children:(0,s.jsx)(eD.A,{className:"h-4 w-4"})})});eL.displayName=e_.PP.displayName;let eF=l.forwardRef((e,a)=>{let{className:t,...l}=e;return(0,s.jsx)(e_.wn,{ref:a,className:(0,p.cn)("flex cursor-default items-center justify-center py-1",t),...l,children:(0,s.jsx)(w.A,{className:"h-4 w-4"})})});eF.displayName=e_.wn.displayName;let eM=l.forwardRef((e,a)=>{let{className:t,children:l,position:n="popper",...r}=e;return(0,s.jsx)(e_.ZL,{children:(0,s.jsxs)(e_.UC,{ref:a,className:(0,p.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===n&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",t),position:n,...r,children:[(0,s.jsx)(eL,{}),(0,s.jsx)(e_.LM,{className:(0,p.cn)("p-1","popper"===n&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:l}),(0,s.jsx)(eF,{})]})})});eM.displayName=e_.UC.displayName,l.forwardRef((e,a)=>{let{className:t,...l}=e;return(0,s.jsx)(e_.JU,{ref:a,className:(0,p.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",t),...l})}).displayName=e_.JU.displayName;let ez=l.forwardRef((e,a)=>{let{className:t,children:l,...n}=e;return(0,s.jsxs)(e_.q7,{ref:a,className:(0,p.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",t),...n,children:[(0,s.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,s.jsx)(e_.VF,{children:(0,s.jsx)(eO.A,{className:"h-4 w-4"})})}),(0,s.jsx)(e_.p4,{children:l})]})});ez.displayName=e_.q7.displayName,l.forwardRef((e,a)=>{let{className:t,...l}=e;return(0,s.jsx)(e_.wv,{ref:a,className:(0,p.cn)("-mx-1 my-1 h-px bg-muted",t),...l})}).displayName=e_.wv.displayName;var eP=t(27300),eZ=t(58260),eY=t(42625),e$=t(28328),eJ=t(18186),eW=t(57082),eH=t(27379),eK=t(68098),eq=t(2160);let eX=z.Ik({name:z.Yj().min(1,{message:"Goal name is required."}).max(50,{message:"Name must be 50 characters or less."}),targetAmount:z.vk(e=>"string"==typeof e?parseFloat(e):e,z.ai().min(1,{message:"Target amount must be greater than 0."})),icon:z.Yj().optional()}),eQ=[{value:"Default",label:"Default",Icon:eP.A},{value:"Savings",label:"Savings",Icon:e=>{let{className:a}=e;return(0,s.jsxs)("svg",{className:a,xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[(0,s.jsx)("path",{d:"M10 21h4"}),(0,s.jsx)("path",{d:"M12 17v4"}),(0,s.jsx)("path",{d:"M10 3h4c2.2 0 4 1.8 4 4v2c0 1.1-.9 2-2 2h-1"}),(0,s.jsx)("path",{d:"M8 11V7c0-2.2 1.8-4 4-4"}),(0,s.jsx)("path",{d:"M19 13c0-1.66-1.34-3-3-3h-2V7"}),(0,s.jsx)("path",{d:"M10 13c2.2 0 4-1.8 4-4"}),(0,s.jsx)("path",{d:"M2 13c2.5 0 2.5-3 5-3s2.5 3 5 3c2.5 0 2.5-3 5-3s2.5 3 5 3"}),(0,s.jsx)("path",{d:"M7.5 13s.5-1 2.5-1 2.5 1 2.5 1"}),(0,s.jsx)("path",{d:"M14 13c2 0 2.5-1 2.5-1"}),(0,s.jsx)("path",{d:"M2 17h.01"})]})}},{value:"Vacation",label:"Vacation",Icon:eZ.A},{value:"Shopping",label:"Shopping",Icon:eY.A},{value:"Car",label:"Car",Icon:e$.A},{value:"Home",label:"Home Renovation",Icon:eJ.A},{value:"Business",label:"Business",Icon:eW.A},{value:"Education",label:"Education",Icon:eH.A},{value:"Wedding",label:"Wedding",Icon:eK.A},{value:"Gift",label:"Gift",Icon:eq.A}];function e0(e){let{onSubmit:a,initialData:t,onClose:l}=e,n=(0,M.mN)({resolver:(0,F.u)(eX),defaultValues:{name:(null==t?void 0:t.name)||"",targetAmount:(null==t?void 0:t.targetAmount)||0,icon:(null==t?void 0:t.icon)||"Default"}});return(0,s.jsx)(P.lV,{...n,children:(0,s.jsxs)("form",{onSubmit:n.handleSubmit(e=>{a(e.name,e.targetAmount,e.icon),t||n.reset({name:"",targetAmount:0,icon:"Default"}),l()}),className:"space-y-4 pt-2",children:[(0,s.jsx)(P.zB,{control:n.control,name:"name",render:e=>{let{field:a}=e;return(0,s.jsxs)(P.eI,{children:[(0,s.jsx)(P.lR,{children:"Goal Name"}),(0,s.jsx)(P.MJ,{children:(0,s.jsx)(d.p,{placeholder:"e.g., New Laptop, Vacation Fund",...a})}),(0,s.jsx)(P.C5,{})]})}}),(0,s.jsx)(P.zB,{control:n.control,name:"targetAmount",render:e=>{let{field:a}=e;return(0,s.jsxs)(P.eI,{children:[(0,s.jsx)(P.lR,{children:"Target Amount (R)"}),(0,s.jsx)(P.MJ,{children:(0,s.jsx)(d.p,{type:"number",placeholder:"e.g., 15000",...a,step:"any"})}),(0,s.jsx)(P.C5,{})]})}}),(0,s.jsx)(P.zB,{control:n.control,name:"icon",render:e=>{let{field:a}=e;return(0,s.jsxs)(P.eI,{children:[(0,s.jsx)(P.lR,{children:"Goal Icon (Optional)"}),(0,s.jsxs)(eB,{onValueChange:a.onChange,defaultValue:a.value,children:[(0,s.jsx)(P.MJ,{children:(0,s.jsx)(eU,{children:(0,s.jsx)(eG,{placeholder:"Select an icon"})})}),(0,s.jsx)(eM,{children:eQ.map(e=>(0,s.jsx)(ez,{value:e.value,children:(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(e.Icon,{className:"h-4 w-4"}),e.label]})},e.value))})]}),(0,s.jsx)(P.C5,{})]})}}),(0,s.jsxs)("div",{className:"flex justify-end gap-2 pt-2",children:[(0,s.jsx)(y.$,{type:"button",variant:"outline",onClick:l,children:"Cancel"}),(0,s.jsx)(y.$,{type:"submit",children:t?"Save Changes":"Set Goal"})]})]})})}var e1=t(519),e2=t(91467),e5=t(81203),e4=t(58127);let e3={Default:eP.A,Vacation:e1.A,Gadget:e2.A};function e6(e){let{goal:a,onSetGoal:t,onUpdateProgress:n,onClearGoal:r,overallRemaining:i,balancesVisible:o}=e,[d,c]=(0,l.useState)(!1),[u,x]=(0,l.useState)(!1),[h,p]=(0,l.useState)(0),[f,j]=(0,l.useState)(!1);(0,l.useEffect)(()=>{a&&p(a.savedAmount)},[a]);let b=e=>o?"R ".concat(e.toFixed(2)):"R ••••",v=(null==a?void 0:a.icon)&&e3[a.icon]?e3[a.icon]:e5.A;if(!a||a.dateAchieved)return(0,s.jsxs)(m.Zp,{children:[(0,s.jsxs)(m.aR,{className:"pb-2",children:[(0,s.jsxs)(m.ZB,{className:"text-lg flex items-center gap-2 font-headline",children:[(null==a?void 0:a.dateAchieved)?(0,s.jsx)(e4.A,{className:"h-5 w-5 text-green-500"}):(0,s.jsx)(e5.A,{className:"h-5 w-5 text-primary"}),(null==a?void 0:a.dateAchieved)?"Goal Achieved!":"Financial Goal"]}),(null==a?void 0:a.dateAchieved)&&a&&(0,s.jsxs)(m.BT,{className:"text-sm",children:["Congrats on achieving: ",a.name,"!"]})]}),(0,s.jsxs)(m.Wu,{children:[(null==a?void 0:a.dateAchieved)&&a&&(0,s.jsxs)("div",{className:"space-y-1 text-center",children:[(0,s.jsx)("p",{className:"text-2xl font-bold text-green-600",children:b(a.targetAmount)}),(0,s.jsxs)("p",{className:"text-xs text-muted-foreground",children:["Achieved on ",new Date(a.dateAchieved).toLocaleDateString()]})]}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground ".concat((null==a?void 0:a.dateAchieved)?"mt-2 text-center":"text-center py-4"),children:(null==a?void 0:a.dateAchieved)?"Ready for a new challenge?":"Set a financial goal to start saving towards something important!"})]}),(0,s.jsx)(m.wL,{children:(0,s.jsxs)(E,{open:d,onOpenChange:c,children:[(0,s.jsx)(I,{asChild:!0,children:(0,s.jsxs)(y.$,{className:"w-full",onClick:()=>c(!0),children:[(0,s.jsx)(W.A,{className:"mr-2 h-4 w-4"})," ",(null==a?void 0:a.dateAchieved)?"Set New Goal":"Set a Goal"]})}),(0,s.jsxs)(D,{className:"sm:max-w-[425px]",children:[(0,s.jsx)(O,{children:(0,s.jsx)(B,{className:"font-headline",children:(null==a?void 0:a.dateAchieved)?"Set New Financial Goal":"Set Financial Goal"})}),(0,s.jsx)(e0,{onSubmit:(e,a,s)=>{t(e,a,s),c(!1)},initialData:null,onClose:()=>c(!1)})]})]})})]});let N=a.targetAmount>0?a.savedAmount/a.targetAmount*100:0;return(0,s.jsxs)(m.Zp,{children:[(0,s.jsxs)(m.aR,{className:"pb-3 pt-4",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)(m.ZB,{className:"text-lg flex items-center gap-2 font-headline",children:[(0,s.jsx)(v,{className:"h-5 w-5 text-primary"}),a.name]}),(0,s.jsxs)("div",{className:"flex gap-1",children:[(0,s.jsxs)(E,{open:d,onOpenChange:c,children:[(0,s.jsx)(I,{asChild:!0,children:(0,s.jsx)(y.$,{variant:"ghost",size:"icon",className:"h-7 w-7",onClick:()=>c(!0),children:(0,s.jsx)(G.A,{className:"h-4 w-4"})})}),(0,s.jsxs)(D,{className:"sm:max-w-[425px]",children:[(0,s.jsx)(O,{children:(0,s.jsx)(B,{className:"font-headline",children:"Edit Financial Goal"})}),(0,s.jsx)(e0,{onSubmit:(e,a,s)=>{t(e,a,s),c(!1)},initialData:a,onClose:()=>c(!1)})]})]}),(0,s.jsx)(y.$,{variant:"ghost",size:"icon",className:"h-7 w-7 text-destructive hover:text-destructive",onClick:()=>j(!0),children:(0,s.jsx)(U.A,{className:"h-4 w-4"})})]})]}),(0,s.jsxs)(m.BT,{className:"text-xs pt-1",children:["Target: ",b(a.targetAmount)," | Saved: ",b(a.savedAmount)]})]}),(0,s.jsxs)(m.Wu,{className:"space-y-3",children:[(0,s.jsx)(g,{value:N,className:"h-2 [&>*]:bg-primary"}),(0,s.jsxs)("div",{className:"text-xs text-muted-foreground",children:[b(a.targetAmount-a.savedAmount)," still to go. You can do it!"]}),o&&i>0&&(0,s.jsxs)("p",{className:"text-xs text-green-600 bg-green-500/10 p-1.5 rounded-md",children:["You have ",b(i)," unallocated in your budget. Consider putting some towards your goal!"]})]}),(0,s.jsx)(m.wL,{children:(0,s.jsxs)(E,{open:u,onOpenChange:x,children:[(0,s.jsx)(I,{asChild:!0,children:(0,s.jsx)(y.$,{className:"w-full",variant:"outline",onClick:()=>{p(a.savedAmount),x(!0)},children:"Log Progress"})}),(0,s.jsxs)(D,{className:"sm:max-w-[425px]",children:[(0,s.jsx)(O,{children:(0,s.jsxs)(B,{className:"font-headline",children:["Log Progress for ",a.name]})}),(0,s.jsxs)("div",{className:"space-y-4 py-2",children:[(0,s.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Current Target: ",b(a.targetAmount)]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"progressAmount",className:"block text-sm font-medium text-foreground mb-1",children:"Total Amount Saved Towards Goal (R)"}),(0,s.jsx)("input",{id:"progressAmount",type:o?"number":"text",value:o?h:"••••",onChange:e=>{let a=parseFloat(e.target.value);p(Math.max(0,isNaN(a)?0:a))},readOnly:!o,className:"w-full p-2 border rounded-md border-input",step:"any"})]}),(0,s.jsx)(y.$,{onClick:()=>{n(h),x(!1)},className:"w-full",children:"Save Progress"})]})]})]})}),(0,s.jsx)(ee,{open:f,onOpenChange:j,children:(0,s.jsxs)(es,{children:[(0,s.jsxs)(el,{children:[(0,s.jsx)(er,{children:"Are you sure?"}),(0,s.jsxs)(ei,{children:['This will clear your current financial goal "',a.name,'". This action cannot be undone.']})]}),(0,s.jsxs)(en,{children:[(0,s.jsx)(ed,{children:"Cancel"}),(0,s.jsx)(eo,{onClick:()=>{r(),j(!1)},className:"bg-destructive hover:bg-destructive/90",children:"Clear Goal"})]})]})})]})}var e7=t(68856),e8=t(85268),e9=t(30462),ae=t(89829),aa=t(54913);let at={trophy:e8.A,star:e9.A,target:e5.A,zap:ae.A,award:e2.A,crown:aa.A},as={gold:"bg-gradient-to-r from-yellow-400 to-yellow-600 text-yellow-900 border-yellow-500/30",silver:"bg-gradient-to-r from-gray-300 to-gray-500 text-gray-900 border-gray-400/30",bronze:"bg-gradient-to-r from-orange-400 to-orange-600 text-orange-900 border-orange-500/30",primary:"bg-gradient-to-r from-primary to-primary/80 text-primary-foreground border-primary/30",success:"bg-gradient-to-r from-green-400 to-green-600 text-green-900 border-green-500/30"},al={sm:"px-2 py-1 text-xs",md:"px-3 py-1.5 text-sm",lg:"px-4 py-2 text-base"};function an(e){let{title:a,description:t,icon:l="trophy",variant:n="gold",size:r="md",animated:o=!0,showConfetti:d=!1,className:c}=e,m=at[l],u=(0,s.jsxs)("div",{className:(0,p.cn)("inline-flex items-center gap-2 rounded-full font-medium border shadow-lg",as[n],al[r],o&&"hover:scale-105 transition-transform duration-200",c),children:[(0,s.jsx)(m,{className:(0,p.cn)("shrink-0","sm"===r?"h-3 w-3":"md"===r?"h-4 w-4":"h-5 w-5")}),(0,s.jsx)("span",{className:"font-semibold",children:a})]});return o?(0,s.jsxs)(i.P.div,{initial:{scale:0,opacity:0},animate:{scale:1,opacity:1},transition:{type:"spring",stiffness:260,damping:20,delay:.1},whileHover:{scale:1.05},whileTap:{scale:.95},className:"inline-block",children:[u,t&&(0,s.jsx)(i.P.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.3},className:"text-xs text-muted-foreground mt-1 text-center",children:t})]}):u}function ar(e){let{title:a,description:t,icon:l="trophy",variant:n="gold",onClose:r}=e,o=at[l];return(0,s.jsxs)(i.P.div,{initial:{x:300,opacity:0},animate:{x:0,opacity:1},exit:{x:300,opacity:0},transition:{type:"spring",stiffness:300,damping:30},className:(0,p.cn)("flex items-center gap-3 p-4 rounded-lg border shadow-lg max-w-sm","bg-card/95 backdrop-blur-sm border-border/50"),children:[(0,s.jsx)(i.P.div,{animate:{rotate:[0,10,-10,0]},transition:{duration:.6,repeat:2},className:(0,p.cn)("flex items-center justify-center w-10 h-10 rounded-full",as[n]),children:(0,s.jsx)(o,{className:"h-5 w-5"})}),(0,s.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,s.jsx)("h4",{className:"font-semibold text-foreground",children:a}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:t})]}),r&&(0,s.jsx)("button",{onClick:r,className:"text-muted-foreground hover:text-foreground transition-colors",children:"\xd7"})]})}var ai=t(90925),ao=t(64509);let ad={BUDGET_STARTER:"BUDGET_STARTER",GOAL_SETTER:"GOAL_SETTER",GOAL_CRUSHER:"GOAL_CRUSHER"},ac={[ad.BUDGET_STARTER]:{title:"Budget Starter!",description:"You've set your income and added your first category!",IconComponent:ao.A},[ad.GOAL_SETTER]:{title:"Goal Setter!",description:"You've set your first financial goal!",IconComponent:e5.A},[ad.GOAL_CRUSHER]:{title:"Goal Crusher!",description:"Congratulations! You've achieved your financial goal!",IconComponent:e8.A}};function am(){var e,a;let{currentUser:t,loading:d}=(0,ai.A)(),c=(0,n.useRouter)(),[m,u]=(0,l.useState)(!1),[h,p]=(0,l.useState)(0),[g,f]=(0,l.useState)([]),[j,b]=(0,l.useState)(null),[y,N]=(0,l.useState)([]),[w,C]=(0,l.useState)(!0),[A,S]=(0,l.useState)(null),[R,k]=(0,l.useState)(!0),{toast:T}=(0,H.dj)(),E=(0,l.useCallback)(()=>t?"budgetWiseData_".concat(t.uid):null,[t]),I=(0,l.useCallback)(e=>{if(!y.includes(e)){N(a=>[...a,e]),S(e);let a=ac[e];a&&(T({title:(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(a.IconComponent,{className:"h-5 w-5 text-accent"}),(0,s.jsx)("span",{children:a.title})]}),description:a.description}),setTimeout(()=>S(null),5e3))}},[y,T]);(0,l.useEffect)(()=>{u(!0)},[]),(0,l.useEffect)(()=>{if(d)return;if(!t){c.push("/login");return}let e=E();if(e&&m){let a=localStorage.getItem(e);if(a)try{let e=JSON.parse(a);p(e.totalIncome||0),f((e.categories||[]).map(e=>({...e,isVisible:void 0===e.isVisible||e.isVisible,subCategories:e.subCategories||[]}))),b(e.financialGoal||null),N(e.achievements||[]),C(void 0===e.balancesVisible||e.balancesVisible)}catch(e){console.error("Failed to parse budget data from localStorage",e),p(0),f([]),b(null),N([]),C(!0)}else p(0),f([]),b(null),N([]),C(!0)}},[t,d,c,m,E]),(0,l.useEffect)(()=>{let e=E();e&&m&&!d&&t&&(localStorage.setItem(e,JSON.stringify({totalIncome:h,categories:g,financialGoal:j,achievements:y,balancesVisible:w})),h>0&&g.length>0&&I(ad.BUDGET_STARTER),(null==j?void 0:j.dateAchieved)&&I(ad.GOAL_CRUSHER))},[h,g,j,y,w,m,I,E,d,t]);let V=(0,l.useCallback)(()=>{C(e=>!e)},[]),_=(0,l.useCallback)(e=>{f(a=>a.map(a=>{var t;return a.id===e?{...a,isVisible:!(null===(t=a.isVisible)||void 0===t||t)}:a}))},[]),D=(0,l.useCallback)(e=>{p(e)},[]),O=(0,l.useCallback)((e,a)=>{let t={id:crypto.randomUUID(),name:e,budget:a,subCategories:[],isVisible:!0};f(e=>[...e,t])},[]),B=(0,l.useCallback)((e,a,t)=>{f(s=>s.map(s=>s.id===e?{...s,name:a,budget:t}:s))},[]),G=(0,l.useCallback)(e=>{f(a=>a.filter(a=>a.id!==e))},[]),U=(0,l.useCallback)((e,a,t)=>{let s=!1;return f(l=>l.map(l=>{if(l.id===e){if(l.subCategories.reduce((e,a)=>e+a.allocatedAmount,0)+t>l.budget)return s=!1,l;let e={id:crypto.randomUUID(),name:a,allocatedAmount:t};return s=!0,{...l,subCategories:[...l.subCategories,e]}}return l})),s},[]),L=(0,l.useCallback)((e,a,t,s)=>{let l=!1;return f(n=>n.map(n=>n.id===e?n.subCategories.filter(e=>e.id!==a).reduce((e,a)=>e+a.allocatedAmount,0)+s>n.budget?(l=!1,n):(l=!0,{...n,subCategories:n.subCategories.map(e=>e.id===a?{...e,name:t,allocatedAmount:s}:e)}):n)),l},[]),F=(0,l.useCallback)((e,a)=>{f(t=>t.map(t=>t.id===e?{...t,subCategories:t.subCategories.filter(e=>e.id!==a)}:t)),T({title:"Subcategory Deleted",description:"Subcategory has been removed."})},[T]),M=(0,l.useCallback)((e,a,t)=>{b({id:(null==j?void 0:j.id)||crypto.randomUUID(),name:e,targetAmount:a,savedAmount:(null==j?void 0:j.id)?j.savedAmount:0,icon:t,dateSet:(null==j?void 0:j.dateSet)||new Date().toISOString(),dateAchieved:null}),y.includes(ad.GOAL_SETTER)&&j||I(ad.GOAL_SETTER),T({title:"Financial Goal Updated!",description:'Your goal "'.concat(e,'" has been set/updated.')})},[j,I,T,y]),z=(0,l.useCallback)(e=>{if(j){let a={...j,savedAmount:e};e>=j.targetAmount&&!j.dateAchieved&&(a.dateAchieved=new Date().toISOString(),I(ad.GOAL_CRUSHER),T({title:"Goal Achieved!",description:"Congratulations on reaching your goal: ".concat(j.name,"!"),duration:5e3})),b(a)}},[j,I,T]),P=(0,l.useCallback)(()=>{b(null),T({title:"Financial Goal Cleared",description:"Your financial goal has been removed."})},[T]),Z=g.reduce((e,a)=>e+a.budget,0),Y=h-Z;return!d&&m&&t?(0,s.jsxs)("div",{className:"flex flex-col min-h-screen bg-background",children:[(0,s.jsx)(o.default,{title:"BudgetWise",balancesVisible:w,onToggleBalances:V}),(0,s.jsx)(r.N,{children:A&&(0,s.jsx)(i.P.div,{initial:{y:-100,opacity:0},animate:{y:0,opacity:1},exit:{y:-100,opacity:0},className:"fixed top-20 right-4 z-50",children:(0,s.jsx)(ar,{title:(null===(e=ac[A])||void 0===e?void 0:e.title)||"Achievement Unlocked!",description:(null===(a=ac[A])||void 0===a?void 0:a.description)||"You've earned a new achievement!",icon:"trophy",variant:"gold",onClose:()=>S(null)})})}),(0,s.jsxs)("main",{className:"flex-grow container mx-auto py-4 sm:py-6 px-2 sm:px-4",children:[y.length>0&&(0,s.jsx)(i.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"mb-6 flex flex-wrap gap-2",children:y.map(e=>{var a;return(0,s.jsx)(an,{title:(null===(a=ac[e])||void 0===a?void 0:a.title)||"Achievement",icon:"trophy",variant:"gold",size:"sm",animated:!0},e)})}),(0,s.jsxs)(i.P.div,{initial:{opacity:0},animate:{opacity:1},transition:{duration:.5},className:"grid grid-cols-1 md:grid-cols-3 gap-4 sm:gap-6",children:[(0,s.jsxs)(i.P.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{duration:.6,delay:.1},className:"md:col-span-1 space-y-4 sm:space-y-6",children:[(0,s.jsx)(x,{totalIncome:h,onIncomeChange:D,balancesVisible:w}),(0,s.jsx)(v,{totalIncome:h,overallTotalAllocated:Z,balancesVisible:w}),(0,s.jsx)(e6,{goal:j,onSetGoal:M,onUpdateProgress:z,onClearGoal:P,overallRemaining:Y,balancesVisible:w}),(0,s.jsx)(eV,{totalIncome:h,categories:g,balancesVisible:w})]}),(0,s.jsx)(i.P.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},transition:{duration:.6,delay:.2},className:"md:col-span-2",children:(0,s.jsx)(ex,{categories:g,onAddCategory:O,onUpdateCategory:B,onDeleteCategory:G,onAddSubCategory:U,onUpdateSubCategory:L,onDeleteSubCategory:F,onToggleCategoryVisibility:_,balancesVisible:w})})]})]})]}):(0,s.jsxs)("div",{className:"flex flex-col min-h-screen",children:[(0,s.jsx)(o.default,{title:"BudgetWise",balancesVisible:w,onToggleBalances:V}),(0,s.jsx)(e7.eX,{})]})}}},e=>{var a=a=>e(e.s=a);e.O(0,[73,671,440,521,422,59,337,375,321,441,684,358],()=>a(67813)),_N_E=e.O()}]);